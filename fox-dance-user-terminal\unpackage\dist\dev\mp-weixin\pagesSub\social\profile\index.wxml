<view class="profile-container data-v-4df458ff"><view class="header-section data-v-4df458ff"><view class="header-bg data-v-4df458ff"></view><view class="header-actions data-v-4df458ff"><u-icon vue-id="709747e7-1" name="scan" color="#fff" size="48rpx" data-event-opts="{{[['^click',[['scanCode']]]]}}" bind:click="__e" class="data-v-4df458ff" bind:__l="__l"></u-icon><u-icon vue-id="709747e7-2" name="setting" color="#fff" size="48rpx" data-event-opts="{{[['^click',[['goSettings']]]]}}" bind:click="__e" class="data-v-4df458ff" bind:__l="__l"></u-icon></view><view class="user-info-section data-v-4df458ff"><view class="user-avatar-container data-v-4df458ff"><u-avatar vue-id="709747e7-3" src="{{userInfo.avatar}}" size="120" data-event-opts="{{[['^click',[['editAvatar']]]]}}" bind:click="__e" class="data-v-4df458ff" bind:__l="__l"></u-avatar></view><view class="user-info-content data-v-4df458ff"><view class="user-info-row data-v-4df458ff"><view class="user-details data-v-4df458ff"><text class="nickname data-v-4df458ff">{{userInfo.nickname}}</text><text class="user-id data-v-4df458ff">{{"ID: "+userInfo.userId}}</text><text class="dance-type data-v-4df458ff">{{"舞种："+(userInfo.danceType||'街舞')}}</text><text class="bio data-v-4df458ff">{{userInfo.bio||'美食爱好者 | 旅行达人 | 摄影 | 生活方式博主'}}</text></view><view class="edit-section data-v-4df458ff"><text data-event-opts="{{[['tap',[['editProfile',['$event']]]]]}}" class="edit-link data-v-4df458ff" bindtap="__e">编辑资料</text></view></view><view class="stats-row data-v-4df458ff"><view data-event-opts="{{[['tap',[['switchTab',[0]]]]]}}" class="stat-item data-v-4df458ff" bindtap="__e"><text class="stat-number data-v-4df458ff">{{userInfo.postCount}}</text><text class="stat-label data-v-4df458ff">帖子</text></view><view data-event-opts="{{[['tap',[['goLikeList',['$event']]]]]}}" class="stat-item data-v-4df458ff" bindtap="__e"><text class="stat-number data-v-4df458ff">{{userInfo.likeCount}}</text><text class="stat-label data-v-4df458ff">获赞</text></view></view></view></view></view><scroll-view class="content data-v-4df458ff" scroll-y="{{true}}"><view class="tabs-container data-v-4df458ff"><u-tabs vue-id="709747e7-4" list="{{tabs}}" current="{{currentTab}}" lineWidth="30" lineColor="#303133" activeStyle="{{({color:'#303133',fontWeight:'bold'})}}" inactiveStyle="{{({color:'#606266'})}}" data-event-opts="{{[['^change',[['switchTab']]]]}}" bind:change="__e" class="data-v-4df458ff" bind:__l="__l"></u-tabs></view><view class="posts-content data-v-4df458ff"><block wx:if="{{$root.g0}}"><view class="post-grid data-v-4df458ff"><block wx:for="{{tabs[currentTab].data}}" wx:for-item="post" wx:for-index="__i0__" wx:key="id"><post-card class="post-card-item data-v-4df458ff" vue-id="{{'709747e7-5-'+__i0__}}" post="{{post}}" data-event-opts="{{[['^click',[['goPostDetail']]],['^userClick',[['goUserProfile']]],['^like',[['onPostLike']]]]}}" bind:click="__e" bind:userClick="__e" bind:like="__e" bind:__l="__l"></post-card></block></view></block><block wx:else><view class="empty-state data-v-4df458ff"><u-empty vue-id="709747e7-6" mode="list" text="暂无内容" class="data-v-4df458ff" bind:__l="__l"></u-empty></view></block></view></scroll-view></view>