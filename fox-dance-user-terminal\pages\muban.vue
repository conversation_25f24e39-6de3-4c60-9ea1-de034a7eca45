<template>
	<view class="settings">
		
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
export default {
	data() {
		return {
			isLogined:true,
		}
	},
	onShow() {
		
	},
	methods: {
		
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.settings{overflow: hidden;}
page{padding-bottom: 0;}
</style>