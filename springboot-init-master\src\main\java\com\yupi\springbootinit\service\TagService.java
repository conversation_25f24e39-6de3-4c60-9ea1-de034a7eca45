package com.yupi.springbootinit.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yupi.springbootinit.model.entity.Tag;

import java.util.List;

/**
 * 标签服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface TagService extends IService<Tag> {

    /**
     * 创建标签
     *
     * @param name 标签名称
     * @param description 标签描述
     * @param color 标签颜色
     * @return 标签ID
     */
    Long createTag(String name, String description, String color);

    /**
     * 根据名称获取或创建标签
     *
     * @param name 标签名称
     * @return 标签
     */
    Tag getOrCreateTag(String name);

    /**
     * 批量获取或创建标签
     *
     * @param names 标签名称列表
     * @return 标签列表
     */
    List<Tag> getOrCreateTags(List<String> names);

    /**
     * 获取热门标签
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<Tag> getHotTags(Integer limit);

    /**
     * 获取使用次数最多的标签
     *
     * @param limit 限制数量
     * @return 标签列表
     */
    List<Tag> getTopUsedTags(Integer limit);

    /**
     * 增加标签使用次数
     *
     * @param tagId 标签ID
     * @param increment 增量
     * @return 是否成功
     */
    Boolean incrementUseCount(Long tagId, Integer increment);

    /**
     * 搜索标签
     *
     * @param keyword 关键词
     * @param current 页码
     * @param pageSize 每页大小
     * @return 标签分页结果
     */
    Page<Tag> searchTags(String keyword, Integer current, Integer pageSize);

    /**
     * 获取推荐标签
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐标签列表
     */
    List<Tag> getRecommendTags(Long userId, Integer limit);

    /**
     * 更新标签热门状态
     *
     * @param tagId 标签ID
     * @param isHot 是否热门
     * @return 是否成功
     */
    Boolean updateHotStatus(Long tagId, Boolean isHot);

    // ==================== 社交功能相关方法 ====================

    /**
     * 获取话题详情
     *
     * @param tagId 话题ID
     * @param currentUserId 当前用户ID
     * @return 话题详情
     */
    Object getTagDetail(Long tagId, Long currentUserId);

    /**
     * 获取话题下的帖子
     *
     * @param tagId 话题ID
     * @param current 页码
     * @param size 每页大小
     * @param sortBy 排序方式
     * @return 帖子列表
     */
    List<Object> getTagPosts(Long tagId, Integer current, Integer size, String sortBy);

    /**
     * 关注话题
     *
     * @param tagId 话题ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean followTag(Long tagId, Long userId);

    /**
     * 取消关注话题
     *
     * @param tagId 话题ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean unfollowTag(Long tagId, Long userId);

    /**
     * 获取用户关注的话题
     *
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @return 话题列表
     */
    List<Object> getFollowingTags(Long userId, Integer current, Integer size);
}
