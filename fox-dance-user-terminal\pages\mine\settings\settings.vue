<template>
	<view class="settings">
		
		<view class="set_con">
			<view class="set_con_li" @click.stop="navTo('/pages/login/xieYi?type=2')">隐私政策<image src="/static/images/introduce_more.png"></image></view>
			<view class="set_con_li" @click="navTo('/pages/mine/settings/feedback')">意见反馈<image src="/static/images/introduce_more.png"></image></view>
			<view class="set_con_li" @click="logoutTap">退出登录<image src="/static/images/introduce_more.png"></image></view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
export default {
	data() {
		return {
			isLogined:true,
		}
	},
	onShow() {
		
	},
	methods: {
		//退出登录
		logoutTap(){
			var that = this;
			uni.showModal({
				title:'温馨提示',
				content:'确定要退出登录吗？',
				success: function (rep) {
					if (rep.confirm) {
						uni.removeStorageSync('token');
						uni.removeStorageSync('userid');
						uni.showToast({
							icon:'none',
							title:'退出成功',
							duration: 2000
						});
						setTimeout(function(){
							uni.navigateBack()
						},1000)
					} else if (rep.cancel) {
						console.log('用户点击取消');
					}
				}
			});
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.settings{overflow: hidden;}
page{padding-bottom: 0;}
</style>