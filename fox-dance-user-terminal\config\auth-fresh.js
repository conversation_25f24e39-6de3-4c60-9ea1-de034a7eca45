import CryptoJs from 'crypto-js'
let SHA1 = CryptoJs.SHA1
let MD5 = CryptoJs.MD5
let AES = CryptoJs.AES
let Latin1 = CryptoJs.enc.Latin1
let mode = CryptoJs.mode.ECB
let padding = CryptoJs.pad.Pkcs7

let authFresh = {
  token: function(url, mobile, password) {
    let path = `${url}:${Date.now()}`
    if (mobile && password) {
      return `${mobile}:${this.aesEncrypt(this.encrypt(password), path)}`
    }

    let user = uni.getStorageSync('user')
    if (user.mobile && user.password) {
      return `${user.mobile}:${this.aesEncrypt(user.password, path)}`
    }

    return null
  },


  encrypt: function(password) {
    return SHA1(`${SHA1('blog_')}${MD5(password)}${MD5('_encrypt')}${SHA1(password)}`)
      .toString().slice(0, 16)
  },

  aesEncrypt: function(password, msg, iValue = '') {
    let message = Latin1.parse(msg)
    let key = Latin1.parse(password.slice(0, 16))
    let iv = Latin1.parse(iValue)
    return AES.encrypt(message, key, {
      iv,
      mode,
      padding
    }).toString()
  },
  aesDecrypt: function(password, ciphertext, iValue = '') {
    let key = Latin1.parse(password.slice(0, 16))
    let iv = Latin1.parse(iValue)
    return AES.decrypt(ciphertext, key, {
      iv,
      mode,
      padding
    }).toString()
  },


  logIn: function(mobile, password, data) {
    uni.setStorageSync('user', {
      mobile,
      password: this.encrypt(password),
      data
    })
  },
  logOut: function() {
    uni.removeStorageSync('user')
  },
  isLogin: function() {
    let user = uni.getStorageSync('user')
    if (!!user) {
      return user
    } else {
      return false
    }
  },
}

export default {
  // #ifndef VUE3
  install(Vue) {
    Vue.prototype.$authFresh = authFresh
  },
  // #endif

  // #ifdef VUE3
  install(app, ops) {
    app.config.globalProperties.$authFresh = authFresh
  },
  // #endif

  authFresh
}
