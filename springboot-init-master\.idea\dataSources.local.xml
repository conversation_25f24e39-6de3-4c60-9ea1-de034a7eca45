<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="IU-241.19416.15">
    <data-source name="@localhost" uuid="73245ab5-7bd8-4242-a4cf-374b8097c583">
      <database-info product="MySQL" version="8.0.34" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.0.34" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <secret-storage>master_key</secret-storage>
      <user-name>root</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="test" />
            <name qname="@" />
            <name qname="my_db" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="test@**************" uuid="97269e7a-3002-48af-a7e9-1af540c6b387">
      <database-info product="MySQL" version="5.7.44-log" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="5.7.44" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
        <jdbc-catalog-is-schema>true</jdbc-catalog-is-schema>
      </database-info>
      <case-sensitivity plain-identifiers="exact" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>ceshi_foxdance_c</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="ceshi_foxdance_c" />
            <name qname="admin_foxdance_c" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="@rm-2zeg25227e8xz8bjz4o.mysql.rds.aliyuncs.com" uuid="03d62e69-8a2e-4988-9366-ee50b8ce05f5">
      <database-info product="MySQL" version="8.0.36" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.0.36" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
        <jdbc-catalog-is-schema>true</jdbc-catalog-is-schema>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <secret-storage>master_key</secret-storage>
      <user-name>admin_foxdance_c</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="@" />
            <name qname="admin_foxdance_c" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="<EMAIL>" uuid="a7e5f657-a31b-4faa-8e7b-71d0ef99bfbd">
      <database-info product="MySQL" version="8.0.36" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.0.36" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
        <jdbc-catalog-is-schema>true</jdbc-catalog-is-schema>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <user-name>admin_foxdance_c</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="@" />
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>