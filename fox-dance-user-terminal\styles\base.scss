@import "./builder.scss";
// @import "https://qxjj.xinzhiyukeji.cn/Maoken_Glitch_Sans.scss";
page {
    /* 定义一些主题色及基础样式 */
    font-family: PingFang SC, Arial, Hiragino Sans GB, Microsoft YaHei, sans-serif;
    font-size: 28rpx;
    color: $-color-normal;
	padding-bottom: env(safe-area-inset-bottom);
    background-color: $-color-body;
}
@font-face{
    font-family: 'Maoken Glitch Sans';
    // src: url('https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/Maoken_Glitch_Sans.subset.OTF') format('opentype');
    src: url('https://file.foxdance.com.cn/ttf/Maoken_Glitch_Sans.subset.OTF') format('opentype');
	
}

@font-face{
    font-family: AaHouDiHei;
    // src: url('https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/AaHouDiHei.ttf') format('opentype');
	src: url('https://file.foxdance.com.cn/ttf/AaHouDiHei.ttf') format('opentype');
}

@font-face{
    font-family: pht;
    // src: url('https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/AaHouDiHei.ttf') format('opentype');
	src: url('https://file.foxdance.com.cn/ttf/pht.otf') format('opentype');
}

.bold {
    font-weight: bold;
}

/* 超出隐藏 */
.line1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.line2 {
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 中划线 */
.line-through {
    text-decoration: line-through;
}


/* br60 */
.br60 {
    border-radius: 60rpx;
}

/* 初始化按钮 */
page button {
    padding: 0;
    margin: 0;
    background-color: transparent;
    font-weight: normal;
    font-size: 28rpx;
    overflow: unset;
	margin-left: 0;
	margin-right: 0;
}
page button::after {
	border: none;
}

button[type=primary] {
	background-color: $-color-primary;
}
.button-hover[type=primary] {
	background-color: $-color-primary;
}
/* 按钮大小 */
button[size="xs"]{
    line-height: 58rpx;
    height: 58rpx;
    font-size: 26rpx;
    padding: 0 30rpx;
}

button[size="sm"] {
    line-height: 62rpx;
    height: 62rpx;
    font-size: 28rpx;
    padding: 0 30rpx;
}

button[size="md"]{
    line-height: 70rpx;
    height: 70rpx;
    font-size: 30rpx;
    padding: 0 30rpx;
}
button[size="lg"]{
    line-height: 80rpx;
    height: 80rpx;
    font-size: 32rpx;
    padding: 0 30rpx;
}


//******图标******/

.icon-xs {
    @include icon-image(28rpx);
}

.icon-sm {
    @include icon-image(30rpx);
}

.icon {
	@include icon-image(34rpx);
}
.icon-md {
	@include icon-image(44rpx);
}
.icon-lg {
    @include icon-image(52rpx);
}

.icon-xl {
    @include icon-image(64rpx);
}

.icon-xxl {
    @include icon-image(120rpx);
}

.img-null {
    width: 300rpx;
    height: 300rpx;
}


/* 隐藏滚动条 */
::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
}
//#ifndef H5
/* 单选 */
radio {
	
	.uni-radio-input {
	    border-radius: 50%;
	    width: 34rpx;
	    height: 34rpx;
	}
	.uni-radio-input {
	    border-radius: 50%;
	    width: 34rpx;
	    height: 34rpx;
		&.uni-radio-input-checked {
			border: 1px solid $-color-primary !important;
			background-color: $-color-primary !important;
		}
	}
} 


/* 多选 */
checkbox {
	.uni-checkbox-input {
	    border-radius: 50%;
	    width: 34rpx;
	    height: 34rpx;
		&.uni-checkbox-input-checked {
		    border: 1px solid $-color-primary !important;
		    background-color: $-color-primary !important;
		    color: #fff !important;
			&::before {
				font-size: 35rpx;
			}
		}
	}
}
//#endif
// #ifdef MP-WEIXIN
/* 单选 */
radio {
	
	.wx-radio-input {
	    border-radius: 50%;
	    width: 34rpx;
	    height: 34rpx;
	}
	.wx-radio-input {
	    border-radius: 50%;
	    width: 34rpx;
	    height: 34rpx;
		&.wx-radio-input-checked {
			border: 1px solid $-color-primary !important;
			background-color: $-color-primary !important;
		}
	}
} 


/* 多选 */
checkbox {
	.wx-checkbox-input {
	    border-radius: 50%;
	    width: 34rpx;
	    height: 34rpx;
		&.wx-checkbox-input-checked {
		    border: 1px solid $-color-primary !important;
		    background-color: $-color-primary !important;
		    color: #fff !important;
			&::before {
				font-size: 35rpx;
			}
		}
	}
}
// #endif



