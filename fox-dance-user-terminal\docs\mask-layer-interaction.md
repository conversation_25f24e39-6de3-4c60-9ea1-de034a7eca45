# 蒙版层交互功能说明文档

## 功能概述
为微信小程序的评论功能添加了蒙版层交互功能，提升用户体验。当用户点击输入框获得焦点时，显示半透明蒙版层，用户可以通过点击蒙版层快速收起键盘。

## 实现的功能

### 1. 蒙版层显示逻辑
- **触发条件**：当输入框获得焦点且键盘弹出时（`isKeyboardShow = true`）
- **显示效果**：半透明黑色蒙版覆盖除输入框外的所有页面内容
- **层级关系**：蒙版层在输入框下方（z-index: 90），在其他内容上方

### 2. 蒙版层交互功能
- **点击蒙版层**：自动收起键盘并隐藏蒙版层
- **输入框失焦**：自动隐藏蒙版层
- **键盘收起**：自动隐藏蒙版层

### 3. 动画效果
- **淡入动画**：蒙版层显示时使用 0.3s 的淡入效果
- **平滑过渡**：与键盘适配功能协同工作，确保交互流畅

## 修改的文件

### 1. comment.vue
**模板修改：**
```html
<!-- 蒙版层 -->
<view v-if="isKeyboardShow" class="mask-layer" @tap="hideMaskAndKeyboard"></view>
```

**方法添加：**
```javascript
// 隐藏蒙版层并收起键盘
hideMaskAndKeyboard() {
  console.log('点击蒙版层，收起键盘');
  
  // 让输入框失去焦点
  if (this.$refs.mainCommentInput) {
    this.$refs.mainCommentInput.blur();
  }
  
  // 强制隐藏键盘
  uni.hideKeyboard();
  
  // 重置键盘状态
  this.isKeyboardShow = false;
  this.keyboardHeight = 0;
  this.inputContainerBottom = 0;
}
```

### 2. comment-detail.vue
**模板修改：**
```html
<!-- 蒙版层：回复状态或键盘弹出时显示 -->
<view v-if="isReplying || isKeyboardShow" class="mask-layer" 
      @tap="isReplying ? cancelReply() : hideMaskAndKeyboard()"></view>
```

**智能交互逻辑：**
- 当处于回复状态时，点击蒙版层执行 `cancelReply()`
- 当仅键盘弹出时，点击蒙版层执行 `hideMaskAndKeyboard()`

### 3. CommentInput.vue
**新增blur方法：**
```javascript
// 失去焦点
blur() {
  // 重置autoFocus状态
  this.autoFocus = false;
  
  // 微信小程序使用选择器进行失焦
  // #ifdef MP-WEIXIN
  const query = uni.createSelectorQuery().in(this);
  query.select('.input-textarea').fields({
    properties: ['blur'],
    context: true
  }, res => {
    if (res && res.context) {
      res.context.blur();
    }
  }).exec();
  // #endif
  
  // 其他平台使用原生方法
  // #ifndef MP-WEIXIN
  const inputComponent = this.$refs.textareaRef;
  if (inputComponent) {
    inputComponent.blur();
  }
  // #endif
}
```

## CSS样式

### 蒙版层样式
```scss
.mask-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 90;
  opacity: 0;
  animation: maskFadeIn 0.3s ease-out forwards;
}
```

### 淡入动画
```scss
@keyframes maskFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
```

## 技术特点

### 1. 微信小程序兼容性
- 使用条件编译确保在微信小程序环境下正确工作
- 针对微信小程序的特殊API进行适配

### 2. 状态管理
- 通过 `isKeyboardShow` 状态精确控制蒙版层显示
- 与现有键盘适配功能完美协同

### 3. 用户体验优化
- 半透明蒙版不会完全遮挡内容，用户仍可看到背景
- 点击蒙版层提供直观的关闭方式
- 平滑的动画效果提升交互体验

### 4. 层级管理
- 蒙版层 z-index: 90
- 输入框容器 z-index: 100
- 确保输入框始终在蒙版层上方

## 使用场景

### 1. 评论页面 (comment.vue)
- 用户点击底部输入框发表评论时
- 键盘弹出，显示蒙版层
- 点击蒙版层快速收起键盘

### 2. 评论详情页面 (comment-detail.vue)
- 用户回复评论时
- 支持两种蒙版层状态：
  - 回复状态蒙版层（点击取消回复）
  - 键盘弹出蒙版层（点击收起键盘）

## 测试建议

### 功能测试
1. **基础交互测试**
   - 点击输入框，验证蒙版层是否正确显示
   - 点击蒙版层，验证键盘是否正确收起

2. **状态同步测试**
   - 验证蒙版层显示/隐藏与键盘状态同步
   - 验证输入框焦点状态与蒙版层状态同步

3. **动画效果测试**
   - 验证蒙版层淡入动画是否流畅
   - 验证与键盘适配动画的协调性

### 兼容性测试
- 微信小程序环境下的完整功能测试
- H5环境下的降级处理测试
- 不同设备尺寸下的显示效果测试

## 注意事项

1. **层级管理**：确保蒙版层不会遮挡输入框本身
2. **状态同步**：蒙版层状态必须与键盘状态保持同步
3. **性能优化**：蒙版层仅在需要时显示，避免不必要的渲染
4. **用户体验**：蒙版层透明度适中，不会完全遮挡背景内容

## 总结

蒙版层交互功能的添加显著提升了微信小程序评论功能的用户体验：
- ✅ 提供直观的键盘收起方式
- ✅ 与现有键盘适配功能完美协同
- ✅ 支持平滑的动画过渡效果
- ✅ 兼容微信小程序环境
- ✅ 保持代码的可维护性和扩展性
