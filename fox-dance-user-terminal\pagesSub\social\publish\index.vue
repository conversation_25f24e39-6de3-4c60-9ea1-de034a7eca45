<template>
  <view class="publish-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <text class="cancel-btn" @click="goBack">取消</text>
        <text class="title">发布帖子</text>
        <text 
          class="publish-btn" 
          :class="{ disabled: !canPublish }"
          @click="publishPost"
        >
          发布
        </text>
      </view>
    </view>

    <scroll-view class="content" scroll-y>
      <!-- 用户信息 -->
      <view class="user-section">
        <u-avatar :src="userInfo.avatar" size="40"></u-avatar>
        <text class="username">{{ userInfo.nickname }}</text>
      </view>

      <!-- 文字输入 -->
      <view class="text-section">
        <textarea
          v-model="postContent"
          class="content-input"
          placeholder="分享你的生活..."
          :maxlength="500"
          auto-height
          :show-confirm-bar="false"
        />
        <view class="char-count">{{ postContent.length }}/500</view>
      </view>

      <!-- 图片上传 -->
      <view class="image-section">
        <view class="image-grid">
          <view 
            v-for="(image, index) in selectedImages" 
            :key="index"
            class="image-item"
          >
            <image :src="image" class="uploaded-image" mode="aspectFill" />
            <view class="delete-btn" @click="removeImage(index)">
              <u-icon name="close" color="#fff" size="16"></u-icon>
            </view>
          </view>
          <view 
            v-if="selectedImages.length < 9"
            class="add-image-btn"
            @click="chooseImage"
          >
            <u-icon name="camera" color="#999" size="32"></u-icon>
            <text class="add-text">添加图片</text>
          </view>
        </view>
      </view>

      <!-- 功能选项 -->
      <view class="options-section">
        <!-- 话题选择 -->
        <view class="option-item" @click="selectTopic">
          <view class="option-left">
            <u-icon name="tags" color="#2979ff" size="20"></u-icon>
            <text class="option-text">添加话题</text>
          </view>
          <view class="option-right">
            <text v-if="selectedTopics.length" class="selected-topics">
              {{ selectedTopics.map(t => '#' + t).join(' ') }}
            </text>
            <u-icon name="arrow-right" color="#999" size="16"></u-icon>
          </view>
        </view>

        <!-- 位置定位 -->
        <view class="option-item" @click="selectLocation">
          <view class="option-left">
            <u-icon name="map" color="#2979ff" size="20"></u-icon>
            <text class="option-text">添加位置</text>
          </view>
          <view class="option-right">
            <text v-if="selectedLocation" class="selected-location">
              {{ selectedLocation.name }}
            </text>
            <u-icon name="arrow-right" color="#999" size="16"></u-icon>
          </view>
        </view>

        <!-- 可见性设置 -->
        <view class="option-item" @click="setVisibility">
          <view class="option-left">
            <u-icon name="eye" color="#2979ff" size="20"></u-icon>
            <text class="option-text">可见性</text>
          </view>
          <view class="option-right">
            <text class="visibility-text">{{ visibilityText }}</text>
            <u-icon name="arrow-right" color="#999" size="16"></u-icon>
          </view>
        </view>
      </view>

      <!-- 提醒文字 -->
      <view class="tips-section">
        <text class="tips-text">
          发布即表示同意《社区公约》，请文明发言，共建和谐社区
        </text>
      </view>
    </scroll-view>

    <!-- 话题选择弹窗 -->
    <u-popup v-model="showTopicModal" mode="bottom" border-radius="20">
      <view class="topic-modal">
        <view class="modal-header">
          <text class="modal-title">选择话题</text>
          <u-icon name="close" @click="showTopicModal = false"></u-icon>
        </view>
        <view class="topic-search">
          <u-input 
            v-model="topicKeyword" 
            placeholder="搜索话题"
            prefix-icon="search"
            @input="searchTopics"
          />
        </view>
        <view class="topic-list">
          <view 
            v-for="topic in filteredTopics" 
            :key="topic.id"
            class="topic-option"
            :class="{ selected: selectedTopics.includes(topic.name) }"
            @click="toggleTopic(topic)"
          >
            <text class="topic-name">#{{ topic.name }}</text>
            <text class="topic-count">{{ topic.postCount }}条帖子</text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 位置选择弹窗 -->
    <u-popup v-model="showLocationModal" mode="bottom" border-radius="20">
      <view class="location-modal">
        <view class="modal-header">
          <text class="modal-title">选择位置</text>
          <u-icon name="close" @click="showLocationModal = false"></u-icon>
        </view>
        <view class="location-list">
          <view 
            v-for="location in nearbyLocations" 
            :key="location.id"
            class="location-option"
            @click="selectLocationItem(location)"
          >
            <u-icon name="map-pin" color="#2979ff" size="16"></u-icon>
            <view class="location-info">
              <text class="location-name">{{ location.name }}</text>
              <text class="location-address">{{ location.address }}</text>
            </view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { createPost, getHotTags } from '@/utils/socialApi.js'

export default {
  name: 'SocialPublish',
  data() {
    return {
      userInfo: {
        avatar: 'https://picsum.photos/100/100?random=999',
        nickname: '我的昵称'
      },
      postContent: '',
      selectedImages: [],
      selectedTopics: [],
      selectedLocation: null,
      visibility: 'public', // public, friends, private
      
      showTopicModal: false,
      showLocationModal: false,
      
      topicKeyword: '',
      allTopics: [
        { id: 1, name: '街舞', postCount: 1234 },
        { id: 2, name: '现代舞', postCount: 856 },
        { id: 3, name: '芭蕾', postCount: 642 },
        { id: 4, name: '拉丁舞', postCount: 789 },
        { id: 5, name: '爵士舞', postCount: 456 },
        { id: 6, name: '民族舞', postCount: 321 },
        { id: 7, name: '古典舞', postCount: 298 },
        { id: 8, name: '舞蹈教学', postCount: 567 },
        { id: 9, name: '舞蹈比赛', postCount: 234 },
        { id: 10, name: '舞蹈培训', postCount: 189 }
      ],
      
      nearbyLocations: [
        {
          id: 1,
          name: '星巴克咖啡',
          address: '北京市朝阳区三里屯太古里'
        },
        {
          id: 2,
          name: '三里屯太古里',
          address: '北京市朝阳区三里屯路19号'
        },
        {
          id: 3,
          name: '朝阳公园',
          address: '北京市朝阳区朝阳公园南路1号'
        }
      ]
    }
  },
  computed: {
    canPublish() {
      return this.postContent.trim().length > 0 || this.selectedImages.length > 0
    },
    
    visibilityText() {
      const map = {
        public: '公开',
        friends: '仅朋友可见',
        private: '仅自己可见'
      }
      return map[this.visibility]
    },
    
    filteredTopics() {
      if (!this.topicKeyword) return this.allTopics
      return this.allTopics.filter(topic => 
        topic.name.includes(this.topicKeyword)
      )
    }
  },
  onLoad() {
    this.loadHotTopics()
  },
  methods: {
    // 加载热门话题
    async loadHotTopics() {
      try {
        const hotTags = await getHotTags(20)
        if (hotTags && hotTags.length > 0) {
          this.allTopics = hotTags.map(tag => ({
            id: tag.tagId || tag.id,
            name: tag.tagName || tag.name,
            postCount: tag.postCount || 0
          }))
        }
      } catch (error) {
        console.error('加载热门话题失败:', error)
        // 使用默认话题列表
      }
    },
    goBack() {
      if (this.postContent || this.selectedImages.length) {
        uni.showModal({
          title: '提示',
          content: '确定要放弃编辑吗？',
          success: (res) => {
            if (res.confirm) {
              uni.navigateBack()
            }
          }
        })
      } else {
        uni.navigateBack()
      }
    },

    chooseImage() {
      const maxCount = 9 - this.selectedImages.length
      uni.chooseImage({
        count: maxCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.selectedImages.push(...res.tempFilePaths)
        }
      })
    },

    removeImage(index) {
      this.selectedImages.splice(index, 1)
    },

    selectTopic() {
      this.showTopicModal = true
    },

    toggleTopic(topic) {
      const index = this.selectedTopics.indexOf(topic.name)
      if (index > -1) {
        this.selectedTopics.splice(index, 1)
      } else {
        if (this.selectedTopics.length < 3) {
          this.selectedTopics.push(topic.name)
        } else {
          this.$u.toast('最多选择3个话题')
        }
      }
    },

    searchTopics() {
      // 搜索话题逻辑
    },

    selectLocation() {
      this.showLocationModal = true
    },

    selectLocationItem(location) {
      this.selectedLocation = location
      this.showLocationModal = false
    },

    setVisibility() {
      uni.showActionSheet({
        itemList: ['公开', '仅朋友可见', '仅自己可见'],
        success: (res) => {
          const visibilityMap = ['public', 'friends', 'private']
          this.visibility = visibilityMap[res.tapIndex]
        }
      })
    },

    async publishPost() {
      if (!this.canPublish) return

      uni.showLoading({ title: '发布中...' })

      try {
        // 构建发布数据
        const postData = {
          content: this.postContent.trim(),
          images: this.selectedImages,
          coverImage: this.selectedImages.length > 0 ? this.selectedImages[0] : null,
          tags: this.selectedTopics.map(topic => topic.name),
          locationName: this.location,
          isPublic: this.visibility === 'public' ? 1 : 0
        }

        // 调用发布API
        const result = await createPost(postData)

        uni.hideLoading()

        if (result) {
          this.$u.toast('发布成功')

          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          this.$u.toast('发布失败，请重试')
        }

      } catch (error) {
        console.error('发布帖子失败:', error)
        uni.hideLoading()
        this.$u.toast('发布失败，请重试')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.publish-container {
  min-height: 100vh;

  background: #f8f9fa;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.cancel-btn, .publish-btn {
  font-size: 16px;
  color: #2979ff;
}

.publish-btn.disabled {
  color: #ccc;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.content {
  margin-top: calc(44px + var(--status-bar-height));
  padding: 16px;
  width: auto;
}

.user-section {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.username {
  margin-left: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.text-section {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
}

.content-input {
  width: 100%;
  min-height: 120px;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
}

.char-count {
  position: absolute;
  bottom: 12px;
  right: 16px;
  font-size: 12px;
  color: #999;
}

.image-section {
  margin-bottom: 16px;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-item {
  position: relative;
  width: calc(33.33% - 6px);
  height: 100px;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.delete-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-image-btn {
  width: calc(33.33% - 6px);
  height: 100px;
  background: #f5f5f5;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.add-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.options-section {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.option-item:last-child {
  border-bottom: none;
}

.option-left {
  display: flex;
  align-items: center;
}

.option-text {
  margin-left: 12px;
  font-size: 15px;
  color: #333;
}

.option-right {
  display: flex;
  align-items: center;
}

.selected-topics, .selected-location, .visibility-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.tips-section {
  padding: 16px;
}

.tips-text {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  text-align: center;
}

.topic-modal, .location-modal {
  background: #fff;
  border-radius: 20px 20px 0 0;
  max-height: 60vh;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.topic-search {
  padding: 16px 20px;
}

.topic-list, .location-list {
  max-height: 300px;
  overflow-y: auto;
}

.topic-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.topic-option.selected {
  background: #f0f8ff;
}

.topic-name {
  font-size: 15px;
  color: #333;
}

.topic-count {
  font-size: 12px;
  color: #999;
}

.location-option {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.location-info {
  margin-left: 12px;
  flex: 1;
}

.location-name {
  font-size: 15px;
  color: #333;
  display: block;
}

.location-address {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  display: block;
}
</style>
