@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.home-container.data-v-de45d8c2 {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 100px;
}
.header.data-v-de45d8c2 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: 25px;
}
.header-content.data-v-de45d8c2 {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}
.logo-text.data-v-de45d8c2 {
  font-size: 36rpx;
  font-weight: 600;
  color: #2979ff;
}
.topic-tabs-container.data-v-de45d8c2 {
  position: fixed;
  top: calc(88rpx + 25px);
  left: 0;
  right: 0;
  z-index: 99;
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;
}
/* uview tabs组件样式优化 */
.topic-tabs-container.data-v-de45d8c2  .u-tabs {
  background: #fff;
}
.topic-tabs-container.data-v-de45d8c2  .u-tabs__wrapper__nav__item {
  padding: 0 32rpx !important;
}
.topic-tabs-container.data-v-de45d8c2  .u-tabs__wrapper__nav__item__text {
  font-size: 28rpx !important;
  font-weight: 500;
}
.topic-tabs-container.data-v-de45d8c2  .u-tabs__wrapper__nav__line {
  border-radius: 6rpx;
}
.post-list.data-v-de45d8c2 {
  margin-top: calc(168rpx + 25px);
  margin: 230rpx 26rpx;
  width: auto;
}
.post-grid.data-v-de45d8c2 {
  display: flex;
  flex-wrap: wrap;
  gap: 14rpx;
  padding-bottom: 40rpx;
}
.post-card-item.data-v-de45d8c2 {
  width: calc(50% - 8rpx);
  margin-bottom: 16rpx;
}
.empty-state.data-v-de45d8c2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 160rpx 40rpx;
}
.empty-text.data-v-de45d8c2 {
  font-size: 32rpx;
  color: #999;
  margin: 32rpx 0 16rpx;
}
.empty-desc.data-v-de45d8c2 {
  font-size: 28rpx;
  color: #ccc;
}
.load-more.data-v-de45d8c2 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}
.load-text.data-v-de45d8c2 {
  margin-left: 16rpx;
  color: #999;
  font-size: 28rpx;
}
/* 响应式设计 */
@media screen and (max-width: 375px) {
.post-list.data-v-de45d8c2 {
    padding: 12rpx;
}
.post-grid.data-v-de45d8c2 {
    gap: 12rpx;
}
.post-card-item.data-v-de45d8c2 {
    width: calc(50% - 6rpx);
}
}
@media screen and (min-width: 768px) {
.post-grid.data-v-de45d8c2 {
    gap: 24rpx;
}
.post-card-item.data-v-de45d8c2 {
    width: calc(33.33% - 16rpx);
}
}
@media screen and (min-width: 1024px) {
.post-list.data-v-de45d8c2 {
    padding: 32rpx 64rpx;
}
.post-card-item.data-v-de45d8c2 {
    width: calc(25% - 18rpx);
}
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.discover-container.data-v-1fcbd0ae {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 200rpx;
}
.header.data-v-1fcbd0ae {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  padding: 25px 32rpx 24rpx;
  border-bottom: 2rpx solid #e4e7ed;
}
.search-bar.data-v-1fcbd0ae {
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
}
.search-placeholder.data-v-1fcbd0ae {
  margin-left: 16rpx;
  color: #999;
  font-size: 28rpx;
}
.content.data-v-1fcbd0ae {
  margin-top: calc(125rpx + 25px);
  padding: 0 32rpx;
  width: auto;
}
.section.data-v-1fcbd0ae {
  margin-bottom: 48rpx;
}
.section-header.data-v-1fcbd0ae {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-title.data-v-1fcbd0ae {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.more-btn.data-v-1fcbd0ae {
  font-size: 28rpx;
  color: #2979ff;
}
.topic-grid.data-v-1fcbd0ae {
  display: flex;
  flex-wrap: wrap;
  gap: 23rpx;
}
.topic-card.data-v-1fcbd0ae {
  width: calc(50% - 12rpx);
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.topic-cover.data-v-1fcbd0ae {
  width: 100%;
  height: 160rpx;
}
.topic-info.data-v-1fcbd0ae {
  padding: 24rpx;
}
.topic-name.data-v-1fcbd0ae {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
}
.topic-count.data-v-1fcbd0ae {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}
.user-scroll.data-v-1fcbd0ae {
  white-space: nowrap;
}
.user-list.data-v-1fcbd0ae {
  display: flex;
  gap: 32rpx;
  padding-bottom: 16rpx;
}
.user-card.data-v-1fcbd0ae {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  min-width: 200rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.user-name.data-v-1fcbd0ae {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin: 16rpx 0 8rpx;
  text-align: center;
}
.user-desc.data-v-1fcbd0ae {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
  text-align: center;
}
.hot-posts.data-v-1fcbd0ae {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.hot-post-item.data-v-1fcbd0ae {
  display: flex;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.hot-post-item.data-v-1fcbd0ae:last-child {
  border-bottom: none;
}
.post-content.data-v-1fcbd0ae {
  flex: 1;
  margin-right: 24rpx;
}
.user-info.data-v-1fcbd0ae {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.username.data-v-1fcbd0ae {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #666;
}
.post-text.data-v-1fcbd0ae {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  display: block;
  margin-bottom: 16rpx;
}
.post-stats.data-v-1fcbd0ae {
  display: flex;
  gap: 32rpx;
}
.stat-item.data-v-1fcbd0ae {
  font-size: 24rpx;
  color: #999;
}
.post-cover.data-v-1fcbd0ae {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
}
.featured-grid.data-v-1fcbd0ae {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.featured-item.data-v-1fcbd0ae {
  position: relative;
  height: 240rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.featured-cover.data-v-1fcbd0ae {
  width: 100%;
  height: 100%;
}
.featured-overlay.data-v-1fcbd0ae {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 32rpx 32rpx;
}
.featured-title.data-v-1fcbd0ae {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  display: block;
}
.featured-subtitle.data-v-1fcbd0ae {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8rpx;
  display: block;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.publish-container.data-v-bb7c3636 {
  min-height: 100vh;
  background: #f8f9fa;
}
.header.data-v-bb7c3636 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding-top: 25px;
}
.header-content.data-v-bb7c3636 {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}
.cancel-btn.data-v-bb7c3636, .publish-btn.data-v-bb7c3636 {
  font-size: 16px;
  color: #2979ff;
}
.publish-btn.disabled.data-v-bb7c3636 {
  color: #ccc;
}
.title.data-v-bb7c3636 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.content.data-v-bb7c3636 {
  margin-top: calc(44px + 25px);
  padding: 16px;
  width: auto;
}
.user-section.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.username.data-v-bb7c3636 {
  margin-left: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
.text-section.data-v-bb7c3636 {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
}
.content-input.data-v-bb7c3636 {
  width: 100%;
  min-height: 120px;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
}
.char-count.data-v-bb7c3636 {
  position: absolute;
  bottom: 12px;
  right: 16px;
  font-size: 12px;
  color: #999;
}
.image-section.data-v-bb7c3636 {
  margin-bottom: 16px;
}
.image-grid.data-v-bb7c3636 {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.image-item.data-v-bb7c3636 {
  position: relative;
  width: calc(33.33% - 6px);
  height: 100px;
}
.uploaded-image.data-v-bb7c3636 {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}
.delete-btn.data-v-bb7c3636 {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-image-btn.data-v-bb7c3636 {
  width: calc(33.33% - 6px);
  height: 100px;
  background: #f5f5f5;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.add-text.data-v-bb7c3636 {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
.options-section.data-v-bb7c3636 {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 16px;
}
.option-item.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}
.option-item.data-v-bb7c3636:last-child {
  border-bottom: none;
}
.option-left.data-v-bb7c3636 {
  display: flex;
  align-items: center;
}
.option-text.data-v-bb7c3636 {
  margin-left: 12px;
  font-size: 15px;
  color: #333;
}
.option-right.data-v-bb7c3636 {
  display: flex;
  align-items: center;
}
.selected-topics.data-v-bb7c3636, .selected-location.data-v-bb7c3636, .visibility-text.data-v-bb7c3636 {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}
.tips-section.data-v-bb7c3636 {
  padding: 16px;
}
.tips-text.data-v-bb7c3636 {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  text-align: center;
}
.topic-modal.data-v-bb7c3636, .location-modal.data-v-bb7c3636 {
  background: #fff;
  border-radius: 20px 20px 0 0;
  max-height: 60vh;
}
.modal-header.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}
.modal-title.data-v-bb7c3636 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.topic-search.data-v-bb7c3636 {
  padding: 16px 20px;
}
.topic-list.data-v-bb7c3636, .location-list.data-v-bb7c3636 {
  max-height: 300px;
  overflow-y: auto;
}
.topic-option.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}
.topic-option.selected.data-v-bb7c3636 {
  background: #f0f8ff;
}
.topic-name.data-v-bb7c3636 {
  font-size: 15px;
  color: #333;
}
.topic-count.data-v-bb7c3636 {
  font-size: 12px;
  color: #999;
}
.location-option.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}
.location-info.data-v-bb7c3636 {
  margin-left: 12px;
  flex: 1;
}
.location-name.data-v-bb7c3636 {
  font-size: 15px;
  color: #333;
  display: block;
}
.location-address.data-v-bb7c3636 {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  display: block;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.message-container.data-v-2caef1dd {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 100px;
}
.header.data-v-2caef1dd {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding-top: 25px;
}
.header-content.data-v-2caef1dd {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}
.title.data-v-2caef1dd {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.header-actions.data-v-2caef1dd {
  display: flex;
  gap: 16px;
}
.quick-actions.data-v-2caef1dd {
  margin-top: calc(44px + 25px);
  background: #fff;
  padding: 16px;
  display: flex;
  justify-content: space-around;
  border-bottom: 8px solid #f8f9fa;
}
.action-item.data-v-2caef1dd {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.action-icon.data-v-2caef1dd {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.action-icon.system.data-v-2caef1dd {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.action-icon.like.data-v-2caef1dd {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.action-icon.follow.data-v-2caef1dd {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.action-text.data-v-2caef1dd {
  font-size: 12px;
  color: #666;
}
.unread-badge.data-v-2caef1dd {
  position: absolute;
  top: -2px;
  right: 8px;
  background: #ff4757;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}
.chat-list.data-v-2caef1dd {
  background: #fff;
}
.chat-item.data-v-2caef1dd {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}
.chat-item.data-v-2caef1dd:last-child {
  border-bottom: none;
}
.chat-avatar.data-v-2caef1dd {
  position: relative;
  margin-right: 12px;
}
.online-dot.data-v-2caef1dd {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #52c41a;
  border: 2px solid #fff;
  border-radius: 6px;
}
.chat-content.data-v-2caef1dd {
  flex: 1;
  min-width: 0;
}
.chat-header.data-v-2caef1dd {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}
.chat-name.data-v-2caef1dd {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
.chat-time.data-v-2caef1dd {
  font-size: 12px;
  color: #999;
}
.chat-preview.data-v-2caef1dd {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.message-preview.data-v-2caef1dd {
  flex: 1;
  min-width: 0;
}
.preview-text.data-v-2caef1dd {
  font-size: 14px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}
.chat-status.data-v-2caef1dd {
  display: flex;
  align-items: center;
  gap: 8px;
}
.unread-count.data-v-2caef1dd {
  background: #ff4757;
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}
.empty-state.data-v-2caef1dd {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}
.empty-text.data-v-2caef1dd {
  font-size: 16px;
  color: #999;
  margin: 16px 0 8px;
}
.empty-desc.data-v-2caef1dd {
  font-size: 14px;
  color: #ccc;
}
.loading-state.data-v-2caef1dd {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}
.loading-text.data-v-2caef1dd {
  margin-left: 8px;
  color: #999;
  font-size: 14px;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.profile-container.data-v-4df458ff {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 100rpx;
}
.header-section.data-v-4df458ff {
  position: relative;
  background: #fff;
}
.header-bg.data-v-4df458ff {
  height: 400rpx;
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
}
.header-actions.data-v-4df458ff {
  position: absolute;
  top: 60rpx;
  right: 32rpx;
  display: flex;
  gap: 32rpx;
  z-index: 10;
}
.user-info-section.data-v-4df458ff {
  padding: 40rpx 50rpx;
  background: #f8f9fa;
}
.user-info-content.data-v-4df458ff {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 50rpx;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.user-avatar-container.data-v-4df458ff {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;
  position: absolute;
  top: 340rpx;
  left: 9%;
}
.user-info-row.data-v-4df458ff {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}
.user-details.data-v-4df458ff {
  flex: 1;
  text-align: left;
}
.edit-section.data-v-4df458ff {
  flex-shrink: 0;
  margin-left: 20rpx;
  display: flex;
  align-items: flex-start;
}
.edit-link.data-v-4df458ff {
  font-size: 28rpx;
  font-weight: 500;
  border: 1rpx solid #2979ff;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  margin: 10rpx;
  color: #2979ff;
}
.nickname.data-v-4df458ff {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}
.user-id.data-v-4df458ff {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 16rpx;
}
.dance-type.data-v-4df458ff {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.bio.data-v-4df458ff {
  font-size: 28rpx;
  color: #999;
  line-height: 1.4;
  display: block;
}
.stats-row.data-v-4df458ff {
  display: flex;
  justify-content: center;
  gap: 80rpx;
  margin-bottom: 0;
}
.stat-item.data-v-4df458ff {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-number.data-v-4df458ff {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.stat-label.data-v-4df458ff {
  font-size: 24rpx;
  color: #999;
}
.tabs-container.data-v-4df458ff {
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}
.content.data-v-4df458ff {
  background: #fff;
  min-height: 60vh;
}
.posts-content.data-v-4df458ff {
  padding: 32rpx;
}
.post-grid.data-v-4df458ff {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.post-card-item.data-v-4df458ff {
  width: calc(50% - 8rpx);
}
.empty-state.data-v-4df458ff {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.social-main-container.data-v-0c4b5bf9 {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.content-area.data-v-0c4b5bf9 {
  flex: 1;
  overflow: hidden;
}

