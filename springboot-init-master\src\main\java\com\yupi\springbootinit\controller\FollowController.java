package com.yupi.springbootinit.controller;

import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.model.vo.FollowVO;
import com.yupi.springbootinit.service.FollowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 关注系统接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/follow")
@Slf4j
@Api(tags = "关注系统接口")
public class FollowController {

    @Resource
    private FollowService followService;

    /**
     * 关注用户
     */
    @PostMapping("/user/{userId}")
    @ApiOperation(value = "关注用户")
    public BaseResponse<Boolean> followUser(@PathVariable Long userId, HttpServletRequest request) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID无效");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            if (currentUserId.equals(userId)) {
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "不能关注自己");
            }

            boolean result = followService.followUser(currentUserId, userId);
            if (result) {
                log.info("关注用户成功 - followerId: {}, followingId: {}", currentUserId, userId);
                return ResultUtils.success(true);
            } else {
                log.warn("关注用户失败 - followerId: {}, followingId: {}", currentUserId, userId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "关注失败，可能已经关注过了");
            }

        } catch (Exception e) {
            log.error("关注用户异常 - followerId: {}, followingId: {}, error: {}", 
                    1L, userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "关注失败");
        }
    }

    /**
     * 取消关注用户
     */
    @DeleteMapping("/user/{userId}")
    @ApiOperation(value = "取消关注用户")
    public BaseResponse<Boolean> unfollowUser(@PathVariable Long userId, HttpServletRequest request) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID无效");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            boolean result = followService.unfollowUser(currentUserId, userId);
            if (result) {
                log.info("取消关注用户成功 - followerId: {}, followingId: {}", currentUserId, userId);
                return ResultUtils.success(true);
            } else {
                log.warn("取消关注用户失败 - followerId: {}, followingId: {}", currentUserId, userId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "取消关注失败");
            }

        } catch (Exception e) {
            log.error("取消关注用户异常 - followerId: {}, followingId: {}, error: {}", 
                    1L, userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "取消关注失败");
        }
    }

    /**
     * 检查关注状态
     */
    @GetMapping("/status/{userId}")
    @ApiOperation(value = "检查关注状态")
    public BaseResponse<Map<String, Object>> checkFollowStatus(@PathVariable Long userId, 
                                                              HttpServletRequest request) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID无效");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            Map<String, Object> status = followService.getFollowStatus(currentUserId, userId);
            log.info("检查关注状态成功 - currentUserId: {}, targetUserId: {}", currentUserId, userId);
            return ResultUtils.success(status);

        } catch (Exception e) {
            log.error("检查关注状态异常 - currentUserId: {}, targetUserId: {}, error: {}", 
                    1L, userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "检查关注状态失败");
        }
    }

    /**
     * 获取关注列表
     */
    @GetMapping("/following/{userId}")
    @ApiOperation(value = "获取关注列表")
    public BaseResponse<List<FollowVO>> getFollowingList(@PathVariable Long userId,
                                                        @RequestParam(defaultValue = "1") Integer current,
                                                        @RequestParam(defaultValue = "20") Integer size,
                                                        HttpServletRequest request) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID无效");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            List<FollowVO> followingList = followService.getFollowingList(userId, current, size, currentUserId);
            log.info("获取关注列表成功 - userId: {}, count: {}", userId, followingList.size());
            return ResultUtils.success(followingList);

        } catch (Exception e) {
            log.error("获取关注列表异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取关注列表失败");
        }
    }

    /**
     * 获取粉丝列表
     */
    @GetMapping("/followers/{userId}")
    @ApiOperation(value = "获取粉丝列表")
    public BaseResponse<List<FollowVO>> getFollowersList(@PathVariable Long userId,
                                                        @RequestParam(defaultValue = "1") Integer current,
                                                        @RequestParam(defaultValue = "20") Integer size,
                                                        HttpServletRequest request) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID无效");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            List<FollowVO> followersList = followService.getFollowersList(userId, current, size, currentUserId);
            log.info("获取粉丝列表成功 - userId: {}, count: {}", userId, followersList.size());
            return ResultUtils.success(followersList);

        } catch (Exception e) {
            log.error("获取粉丝列表异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取粉丝列表失败");
        }
    }

    /**
     * 获取关注统计
     */
    @GetMapping("/stats/{userId}")
    @ApiOperation(value = "获取关注统计")
    public BaseResponse<Map<String, Integer>> getFollowStats(@PathVariable Long userId) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID无效");
        }

        try {
            Map<String, Integer> stats = followService.getFollowStats(userId);
            log.info("获取关注统计成功 - userId: {}, stats: {}", userId, stats);
            return ResultUtils.success(stats);

        } catch (Exception e) {
            log.error("获取关注统计异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取关注统计失败");
        }
    }

    /**
     * 批量检查关注状态
     */
    @PostMapping("/batch-status")
    @ApiOperation(value = "批量检查关注状态")
    public BaseResponse<Map<Long, Boolean>> batchCheckFollowStatus(@RequestBody List<Long> userIds,
                                                                  HttpServletRequest request) {
        if (userIds == null || userIds.isEmpty()) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID列表不能为空");
        }

        if (userIds.size() > 100) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "批量查询用户数量不能超过100");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            Map<Long, Boolean> statusMap = followService.batchCheckFollowStatus(currentUserId, userIds);
            log.info("批量检查关注状态成功 - currentUserId: {}, userCount: {}", currentUserId, userIds.size());
            return ResultUtils.success(statusMap);

        } catch (Exception e) {
            log.error("批量检查关注状态异常 - currentUserId: {}, error: {}", 1L, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "批量检查关注状态失败");
        }
    }

    /**
     * 获取互相关注的用户列表
     */
    @GetMapping("/mutual/{userId}")
    @ApiOperation(value = "获取互相关注的用户列表")
    public BaseResponse<List<FollowVO>> getMutualFollows(@PathVariable Long userId,
                                                        @RequestParam(defaultValue = "1") Integer current,
                                                        @RequestParam(defaultValue = "20") Integer size,
                                                        HttpServletRequest request) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID无效");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            List<FollowVO> mutualFollows = followService.getMutualFollows(userId, current, size, currentUserId);
            log.info("获取互相关注列表成功 - userId: {}, count: {}", userId, mutualFollows.size());
            return ResultUtils.success(mutualFollows);

        } catch (Exception e) {
            log.error("获取互相关注列表异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取互相关注列表失败");
        }
    }
}
