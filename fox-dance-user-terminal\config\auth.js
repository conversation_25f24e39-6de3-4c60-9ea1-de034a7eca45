import CryptoJs from 'crypto-js'
let HmacMD5 = CryptoJs.HmacMD5
let AES = CryptoJs.AES
let Utf8 = CryptoJs.enc.Utf8
let mode = CryptoJs.mode.ECB
let padding = CryptoJs.pad.Pkcs7

let auth = {
  token: function(url, mobile, password) {
    let path = `${url}:${Date.now()}`
    if (mobile && password) {
      return `bearer ${mobile}:${this.aesEncrypt(this.encrypt(password), path)}`
    }

    let user = uni.getStorageSync('user')
    if (user.mobile && user.password) {
      return `bearer ${user.mobile}:${this.aesEncrypt(user.password, path)}`
    }

    return null
  },


  encrypt: function(password) {
    return HmacMD5(password, password).toString().slice(0, 16)
  },

  aesEncrypt: function(password, msg, iValue = 'O2%=!ExPCuY6SKX(') {
    let message = Utf8.parse(msg)
    let key = Utf8.parse(password.slice(0, 16))
    let iv = Utf8.parse(iValue)
    return AES.encrypt(message, key, {
      iv: iv,
      mode: mode,
      padding: padding
    }).toString()
  },
  aesDecrypt: function(password, ciphertext, iValue = 'O2%=!ExPCuY6SKX(') {
    let key = Utf8.parse(password.slice(0, 16))
    let iv = Utf8.parse(iValue)
    return AES.decrypt(ciphertext, key, {
      iv: iv,
      mode: mode,
      padding: padding
    }).toString()
  },


  logIn: function(mobile, password, data) {
    uni.setStorageSync('user', {
      mobile,
      password: this.encrypt(password),
      data
    })
  },
  logOut: function() {
    uni.removeStorageSync('user')
  },
  isLogin: function() {
    let user = uni.getStorageSync('user')
    if (!!user) {
      return user
    } else {
      return false
    }
  },
}

export default {
  // #ifndef VUE3
  install(Vue) {
    Vue.prototype.$auth = auth
  },
  // #endif

  // #ifdef VUE3
  install(app, ops) {
    app.config.globalProperties.$auth = auth
  },
  // #endif

  auth
}
