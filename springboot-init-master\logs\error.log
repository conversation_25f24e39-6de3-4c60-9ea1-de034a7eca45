2025-07-17 10:15:59.734 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 10:24:20.848 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 10:32:45.634 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 10:34:30.695 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 10:48:38.291 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 15:44:25.407 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 15:44:57.458 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 15:46:34.096 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 15:46:35.059 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userProfileController': Lookup method resolution failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.yupi.springbootinit.controller.UserProfileController] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@6ac64488]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:289)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1302)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.yupi.springbootinit.controller.UserProfileController] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@6ac64488]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:485)
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:321)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:267)
	... 23 common frames omitted
Caused by: java.lang.NoClassDefFoundError: UserProfileUpdateRequest
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2504)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:467)
	... 25 common frames omitted
Caused by: java.lang.ClassNotFoundException: UserProfileUpdateRequest
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:145)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	... 29 common frames omitted
2025-07-17 15:47:05.075 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 15:47:06.500 [restartedMain] ERROR s.d.s.w.p.DefaultRequestHandlerCombiner - Unable to index request handlers Type UserProfileVO not present. Request handlers with issuesRequest Handlers with duplicate keys {	0. RequestHandlerKey{pathMappings=[/user/profile], supportedMethods=[GET], supportedMediaTypes=[], producibleMediaTypes=[]}	1. RequestHandlerKey{pathMappings=[/user/profile], supportedMethods=[PUT], supportedMediaTypes=[], producibleMediaTypes=[]}}
2025-07-17 15:47:06.662 [restartedMain] ERROR s.d.s.w.p.DocumentationPluginsBootstrapper - Unable to scan documentation context default
java.lang.TypeNotPresentException: Type UserProfileVO not present
	at java.base/sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:117)
	at java.base/sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:125)
	at java.base/sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
	at java.base/sun.reflect.generics.visitor.Reifier.reifyTypeArguments(Reifier.java:68)
	at java.base/sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:138)
	at java.base/sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
	at java.base/sun.reflect.generics.repository.MethodRepository.computeReturnType(MethodRepository.java:75)
	at java.base/sun.reflect.generics.repository.MethodRepository.getReturnType(MethodRepository.java:66)
	at java.base/java.lang.reflect.Method.getGenericReturnType(Method.java:296)
	at com.fasterxml.classmate.ResolvedTypeWithMembers.resolveMethod(ResolvedTypeWithMembers.java:570)
	at com.fasterxml.classmate.ResolvedTypeWithMembers.resolveMemberMethods(ResolvedTypeWithMembers.java:388)
	at com.fasterxml.classmate.ResolvedTypeWithMembers.getMemberMethods(ResolvedTypeWithMembers.java:201)
	at springfox.documentation.spring.web.readers.operation.HandlerMethodResolver.getMemberMethods(HandlerMethodResolver.java:151)
	at springfox.documentation.spring.web.readers.operation.HandlerMethodResolver.resolvedMethod(HandlerMethodResolver.java:136)
	at springfox.documentation.spring.web.readers.operation.HandlerMethodResolver.methodReturnType(HandlerMethodResolver.java:65)
	at springfox.documentation.spring.web.WebMvcRequestHandler.getReturnType(WebMvcRequestHandler.java:147)
	at springfox.documentation.spi.service.contexts.RequestMappingContext.getReturnType(RequestMappingContext.java:213)
	at springfox.documentation.spi.service.contexts.Orderings.qualifiedMethodName(Orderings.java:76)
	at java.base/java.util.Comparator.lambda$comparing$77a9974f$1(Comparator.java:473)
	at java.base/java.util.TimSort.countRunAndMakeAscending(TimSort.java:355)
	at java.base/java.util.TimSort.sort(TimSort.java:220)
	at java.base/java.util.Arrays.sort(Arrays.java:1307)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:353)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at springfox.documentation.spring.web.scanners.ApiListingScanner.sortedByMethods(ApiListingScanner.java:205)
	at springfox.documentation.spring.web.scanners.ApiListingScanner.scan(ApiListingScanner.java:142)
	at springfox.documentation.spring.web.scanners.ApiDocumentationScanner.scan(ApiDocumentationScanner.java:69)
	at springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.scanDocumentation(AbstractDocumentationPluginsBootstrapper.java:97)
	at springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins(AbstractDocumentationPluginsBootstrapper.java:83)
	at springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start(DocumentationPluginsBootstrapper.java:94)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.ClassNotFoundException: UserProfileVO
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:145)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at java.base/sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:114)
	... 53 common frames omitted
2025-07-17 15:47:32.342 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 15:49:17.274 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-17 15:50:40.846 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
