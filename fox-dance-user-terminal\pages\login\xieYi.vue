<template>
	<view>
		<view class="">
			<rich-text :nodes="Cont"></rich-text>
		</view>
	</view>
</template>

<script>
import {
	XieYi,
	cardsApi
} from '@/config/http.achieve.js' 
import util from '@/utils/utils.js';
export default {
	data() {
		return {
			type: '',
			Cont: ""
		}
	},
	onLoad(options) {
		
		// let item = this.$Route.query
		if (options.type) {
			this.type = options.type
			if (this.type == 1) {
				uni.setNavigationBarTitle({
					title: "用户注册购卡协议"
				})
			}
			if (this.type == 2) {
				uni.setNavigationBarTitle({
					title: "隐私政策"
				})
			}
			if (this.type == 3) {
				uni.setNavigationBarTitle({
					title: "用户授权协议"
				})
			}
			if (this.type == 4) {
				uni.setNavigationBarTitle({
					title: "平台服务协议"
				})
			}
			if (this.type == 5) {
				uni.setNavigationBarTitle({
					title: "活动规则"
				})
			}
			if (this.type == 99) {
				uni.setNavigationBarTitle({
					title: "会员服务协议"
				})
			}
			this.xieYiFun()
		}
	},
	methods: {
		xieYiFun() {
			uni.showLoading({
				title: '加载中'
			});
			if(this.type == 99){
				cardsApi({
					type:1
				}).then(res => {
					uni.hideLoading();
					this.Cont = util.formatRichText(res.data.member_service_agreement)
				})
			}else{
				XieYi({
					type:this.type
				}).then(res => {
					uni.hideLoading();
					this.Cont = util.formatRichText(res.data)
				})
			}
			
		}
	}
}
</script>

<style>
	page {
		background-color: #fff;

		border-top: 2rpx solid #f8f8f8;
		box-sizing: border-box;
		padding: 39rpx 32rpx 0;
	}
</style>