<template>
  <view class="profile-container">
    <!-- 蓝色渐变背景头部 -->
    <view class="header-section">
      <view class="header-bg"></view>

      <!-- 顶部操作按钮 -->
      <view class="header-actions">
        <u-icon name="scan" color="#fff" size="48rpx" @click="scanCode"></u-icon>
        <u-icon name="setting" color="#fff" size="48rpx" @click="goSettings"></u-icon>
      </view>

      <!-- 用户信息 -->
      <view class="user-info-section">
        <view class="user-avatar-container">
          <u-avatar :src="userInfo.avatar" size="120" @click="editAvatar"></u-avatar>
        </view>

        <!-- 用户信息内容容器 -->
        <view class="user-info-content">
          <view class="user-info-row">
            <!-- 左侧用户信息 -->
            <view class="user-details">
              <text class="nickname">{{ userInfo.nickname }}</text>
              <text class="user-id">ID: {{ userInfo.userId }}</text>
              <text class="dance-type">舞种：{{ userInfo.danceType || '街舞' }}</text>
              <text class="bio">{{ userInfo.bio || '美食爱好者 | 旅行达人 | 摄影 | 生活方式博主' }}</text>
            </view>

            <!-- 右侧编辑链接 -->
            <view class="edit-section">
              <text class="edit-link" @click="editProfile">编辑资料</text>
            </view>
          </view>

          <!-- 数据统计 -->
          <view class="stats-row">
            <view class="stat-item" @click="switchTab(0)">
              <text class="stat-number">{{ userInfo.postCount }}</text>
              <text class="stat-label">帖子</text>
            </view>
            <view class="stat-item" @click="goLikeList">
              <text class="stat-number">{{ userInfo.likeCount }}</text>
              <text class="stat-label">获赞</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <scroll-view class="content" scroll-y>
      <!-- 操作按钮 -->
      <view class="tabs-container">
        <u-tabs
          :list="tabs"
          :current="currentTab"
          @change="switchTab"
          lineWidth="30"
          lineColor="#303133"
          :activeStyle="{ color: '#303133', fontWeight: 'bold' }"
          :inactiveStyle="{ color: '#606266' }"
        ></u-tabs>
      </view>

      <!-- 帖子列表 -->
      <view class="posts-content">
        <view v-if="tabs[currentTab] && tabs[currentTab].data.length > 0" class="post-grid">
          <PostCard
            v-for="post in tabs[currentTab].data"
            :key="post.id"
            :post="post"
            class="post-card-item"
            @click="goPostDetail"
            @user-click="goUserProfile"
            @like="onPostLike"
          />
        </view>
        <view v-else class="empty-state">
          <u-empty mode="list" text="暂无内容"></u-empty>
        </view>
      </view>
    </scroll-view>

  </view>
</template>

<script>
import PostCard from '../components/PostCard.vue'
import { getUserProfile, updateUserProfile, getPostList, likePost, unlikePost } from '@/utils/socialApi.js'

export default {
  name: 'SocialProfile',
  components: {
    PostCard
  },
  data() {
    return {
      userInfo: {
        userId: 'xiaoming_zhang',
        nickname: '张小明',
        avatar: '/static/images/toux.png',
        bio: '美食爱好者 | 旅行达人 | 摄影师 | 生活方式博主',
        danceType: '街舞',
        postCount: 128,
        followingCount: 256,
        followersCount: 1024,
        likeCount: 8547,
        draftCount: 3
      },
      currentTab: 0,
      isInitialized: false,
      tabs: [
        {
          name: '作品',
          data: [
            {
              id: 1,
              title: '你喜欢什么颜色的?',
              coverImage: 'https://picsum.photos/400/400?random=100',
              username: '张小明',
              userAvatar: '/static/images/toux.png',
              content: '分享一下今天的心情',
              likeCount: 219,
              commentCount: 15,
              isLiked: false,
              createTime: new Date(Date.now() - 3600000)
            },
            {
              id: 2,
              title: '这是在哪拍的?',
              coverImage: 'https://picsum.photos/400/400?random=102',
              username: '张小明',
              userAvatar: '/static/images/toux.png',
              content: '美丽的风景',
              likeCount: 16,
              commentCount: 3,
              isLiked: true,
              createTime: new Date(Date.now() - 7200000)
            },
            {
              id: 3,
              title: '特角色',
              coverImage: 'https://picsum.photos/400/400?random=103',
              username: '张小明',
              userAvatar: '/static/images/toux.png',
              content: '今天的造型',
              likeCount: 12,
              commentCount: 2,
              isLiked: false,
              createTime: new Date(Date.now() - 10800000)
            },
            {
              id: 4,
              title: '这才是自由',
              coverImage: 'https://picsum.photos/400/400?random=104',
              username: '张小明',
              userAvatar: '/static/images/toux.png',
              content: '享受自由的感觉',
              likeCount: 3157,
              commentCount: 89,
              isLiked: true,
              createTime: new Date(Date.now() - 14400000)
            }
          ]
        },
        { name: '喜欢', data: [] },
        { name: '收藏', data: [] }
      ]
    }
  },
  onLoad() {
    console.log('Profile页面 onLoad')
    // 延迟加载，确保页面完全初始化
    this.$nextTick(() => {
      setTimeout(() => {
        this.initializeData()
      }, 100)
    })
  },
  onShow() {
    console.log('Profile页面 onShow')
    // 页面显示时刷新数据
    if (this.isInitialized) {
      this.loadUserInfo()
    }
  },
  methods: {
    // 初始化数据
    async initializeData() {
      console.log('初始化Profile页面数据...')
      try {
        await this.loadUserInfo()
        this.isInitialized = true
      } catch (error) {
        console.error('初始化数据失败:', error)
      }
    },

    async loadUserInfo() {
      try {
        // TODO: 获取当前用户ID，这里暂时使用固定值
        const currentUserId = 1

        const result = await getUserProfile(currentUserId)
        console.log('用户信息API返回:', result)

        if (result && result.code === 0 && result.data) {
          const userProfile = result.data
          this.userInfo = {
            id: userProfile.userId,
            nickname: userProfile.nickname || '舞蹈爱好者',
            avatar: userProfile.avatar || 'https://picsum.photos/200/200?random=1',
            bio: userProfile.bio || '热爱舞蹈，享受生活',
            danceType: userProfile.danceType || '街舞',
            level: userProfile.level || 5,
            followingCount: userProfile.followingCount || 0,
            followerCount: userProfile.followerCount || 0,
            postCount: userProfile.postCount || 0,
            likeCount: userProfile.likeReceivedCount || 0
          }
        } else {
          console.log('用户信息API返回格式不正确，使用默认数据')
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
        // 使用默认用户信息
        this.userInfo = {
          id: 1,
          nickname: '舞蹈爱好者',
          avatar: 'https://picsum.photos/200/200?random=1',
          bio: '热爱舞蹈，享受生活',
          danceType: '街舞',
          level: 5,
          followingCount: 128,
          followerCount: 256,
          postCount: 42,
          likeCount: 1024
        }
      }

      // 加载当前标签页数据
      this.loadTabData(this.currentTab)
    },

    async loadTabData(tabIndex) {
      if (this.tabs[tabIndex].loading) return

      this.$set(this.tabs[tabIndex], 'loading', true)

      try {
        let data = []

        switch (tabIndex) {
          case 0: // 帖子
            data = await this.loadUserPosts()
            break
          case 1: // 喜欢
            data = await this.loadLikedPosts()
            break
          case 2: // 收藏
            data = await this.loadCollectedPosts()
            break
        }

        this.$set(this.tabs[tabIndex], 'data', data)

      } catch (error) {
        console.error(`加载标签页${tabIndex}数据失败:`, error)
        // 使用模拟数据作为后备
        if (tabIndex > 0 && this.tabs[tabIndex].data.length === 0) {
          const mockData = this.generateMockPostsForTab(tabIndex)
          this.$set(this.tabs[tabIndex], 'data', mockData)
        }
      } finally {
        this.$set(this.tabs[tabIndex], 'loading', false)
      }
    },

    // 加载用户发布的帖子
    async loadUserPosts() {
      try {
        const currentUserId = 1 // TODO: 获取当前用户ID
        const result = await getPostList({
          current: 1,
          size: 20,
          userId: currentUserId,
          sortField: 'createTime',
          sortOrder: 'desc'
        })

        console.log('用户帖子API返回:', result)

        if (result && result.code === 0 && result.data && result.data.records) {
          return result.data.records.map(post => ({
            id: post.id,
            title: post.title || '',
            coverImage: (post.images && post.images[0]) || 'https://picsum.photos/400/400?random=' + post.id,
            username: post.nickname || this.userInfo.nickname,
            userAvatar: post.avatar || this.userInfo.avatar,
            content: post.content,
            likeCount: post.likeCount || 0,
            commentCount: post.commentCount || 0,
            isLiked: post.isLiked || false,
            createTime: new Date(post.createTime)
          }))
        } else {
          console.log('用户帖子API返回格式不正确，使用模拟数据')
          return this.generateMockPostsForTab(0)
        }
      } catch (error) {
        console.error('加载用户帖子失败:', error)
        // 返回模拟数据作为后备
        return this.generateMockPostsForTab(0)
      }
    },

    // 加载用户点赞的帖子
    async loadLikedPosts() {
      try {
        // TODO: 实现获取用户点赞帖子的API
        // 暂时返回模拟数据
        return this.generateMockPostsForTab(1)
      } catch (error) {
        console.error('加载点赞帖子失败:', error)
        return []
      }
    },

    // 加载用户收藏的帖子
    async loadCollectedPosts() {
      try {
        // TODO: 实现获取用户收藏帖子的API
        // 暂时返回模拟数据
        return this.generateMockPostsForTab(2)
      } catch (error) {
        console.error('加载收藏帖子失败:', error)
        return []
      }
    },

    generateMockPostsForTab(tabIndex) {
      const posts = []
      const titles = {
        0: ['我的舞蹈作品', '今日练习', '舞蹈分享', '技巧展示'], // 作品
        1: ['超爱的瞬间', '百看不厌', '为它点赞', '今日最佳'], // 喜欢
        2: ['我的珍藏', '稍后再看', '深度好文', '灵感来源'] // 收藏
      }
      const contents = {
        0: ['分享我的舞蹈作品', '今天的练习成果', '和大家分享舞蹈心得', '新学的舞蹈技巧'],
        1: ['这个瞬间太美了', '真的很棒', '必须点赞', '今天最棒的内容'],
        2: ['值得收藏', '有空再看', '写得很好', '很有启发']
      }

      for (let i = 0; i < 6; i++) {
        const tabTitles = titles[tabIndex] || titles[0]
        const tabContents = contents[tabIndex] || contents[0]

        posts.push({
          id: tabIndex * 10 + i,
          title: tabTitles[i % 4],
          coverImage: `https://picsum.photos/400/400?random=${tabIndex * 10 + i}`,
          username: this.userInfo.nickname || '舞蹈爱好者',
          userAvatar: this.userInfo.avatar || 'https://picsum.photos/200/200?random=1',
          content: tabContents[i % 4],
          likeCount: Math.floor(Math.random() * 1000),
          commentCount: Math.floor(Math.random() * 50),
          isLiked: Math.random() > 0.5,
          createTime: new Date(Date.now() - Math.random() * 86400000 * 30)
        })
      }
      return posts
    },

    switchTab(item) {
      const index = typeof item === 'object' ? item.index : item;
      if (this.currentTab === index) return
      this.currentTab = index
      this.loadTabData(index)
    },

    scanCode() {
      uni.scanCode({
        success: (res) => {
          console.log('扫码结果:', res)
        }
      })
    },

    goSettings() {
      uni.navigateTo({
        url: '/pagesSub/social/settings/index'
      })
    },

    editAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 上传头像
          this.userInfo.avatar = res.tempFilePaths[0]
        }
      })
    },

    editProfile() {
      uni.navigateTo({
        url: '/pagesSub/social/profile/edit'
      })
    },

    goLikeList() {
      uni.navigateTo({
        url: '/pagesSub/social/like/list'
      })
    },

    viewPost(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      })
    },

    // PostCard组件需要的方法
    goPostDetail(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      })
    },

    goUserProfile(post) {
      // 如果是自己的帖子，不需要跳转
      if (post.username === this.userInfo.nickname) return

      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${post.userId || post.id}`
      })
    },

    async onPostLike(post) {
      try {
        if (post.isLiked) {
          // 取消点赞
          await unlikePost(post.id)
          post.isLiked = false
          post.likeCount = Math.max(0, post.likeCount - 1)
          uni.showToast({
            title: '取消点赞',
            icon: 'none',
            duration: 1000
          })
        } else {
          // 点赞
          await likePost(post.id)
          post.isLiked = true
          post.likeCount += 1
          uni.showToast({
            title: '点赞成功',
            icon: 'success',
            duration: 1000
          })
        }

        // 更新对应标签页中的帖子数据
        const currentTabData = this.tabs[this.currentTab].data
        const index = currentTabData.findIndex(p => p.id === post.id)
        if (index !== -1) {
          this.$set(currentTabData, index, { ...post })
        }

      } catch (error) {
        console.error('点赞操作失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 100rpx;
}

.header-section {
  position: relative;
  background: #fff;
}

.header-bg {
  height: 400rpx;
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
}

.header-actions {
  position: absolute;
  top: 60rpx;
  right: 32rpx;
  display: flex;
  gap: 32rpx;
  z-index: 10;
}

.user-info-section {
  padding: 40rpx 50rpx;
  background: #f8f9fa;
}

.user-info-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 50rpx;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.user-avatar-container {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;
  position: absolute;
  top: 340rpx;
  left: 9%;
}

.user-info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.user-details {
  flex: 1;
  text-align: left;
}

.edit-section {
  flex-shrink: 0;
  margin-left: 20rpx;
  display: flex;
  align-items: flex-start;
}

.edit-link {
  font-size: 28rpx;
  font-weight: 500;
  border: 1rpx solid #2979ff;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  margin: 10rpx;
  color: #2979ff;
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 16rpx;
}

.dance-type {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.bio {
  font-size: 28rpx;
  color: #999;
  line-height: 1.4;
  display: block;
}

.stats-row {
  display: flex;
  justify-content: center;
  gap: 80rpx;
  margin-bottom: 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.tabs-container {
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.content {
  background: #fff;
  min-height: 60vh;
}

.posts-content {
  padding: 32rpx;
}

.post-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 14rpx;
}

.post-card-item {
  width: calc(50% - 8rpx);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
</style>
