<view class="detail-container data-v-0722cd1a"><scroll-view class="content data-v-0722cd1a" scroll-y="{{true}}"><view class="post-detail data-v-0722cd1a"><view class="user-info data-v-0722cd1a"><u-avatar vue-id="dd35d41a-1" src="{{postData.userAvatar}}" size="50" data-event-opts="{{[['^click',[['goUserProfile']]]]}}" bind:click="__e" class="data-v-0722cd1a" bind:__l="__l"></u-avatar><view class="user-details data-v-0722cd1a"><text class="username data-v-0722cd1a">{{postData.username}}</text><text class="time data-v-0722cd1a">{{$root.m0}}</text></view><follow-button vue-id="dd35d41a-2" user="{{$root.a0}}" followed="{{postData.isFollowed}}" size="mini" data-event-opts="{{[['^follow',[['onUserFollow']]],['^change',[['onFollowChange']]]]}}" bind:follow="__e" bind:change="__e" class="data-v-0722cd1a" bind:__l="__l"></follow-button></view><view class="post-content data-v-0722cd1a"><text class="content-text data-v-0722cd1a">{{postData.content}}</text></view><block wx:if="{{$root.g0}}"><view class="topic-tags data-v-0722cd1a"><block wx:for="{{postData.topics}}" wx:for-item="topic" wx:for-index="__i0__" wx:key="*this"><text data-event-opts="{{[['tap',[['goTopic',['$0'],[[['postData.topics','',__i0__]]]]]]]}}" class="topic-tag data-v-0722cd1a" bindtap="__e">{{'#'+topic+''}}</text></block></view></block><block wx:if="{{$root.g1}}"><view class="post-images data-v-0722cd1a"><swiper class="image-swiper data-v-0722cd1a" indicator-dots="{{$root.g2>1}}" autoplay="{{false}}" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.5)" indicator-active-color="#fff"><block wx:for="{{postData.images}}" wx:for-item="img" wx:for-index="index" wx:key="index"><swiper-item class="data-v-0722cd1a"><image class="swiper-image data-v-0722cd1a" src="{{img}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',[index]]]]]}}" bindtap="__e"></image></swiper-item></block></swiper></view></block><view class="post-stats data-v-0722cd1a"><text class="stat-item data-v-0722cd1a">{{postData.likeCount+"人点赞"}}</text><text class="stat-item data-v-0722cd1a">{{postData.commentCount+"条评论"}}</text><text class="stat-item data-v-0722cd1a">{{postData.shareCount+"次分享"}}</text></view><view class="action-bar data-v-0722cd1a"><view data-event-opts="{{[['tap',[['toggleLike',['$event']]]]]}}" class="action-item data-v-0722cd1a" bindtap="__e"><u-icon vue-id="dd35d41a-3" name="{{postData.isLiked?'heart-fill':'heart'}}" color="{{postData.isLiked?'#ff4757':'#666'}}" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="action-text data-v-0722cd1a">点赞</text></view><view data-event-opts="{{[['tap',[['focusComment',['$event']]]]]}}" class="action-item data-v-0722cd1a" bindtap="__e"><u-icon vue-id="dd35d41a-4" name="chat" color="#666" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="action-text data-v-0722cd1a">评论</text></view><view data-event-opts="{{[['tap',[['sharePost',['$event']]]]]}}" class="action-item data-v-0722cd1a" bindtap="__e"><u-icon vue-id="dd35d41a-5" name="share" color="#666" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="action-text data-v-0722cd1a">分享</text></view></view></view><view class="comments-section data-v-0722cd1a"><view class="comments-header data-v-0722cd1a"><text class="comments-title data-v-0722cd1a">{{"评论 "+$root.g3}}</text><u-sticky vue-id="dd35d41a-6" bgColor="#fff" class="data-v-0722cd1a" bind:__l="__l" vue-slots="{{['default']}}"><u-tabs vue-id="{{('dd35d41a-7')+','+('dd35d41a-6')}}" list="{{tabList}}" current="{{currentTab}}" scrollable="{{false}}" activeColor="#2979ff" inactiveColor="#999" fontSize="28" lineColor="#2979ff" lineWidth="20" lineHeight="3" height="40" data-event-opts="{{[['^change',[['changeSortType']]]]}}" bind:change="__e" class="data-v-0722cd1a" bind:__l="__l"></u-tabs></u-sticky></view><view class="comment-list data-v-0722cd1a"><block wx:for="{{$root.l0}}" wx:for-item="comment" wx:for-index="__i1__" wx:key="id"><view class="comment-item data-v-0722cd1a"><u-avatar vue-id="{{'dd35d41a-8-'+__i1__}}" src="{{comment.$orig.userAvatar}}" size="36" class="data-v-0722cd1a" bind:__l="__l"></u-avatar><view class="comment-content data-v-0722cd1a"><view class="comment-header data-v-0722cd1a"><text class="comment-username data-v-0722cd1a">{{comment.$orig.username}}</text><text class="comment-time data-v-0722cd1a">{{comment.m1}}</text></view><text class="comment-text data-v-0722cd1a">{{comment.$orig.content}}</text><block wx:if="{{comment.g4}}"><view class="replies data-v-0722cd1a"><block wx:for="{{comment.$orig.replies}}" wx:for-item="reply" wx:for-index="__i2__" wx:key="id"><view class="reply-item data-v-0722cd1a"><text class="reply-user data-v-0722cd1a">{{reply.username}}</text><text class="reply-text data-v-0722cd1a">{{reply.content}}</text></view></block></view></block><view class="comment-actions data-v-0722cd1a"><view data-event-opts="{{[['tap',[['toggleCommentLike',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="comment-action data-v-0722cd1a" bindtap="__e"><u-icon vue-id="{{'dd35d41a-9-'+__i1__}}" name="{{comment.$orig.isLiked?'heart-fill':'heart'}}" color="{{comment.$orig.isLiked?'#ff4757':'#999'}}" size="16" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="action-count data-v-0722cd1a">{{comment.$orig.likeCount||''}}</text></view><view data-event-opts="{{[['tap',[['replyComment',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="comment-action data-v-0722cd1a" bindtap="__e"><u-icon vue-id="{{'dd35d41a-10-'+__i1__}}" name="chat" color="#999" size="16" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="action-text data-v-0722cd1a">回复</text></view></view></view></view></block></view></view></scroll-view><view class="comment-input-bar data-v-0722cd1a"><view class="input-container data-v-0722cd1a"><u-avatar vue-id="dd35d41a-11" src="{{currentUser.avatar}}" size="32" class="data-v-0722cd1a" bind:__l="__l"></u-avatar><input class="comment-input data-v-0722cd1a vue-ref" placeholder="写评论..." data-ref="commentInput" data-event-opts="{{[['focus',[['onInputFocus',['$event']]]],['blur',[['onInputBlur',['$event']]]],['input',[['__set_model',['','commentText','$event',[]]]]]]}}" value="{{commentText}}" bindfocus="__e" bindblur="__e" bindinput="__e"/><text data-event-opts="{{[['tap',[['sendComment',['$event']]]]]}}" class="{{['send-btn','data-v-0722cd1a',($root.g5)?'active':'']}}" bindtap="__e">发送</text></view></view></view>