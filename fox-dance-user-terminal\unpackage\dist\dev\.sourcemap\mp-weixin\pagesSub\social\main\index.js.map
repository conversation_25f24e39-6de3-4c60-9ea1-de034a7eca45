{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?4c83", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?12d0", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?2eca", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?89d0", "uni-app:///pagesSub/social/main/index.vue", null, "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "uni-app:///pagesSub/social/home/<USER>", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?ac56", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?f6d4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?752b", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?ebdf", "uni-app:///pagesSub/social/discover/index.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?aac2", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?659d", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?06f6", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?ec02", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?97c8", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?f53f", "uni-app:///pagesSub/social/publish/index.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?a837", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?a170", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?3cec", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?3929", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?cd03", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?4a77", "uni-app:///pagesSub/social/message/index.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?8170", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?56ee", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?a76f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?fe51", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?fb44", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?c1c1", "uni-app:///pagesSub/social/profile/index.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?40a8", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?c29c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?eb53", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?26c8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "TabBar", "HomePage", "DiscoverPage", "PublishPage", "MessagePage", "ProfilePage", "data", "currentTab", "homePageKey", "discoverPageKey", "messagePageKey", "onLoad", "onShow", "methods", "handleTabChange", "console", "refreshCurrentTab", "refreshHomePage", "refreshDiscoverPage", "refreshMessagePage", "PostCard", "postList", "loading", "refreshing", "page", "pageSize", "currentTopic", "currentTopicIndex", "topicList", "id", "hasMore", "isInitialized", "activated", "initializeData", "loadHotTags", "result", "allOption", "loadPosts", "refresh", "params", "current", "size", "sortField", "sortOrder", "selectedTag", "posts", "title", "username", "userAvatar", "content", "coverImage", "images", "topics", "topicId", "likeCount", "commentCount", "isLiked", "createTime", "mockPosts", "generateMockPosts", "life", "titles", "food", "travel", "photography", "fitness", "fashion", "music", "movie", "reading", "availableTopics", "onRefresh", "loadMore", "formatTime", "onPostLike", "post", "index", "uni", "icon", "goPostDetail", "url", "goUserProfile", "goSearch", "selectTopic", "dateString", "FollowButton", "hotTopics", "recommendUsers", "hotPosts", "<PERSON><PERSON><PERSON><PERSON>", "loadDiscoverData", "loadHotTopics", "cover", "postCount", "loadRecommendUsers", "nickname", "avatar", "description", "isFollowed", "loadHotPosts", "loadFeaturedContent", "subtitle", "onUserFollow", "onFollowChange", "user", "goTopic", "goTopicList", "forceRefresh", "goFeaturedDetail", "userInfo", "postContent", "selectedImages", "selectedTopics", "selectedLocation", "visibility", "showTopicModal", "showLocationModal", "topicKeyword", "allTopics", "nearbyLocations", "address", "computed", "canPublish", "visibilityText", "public", "friends", "private", "filteredTopics", "topic", "hotTags", "goBack", "success", "chooseImage", "count", "sizeType", "sourceType", "removeImage", "toggleTopic", "searchTopics", "selectLocation", "selectLocationItem", "setVisibility", "itemList", "publishPost", "postData", "tags", "locationName", "isPublic", "setTimeout", "chatList", "systemUnreadCount", "likeUnreadCount", "followUnreadCount", "loadUnreadCounts", "unreadData", "loadChatList", "userId", "lastMessage", "lastMessageTime", "lastMessageType", "unreadCount", "isOnline", "isMuted", "generateMockChatList", "openChat", "chat", "conversationId", "showChatActions", "toggleChatTop", "toggleChatMute", "deleteChat", "startNewChat", "goSystemMessages", "goLikeMessages", "goFollowMessages", "bio", "danceType", "followingCount", "followersCount", "draftCount", "tabs", "loadUserInfo", "currentUserId", "userProfile", "level", "followerCount", "loadTabData", "tabIndex", "mockData", "loadUserPosts", "loadLikedPosts", "loadCollectedPosts", "generateMockPostsForTab", "switchTab", "scanCode", "goSettings", "editAvatar", "editProfile", "goLikeList", "viewPost", "duration", "currentTabData"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0BxvB;AACA;AACA;AACA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;;MAEA;MACA;QACA;MACA;MAEAC;IACA;IAEA;IACAC;MAAA;MACA;QACA;UACA;YAAA;YACA;YACA;UACA;YAAA;YACA;YACA;UACA;YAAA;YACA;YACA;UACA;YACAD;QAAA;MAEA;IACA;IAEA;IACAE;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC7IA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACyExvB;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACArB;EACAC;IACAqB;EACA;EACAd;IACA;MACAe;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,YACA;QAAA9B;QAAA+B;MAAA,GACA;QAAA/B;QAAA+B;MAAA,GACA;QAAA/B;QAAA+B;MAAA,GACA;QAAA/B;QAAA+B;MAAA,GACA;QAAA/B;QAAA+B;MAAA,GACA;QAAA/B;QAAA+B;MAAA,GACA;QAAA/B;QAAA+B;MAAA,GACA;QAAA/B;QAAA+B;MAAA,GACA;QAAA/B;QAAA+B;MAAA,GACA;QAAA/B;QAAA+B;MAAA,EACA;MACAC;MACAC;IACA;EACA;EACApB;IACA;EACA;EAEA;EACAC;IACA;IACA;MACA;IACA;EACA;EAEA;EACAoB;IACA;IACA;MACA;IACA;EACA;EACAnB;IACA;IACAoB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAlB;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACApB;gBAEA;kBACA;kBACAqB;kBACA;oBAAA;sBACAtC;sBACA+B;oBACA;kBAAA;kBACAd;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAsB;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBACAvB;gBAAA;cAAA;gBAIAA;gBACA;gBAAA;gBAEAwB;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA;kBACA;kBACAC;oBAAA;kBAAA;kBACA;oBACAL;kBACA;gBACA;gBAEAxB;gBAAA;gBAAA,OACA;cAAA;gBAAAoB;gBACApB;gBAEA;kBACA8B;oBAAA;sBACAhB;sBACAiB;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBAAA;kBACA1C;kBAEA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;kBACAA;;kBAEA;kBACA;gBACA;kBACA;kBACA2C;kBACA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA3C;gBACA;gBACA2C;gBACA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;MACA;QACAC;UAAAC;UAAA/D;QAAA;QACAgE;UAAAD;UAAA/D;QAAA;QACAiE;UAAAF;UAAA/D;QAAA;QACAkE;UAAAH;UAAA/D;QAAA;QACAmE;UAAAJ;UAAA/D;QAAA;QACAoE;UAAAL;UAAA/D;QAAA;QACAqE;UAAAN;UAAA/D;QAAA;QACAsE;UAAAP;UAAA/D;QAAA;QACAuE;UAAAR;UAAA/D;QAAA;MACA;;MAEA;MACA;MACA;QACAwE;MACA;MAEA;QACA;QACA;QACA;QACA;QAEAzB;UACAhB;UACAiB;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACA;IACA;IAEAc;MACAxD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAyD;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAA;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBACAA;gBACAA;cAAA;gBAGA;gBACAC;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA7D;gBACA8D;kBACA/B;kBACAgC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACAF;QACAG;MACA;IACA;IAEAC;MACAJ;QACAG;MACA;IACA;IAEAE;MACAL;QACAG;MACA;IACA;IAEAG;MACA;MAEA;MACA;MACA;MACA;;MAEA;MACA;IACA;EAAA,6EAGAC;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA;EACA,oFAEA;IAAA;MAAA;IACA;MAAA;IAAA;IACA;EACA,oFAEA;IACA;EACA,oFAGA;IACArE;IACA;IACA;IACA;IACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AClaA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC2HxvB;EACAjB;EACAC;IACAsF;EACA;EACA/E;IACA;MACAgF;MACAC;MACAC;MACAC;IACA;EACA;EACA9E;IACA;EACA;EAEAC;IACA;IACA;MACAG;MACA;IACA;EACA;EAEA;EACAiB;IACA;MACAjB;MACA;IACA;EACA;EACAF;IACA6E;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA,kBACA;QACA9D;QACA/B;QACA8F;QACAC;MACA,GACA;QACAhE;QACA/B;QACA8F;QACAC;MACA,GACA;QACAhE;QACA/B;QACA8F;QACAC;MACA,GACA;QACAhE;QACA/B;QACA8F;QACAC;MACA,EACA;IACA;IAEAC;MACA;MACA,uBACA;QACAjE;QACAkE;QACAC;QACAC;QACAC;MACA,GACA;QACArE;QACAkE;QACAC;QACAC;QACAC;MACA,GACA;QACArE;QACAkE;QACAC;QACAC;QACAC;MACA,GACA;QACArE;QACAkE;QACAC;QACAC;QACAC;MACA,GACA;QACArE;QACAkE;QACAC;QACAC;QACAC;MACA,GACA;QACArE;QACAkE;QACAC;QACAC;QACAC;MACA,GACA;QACArE;QACAkE;QACAC;QACAC;QACAC;MACA,GACA;QACArE;QACAkE;QACAC;QACAC;QACAC;MACA,GACA;QACArE;QACAkE;QACAC;QACAC;QACAC;MACA,GACA;QACArE;QACAkE;QACAC;QACAC;QACAC;MACA,EACA;IACA;IAEAC;MACA;MACA,iBACA;QACAtE;QACAkB;QACAC;QACAC;QACAC;QACAI;QACAC;MACA,GACA;QACA1B;QACAkB;QACAC;QACAC;QACAC;QACAI;QACAC;MACA,EACA;IACA;IAEA6C;MACA;MACA,wBACA;QACAvE;QACAiB;QACAuD;QACAT;MACA,GACA;QACA/D;QACAiB;QACAuD;QACAT;MACA,EACA;IACA;IAEAU;MACAvF;MACA;IACA;IAEAwF;MACA;MACA;QAAA;MAAA;MACA;QACAC;MACA;MACAzF;IACA;IAEAmE;MACAL;QACAG;MACA;IACA;IAEAyB;MACA5B;QACAG;MACA;IACA;IAEA0B;MACA7B;QACAG;MACA;IACA;IAEA;IACA2B;MACA5F;MACA;MACA;MACA;MACA;MACA;IACA;IAEAkE;MACAJ;QACAG;MACA;IACA;IAIAD;MACAF;QACAG;MACA;IACA;IAEA4B;MACA/B;QACAG;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrXA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyKxvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAlF;EACAQ;IACA;MACAuG;QACAb;QACAD;MACA;MACAe;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEAC;MACAC;MAEAC;MACAC,YACA;QAAAzF;QAAA/B;QAAA+F;MAAA,GACA;QAAAhE;QAAA/B;QAAA+F;MAAA,GACA;QAAAhE;QAAA/B;QAAA+F;MAAA,GACA;QAAAhE;QAAA/B;QAAA+F;MAAA,GACA;QAAAhE;QAAA/B;QAAA+F;MAAA,GACA;QAAAhE;QAAA/B;QAAA+F;MAAA,GACA;QAAAhE;QAAA/B;QAAA+F;MAAA,GACA;QAAAhE;QAAA/B;QAAA+F;MAAA,GACA;QAAAhE;QAAA/B;QAAA+F;MAAA,GACA;QAAAhE;QAAA/B;QAAA+F;MAAA,EACA;MAEA0B,kBACA;QACA1F;QACA/B;QACA0H;MACA,GACA;QACA3F;QACA/B;QACA0H;MACA,GACA;QACA3F;QACA/B;QACA0H;MACA;IAEA;EACA;EACAC;IACAC;MACA;IACA;IAEAC;MACA;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QAAA,OACAC;MAAA,EACA;IACA;EACA;EACArH;IACA;EACA;EACAE;IACA;IACA8E;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAsC;gBACA;kBACA;oBAAA;sBACApG;sBACA/B;sBACA+F;oBACA;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9E;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAmH;MACA;QACArD;UACA/B;UACAG;UACAkF;YACA;cACAtD;YACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEAuD;MAAA;MACA;MACAvD;QACAwD;QACAC;QACAC;QACAJ;UAAA;UACA;QACA;MACA;IACA;IAEAK;MACA;IACA;IAEArD;MACA;IACA;IAEAsD;MACA;MACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MAAA;MACAhE;QACAiE;QACAX;UACA;UACA;QACA;MACA;IACA;IAEAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAlE;kBAAA/B;gBAAA;gBAAA;gBAGA;gBACAkG;kBACA/F;kBACAE;kBACAD;kBACA+F;oBAAA;kBAAA;kBACAC;kBACAC;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAhH;gBAEA0C;gBAEA;kBACA;kBAEAuE;oBACAvE;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA9D;gBACA8D;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpXA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6GxvB;AAAA;AAAA;AAAA,eAEA;EACA/E;EACAQ;IACA;MACA+I;MACA/H;MACAC;MACA+H;MACAC;MACAC;IACA;EACA;EACA7I;IACA;IACA;EACA;EAEAC;IACA;IACA;MACAG;MACA;MACA;IACA;EACA;EAEA;EACAiB;IACA;MACAjB;MACA;MACA;IACA;EACA;EACAF;IACA;IACA4I;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA3I;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA4I;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;kBACAnH;kBACAC;gBACA;cAAA;gBAHAN;gBAKA;kBACA;oBAAA;sBACAN;sBACA+H;sBACA9J;sBACAkG;sBACA6D;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBAAA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnJ;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAoJ;MACA,QACA;QACAtI;QACA/B;QACAkG;QACA6D;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACArI;QACA/B;QACAkG;QACA6D;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACArI;QACA/B;QACAkG;QACA6D;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACArI;QACA/B;QACAkG;QACA6D;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACArI;QACA/B;QACAkG;QACA6D;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA,EACA;IACA;IAEAzF;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MAEA;MACA;IACA;IAEAF;MACA;MACA;IACA;IAEA6F;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,MAGAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAV;gBACA;cAAA;gBAEA;gBACAS;;gBAEA;gBACAzF;kBAAA;gBAAA;gBACA;kBACA;gBACA;cAAA;gBAGAC;kBACAG;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAjE;gBACA;gBACA8D;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuF;MAAA;MACA;MAEA1F;QACAiE;QACAX;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEAqC;MACA;MACA;IACA;IAEAC;MACAJ;MACA;IACA;IAEAK;MAAA;MACA7F;QACA/B;QACAG;QACAkF;UACA;YACA;cAAA;YAAA;YACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAjD;MACAL;QACAG;MACA;IACA;IAEA2F;MACA9F;QACAG;MACA;IACA;IAEA4F;MACA/F;QACAG;MACA;IACA;IAEA6F;MACAhG;QACAG;MACA;IACA;IAEA8F;MACAjG;QACAG;MACA;IACA;IAEA;IACA2B;MACA5F;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzYA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,aAAa,+OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACwFxvB;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAjB;EACAC;IACAqB;EACA;EACAd;IACA;MACAuG;QACA+C;QACA7D;QACAC;QACA+E;QACAC;QACAnF;QACAoF;QACAC;QACA5H;QACA6H;MACA;MACA5K;MACAwB;MACAqJ,OACA;QACAtL;QACAQ,OACA;UACAuB;UACAiB;UACAI;UACAH;UACAC;UACAC;UACAK;UACAC;UACAC;UACAC;QACA,GACA;UACA5B;UACAiB;UACAI;UACAH;UACAC;UACAC;UACAK;UACAC;UACAC;UACAC;QACA,GACA;UACA5B;UACAiB;UACAI;UACAH;UACAC;UACAC;UACAK;UACAC;UACAC;UACAC;QACA,GACA;UACA5B;UACAiB;UACAI;UACAH;UACAC;UACAC;UACAK;UACAC;UACAC;UACAC;QACA;MAEA,GACA;QAAA3D;QAAAQ;MAAA,GACA;QAAAR;QAAAQ;MAAA;IAEA;EACA;EACAK;IAAA;IACAI;IACA;IACA;MACAqI;QACA;MACA;IACA;EACA;EACAxI;IACAG;IACA;IACA;MACA;IACA;EACA;EACAF;IACA;IACAoB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAlB;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAsK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA;gBAAA,OAEA;cAAA;gBAAAnJ;gBACApB;gBAEA;kBACAwK;kBACA;oBACA1J;oBACAkE;oBACAC;oBACA+E;oBACAC;oBACAQ;oBACAP;oBACAQ;oBACA5F;oBACAvC;kBACA;gBACA;kBACAvC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;kBACAc;kBACAkE;kBACAC;kBACA+E;kBACAC;kBACAQ;kBACAP;kBACAQ;kBACA5F;kBACAvC;gBACA;cAAA;gBAGA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAoI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAGApL;gBAAA,eAEAqL;gBAAA,kCACA,yBAGA,0BAGA;gBAAA;cAAA;gBAAA;gBAAA,OALA;cAAA;gBAAArL;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAS;gBACA;gBACA;kBACA6K;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAP;gBAAA;gBAAA,OACA;kBACA9I;kBACAC;kBACAmH;kBACAlH;kBACAC;gBACA;cAAA;gBANAR;gBAQApB;gBAAA,MAEAoB;kBAAA;kBAAA;gBAAA;gBAAA,kCACAA;kBAAA;oBACAN;oBACAiB;oBACAI;oBACAH;oBACAC;oBACAC;oBACAK;oBACAC;oBACAC;oBACAC;kBACA;gBAAA;cAAA;gBAEA1C;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA+K;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,kCAIA;cAAA;gBAAA;gBAAA;gBAEA/K;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgL;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,kCAIA;cAAA;gBAAA;gBAAA;gBAEAhL;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAiL;MACA;MACA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MACA;QACA;QACA;QACA;MACA;MAEA;QACA;QACA;QAEAnJ;UACAhB;UACAiB;UACAI;UACAH;UACAC;UACAC;UACAK;UACAC;UACAC;UACAC;QACA;MACA;MACA;IACA;IAEAwI;MACA;MACA;MACA;MACA;IACA;IAEAC;MACArH;QACAsD;UACApH;QACA;MACA;IACA;IAEAoL;MACAtH;QACAG;MACA;IACA;IAEAoH;MAAA;MACAvH;QACAwD;QACAC;QACAC;QACAJ;UACA;UACA;QACA;MACA;IACA;IAEAkE;MACAxH;QACAG;MACA;IACA;IAEAsH;MACAzH;QACAG;MACA;IACA;IAEAuH;MACA1H;QACAG;MACA;IACA;IAEA;IACAD;MACAF;QACAG;MACA;IACA;IAEAC;MACA;MACA;MAEAJ;QACAG;MACA;IACA;IAEAN;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAA;gBACAA;gBACAE;kBACA/B;kBACAgC;kBACA0H;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBACA7H;gBACAA;gBACAE;kBACA/B;kBACAgC;kBACA0H;gBACA;cAAA;gBAGA;gBACAC;gBACA7H;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA7D;gBACA8D;kBACA/B;kBACAgC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACheA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/main/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/main/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0c4b5bf9&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0c4b5bf9&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c4b5bf9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/main/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0c4b5bf9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"social-main-container\">\n    <!-- 内容区域 -->\n    <view class=\"content-area\">\n      <!-- 首页内容 -->\n      <HomePage v-if=\"currentTab === 0\" ref=\"homePage\" :key=\"homePageKey\" />\n\n      <!-- 发现页面内容 -->\n      <DiscoverPage v-if=\"currentTab === 1\" ref=\"discoverPage\" :key=\"discoverPageKey\" />\n\n      <PublishPage v-if=\"currentTab === 2\" />\n\n      <!-- 消息页面内容 -->\n      <MessagePage v-if=\"currentTab === 3\" ref=\"messagePage\" :key=\"messagePageKey\" />\n\n      <!-- 我的页面内容 -->\n      <ProfilePage v-if=\"currentTab === 4\" />\n    </view>\n\n    <!-- 底部导航 -->\n    <TabBar :currentTab=\"currentTab\" @tab-change=\"handleTabChange\" />\n  </view>\n</template>\n\n<script>\nimport TabBar from '../components/TabBar.vue'\nimport HomePage from '../home/<USER>'\nimport DiscoverPage from '../discover/index.vue'\nimport PublishPage from '../publish/index.vue'\nimport MessagePage from '../message/index.vue'\nimport ProfilePage from '../profile/index.vue'\n\nexport default {\n  name: 'SocialMain',\n  components: {\n    TabBar,\n    HomePage,\n    DiscoverPage,\n    PublishPage,\n    MessagePage,\n    ProfilePage\n  },\n  data() {\n    return {\n      currentTab: 0, // 默认显示首页\n      homePageKey: 0, // 用于强制刷新HomePage组件\n      discoverPageKey: 0, // 用于强制刷新DiscoverPage组件\n      messagePageKey: 0 // 用于强制刷新MessagePage组件\n    }\n  },\n  onLoad(options) {\n    // 根据传入的参数设置当前选项卡\n    if (options.tab) {\n      this.currentTab = parseInt(options.tab)\n    }\n  },\n\n  onShow() {\n    // 页面显示时，刷新当前tab的数据\n    this.refreshCurrentTab()\n  },\n  methods: {\n    // 处理 TabBar 切换事件\n    handleTabChange(data) {\n      const { index } = data\n      const previousTab = this.currentTab\n      this.currentTab = index\n\n      // 切换tab时刷新对应页面数据\n      if (previousTab !== index) {\n        this.refreshCurrentTab()\n      }\n\n      console.log('切换到选项卡:', index)\n    },\n\n    // 刷新当前tab的数据\n    refreshCurrentTab() {\n      this.$nextTick(() => {\n        switch (this.currentTab) {\n          case 0: // 首页\n            this.refreshHomePage()\n            break\n          case 1: // 发现页\n            this.refreshDiscoverPage()\n            break\n          case 3: // 消息页\n            this.refreshMessagePage()\n            break\n          default:\n            console.log('当前tab无需刷新:', this.currentTab)\n        }\n      })\n    },\n\n    // 刷新首页数据\n    refreshHomePage() {\n      if (this.$refs.homePage) {\n        if (this.$refs.homePage.forceRefresh) {\n          this.$refs.homePage.forceRefresh()\n        } else if (this.$refs.homePage.onRefresh) {\n          this.$refs.homePage.onRefresh()\n        } else {\n          this.homePageKey += 1\n        }\n      } else {\n        this.homePageKey += 1\n      }\n    },\n\n    // 刷新发现页数据\n    refreshDiscoverPage() {\n      if (this.$refs.discoverPage) {\n        if (this.$refs.discoverPage.forceRefresh) {\n          this.$refs.discoverPage.forceRefresh()\n        } else if (this.$refs.discoverPage.loadDiscoverData) {\n          this.$refs.discoverPage.loadDiscoverData()\n        } else {\n          this.discoverPageKey += 1\n        }\n      } else {\n        this.discoverPageKey += 1\n      }\n    },\n\n    // 刷新消息页数据\n    refreshMessagePage() {\n      if (this.$refs.messagePage) {\n        if (this.$refs.messagePage.forceRefresh) {\n          this.$refs.messagePage.forceRefresh()\n        } else if (this.$refs.messagePage.loadChatList) {\n          this.$refs.messagePage.loadUnreadCounts()\n          this.$refs.messagePage.loadChatList()\n        } else {\n          this.messagePageKey += 1\n        }\n      } else {\n        this.messagePageKey += 1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.social-main-container {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.content-area {\n  flex: 1;\n  overflow: hidden;\n}\n</style>\n", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=de45d8c2&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"de45d8c2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/home/<USER>\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=de45d8c2&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uSticky: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-sticky/u-sticky\" */ \"@/components/uview-ui/components/u-sticky/u-sticky.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.postList.length && !_vm.loading\n  var m0 = g0 ? _vm.getEmptyText() : null\n  var m1 = g0 ? _vm.getEmptyDesc() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"home-container\">\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <view class=\"logo\">\n          <text class=\"logo-text\">社区</text>\n        </view>\n        <view class=\"header-actions\">\n          <u-icon name=\"search\" size=\"24\" color=\"#666\" @click=\"goSearch\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 话题标签栏 -->\n    <view class=\"topic-tabs-container\">\n      <u-sticky bgColor=\"#fff\">\n        <u-tabs\n          :list=\"topicList\"\n          :current=\"currentTopicIndex\"\n          @change=\"selectTopic\"\n          :scrollable=\"true\"\n          activeColor=\"#2979ff\"\n          inactiveColor=\"#666\"\n          fontSize=\"28\"\n          lineColor=\"#2979ff\"\n          lineWidth=\"40\"\n          lineHeight=\"6\"\n          height=\"80\"\n          itemStyle=\"padding: 0 32rpx;\"\n        ></u-tabs>\n      </u-sticky>\n    </view>\n\n    <!-- 帖子网格列表 -->\n    <scroll-view\n      class=\"post-list\"\n      scroll-y\n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <view class=\"post-grid\">\n        <PostCard\n          v-for=\"post in postList\"\n          :key=\"post.id\"\n          :post=\"post\"\n          class=\"post-card-item\"\n          @click=\"goPostDetail\"\n          @user-click=\"goUserProfile\"\n          @like=\"onPostLike\"\n        />\n      </view>\n\n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"loading\">\n        <u-loading mode=\"circle\" size=\"24\"></u-loading>\n        <text class=\"load-text\">加载中...</text>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"!postList.length && !loading\" class=\"empty-state\">\n        <u-icon name=\"file-text\" color=\"#ccc\" size=\"120rpx\"></u-icon>\n        <text class=\"empty-text\">{{ getEmptyText() }}</text>\n        <text class=\"empty-desc\">{{ getEmptyDesc() }}</text>\n      </view>\n    </scroll-view>\n\n  </view>\n</template>\n\n<script>\nimport PostCard from '../components/PostCard.vue'\nimport { getPostList, getHotTags, likePost, unlikePost } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'SocialHome',\n  components: {\n    PostCard\n  },\n  data() {\n    return {\n      postList: [],\n      loading: false,\n      refreshing: false,\n      page: 1,\n      pageSize: 10,\n      currentTopic: 'all',\n      currentTopicIndex: 0,\n      topicList: [\n        { name: '全部', id: 'all' },\n        { name: '街舞', id: 'street-dance' },\n        { name: '现代舞', id: 'modern-dance' },\n        { name: '芭蕾', id: 'ballet' },\n        { name: '拉丁舞', id: 'latin-dance' },\n        { name: '爵士舞', id: 'jazz-dance' },\n        { name: '民族舞', id: 'folk-dance' },\n        { name: '古典舞', id: 'classical-dance' },\n        { name: '舞蹈教学', id: 'dance-teaching' },\n        { name: '舞蹈比赛', id: 'dance-competition' }\n      ],\n      hasMore: true,\n      isInitialized: false // 标记是否已初始化\n    }\n  },\n  onLoad() {\n    this.initializeData()\n  },\n\n  // 页面显示时重新加载数据\n  onShow() {\n    // 如果还没有初始化，或者数据为空，重新加载\n    if (!this.isInitialized || !this.postList || this.postList.length === 0) {\n      this.initializeData()\n    }\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    // 如果还没有初始化，或者数据为空，重新加载\n    if (!this.isInitialized || !this.postList || this.postList.length === 0) {\n      this.initializeData()\n    }\n  },\n  methods: {\n    // 初始化数据\n    async initializeData() {\n      console.log('初始化首页数据...')\n      try {\n        await this.loadHotTags()\n        await this.loadPosts(true)\n        this.isInitialized = true\n      } catch (error) {\n        console.error('初始化数据失败:', error)\n      }\n    },\n\n    // 加载热门话题\n    async loadHotTags() {\n      try {\n        const result = await getHotTags(10)\n        console.log('热门标签API返回:', result)\n\n        if (result && result.code === 0 && result.data && result.data.length > 0) {\n          // 保留\"全部\"选项，添加热门话题\n          const allOption = this.topicList[0]\n          this.topicList = [allOption, ...result.data.map(tag => ({\n            name: tag.name,\n            id: tag.id\n          }))]\n          console.log('话题列表更新:', this.topicList)\n        }\n      } catch (error) {\n        console.error('加载热门话题失败:', error)\n        // 使用默认话题列表\n      }\n    },\n\n    async loadPosts(refresh = false) {\n      if (this.loading) {\n        console.log('正在加载中，跳过重复请求')\n        return\n      }\n\n      console.log('开始加载帖子数据，refresh:', refresh)\n      this.loading = true\n      try {\n        const params = {\n          current: refresh ? 1 : this.page,\n          size: this.pageSize,\n          sortField: 'createTime',\n          sortOrder: 'desc'\n        }\n\n        // 如果选择了特定话题，添加标签筛选\n        if (this.currentTopic !== 'all') {\n          // 根据tagId获取标签名称\n          const selectedTag = this.topicList.find(topic => topic.id === this.currentTopic)\n          if (selectedTag) {\n            params.tags = [selectedTag.name]\n          }\n        }\n\n        console.log('API请求参数:', params)\n        const result = await getPostList(params)\n        console.log('API返回结果:', result)\n\n        if (result.code == 0) {\n          const posts = result.data.records.map(post => ({\n            id: post.id,\n            title: post.title || '',\n            username: post.nickname || '无名氏',\n            userAvatar: 'https://file.foxdance.com.cn' + post.avatar || '/static/images/toux.png',\n            content: post.content,\n            coverImage: (post.images && post.images[0]) || '/static/images/wusj.png',\n            images: post.images || [],\n            topics: post.tags || [],\n            topicId: this.currentTopic,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            isLiked: post.isLiked || false,\n            createTime: new Date(post.createTime)\n          }))\n          console.log('posts:', posts)\n\n          if (refresh) {\n            this.postList = posts\n            this.page = 2\n          } else {\n            this.postList = [...this.postList, ...posts]\n            this.page++\n          }\n          console.log('this.postList:', this.postList)\n\n          // 检查是否还有更多数据\n          this.hasMore = result.data.records.length >= this.pageSize\n        } else {\n          // 如果API调用失败，使用模拟数据\n          const mockPosts = this.generateMockPosts()\n          if (refresh) {\n            this.postList = mockPosts\n            this.page = 2\n          } else {\n            this.postList = [...this.postList, ...mockPosts]\n            this.page++\n          }\n        }\n\n      } catch (error) {\n        console.error('加载帖子失败:', error)\n        // 使用模拟数据作为后备\n        const mockPosts = this.generateMockPosts()\n        if (refresh) {\n          this.postList = mockPosts\n          this.page = 2\n        } else {\n          this.postList = [...this.postList, ...mockPosts]\n          this.page++\n        }\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    generateMockPosts() {\n      const posts = []\n      const topicData = {\n        life: { titles: ['今日穿搭分享', '生活小确幸', '宠物日常', '家居装饰'], name: '生活' },\n        food: { titles: ['美食探店记录', '美妆教程', '手工制作'], name: '美食' },\n        travel: { titles: ['旅行日记', '风景随拍'], name: '旅行' },\n        photography: { titles: ['摄影作品', '风景随拍'], name: '摄影' },\n        fitness: { titles: ['健身打卡'], name: '健身' },\n        fashion: { titles: ['今日穿搭分享', '美妆教程'], name: '时尚' },\n        music: { titles: ['音乐推荐'], name: '音乐' },\n        movie: { titles: ['电影观后感'], name: '电影' },\n        reading: { titles: ['读书笔记', '学习心得'], name: '读书' }\n      }\n\n      // 如果选择了特定话题，只生成该话题的帖子\n      let availableTopics = Object.keys(topicData)\n      if (this.currentTopic !== 'all') {\n        availableTopics = [this.currentTopic]\n      }\n\n      for (let i = 0; i < this.pageSize; i++) {\n        const randomTopicKey = availableTopics[Math.floor(Math.random() * availableTopics.length)]\n        const topicInfo = topicData[randomTopicKey]\n        const randomTitle = topicInfo.titles[Math.floor(Math.random() * topicInfo.titles.length)]\n        const randomId = Math.floor(Math.random() * 1000)\n\n        posts.push({\n          id: Date.now() + i,\n          title: randomTitle,\n          username: `用户${randomId}`,\n          userAvatar: `https://picsum.photos/100/100?random=${randomId}`,\n          content: `${randomTitle} - 分享我的生活点滴，希望大家喜欢！`,\n          coverImage: `https://picsum.photos/300/400?random=${Date.now() + i}`,\n          images: [`https://picsum.photos/300/400?random=${Date.now() + i}`],\n          topics: [topicInfo.name],\n          topicId: randomTopicKey,\n          likeCount: Math.floor(Math.random() * 2000),\n          commentCount: Math.floor(Math.random() * 100),\n          isLiked: Math.random() > 0.7,\n          createTime: new Date(Date.now() - Math.random() * 86400000 * 7)\n        })\n      }\n      return posts\n    },\n\n    onRefresh() {\n      console.log('刷新首页数据...')\n      this.refreshing = true\n      this.page = 1\n      this.postList = []\n      this.hasMore = true\n      // 重新加载热门话题和帖子\n      this.loadHotTags()\n      this.loadPosts(true)\n    },\n\n    loadMore() {\n      if (!this.loading) {\n        this.page++\n        this.loadPosts()\n      }\n    },\n\n    formatTime(time) {\n      const now = new Date()\n      const diff = now - new Date(time)\n      const minutes = Math.floor(diff / 60000)\n      const hours = Math.floor(diff / 3600000)\n      const days = Math.floor(diff / 86400000)\n      \n      if (minutes < 60) return `${minutes}分钟前`\n      if (hours < 24) return `${hours}小时前`\n      return `${days}天前`\n    },\n\n    async onPostLike(post) {\n      try {\n        if (post.isLiked) {\n          // 取消点赞\n          await unlikePost(post.id)\n          post.isLiked = false\n          post.likeCount = Math.max(0, post.likeCount - 1)\n        } else {\n          // 点赞\n          await likePost(post.id)\n          post.isLiked = true\n          post.likeCount += 1\n        }\n\n        // 更新帖子列表中的数据\n        const index = this.postList.findIndex(p => p.id === post.id)\n        if (index !== -1) {\n          this.$set(this.postList, index, { ...post })\n        }\n\n      } catch (error) {\n        console.error('点赞操作失败:', error)\n        uni.showToast({\n          title: '操作失败',\n          icon: 'none'\n        })\n      }\n    },\n\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    },\n\n    goUserProfile(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${post.userId || post.id}`\n      })\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: '/pagesSub/social/search/index'\n      })\n    },\n\n    selectTopic(index) {\n      if (this.currentTopicIndex === index) return\n\n      this.currentTopicIndex = index\n      this.currentTopic = this.topicList[index].id\n      this.page = 1\n      this.postList = []\n\n      // 重新加载帖子\n      this.loadPosts(true)\n    },\n\n    // 格式化时间\n    formatTime(dateString) {\n      if (!dateString) return ''\n\n      const date = new Date(dateString)\n      const now = new Date()\n      const diff = now - date\n\n      const minutes = Math.floor(diff / (1000 * 60))\n      const hours = Math.floor(diff / (1000 * 60 * 60))\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\n\n      if (minutes < 1) return '刚刚'\n      if (minutes < 60) return `${minutes}分钟前`\n      if (hours < 24) return `${hours}小时前`\n      if (days < 7) return `${days}天前`\n\n      return date.toLocaleDateString()\n    },\n\n    getEmptyText() {\n      const currentTopicName = this.topicList.find(topic => topic.id === this.currentTopic)?.name || '全部'\n      return this.currentTopic === 'all' ? '暂无帖子' : `暂无${currentTopicName}相关帖子`\n    },\n\n    getEmptyDesc() {\n      return this.currentTopic === 'all' ? '快来发布第一条帖子吧' : '换个话题看看其他内容吧'\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log('强制刷新首页数据...')\n      this.isInitialized = false\n      this.postList = []\n      this.page = 1\n      this.hasMore = true\n      this.initializeData()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.home-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 100px;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 2rpx solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 88rpx;\n\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 32rpx;\n\n}\n\n.logo-text {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #2979ff;\n}\n\n.topic-tabs-container {\n  position: fixed;\n  top: calc(88rpx + var(--status-bar-height));\n  left: 0;\n  right: 0;\n  z-index: 99;\n  background: #fff;\n  border-bottom: 2rpx solid #f0f0f0;\n}\n\n/* uview tabs组件样式优化 */\n.topic-tabs-container ::v-deep .u-tabs {\n  background: #fff;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item {\n  padding: 0 32rpx !important;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item__text {\n  font-size: 28rpx !important;\n  font-weight: 500;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__line {\n  border-radius: 6rpx;\n}\n\n.post-list {\n  margin-top: calc(168rpx + var(--status-bar-height));\n  margin: 230rpx 26rpx;\n  width: auto;\n}\n\n.post-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 14rpx;\n  padding-bottom: 40rpx;\n}\n\n.post-card-item {\n  width: calc(50% - 8rpx);\n  margin-bottom: 16rpx;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 160rpx 40rpx;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #999;\n  margin: 32rpx 0 16rpx;\n}\n\n.empty-desc {\n  font-size: 28rpx;\n  color: #ccc;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.load-text {\n  margin-left: 16rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 375px) {\n  .post-list {\n    padding: 12rpx;\n  }\n\n  .post-grid {\n    gap: 12rpx;\n  }\n\n  .post-card-item {\n    width: calc(50% - 6rpx);\n  }\n}\n\n@media screen and (min-width: 768px) {\n  .post-grid {\n    gap: 24rpx;\n  }\n\n  .post-card-item {\n    width: calc(33.33% - 16rpx);\n  }\n}\n\n@media screen and (min-width: 1024px) {\n  .post-list {\n    padding: 32rpx 64rpx;\n  }\n\n  .post-card-item {\n    width: calc(25% - 18rpx);\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752897206143\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1fcbd0ae&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1fcbd0ae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/discover/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1fcbd0ae&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"discover-container\">\n    <!-- 顶部搜索 -->\n    <view class=\"header\">\n      <view class=\"search-bar\" @click=\"goSearch\">\n        <u-icon name=\"search\" size=\"18\" color=\"#999\"></u-icon>\n        <text class=\"search-placeholder\">搜索话题、用户...</text>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 热门话题 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">热门话题</text>\n          <text class=\"more-btn\" @click=\"goTopicList\">更多</text>\n        </view>\n        <view class=\"topic-grid\">\n          <view \n            v-for=\"topic in hotTopics\" \n            :key=\"topic.id\"\n            class=\"topic-card\"\n            @click=\"goTopic(topic)\"\n          >\n            <image :src=\"topic.cover\" class=\"topic-cover\" mode=\"aspectFill\" />\n            <view class=\"topic-info\">\n              <text class=\"topic-name\">#{{ topic.name }}</text>\n              <text class=\"topic-count\">{{ topic.postCount }}条帖子</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 推荐用户 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">推荐关注</text>\n        </view>\n        <scroll-view class=\"user-scroll\" scroll-x>\n          <view class=\"user-list\">\n            <view \n              v-for=\"user in recommendUsers\" \n              :key=\"user.id\"\n              class=\"user-card\"\n              @click=\"goUserProfile(user)\"\n            >\n              <u-avatar :src=\"user.avatar\" size=\"60\"></u-avatar>\n              <text class=\"user-name\">{{ user.nickname }}</text>\n              <text class=\"user-desc\">{{ user.description }}</text>\n              <FollowButton\n                :user=\"user\"\n                :followed=\"user.isFollowed\"\n                size=\"mini\"\n                @follow=\"onUserFollow\"\n                @change=\"onFollowChange\"\n                @click.stop\n              />\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 热门帖子 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">热门帖子</text>\n        </view>\n        <view class=\"hot-posts\">\n          <view \n            v-for=\"post in hotPosts\" \n            :key=\"post.id\"\n            class=\"hot-post-item\"\n            @click=\"goPostDetail(post)\"\n          >\n            <view class=\"post-content\">\n              <view class=\"user-info\">\n                <u-avatar :src=\"post.userAvatar\" size=\"32\"></u-avatar>\n                <text class=\"username\">{{ post.username }}</text>\n              </view>\n              <text class=\"post-text\">{{ post.content }}</text>\n              <view class=\"post-stats\">\n                <text class=\"stat-item\">{{ post.likeCount }}赞</text>\n                <text class=\"stat-item\">{{ post.commentCount }}评论</text>\n              </view>\n            </view>\n            <image \n              v-if=\"post.coverImage\" \n              :src=\"post.coverImage\" \n              class=\"post-cover\"\n              mode=\"aspectFill\"\n            />\n          </view>\n        </view>\n      </view>\n\n      <!-- 精选内容 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">精选内容</text>\n        </view>\n        <view class=\"featured-grid\">\n          <view \n            v-for=\"item in featuredContent\" \n            :key=\"item.id\"\n            class=\"featured-item\"\n            @click=\"goFeaturedDetail(item)\"\n          >\n            <image :src=\"item.cover\" class=\"featured-cover\" mode=\"aspectFill\" />\n            <view class=\"featured-overlay\">\n              <text class=\"featured-title\">{{ item.title }}</text>\n              <text class=\"featured-subtitle\">{{ item.subtitle }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n\n  </view>\n</template>\n\n<script>\nimport FollowButton from '../components/FollowButton.vue'\n\nexport default {\n  name: 'SocialDiscover',\n  components: {\n    FollowButton\n  },\n  data() {\n    return {\n      hotTopics: [],\n      recommendUsers: [],\n      hotPosts: [],\n      featuredContent: []\n    }\n  },\n  onLoad() {\n    this.loadDiscoverData()\n  },\n\n  onShow() {\n    // 页面显示时检查是否需要加载数据\n    if (!this.hotTopics || this.hotTopics.length === 0) {\n      console.log('发现页显示时重新加载数据')\n      this.loadDiscoverData()\n    }\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    if (!this.hotTopics || this.hotTopics.length === 0) {\n      console.log('发现页激活时重新加载数据')\n      this.loadDiscoverData()\n    }\n  },\n  methods: {\n    loadDiscoverData() {\n      this.loadHotTopics()\n      this.loadRecommendUsers()\n      this.loadHotPosts()\n      this.loadFeaturedContent()\n    },\n\n    loadHotTopics() {\n      // 模拟热门话题数据\n      this.hotTopics = [\n        {\n          id: 1,\n          name: '今日穿搭',\n          cover: 'https://picsum.photos/200/120?random=1',\n          postCount: 1234\n        },\n        {\n          id: 2,\n          name: '美食分享',\n          cover: 'https://picsum.photos/200/120?random=2',\n          postCount: 856\n        },\n        {\n          id: 3,\n          name: '旅行日记',\n          cover: 'https://picsum.photos/200/120?random=3',\n          postCount: 642\n        },\n        {\n          id: 4,\n          name: '生活记录',\n          cover: 'https://picsum.photos/200/120?random=4',\n          postCount: 789\n        }\n      ]\n    },\n\n    loadRecommendUsers() {\n      // 模拟推荐用户数据\n      this.recommendUsers = [\n        {\n          id: 1,\n          nickname: '小美',\n          avatar: 'https://picsum.photos/100/100?random=10',\n          description: '分享生活美好',\n          isFollowed: false\n        },\n        {\n          id: 2,\n          nickname: '旅行达人',\n          avatar: 'https://picsum.photos/100/100?random=11',\n          description: '世界那么大',\n          isFollowed: false\n        },\n        {\n          id: 3,\n          nickname: '美食家',\n          avatar: 'https://picsum.photos/100/100?random=12',\n          description: '发现美味',\n          isFollowed: true\n        },\n        {\n          id: 4,\n          nickname: '摄影师',\n          avatar: 'https://picsum.photos/100/100?random=13',\n          description: '记录美好瞬间',\n          isFollowed: false\n        },\n        {\n          id: 5,\n          nickname: '健身达人',\n          avatar: 'https://picsum.photos/100/100?random=14',\n          description: '健康生活方式',\n          isFollowed: false\n        },\n        {\n          id: 6,\n          nickname: '音乐人',\n          avatar: 'https://picsum.photos/100/100?random=15',\n          description: '用音乐表达情感',\n          isFollowed: true\n        },\n        {\n          id: 7,\n          nickname: '读书爱好者',\n          avatar: 'https://picsum.photos/100/100?random=16',\n          description: '书中自有黄金屋',\n          isFollowed: false\n        },\n        {\n          id: 8,\n          nickname: '手工达人',\n          avatar: 'https://picsum.photos/100/100?random=17',\n          description: '手作温暖生活',\n          isFollowed: false\n        },\n        {\n          id: 9,\n          nickname: '宠物博主',\n          avatar: 'https://picsum.photos/100/100?random=18',\n          description: '萌宠日常分享',\n          isFollowed: true\n        },\n        {\n          id: 10,\n          nickname: '科技极客',\n          avatar: 'https://picsum.photos/100/100?random=19',\n          description: '探索科技前沿',\n          isFollowed: false\n        }\n      ]\n    },\n\n    loadHotPosts() {\n      // 模拟热门帖子数据\n      this.hotPosts = [\n        {\n          id: 1,\n          username: '时尚博主',\n          userAvatar: 'https://picsum.photos/100/100?random=20',\n          content: '今天的穿搭分享，简约而不简单的搭配技巧...',\n          coverImage: 'https://picsum.photos/120/120?random=30',\n          likeCount: 234,\n          commentCount: 45\n        },\n        {\n          id: 2,\n          username: '美食探店',\n          userAvatar: 'https://picsum.photos/100/100?random=21',\n          content: '发现了一家超棒的咖啡店，环境和咖啡都很赞！',\n          coverImage: 'https://picsum.photos/120/120?random=31',\n          likeCount: 189,\n          commentCount: 32\n        }\n      ]\n    },\n\n    loadFeaturedContent() {\n      // 模拟精选内容数据\n      this.featuredContent = [\n        {\n          id: 1,\n          title: '春日穿搭指南',\n          subtitle: '时尚达人教你搭配',\n          cover: 'https://picsum.photos/300/200?random=40'\n        },\n        {\n          id: 2,\n          title: '周末好去处',\n          subtitle: '城市探索攻略',\n          cover: 'https://picsum.photos/300/200?random=41'\n        }\n      ]\n    },\n\n    onUserFollow(data) {\n      console.log('关注操作:', data)\n      // 这里可以调用API进行关注/取消关注操作\n    },\n\n    onFollowChange(data) {\n      // 更新本地数据\n      const user = this.recommendUsers.find(u => u.id === data.user.id)\n      if (user) {\n        user.isFollowed = data.isFollowed\n      }\n      console.log('关注状态变化:', data)\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: '/pagesSub/social/search/index'\n      })\n    },\n\n    goTopic(topic) {\n      uni.navigateTo({\n        url: `/pagesSub/social/topic/detail?id=${topic.id}`\n      })\n    },\n\n    goTopicList() {\n      uni.navigateTo({\n        url: '/pagesSub/social/topic/list'\n      })\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log('强制刷新发现页数据...')\n      this.hotTopics = []\n      this.recommendUsers = []\n      this.hotPosts = []\n      this.featuredContent = []\n      this.loadDiscoverData()\n    },\n\n    goUserProfile(user) {\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${user.id}`\n      })\n    },\n\n\n\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    },\n\n    goFeaturedDetail(item) {\n      uni.navigateTo({\n        url: `/pagesSub/social/featured/detail?id=${item.id}`\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.discover-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 200rpx;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  padding: var(--status-bar-height) 32rpx 24rpx;\n  border-bottom: 2rpx solid #e4e7ed;\n}\n\n.search-bar {\n  height: 72rpx;\n  background: #f5f5f5;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  padding: 0 32rpx;\n}\n\n.search-placeholder {\n  margin-left: 16rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.content {\n  margin-top: calc(125rpx + var(--status-bar-height));\n  padding: 0 32rpx;\n  width: auto;\n}\n\n.section {\n  margin-bottom: 48rpx;\n}\n\n.section-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 24rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.more-btn {\n  font-size: 28rpx;\n  color: #2979ff;\n}\n\n.topic-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 22rpx;\n}\n\n.topic-card {\n  width: calc(50% - 12rpx);\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.topic-cover {\n  width: 100%;\n  height: 160rpx;\n}\n\n.topic-info {\n  padding: 24rpx;\n}\n\n.topic-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  display: block;\n}\n\n.topic-count {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 8rpx;\n  display: block;\n}\n\n.user-scroll {\n  white-space: nowrap;\n}\n\n.user-list {\n  display: flex;\n  gap: 32rpx;\n  padding-bottom: 16rpx;\n}\n\n.user-card {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 32rpx 24rpx;\n  min-width: 200rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.user-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin: 16rpx 0 8rpx;\n  text-align: center;\n}\n\n.user-desc {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 24rpx;\n  text-align: center;\n}\n\n.hot-posts {\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.hot-post-item {\n  display: flex;\n  padding: 32rpx;\n  border-bottom: 2rpx solid #f0f0f0;\n}\n\n.hot-post-item:last-child {\n  border-bottom: none;\n}\n\n.post-content {\n  flex: 1;\n  margin-right: 24rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.username {\n  margin-left: 16rpx;\n  font-size: 26rpx;\n  color: #666;\n}\n\n.post-text {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.4;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n.post-stats {\n  display: flex;\n  gap: 32rpx;\n}\n\n.stat-item {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.post-cover {\n  width: 160rpx;\n  height: 160rpx;\n  border-radius: 16rpx;\n}\n\n.featured-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.featured-item {\n  position: relative;\n  height: 240rpx;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.featured-cover {\n  width: 100%;\n  height: 100%;\n}\n\n.featured-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));\n  padding: 40rpx 32rpx 32rpx;\n}\n\n.featured-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #fff;\n  display: block;\n}\n\n.featured-subtitle {\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 8rpx;\n  display: block;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752827279629\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=bb7c3636&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bb7c3636\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/publish/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=bb7c3636&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-input/u-input\" */ \"@/components/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.postContent.length\n  var g1 = _vm.selectedImages.length\n  var g2 = _vm.selectedTopics.length\n  var g3 = g2\n    ? _vm.selectedTopics\n        .map(function (t) {\n          return \"#\" + t\n        })\n        .join(\" \")\n    : null\n  var l0 = _vm.__map(_vm.filteredTopics, function (topic, __i0__) {\n    var $orig = _vm.__get_orig(topic)\n    var g4 = _vm.selectedTopics.includes(topic.name)\n    return {\n      $orig: $orig,\n      g4: g4,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showTopicModal = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showLocationModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"publish-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"cancel-btn\" @click=\"goBack\">取消</text>\n        <text class=\"title\">发布帖子</text>\n        <text \n          class=\"publish-btn\" \n          :class=\"{ disabled: !canPublish }\"\n          @click=\"publishPost\"\n        >\n          发布\n        </text>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 用户信息 -->\n      <view class=\"user-section\">\n        <u-avatar :src=\"userInfo.avatar\" size=\"40\"></u-avatar>\n        <text class=\"username\">{{ userInfo.nickname }}</text>\n      </view>\n\n      <!-- 文字输入 -->\n      <view class=\"text-section\">\n        <textarea\n          v-model=\"postContent\"\n          class=\"content-input\"\n          placeholder=\"分享你的生活...\"\n          :maxlength=\"500\"\n          auto-height\n          :show-confirm-bar=\"false\"\n        />\n        <view class=\"char-count\">{{ postContent.length }}/500</view>\n      </view>\n\n      <!-- 图片上传 -->\n      <view class=\"image-section\">\n        <view class=\"image-grid\">\n          <view \n            v-for=\"(image, index) in selectedImages\" \n            :key=\"index\"\n            class=\"image-item\"\n          >\n            <image :src=\"image\" class=\"uploaded-image\" mode=\"aspectFill\" />\n            <view class=\"delete-btn\" @click=\"removeImage(index)\">\n              <u-icon name=\"close\" color=\"#fff\" size=\"16\"></u-icon>\n            </view>\n          </view>\n          <view \n            v-if=\"selectedImages.length < 9\"\n            class=\"add-image-btn\"\n            @click=\"chooseImage\"\n          >\n            <u-icon name=\"camera\" color=\"#999\" size=\"32\"></u-icon>\n            <text class=\"add-text\">添加图片</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 功能选项 -->\n      <view class=\"options-section\">\n        <!-- 话题选择 -->\n        <view class=\"option-item\" @click=\"selectTopic\">\n          <view class=\"option-left\">\n            <u-icon name=\"tags\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">添加话题</text>\n          </view>\n          <view class=\"option-right\">\n            <text v-if=\"selectedTopics.length\" class=\"selected-topics\">\n              {{ selectedTopics.map(t => '#' + t).join(' ') }}\n            </text>\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n\n        <!-- 位置定位 -->\n        <view class=\"option-item\" @click=\"selectLocation\">\n          <view class=\"option-left\">\n            <u-icon name=\"map\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">添加位置</text>\n          </view>\n          <view class=\"option-right\">\n            <text v-if=\"selectedLocation\" class=\"selected-location\">\n              {{ selectedLocation.name }}\n            </text>\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n\n        <!-- 可见性设置 -->\n        <view class=\"option-item\" @click=\"setVisibility\">\n          <view class=\"option-left\">\n            <u-icon name=\"eye\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">可见性</text>\n          </view>\n          <view class=\"option-right\">\n            <text class=\"visibility-text\">{{ visibilityText }}</text>\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n      </view>\n\n      <!-- 提醒文字 -->\n      <view class=\"tips-section\">\n        <text class=\"tips-text\">\n          发布即表示同意《社区公约》，请文明发言，共建和谐社区\n        </text>\n      </view>\n    </scroll-view>\n\n    <!-- 话题选择弹窗 -->\n    <u-popup v-model=\"showTopicModal\" mode=\"bottom\" border-radius=\"20\">\n      <view class=\"topic-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">选择话题</text>\n          <u-icon name=\"close\" @click=\"showTopicModal = false\"></u-icon>\n        </view>\n        <view class=\"topic-search\">\n          <u-input \n            v-model=\"topicKeyword\" \n            placeholder=\"搜索话题\"\n            prefix-icon=\"search\"\n            @input=\"searchTopics\"\n          />\n        </view>\n        <view class=\"topic-list\">\n          <view \n            v-for=\"topic in filteredTopics\" \n            :key=\"topic.id\"\n            class=\"topic-option\"\n            :class=\"{ selected: selectedTopics.includes(topic.name) }\"\n            @click=\"toggleTopic(topic)\"\n          >\n            <text class=\"topic-name\">#{{ topic.name }}</text>\n            <text class=\"topic-count\">{{ topic.postCount }}条帖子</text>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n\n    <!-- 位置选择弹窗 -->\n    <u-popup v-model=\"showLocationModal\" mode=\"bottom\" border-radius=\"20\">\n      <view class=\"location-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">选择位置</text>\n          <u-icon name=\"close\" @click=\"showLocationModal = false\"></u-icon>\n        </view>\n        <view class=\"location-list\">\n          <view \n            v-for=\"location in nearbyLocations\" \n            :key=\"location.id\"\n            class=\"location-option\"\n            @click=\"selectLocationItem(location)\"\n          >\n            <u-icon name=\"map-pin\" color=\"#2979ff\" size=\"16\"></u-icon>\n            <view class=\"location-info\">\n              <text class=\"location-name\">{{ location.name }}</text>\n              <text class=\"location-address\">{{ location.address }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport { createPost, getHotTags } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'SocialPublish',\n  data() {\n    return {\n      userInfo: {\n        avatar: 'https://picsum.photos/100/100?random=999',\n        nickname: '我的昵称'\n      },\n      postContent: '',\n      selectedImages: [],\n      selectedTopics: [],\n      selectedLocation: null,\n      visibility: 'public', // public, friends, private\n      \n      showTopicModal: false,\n      showLocationModal: false,\n      \n      topicKeyword: '',\n      allTopics: [\n        { id: 1, name: '街舞', postCount: 1234 },\n        { id: 2, name: '现代舞', postCount: 856 },\n        { id: 3, name: '芭蕾', postCount: 642 },\n        { id: 4, name: '拉丁舞', postCount: 789 },\n        { id: 5, name: '爵士舞', postCount: 456 },\n        { id: 6, name: '民族舞', postCount: 321 },\n        { id: 7, name: '古典舞', postCount: 298 },\n        { id: 8, name: '舞蹈教学', postCount: 567 },\n        { id: 9, name: '舞蹈比赛', postCount: 234 },\n        { id: 10, name: '舞蹈培训', postCount: 189 }\n      ],\n      \n      nearbyLocations: [\n        {\n          id: 1,\n          name: '星巴克咖啡',\n          address: '北京市朝阳区三里屯太古里'\n        },\n        {\n          id: 2,\n          name: '三里屯太古里',\n          address: '北京市朝阳区三里屯路19号'\n        },\n        {\n          id: 3,\n          name: '朝阳公园',\n          address: '北京市朝阳区朝阳公园南路1号'\n        }\n      ]\n    }\n  },\n  computed: {\n    canPublish() {\n      return this.postContent.trim().length > 0 || this.selectedImages.length > 0\n    },\n    \n    visibilityText() {\n      const map = {\n        public: '公开',\n        friends: '仅朋友可见',\n        private: '仅自己可见'\n      }\n      return map[this.visibility]\n    },\n    \n    filteredTopics() {\n      if (!this.topicKeyword) return this.allTopics\n      return this.allTopics.filter(topic => \n        topic.name.includes(this.topicKeyword)\n      )\n    }\n  },\n  onLoad() {\n    this.loadHotTopics()\n  },\n  methods: {\n    // 加载热门话题\n    async loadHotTopics() {\n      try {\n        const hotTags = await getHotTags(20)\n        if (hotTags && hotTags.length > 0) {\n          this.allTopics = hotTags.map(tag => ({\n            id: tag.tagId || tag.id,\n            name: tag.tagName || tag.name,\n            postCount: tag.postCount || 0\n          }))\n        }\n      } catch (error) {\n        console.error('加载热门话题失败:', error)\n        // 使用默认话题列表\n      }\n    },\n    goBack() {\n      if (this.postContent || this.selectedImages.length) {\n        uni.showModal({\n          title: '提示',\n          content: '确定要放弃编辑吗？',\n          success: (res) => {\n            if (res.confirm) {\n              uni.navigateBack()\n            }\n          }\n        })\n      } else {\n        uni.navigateBack()\n      }\n    },\n\n    chooseImage() {\n      const maxCount = 9 - this.selectedImages.length\n      uni.chooseImage({\n        count: maxCount,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          this.selectedImages.push(...res.tempFilePaths)\n        }\n      })\n    },\n\n    removeImage(index) {\n      this.selectedImages.splice(index, 1)\n    },\n\n    selectTopic() {\n      this.showTopicModal = true\n    },\n\n    toggleTopic(topic) {\n      const index = this.selectedTopics.indexOf(topic.name)\n      if (index > -1) {\n        this.selectedTopics.splice(index, 1)\n      } else {\n        if (this.selectedTopics.length < 3) {\n          this.selectedTopics.push(topic.name)\n        } else {\n          this.$u.toast('最多选择3个话题')\n        }\n      }\n    },\n\n    searchTopics() {\n      // 搜索话题逻辑\n    },\n\n    selectLocation() {\n      this.showLocationModal = true\n    },\n\n    selectLocationItem(location) {\n      this.selectedLocation = location\n      this.showLocationModal = false\n    },\n\n    setVisibility() {\n      uni.showActionSheet({\n        itemList: ['公开', '仅朋友可见', '仅自己可见'],\n        success: (res) => {\n          const visibilityMap = ['public', 'friends', 'private']\n          this.visibility = visibilityMap[res.tapIndex]\n        }\n      })\n    },\n\n    async publishPost() {\n      if (!this.canPublish) return\n\n      uni.showLoading({ title: '发布中...' })\n\n      try {\n        // 构建发布数据\n        const postData = {\n          content: this.postContent.trim(),\n          images: this.selectedImages,\n          coverImage: this.selectedImages.length > 0 ? this.selectedImages[0] : null,\n          tags: this.selectedTopics.map(topic => topic.name),\n          locationName: this.location,\n          isPublic: this.visibility === 'public' ? 1 : 0\n        }\n\n        // 调用发布API\n        const result = await createPost(postData)\n\n        uni.hideLoading()\n\n        if (result) {\n          this.$u.toast('发布成功')\n\n          setTimeout(() => {\n            uni.navigateBack()\n          }, 1500)\n        } else {\n          this.$u.toast('发布失败，请重试')\n        }\n\n      } catch (error) {\n        console.error('发布帖子失败:', error)\n        uni.hideLoading()\n        this.$u.toast('发布失败，请重试')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.publish-container {\n  min-height: 100vh;\n\n  background: #f8f9fa;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 1px solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n}\n\n.cancel-btn, .publish-btn {\n  font-size: 16px;\n  color: #2979ff;\n}\n\n.publish-btn.disabled {\n  color: #ccc;\n}\n\n.title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.content {\n  margin-top: calc(44px + var(--status-bar-height));\n  padding: 16px;\n  width: auto;\n}\n\n.user-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.username {\n  margin-left: 12px;\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.text-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  position: relative;\n}\n\n.content-input {\n  width: 100%;\n  min-height: 120px;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #333;\n}\n\n.char-count {\n  position: absolute;\n  bottom: 12px;\n  right: 16px;\n  font-size: 12px;\n  color: #999;\n}\n\n.image-section {\n  margin-bottom: 16px;\n}\n\n.image-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.image-item {\n  position: relative;\n  width: calc(33.33% - 6px);\n  height: 100px;\n}\n\n.uploaded-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n}\n\n.delete-btn {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  width: 24px;\n  height: 24px;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-image-btn {\n  width: calc(33.33% - 6px);\n  height: 100px;\n  background: #f5f5f5;\n  border: 2px dashed #ddd;\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-text {\n  font-size: 12px;\n  color: #999;\n  margin-top: 4px;\n}\n\n.options-section {\n  background: #fff;\n  border-radius: 12px;\n  margin-bottom: 16px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.option-item:last-child {\n  border-bottom: none;\n}\n\n.option-left {\n  display: flex;\n  align-items: center;\n}\n\n.option-text {\n  margin-left: 12px;\n  font-size: 15px;\n  color: #333;\n}\n\n.option-right {\n  display: flex;\n  align-items: center;\n}\n\n.selected-topics, .selected-location, .visibility-text {\n  font-size: 14px;\n  color: #666;\n  margin-right: 8px;\n}\n\n.tips-section {\n  padding: 16px;\n}\n\n.tips-text {\n  font-size: 12px;\n  color: #999;\n  line-height: 1.4;\n  text-align: center;\n}\n\n.topic-modal, .location-modal {\n  background: #fff;\n  border-radius: 20px 20px 0 0;\n  max-height: 60vh;\n}\n\n.modal-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20px 20px 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.modal-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.topic-search {\n  padding: 16px 20px;\n}\n\n.topic-list, .location-list {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.topic-option {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.topic-option.selected {\n  background: #f0f8ff;\n}\n\n.topic-name {\n  font-size: 15px;\n  color: #333;\n}\n\n.topic-count {\n  font-size: 12px;\n  color: #999;\n}\n\n.location-option {\n  display: flex;\n  align-items: center;\n  padding: 12px 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.location-info {\n  margin-left: 12px;\n  flex: 1;\n}\n\n.location-name {\n  font-size: 15px;\n  color: #333;\n  display: block;\n}\n\n.location-address {\n  font-size: 12px;\n  color: #999;\n  margin-top: 2px;\n  display: block;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752818685188\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2caef1dd&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2caef1dd\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/message/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2caef1dd&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.chatList, function (chat, __i0__) {\n    var $orig = _vm.__get_orig(chat)\n    var m0 = _vm.formatTime(chat.lastMessageTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = !_vm.chatList.length && !_vm.loading\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"message-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"title\">消息</text>\n        <view class=\"header-actions\">\n          <u-icon name=\"search\" size=\"24\" color=\"#333\" @click=\"goSearch\"></u-icon>\n          <u-icon name=\"plus\" size=\"24\" color=\"#333\" @click=\"startNewChat\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 功能入口 -->\n    <view class=\"quick-actions\">\n      <view class=\"action-item\" @click=\"goSystemMessages\">\n        <view class=\"action-icon system\">\n          <u-icon name=\"bell\" color=\"#fff\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"action-text\">系统消息</text>\n        <view v-if=\"systemUnreadCount\" class=\"unread-badge\">{{ systemUnreadCount }}</view>\n      </view>\n      \n      <view class=\"action-item\" @click=\"goLikeMessages\">\n        <view class=\"action-icon like\">\n          <u-icon name=\"heart\" color=\"#fff\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"action-text\">赞和评论</text>\n        <view v-if=\"likeUnreadCount\" class=\"unread-badge\">{{ likeUnreadCount }}</view>\n      </view>\n      \n      <view class=\"action-item\" @click=\"goFollowMessages\">\n        <view class=\"action-icon follow\">\n          <u-icon name=\"account-fill\" color=\"#fff\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"action-text\">新粉丝</text>\n        <view v-if=\"followUnreadCount\" class=\"unread-badge\">{{ followUnreadCount }}</view>\n      </view>\n    </view>\n\n    <scroll-view \n      class=\"chat-list\" \n      scroll-y \n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <!-- 聊天列表 -->\n      <view \n        v-for=\"chat in chatList\" \n        :key=\"chat.id\"\n        class=\"chat-item\"\n        @click=\"openChat(chat)\"\n        @longpress=\"showChatActions(chat)\"\n      >\n        <view class=\"chat-avatar\">\n          <u-avatar :src=\"chat.avatar\" size=\"50\"></u-avatar>\n          <view v-if=\"chat.isOnline\" class=\"online-dot\"></view>\n        </view>\n        \n        <view class=\"chat-content\">\n          <view class=\"chat-header\">\n            <text class=\"chat-name\">{{ chat.name }}</text>\n            <text class=\"chat-time\">{{ formatTime(chat.lastMessageTime) }}</text>\n          </view>\n          \n          <view class=\"chat-preview\">\n            <view class=\"message-preview\">\n              <text v-if=\"chat.lastMessageType === 'text'\" class=\"preview-text\">\n                {{ chat.lastMessage }}\n              </text>\n              <text v-else-if=\"chat.lastMessageType === 'image'\" class=\"preview-text\">\n                [图片]\n              </text>\n              <text v-else-if=\"chat.lastMessageType === 'voice'\" class=\"preview-text\">\n                [语音]\n              </text>\n              <text v-else class=\"preview-text\">\n                {{ chat.lastMessage }}\n              </text>\n            </view>\n            \n            <view class=\"chat-status\">\n              <view v-if=\"chat.unreadCount\" class=\"unread-count\">\n                {{ chat.unreadCount > 99 ? '99+' : chat.unreadCount }}\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"!chatList.length && !loading\" class=\"empty-state\">\n        <u-icon name=\"chat\" color=\"#ccc\" size=\"60\"></u-icon>\n        <text class=\"empty-text\">暂无消息</text>\n        <text class=\"empty-desc\">开始与朋友聊天吧</text>\n      </view>\n\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-state\">\n        <u-loading mode=\"circle\" size=\"24\"></u-loading>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n    </scroll-view>\n\n  </view>\n</template>\n\n<script>\nimport { getConversations, getUnreadCount, markMessageAsRead } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'SocialMessage',\n  data() {\n    return {\n      chatList: [],\n      loading: false,\n      refreshing: false,\n      systemUnreadCount: 3,\n      likeUnreadCount: 12,\n      followUnreadCount: 5\n    }\n  },\n  onLoad() {\n    this.loadUnreadCounts()\n    this.loadChatList()\n  },\n\n  onShow() {\n    // 页面显示时检查是否需要加载数据\n    if (!this.chatList || this.chatList.length === 0) {\n      console.log('消息页显示时重新加载数据')\n      this.loadUnreadCounts()\n      this.loadChatList()\n    }\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    if (!this.chatList || this.chatList.length === 0) {\n      console.log('消息页激活时重新加载数据')\n      this.loadUnreadCounts()\n      this.loadChatList()\n    }\n  },\n  methods: {\n    // 加载未读消息统计\n    async loadUnreadCounts() {\n      try {\n        const unreadData = await getUnreadCount()\n        if (unreadData) {\n          this.systemUnreadCount = unreadData.systemCount || 0\n          this.likeUnreadCount = unreadData.likeCount || 0\n          this.followUnreadCount = unreadData.followCount || 0\n        }\n      } catch (error) {\n        console.error('加载未读消息统计失败:', error)\n        // 使用默认值\n      }\n    },\n\n    async loadChatList() {\n      this.loading = true\n      try {\n        const result = await getConversations({\n          current: 1,\n          size: 50\n        })\n\n        if (result && result.length > 0) {\n          this.chatList = result.map(conversation => ({\n            id: conversation.id,\n            userId: conversation.otherUserId,\n            name: conversation.otherUserNickname || '用户' + conversation.otherUserId,\n            avatar: conversation.otherUserAvatar || 'https://picsum.photos/100/100?random=' + conversation.otherUserId,\n            lastMessage: conversation.lastMessageContent || '',\n            lastMessageTime: new Date(conversation.lastMessageTime),\n            lastMessageType: 'text',\n            unreadCount: conversation.unreadCount || 0,\n            isOnline: false,\n            isMuted: false\n          }))\n        } else {\n          // 使用模拟数据作为后备\n          this.chatList = this.generateMockChatList()\n        }\n      } catch (error) {\n        console.error('加载聊天列表失败:', error)\n        // 使用模拟数据作为后备\n        this.chatList = this.generateMockChatList()\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n\n    generateMockChatList() {\n      return [\n        {\n          id: 1,\n          name: '小美',\n          avatar: 'https://picsum.photos/100/100?random=700',\n          lastMessage: '今天的穿搭真好看！',\n          lastMessageTime: new Date(Date.now() - 300000), // 5分钟前\n          lastMessageType: 'text',\n          unreadCount: 2,\n          isOnline: true,\n          isMuted: false\n        },\n        {\n          id: 2,\n          name: '旅行达人',\n          avatar: 'https://picsum.photos/100/100?random=701',\n          lastMessage: '分享一下这次旅行的照片',\n          lastMessageTime: new Date(Date.now() - 1800000), // 30分钟前\n          lastMessageType: 'image',\n          unreadCount: 0,\n          isOnline: false,\n          isMuted: false\n        },\n        {\n          id: 3,\n          name: '美食家',\n          avatar: 'https://picsum.photos/100/100?random=702',\n          lastMessage: '这家餐厅真的很不错',\n          lastMessageTime: new Date(Date.now() - 3600000), // 1小时前\n          lastMessageType: 'text',\n          unreadCount: 1,\n          isOnline: true,\n          isMuted: true\n        },\n        {\n          id: 4,\n          name: '摄影师',\n          avatar: 'https://picsum.photos/100/100?random=703',\n          lastMessage: '发了一段语音',\n          lastMessageTime: new Date(Date.now() - 7200000), // 2小时前\n          lastMessageType: 'voice',\n          unreadCount: 0,\n          isOnline: false,\n          isMuted: false\n        },\n        {\n          id: 5,\n          name: '健身达人',\n          avatar: 'https://picsum.photos/100/100?random=704',\n          lastMessage: '明天一起去健身房吗？',\n          lastMessageTime: new Date(Date.now() - 86400000), // 1天前\n          lastMessageType: 'text',\n          unreadCount: 0,\n          isOnline: false,\n          isMuted: false\n        }\n      ]\n    },\n\n    formatTime(time) {\n      const now = new Date()\n      const diff = now - new Date(time)\n      const minutes = Math.floor(diff / 60000)\n      const hours = Math.floor(diff / 3600000)\n      const days = Math.floor(diff / 86400000)\n      \n      if (minutes < 1) return '刚刚'\n      if (minutes < 60) return `${minutes}分钟前`\n      if (hours < 24) return `${hours}小时前`\n      if (days < 7) return `${days}天前`\n      \n      const date = new Date(time)\n      return `${date.getMonth() + 1}/${date.getDate()}`\n    },\n\n    onRefresh() {\n      this.refreshing = true\n      this.loadChatList()\n    },\n\n    async openChat(chat) {\n      try {\n        // 标记消息已读\n        if (chat.unreadCount > 0) {\n          await markMessageAsRead({\n            conversationId: chat.id,\n            userId: chat.userId\n          })\n\n          // 清除未读数\n          chat.unreadCount = 0\n\n          // 更新聊天列表\n          const index = this.chatList.findIndex(c => c.id === chat.id)\n          if (index !== -1) {\n            this.$set(this.chatList, index, { ...chat })\n          }\n        }\n\n        uni.navigateTo({\n          url: `/pagesSub/social/chat/detail?id=${chat.userId}&name=${encodeURIComponent(chat.name)}`\n        })\n      } catch (error) {\n        console.error('打开聊天失败:', error)\n        // 即使标记已读失败，也要跳转到聊天页面\n        uni.navigateTo({\n          url: `/pagesSub/social/chat/detail?id=${chat.userId}&name=${encodeURIComponent(chat.name)}`\n        })\n      }\n    },\n\n    showChatActions(chat) {\n      const actions = ['置顶', chat.isMuted ? '取消免打扰' : '免打扰', '删除聊天']\n      \n      uni.showActionSheet({\n        itemList: actions,\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0:\n              this.toggleChatTop(chat)\n              break\n            case 1:\n              this.toggleChatMute(chat)\n              break\n            case 2:\n              this.deleteChat(chat)\n              break\n          }\n        }\n      })\n    },\n\n    toggleChatTop(chat) {\n      // 置顶/取消置顶逻辑\n      this.$u.toast('置顶成功')\n    },\n\n    toggleChatMute(chat) {\n      chat.isMuted = !chat.isMuted\n      this.$u.toast(chat.isMuted ? '已开启免打扰' : '已关闭免打扰')\n    },\n\n    deleteChat(chat) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这个聊天吗？',\n        success: (res) => {\n          if (res.confirm) {\n            const index = this.chatList.findIndex(item => item.id === chat.id)\n            if (index > -1) {\n              this.chatList.splice(index, 1)\n              this.$u.toast('删除成功')\n            }\n          }\n        }\n      })\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: '/pagesSub/social/search/chat'\n      })\n    },\n\n    startNewChat() {\n      uni.navigateTo({\n        url: '/pagesSub/social/contact/select'\n      })\n    },\n\n    goSystemMessages() {\n      uni.navigateTo({\n        url: '/pagesSub/social/message/system'\n      })\n    },\n\n    goLikeMessages() {\n      uni.navigateTo({\n        url: '/pagesSub/social/message/likes'\n      })\n    },\n\n    goFollowMessages() {\n      uni.navigateTo({\n        url: '/pagesSub/social/message/followers'\n      })\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log('强制刷新消息页数据...')\n      this.chatList = []\n      this.loadUnreadCounts()\n      this.loadChatList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.message-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 100px;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 1px solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n}\n\n.title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.header-actions {\n  display: flex;\n  gap: 16px;\n}\n\n.quick-actions {\n  margin-top: calc(44px + var(--status-bar-height));\n  background: #fff;\n  padding: 16px;\n  display: flex;\n  justify-content: space-around;\n  border-bottom: 8px solid #f8f9fa;\n}\n\n.action-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n}\n\n.action-icon {\n  width: 44px;\n  height: 44px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8px;\n}\n\n.action-icon.system {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.action-icon.like {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.action-icon.follow {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.action-text {\n  font-size: 12px;\n  color: #666;\n}\n\n.unread-badge {\n  position: absolute;\n  top: -2px;\n  right: 8px;\n  background: #ff4757;\n  color: #fff;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  min-width: 16px;\n  text-align: center;\n}\n\n.chat-list {\n  background: #fff;\n}\n\n.chat-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.chat-item:last-child {\n  border-bottom: none;\n}\n\n.chat-avatar {\n  position: relative;\n  margin-right: 12px;\n}\n\n.online-dot {\n  position: absolute;\n  bottom: 2px;\n  right: 2px;\n  width: 12px;\n  height: 12px;\n  background: #52c41a;\n  border: 2px solid #fff;\n  border-radius: 6px;\n}\n\n.chat-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.chat-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 4px;\n}\n\n.chat-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.chat-time {\n  font-size: 12px;\n  color: #999;\n}\n\n.chat-preview {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.message-preview {\n  flex: 1;\n  min-width: 0;\n}\n\n.preview-text {\n  font-size: 14px;\n  color: #666;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: block;\n}\n\n.chat-status {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.unread-count {\n  background: #ff4757;\n  color: #fff;\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 10px;\n  min-width: 18px;\n  text-align: center;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80px 20px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #999;\n  margin: 16px 0 8px;\n}\n\n.empty-desc {\n  font-size: 14px;\n  color: #ccc;\n}\n\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.loading-text {\n  margin-left: 8px;\n  color: #999;\n  font-size: 14px;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752827020003\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4df458ff&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4df458ff\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4df458ff&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-empty/u-empty\" */ \"@/components/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.tabs[_vm.currentTab] && _vm.tabs[_vm.currentTab].data.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"profile-container\">\n    <!-- 蓝色渐变背景头部 -->\n    <view class=\"header-section\">\n      <view class=\"header-bg\"></view>\n\n      <!-- 顶部操作按钮 -->\n      <view class=\"header-actions\">\n        <u-icon name=\"scan\" color=\"#fff\" size=\"48rpx\" @click=\"scanCode\"></u-icon>\n        <u-icon name=\"setting\" color=\"#fff\" size=\"48rpx\" @click=\"goSettings\"></u-icon>\n      </view>\n\n      <!-- 用户信息 -->\n      <view class=\"user-info-section\">\n        <view class=\"user-avatar-container\">\n          <u-avatar :src=\"userInfo.avatar\" size=\"120\" @click=\"editAvatar\"></u-avatar>\n        </view>\n\n        <!-- 用户信息内容容器 -->\n        <view class=\"user-info-content\">\n          <view class=\"user-info-row\">\n            <!-- 左侧用户信息 -->\n            <view class=\"user-details\">\n              <text class=\"nickname\">{{ userInfo.nickname }}</text>\n              <text class=\"user-id\">ID: {{ userInfo.userId }}</text>\n              <text class=\"dance-type\">舞种：{{ userInfo.danceType || '街舞' }}</text>\n              <text class=\"bio\">{{ userInfo.bio || '美食爱好者 | 旅行达人 | 摄影 | 生活方式博主' }}</text>\n            </view>\n\n            <!-- 右侧编辑链接 -->\n            <view class=\"edit-section\">\n              <text class=\"edit-link\" @click=\"editProfile\">编辑资料</text>\n            </view>\n          </view>\n\n          <!-- 数据统计 -->\n          <view class=\"stats-row\">\n            <view class=\"stat-item\" @click=\"switchTab(0)\">\n              <text class=\"stat-number\">{{ userInfo.postCount }}</text>\n              <text class=\"stat-label\">帖子</text>\n            </view>\n            <view class=\"stat-item\" @click=\"goLikeList\">\n              <text class=\"stat-number\">{{ userInfo.likeCount }}</text>\n              <text class=\"stat-label\">获赞</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 操作按钮 -->\n      <view class=\"tabs-container\">\n        <u-tabs\n          :list=\"tabs\"\n          :current=\"currentTab\"\n          @change=\"switchTab\"\n          lineWidth=\"30\"\n          lineColor=\"#303133\"\n          :activeStyle=\"{ color: '#303133', fontWeight: 'bold' }\"\n          :inactiveStyle=\"{ color: '#606266' }\"\n        ></u-tabs>\n      </view>\n\n      <!-- 帖子列表 -->\n      <view class=\"posts-content\">\n        <view v-if=\"tabs[currentTab] && tabs[currentTab].data.length > 0\" class=\"post-grid\">\n          <PostCard\n            v-for=\"post in tabs[currentTab].data\"\n            :key=\"post.id\"\n            :post=\"post\"\n            class=\"post-card-item\"\n            @click=\"goPostDetail\"\n            @user-click=\"goUserProfile\"\n            @like=\"onPostLike\"\n          />\n        </view>\n        <view v-else class=\"empty-state\">\n          <u-empty mode=\"list\" text=\"暂无内容\"></u-empty>\n        </view>\n      </view>\n    </scroll-view>\n\n  </view>\n</template>\n\n<script>\nimport PostCard from '../components/PostCard.vue'\nimport { getUserProfile, updateUserProfile, getPostList, likePost, unlikePost } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'SocialProfile',\n  components: {\n    PostCard\n  },\n  data() {\n    return {\n      userInfo: {\n        userId: 'xiaoming_zhang',\n        nickname: '张小明',\n        avatar: '/static/images/toux.png',\n        bio: '美食爱好者 | 旅行达人 | 摄影师 | 生活方式博主',\n        danceType: '街舞',\n        postCount: 128,\n        followingCount: 256,\n        followersCount: 1024,\n        likeCount: 8547,\n        draftCount: 3\n      },\n      currentTab: 0,\n      isInitialized: false,\n      tabs: [\n        {\n          name: '作品',\n          data: [\n            {\n              id: 1,\n              title: '你喜欢什么颜色的?',\n              coverImage: 'https://picsum.photos/400/400?random=100',\n              username: '张小明',\n              userAvatar: '/static/images/toux.png',\n              content: '分享一下今天的心情',\n              likeCount: 219,\n              commentCount: 15,\n              isLiked: false,\n              createTime: new Date(Date.now() - 3600000)\n            },\n            {\n              id: 2,\n              title: '这是在哪拍的?',\n              coverImage: 'https://picsum.photos/400/400?random=102',\n              username: '张小明',\n              userAvatar: '/static/images/toux.png',\n              content: '美丽的风景',\n              likeCount: 16,\n              commentCount: 3,\n              isLiked: true,\n              createTime: new Date(Date.now() - 7200000)\n            },\n            {\n              id: 3,\n              title: '特角色',\n              coverImage: 'https://picsum.photos/400/400?random=103',\n              username: '张小明',\n              userAvatar: '/static/images/toux.png',\n              content: '今天的造型',\n              likeCount: 12,\n              commentCount: 2,\n              isLiked: false,\n              createTime: new Date(Date.now() - 10800000)\n            },\n            {\n              id: 4,\n              title: '这才是自由',\n              coverImage: 'https://picsum.photos/400/400?random=104',\n              username: '张小明',\n              userAvatar: '/static/images/toux.png',\n              content: '享受自由的感觉',\n              likeCount: 3157,\n              commentCount: 89,\n              isLiked: true,\n              createTime: new Date(Date.now() - 14400000)\n            }\n          ]\n        },\n        { name: '喜欢', data: [] },\n        { name: '收藏', data: [] }\n      ]\n    }\n  },\n  onLoad() {\n    console.log('Profile页面 onLoad')\n    // 延迟加载，确保页面完全初始化\n    this.$nextTick(() => {\n      setTimeout(() => {\n        this.initializeData()\n      }, 100)\n    })\n  },\n  onShow() {\n    console.log('Profile页面 onShow')\n    // 页面显示时刷新数据\n    if (this.isInitialized) {\n      this.loadUserInfo()\n    }\n  },\n  methods: {\n    // 初始化数据\n    async initializeData() {\n      console.log('初始化Profile页面数据...')\n      try {\n        await this.loadUserInfo()\n        this.isInitialized = true\n      } catch (error) {\n        console.error('初始化数据失败:', error)\n      }\n    },\n\n    async loadUserInfo() {\n      try {\n        // TODO: 获取当前用户ID，这里暂时使用固定值\n        const currentUserId = 1\n\n        const result = await getUserProfile(currentUserId)\n        console.log('用户信息API返回:', result)\n\n        if (result && result.code === 0 && result.data) {\n          const userProfile = result.data\n          this.userInfo = {\n            id: userProfile.userId,\n            nickname: userProfile.nickname || '舞蹈爱好者',\n            avatar: userProfile.avatar || 'https://picsum.photos/200/200?random=1',\n            bio: userProfile.bio || '热爱舞蹈，享受生活',\n            danceType: userProfile.danceType || '街舞',\n            level: userProfile.level || 5,\n            followingCount: userProfile.followingCount || 0,\n            followerCount: userProfile.followerCount || 0,\n            postCount: userProfile.postCount || 0,\n            likeCount: userProfile.likeReceivedCount || 0\n          }\n        } else {\n          console.log('用户信息API返回格式不正确，使用默认数据')\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n        // 使用默认用户信息\n        this.userInfo = {\n          id: 1,\n          nickname: '舞蹈爱好者',\n          avatar: 'https://picsum.photos/200/200?random=1',\n          bio: '热爱舞蹈，享受生活',\n          danceType: '街舞',\n          level: 5,\n          followingCount: 128,\n          followerCount: 256,\n          postCount: 42,\n          likeCount: 1024\n        }\n      }\n\n      // 加载当前标签页数据\n      this.loadTabData(this.currentTab)\n    },\n\n    async loadTabData(tabIndex) {\n      if (this.tabs[tabIndex].loading) return\n\n      this.$set(this.tabs[tabIndex], 'loading', true)\n\n      try {\n        let data = []\n\n        switch (tabIndex) {\n          case 0: // 帖子\n            data = await this.loadUserPosts()\n            break\n          case 1: // 喜欢\n            data = await this.loadLikedPosts()\n            break\n          case 2: // 收藏\n            data = await this.loadCollectedPosts()\n            break\n        }\n\n        this.$set(this.tabs[tabIndex], 'data', data)\n\n      } catch (error) {\n        console.error(`加载标签页${tabIndex}数据失败:`, error)\n        // 使用模拟数据作为后备\n        if (tabIndex > 0 && this.tabs[tabIndex].data.length === 0) {\n          const mockData = this.generateMockPostsForTab(tabIndex)\n          this.$set(this.tabs[tabIndex], 'data', mockData)\n        }\n      } finally {\n        this.$set(this.tabs[tabIndex], 'loading', false)\n      }\n    },\n\n    // 加载用户发布的帖子\n    async loadUserPosts() {\n      try {\n        const currentUserId = 1 // TODO: 获取当前用户ID\n        const result = await getPostList({\n          current: 1,\n          size: 20,\n          userId: currentUserId,\n          sortField: 'createTime',\n          sortOrder: 'desc'\n        })\n\n        console.log('用户帖子API返回:', result)\n\n        if (result && result.code === 0 && result.data && result.data.records) {\n          return result.data.records.map(post => ({\n            id: post.id,\n            title: post.title || '',\n            coverImage: (post.images && post.images[0]) || 'https://picsum.photos/400/400?random=' + post.id,\n            username: post.nickname || this.userInfo.nickname,\n            userAvatar: post.avatar || this.userInfo.avatar,\n            content: post.content,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            isLiked: post.isLiked || false,\n            createTime: new Date(post.createTime)\n          }))\n        } else {\n          console.log('用户帖子API返回格式不正确，使用模拟数据')\n          return this.generateMockPostsForTab(0)\n        }\n      } catch (error) {\n        console.error('加载用户帖子失败:', error)\n        // 返回模拟数据作为后备\n        return this.generateMockPostsForTab(0)\n      }\n    },\n\n    // 加载用户点赞的帖子\n    async loadLikedPosts() {\n      try {\n        // TODO: 实现获取用户点赞帖子的API\n        // 暂时返回模拟数据\n        return this.generateMockPostsForTab(1)\n      } catch (error) {\n        console.error('加载点赞帖子失败:', error)\n        return []\n      }\n    },\n\n    // 加载用户收藏的帖子\n    async loadCollectedPosts() {\n      try {\n        // TODO: 实现获取用户收藏帖子的API\n        // 暂时返回模拟数据\n        return this.generateMockPostsForTab(2)\n      } catch (error) {\n        console.error('加载收藏帖子失败:', error)\n        return []\n      }\n    },\n\n    generateMockPostsForTab(tabIndex) {\n      const posts = []\n      const titles = {\n        0: ['我的舞蹈作品', '今日练习', '舞蹈分享', '技巧展示'], // 作品\n        1: ['超爱的瞬间', '百看不厌', '为它点赞', '今日最佳'], // 喜欢\n        2: ['我的珍藏', '稍后再看', '深度好文', '灵感来源'] // 收藏\n      }\n      const contents = {\n        0: ['分享我的舞蹈作品', '今天的练习成果', '和大家分享舞蹈心得', '新学的舞蹈技巧'],\n        1: ['这个瞬间太美了', '真的很棒', '必须点赞', '今天最棒的内容'],\n        2: ['值得收藏', '有空再看', '写得很好', '很有启发']\n      }\n\n      for (let i = 0; i < 6; i++) {\n        const tabTitles = titles[tabIndex] || titles[0]\n        const tabContents = contents[tabIndex] || contents[0]\n\n        posts.push({\n          id: tabIndex * 10 + i,\n          title: tabTitles[i % 4],\n          coverImage: `https://picsum.photos/400/400?random=${tabIndex * 10 + i}`,\n          username: this.userInfo.nickname || '舞蹈爱好者',\n          userAvatar: this.userInfo.avatar || 'https://picsum.photos/200/200?random=1',\n          content: tabContents[i % 4],\n          likeCount: Math.floor(Math.random() * 1000),\n          commentCount: Math.floor(Math.random() * 50),\n          isLiked: Math.random() > 0.5,\n          createTime: new Date(Date.now() - Math.random() * 86400000 * 30)\n        })\n      }\n      return posts\n    },\n\n    switchTab(item) {\n      const index = typeof item === 'object' ? item.index : item;\n      if (this.currentTab === index) return\n      this.currentTab = index\n      this.loadTabData(index)\n    },\n\n    scanCode() {\n      uni.scanCode({\n        success: (res) => {\n          console.log('扫码结果:', res)\n        }\n      })\n    },\n\n    goSettings() {\n      uni.navigateTo({\n        url: '/pagesSub/social/settings/index'\n      })\n    },\n\n    editAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          // 上传头像\n          this.userInfo.avatar = res.tempFilePaths[0]\n        }\n      })\n    },\n\n    editProfile() {\n      uni.navigateTo({\n        url: '/pagesSub/social/profile/edit'\n      })\n    },\n\n    goLikeList() {\n      uni.navigateTo({\n        url: '/pagesSub/social/like/list'\n      })\n    },\n\n    viewPost(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    },\n\n    // PostCard组件需要的方法\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    },\n\n    goUserProfile(post) {\n      // 如果是自己的帖子，不需要跳转\n      if (post.username === this.userInfo.nickname) return\n\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${post.userId || post.id}`\n      })\n    },\n\n    async onPostLike(post) {\n      try {\n        if (post.isLiked) {\n          // 取消点赞\n          await unlikePost(post.id)\n          post.isLiked = false\n          post.likeCount = Math.max(0, post.likeCount - 1)\n          uni.showToast({\n            title: '取消点赞',\n            icon: 'none',\n            duration: 1000\n          })\n        } else {\n          // 点赞\n          await likePost(post.id)\n          post.isLiked = true\n          post.likeCount += 1\n          uni.showToast({\n            title: '点赞成功',\n            icon: 'success',\n            duration: 1000\n          })\n        }\n\n        // 更新对应标签页中的帖子数据\n        const currentTabData = this.tabs[this.currentTab].data\n        const index = currentTabData.findIndex(p => p.id === post.id)\n        if (index !== -1) {\n          this.$set(currentTabData, index, { ...post })\n        }\n\n      } catch (error) {\n        console.error('点赞操作失败:', error)\n        uni.showToast({\n          title: '操作失败',\n          icon: 'none'\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-container {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding-bottom: 100rpx;\n}\n\n.header-section {\n  position: relative;\n  background: #fff;\n}\n\n.header-bg {\n  height: 400rpx;\n  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);\n}\n\n.header-actions {\n  position: absolute;\n  top: 60rpx;\n  right: 32rpx;\n  display: flex;\n  gap: 32rpx;\n  z-index: 10;\n}\n\n.user-info-section {\n  padding: 40rpx 50rpx;\n  background: #f8f9fa;\n}\n\n.user-info-content {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-top: 50rpx;\n  border: 1rpx solid #e9ecef;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n.user-avatar-container {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 24rpx;\n  position: absolute;\n  top: 340rpx;\n  left: 9%;\n}\n\n.user-info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 24rpx;\n}\n\n.user-details {\n  flex: 1;\n  text-align: left;\n}\n\n.edit-section {\n  flex-shrink: 0;\n  margin-left: 20rpx;\n  display: flex;\n  align-items: flex-start;\n}\n\n.edit-link {\n  font-size: 28rpx;\n  font-weight: 500;\n  border: 1rpx solid #2979ff;\n  border-radius: 10rpx;\n  padding: 10rpx 20rpx;\n  margin: 10rpx;\n  color: #2979ff;\n}\n\n.nickname {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n.user-id {\n  font-size: 24rpx;\n  color: #999;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n.dance-type {\n  font-size: 26rpx;\n  color: #999;\n  display: block;\n  margin-bottom: 16rpx;\n  font-weight: 500;\n}\n\n.bio {\n  font-size: 28rpx;\n  color: #999;\n  line-height: 1.4;\n  display: block;\n}\n\n.stats-row {\n  display: flex;\n  justify-content: center;\n  gap: 80rpx;\n  margin-bottom: 0;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-number {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.tabs-container {\n  background: #fff;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.content {\n  background: #fff;\n  min-height: 60vh;\n}\n\n.posts-content {\n  padding: 32rpx;\n}\n\n.post-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 14rpx;\n}\n\n.post-card-item {\n  width: calc(50% - 8rpx);\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752890735627\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0c4b5bf9&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0c4b5bf9&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752827064694\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}