<template>
	<view class="myCoursexq" :style="{ '--qjbutton-color': qjbutton }" v-if="courseDetail.id">
		<view class="kcxq_video" :class="speedState ? 'qpvideo' : ''" v-if="courseDetail.videoFileList.length > 0">
			<video v-for="(item,index) in courseDetail.videoFileList" :key="index" :src="item.url" controls :id="'videoId_'+index" @fullscreenchange="handleFullScreen" @controlstoggle="handleControlstoggle">
				<!-- 倍速按钮 -->
				<cover-view v-show="controlsToggle" class="speed">
					<!-- <cover-view @click="speedNum=true" class="doubleSpeed">倍速</cover-view> -->
					<cover-view @click="downloadFile(item.url)" class="doubleSpeed">下载视频</cover-view>
					<cover-view @click="speedTap(index)" class="doubleSpeed">倍速</cover-view>
				</cover-view>
				<!-- 倍速面板 -->
				<cover-view class="speedNumBox" v-if="item.toggle">
					<cover-view class="number" @click.stop="handleSetSpeedRate(0.5,index)" :class="0.5 == speedRate ? 'activeClass' :'' ">0.5倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(0.8,index)" :class="0.8 == speedRate ? 'activeClass' :'' ">0.8倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(1,index)" :class="1 == speedRate ? 'activeClass' :'' ">1倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(1.25,index)" :class="1.25 == speedRate ? 'activeClass' :'' ">1.25倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(1.5,index)" :class="1.5 == speedRate ? 'activeClass' :'' ">1.5倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(2,index)" :class="2 == speedRate ? 'activeClass' :'' ">2倍速</cover-view>
					<cover-view class="number" @click.stop="speedTap(index)">取消</cover-view>
				</cover-view>
			</video>
		</view>
		
		<view class="kcxq_one">
			<view class="kcxq_one_a">{{courseDetail.course.name}}<text v-if="courseDetail.level_name != ''">{{courseDetail.level_name}}</text></view>
			<view class="kcxq_one_bz" v-if="courseDetail.notes != ''"><image src="/static/images/icon59.png"></image>{{courseDetail.notes}}</view>
			<view class="kcxq_one_b" v-if="courseDetail.teacher">
				<image :src="imgbaseUrl + courseDetail.teacher.image" mode="aspectFit" class="kcxq_one_b_l"></image>
				<view class="kcxq_one_b_r">
					<view class="kcxq_one_b_r_l"><view>{{courseDetail.teacher.name}}</view><text v-if="courseDetail.teacher.work_year*1 > 0">{{courseDetail.teacher.work_year}}年经验</text></view>
					<view class="kcxq_one_b_r_r" @click="navTo('/pages/index/teacherDetail?id=' + courseDetail.teacher.id)">老师详情<image src="/static/images/introduce_more.png"></image></view>
				</view>
			</view>
			<view class="kcxq_one_c" :style="courseDetail.teacher ? '' : 'margin-left:0'">
				<view>上课时间：{{courseDetail.start_time}}</view>
				<view>课程时长：{{courseDetail.duration}}分钟</view>
				<view @click="dhTap">上课地址：{{courseDetail.store.address}}<image src="/static/images/icon18.png"></image></view>
			</view>
		</view>
		
		<view class="kcxq_two" v-if="courseDetail.appointment_number*1 > 0">
			<view class="kcxq_two_xf">已预约{{courseDetail.appointment_number*1}}人<template v-if="courseDetail.waiting_number*1 > 0">、前方{{courseDetail.waiting_number*1}}人在等位</template></view>
			<view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>学生</text><text></text></view></view>
			<view class="kcxq_two_b">
				<view v-for="(item,index) in courseDetail.appointment_people" :key="index"><image :src="imgbaseUrl + item.avatar"></image><text>{{item.nickname}}</text></view>
			</view>
		</view>
		
		<view class="kcxq_two kcxq_thr" :class="speedState_tc ? 'qpvideo' : ''">
			<view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>本节回顾</text><text></text></view></view>
			<view class="kcxq_thr_b">
				<view class="kcxq_thr_b_t" v-if="courseDetail.music_link != ''">
					<view><image src="/static/images/icon37.png"></image><text>{{courseDetail.music_link}}</text></view>
				</view>
				
				<view class="videoLi" v-for="(item,index) in courseDetail.reviewVideoFileList" :key="index" >
					<image src="/static/images/bf.png" class="videoLi_bf" v-if="!item.toggleVideo"></image>
					<image :src="item.image_url" class="videoLi_bj" @click="bfTap(index,item.url)" v-if="!item.toggleVideo" mode="aspectFill"></image>
					<!-- <video v-for="(item,index) in courseDetail.reviewVideoFileList" :key="index" :src="item.url" controls :id="'videoId_tc_'+index"  @fullscreenchange="handleFullScreen_tc" @controlstoggle="handleControlstoggle_tc" style="display: block;width: 100%;position: relative;" v-if="courseDetail.review_video != ''"> -->
					<video :src="item.url" controls :id="'videoId_tc_'+index"  @fullscreenchange="handleFullScreen_tc" @controlstoggle="handleControlstoggle_tc" style="display: block;width: 100%;position: relative;" v-if="courseDetail.review_video != '' && item.toggleVideo">
						<!-- 倍速按钮 -->
						<cover-view v-show="controlsToggle_tc" class="speed">
							<!-- <cover-view @click="speedNum=true" class="doubleSpeed">倍速</cover-view> -->
							<cover-view @click="downloadFile(item.url)" class="doubleSpeed">下载视频</cover-view>
							<cover-view @click="speedTap_tc(index)" class="doubleSpeed">倍速</cover-view>
						</cover-view>
						<!-- 倍速面板 -->
						<cover-view class="speedNumBox" v-if="item.toggle">
							<cover-view class="number" @click.stop="handleSetSpeedRate_tc(0.5,index)" :class="0.5 == speedRate_tc ? 'activeClass' :'' ">0.5倍速</cover-view>
							<cover-view class="number" @click.stop="handleSetSpeedRate_tc(0.8,index)" :class="0.8 == speedRate_tc ? 'activeClass' :'' ">0.8倍速</cover-view>
							<cover-view class="number" @click.stop="handleSetSpeedRate_tc(1,index)" :class="1 == speedRate_tc ? 'activeClass' :'' ">1倍速</cover-view>
							<cover-view class="number" @click.stop="handleSetSpeedRate_tc(1.25,index)" :class="1.25 == speedRate_tc ? 'activeClass' :'' ">1.25倍速</cover-view>
							<cover-view class="number" @click.stop="handleSetSpeedRate_tc(1.5,index)" :class="1.5 == speedRate_tc ? 'activeClass' :'' ">1.5倍速</cover-view>
							<cover-view class="number" @click.stop="handleSetSpeedRate_tc(2,index)" :class="2 == speedRate_tc ? 'activeClass' :'' ">2倍速</cover-view>
							<cover-view class="number" @click.stop="speedTap_tc(index)">取消</cover-view>
						</cover-view>
					</video>
				</view>
			</view>
			<view v-if="courseDetail.music_link == '' && courseDetail.reviewVideoFileList.length == 0" style="width:100%;text-align:center;font-size:26rpx;">暂无回顾内容</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<!-- 0=未报名,1=待开课,2=授课中,3=已完成,4=等位中,5=已取消 -->
		<view class="kcxq_foo">
			<view class="kcxq_foo_l">
				<view @click="homeTap"><image src="/static/tabbar/tab_home.png"></image>首页</view>
			</view>
			<view class="kcxq_foo_r">
				
				
				<view class="back_999" v-if="courseDetail.status == 1" @click="yyToggle = true">取消预约</view>
				<view class="back_999" v-else-if="courseDetail.status == 2">授课中</view>
				<view class="back_999" v-else-if="courseDetail.status == 3">已完成</view>
				<view class="back_999" v-else-if="courseDetail.status == 4" @click="yyToggle = true">取消等位</view>
				<view class="back_999" v-else-if="courseDetail.status == 5">已取消</view>
				<!-- 从列表进入才展示 未开始预约和截止预约 -->
				<view class="back_999" v-else-if="courseDetail.status == 6">未开始预约</view>
				<view class="back_999" v-else-if="courseDetail.status == 7">截止预约</view>
				<!-- 未开启等位并且课程预约爆满 (课程预约满员时开启等位:0=关闭,1=开启)-->
				<view class="back" style="background:#BEBEBE"
					v-else-if="courseDetail.equivalent*1 == 0 && (courseDetail.appointment_number*1 >= courseDetail.maximum_reservation*1)"
					@click.stop="kqhyts">预约</view>
				<view class="back" :style="courseDetail.member == 0 ? 'background:#BEBEBE' : ''"
					v-else-if="courseDetail.member == 0" @click.stop="ljtkToggle = true">预约</view>
				<!-- 开启等位 -->
				<view class="back" v-else
					@click.stop="yypdTo(courseDetail,'/pages/Schedule/Schedulexq?id' + courseDetail.id)">
					{{courseDetail.waiting_number*1 > 0 ? '去排队' : '预约'}}</view>
				
			</view>
		</view>
		
		<!-- 取消预约弹窗 go -->
		<view class="xjTanc" v-if="yyToggle">
			<view class="xjTanc_n">
				<view class="xjTanc_a">温馨提示</view>
				<view class="xjTanc_b">是否取消预约？</view>
				<view class="xjTanc_c">
					<view @click="yyToggle = false">取消</view>
					<view class="bak" @click="qxyySubTap">确认</view>
				</view>
			</view>
		</view>
		<!-- 取消预约弹窗 end -->
		
		
		<!-- 提示预约弹窗 go -->
		<view class="yytnCon" v-if="ljtkToggle"><view class="yytnCon_n"><image :src="imgbaseUrlOss + '/userreport/icon55.png'"></image><text @click="ljktTap"></text></view><image src="/static/images/icon56.png" @click="ljtkToggle = false"></image></view>
		<!-- 提示预约弹窗 end -->
		
	</view>
</template>


<script>
import {
	myCourseXqApi,
	cancelCourseApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			navLists:['全部','等位中','待开课','授课中','已完成'],
			courseDetail:{id:0},
			kcId:0,
			imgbaseUrl:'',
			yyToggle:false,
			
			controlsToggle:false,//是否显示状态
			speedState:false,//是否进入全屏
			speedNum:false,//是否显示倍速
			speedRate:0,//当前倍数
			
			controlsToggle_tc:false,//弹窗课包目录>是否显示状态
			speedState_tc:false,//弹窗课包目录>是否进入全屏
			speedNum_tc:false,//弹窗课包目录>是否显示倍速
			speedRate_tc:0,//弹窗课包目录>当前倍数
			ljtkToggle:false,
			qjbutton:'#131315',
			type:0,
			imgbaseUrlOss:"",
			appointmentrecordid:0
		}
	},
	onShow() {
		this.imgbaseUrlOss = this.$baseUrlOss;
	},
	onLoad(option) {
		this.appointmentrecordid = option.appointmentrecordid ? option.appointmentrecordid : 0
		this.type = option.type ? option.type : 0
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.imgbaseUrl = this.$baseUrl;
		this.kcId = option.id;
		this.courseData();//课程详情
		uni.setStorageSync('schedulesx',1);
	},
	methods: {
		bfTap(index){
			console.log(index)
			this.courseDetail.reviewVideoFileList[index].toggleVideo = true
		},
		//点击倍数
		speedTap(index){
			// this.speedNum = true;
			this.courseDetail.videoFileList[index].toggle = !this.courseDetail.videoFileList[index].toggle;
		},
		//监听进入全屏 和 退出全屏
		handleFullScreen(e){
			// console.log('监听进入全屏1',e);
			// console.log('监听进入全屏2',e.detail.fullScreen);
			this.speedState = e.detail.fullScreen;
			this.speedNum = false;
			// this.courseDetail.course.video_arr[index].toggle = false;
		},
		//2.控件（播放/暂停按钮、播放进度、时间）是显示状态
		handleControlstoggle(e){
			// console.log(e.detail.show);
			this.controlsToggle = e.detail.show
		},
		//设置倍速速度
		handleSetSpeedRate(rate,index){
			 let videoContext = uni.createVideoContext("videoId_"+index);
			 videoContext.playbackRate(rate);
			 this.speedRate = rate;
			 this.speedNum = false;
			 this.courseDetail.videoFileList[index].toggle = false;
			 uni.showToast({
			 	icon: 'none',
			 	title: '已切换至' + rate + '倍数',
				duration:2000
			 });
			 
		},
		//弹窗课包目录>点击倍数
		speedTap_tc(index){
			this.speedNum_tc = true;
			this.courseDetail.reviewVideoFileList[index].toggle = !this.courseDetail.reviewVideoFileList[index].toggle
		},
		//弹窗课包目录>监听进入全屏 和 退出全屏
		handleFullScreen_tc(e){
			// console.log('监听进入全屏1',e);
			// console.log('监听进入全屏2',e.detail.fullScreen);
			this.speedState_tc = e.detail.fullScreen;
			this.speedNum_tc = false;
		},
		//弹窗课包目录>2.控件（播放/暂停按钮、播放进度、时间）是显示状态
		handleControlstoggle_tc(e){
			// console.log(e.detail.show);
			this.controlsToggle_tc = e.detail.show
		},
		//设置倍速速度
		handleSetSpeedRate_tc(rate,index){
			 let videoContext_tc = uni.createVideoContext("videoId_tc_"+index);
			 videoContext_tc.playbackRate(rate);
			 this.speedRate_tc = rate;
			 this.speedNum_tc = false;
			 uni.showToast({
			 	icon: 'none',
			 	title: '已切换至' + rate + '倍数',
				duration:2000
			 });
			 this.courseDetail.reviewVideoFileList[index].toggle = false;
		},
		//取消预约提交
		qxyySubTap(){
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			cancelCourseApi({
				id:that.courseDetail.appointment_record_id,
			}).then(res => {
				console.log('取消预约提交',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.yyToggle = false;
					uni.showToast({
						icon:'success',
						title: '取消成功',
						duration: 2000
					});
					that.courseData();//课程详情
				}else{
					that.yyToggle = false;
				}
			})
		},
		//课程详情
		courseData(){
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			myCourseXqApi({
				id:that.kcId,
				type:that.type,
				AppointmentRecordId:that.appointmentrecordid
			}).then(res => {
				console.log('课程详情1',res)
				if (res.code == 1) {
					uni.hideLoading();
					// res.data.status = 1;
					// res.data.music_link = 'www.hsdf.com'
					// res.data.course.video = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734320048014_ces.mp4'
					// res.data.review_video = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734320048014_ces.mp4'
					/*res.data.videoFileList = [
						{
							url:'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734320048014_ces.mp4',
							id:0,
						}
					]
					res.data.reviewVideoFileList = [
						{
							url:'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734320048014_ces.mp4',
							id:0,
						}
					]*/
					for(var i=0;i<res.data.videoFileList.length;i++){
						res.data.videoFileList[i].toggle = false;
					}
					for(var j=0;j<res.data.reviewVideoFileList.length;j++){
						res.data.reviewVideoFileList[j].toggle = false;
						res.data.reviewVideoFileList[j].toggleVideo = false;
					}
					that.courseDetail = res.data;
				}
			})
		},
		homeTap(){
			uni.switchTab({
				url:'/pages/index/index'
			})
		},
		//导航
		dhTap(){
			var that = this;
			uni.openLocation({
				name:that.courseDetail.store.address,
				latitude: that.courseDetail.store.latitude*1,
				longitude: that.courseDetail.store.longitude*1,
				success: function () {
					console.log('success');
				}
			});
		},
		//预约约课/排队
		yypdTo(item){
			if(!this.isLogined){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
				return false;
			}
			console.log(item,'item');
			// return false;
			// 未开启会员
			if(item.member == 0){
				this.ljtkToggle = true
				return false;
			}
			uni.navigateTo({
				url:'/pages/Schedule/confirmOrder?id=' + item.id + '&storeid=' + item.store.id
			})
		},
		//预约爆满
		kqhyts(){
			uni.showToast({
				title: '预约课程已满',
				icon: 'none',
				duration: 1000
			})
		},
		//立即开通会员
		ljktTap(){
			this.ljtkToggle = false;
			uni.switchTab({
				url:'/pages/buy/buy'
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		},
		// 下载并处理附件
		downloadFile(url) {
		  if(url == ''){
			  uni.showToast({
			  	title: '暂无可下载的附件',
			  	icon: 'none'
			  });
			  return false;
		  }
		  // 检查文件类型
		  const ext = url.split('.').pop().toLowerCase();
		  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext);
		  const isVideo = ['mp4', 'mov', 'avi', 'm4v'].includes(ext);
		
		  uni.showLoading({ title: '下载中...' });
		  var that = this;
		  // 下载文件到临时路径
		  uni.downloadFile({
		    url: url,
		    success: (res) => {
		      if (res.statusCode === 200) {
		        const tempPath = res.tempFilePath;
				that.saveVideo(tempPath);
		      }
		    },
		    fail: (err) => {
		      uni.showToast({ title: '下载失败', icon: 'none' });
		    },
		    complete: () => {
		      uni.hideLoading();
		    }
		  });
		},
		// 保存视频到相册
		saveVideo(tempPath) {
		  uni.saveVideoToPhotosAlbum({
		    filePath: tempPath,
		    success: () => {
		      uni.showToast({ title: '视频已保存到相册' });
		    },
		    fail: (err) => {
		      // 处理权限问题（同图片保存）
		    }
		  });
		},
		
	}
}
</script>

<style lang="scss">
page{padding-bottom: 0;}
</style>