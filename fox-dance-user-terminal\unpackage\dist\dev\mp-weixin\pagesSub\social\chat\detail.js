(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesSub/social/chat/detail"],{

/***/ 818:
/*!*******************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/main.js?{"page":"pagesSub%2Fsocial%2Fchat%2Fdetail"} ***!
  \*******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _detail = _interopRequireDefault(__webpack_require__(/*! ./pagesSub/social/chat/detail.vue */ 819));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_detail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 819:
/*!**********************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue ***!
  \**********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./detail.vue?vue&type=template&id=7a033bbb&scoped=true& */ 820);
/* harmony import */ var _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./detail.vue?vue&type=script&lang=js& */ 822);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true& */ 824);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 73);

var renderjs





/* normalize component */

var component = Object(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "7a033bbb",
  null,
  false,
  _detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesSub/social/chat/detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 820:
/*!*****************************************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=template&id=7a033bbb&scoped=true& ***!
  \*****************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=7a033bbb&scoped=true& */ 821);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 821:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=template&id=7a033bbb&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uAvatar: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-avatar/u-avatar */ "components/uview-ui/components/u-avatar/u-avatar").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-avatar/u-avatar.vue */ 1072))
    },
    uIcon: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-icon/u-icon */ "components/uview-ui/components/u-icon/u-icon").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-icon/u-icon.vue */ 870))
    },
    uInput: function () {
      return Promise.all(/*! import() | components/uview-ui/components/u-input/u-input */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uview-ui/components/u-input/u-input")]).then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-input/u-input.vue */ 1086))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.__map(_vm.messageList, function (message, __i0__) {
    var $orig = _vm.__get_orig(message)
    var m0 = message.showTime ? _vm.formatMessageTime(message.timestamp) : null
    return {
      $orig: $orig,
      m0: m0,
    }
  })
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 822:
/*!***********************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js& */ 823);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 823:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 83));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 85));
var _socialApi = __webpack_require__(/*! @/utils/socialApi.js */ 716);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  name: 'ChatDetail',
  data: function data() {
    return {
      chatId: '',
      chatName: '',
      otherUserAvatar: 'https://picsum.photos/100/100?random=800',
      isOnline: true,
      messageList: [],
      inputText: '',
      scrollTop: 0,
      showExtensions: false,
      showEmojis: false,
      voiceMode: false,
      isRecording: false,
      currentUser: {
        avatar: 'https://picsum.photos/100/100?random=999'
      },
      emojiList: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏'],
      // uview输入框自定义样式
      inputCustomStyle: {
        backgroundColor: 'transparent',
        fontSize: '32rpx',
        lineHeight: '1.4',
        minHeight: '40rpx',
        maxHeight: '200rpx',
        padding: '0',
        color: '#333'
      },
      // placeholder样式
      placeholderStyle: 'color: #999; font-size: 32rpx;'
    };
  },
  onLoad: function onLoad(options) {
    this.chatId = options.id;
    this.chatName = options.name || '聊天';
    this.loadMessages();
  },
  methods: {
    loadMessages: function loadMessages() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var result;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                _context.next = 3;
                return (0, _socialApi.getConversationMessages)(_this.chatId, {
                  current: 1,
                  size: 50
                });
              case 3:
                result = _context.sent;
                if (!(result && result.records)) {
                  _context.next = 10;
                  break;
                }
                _this.messageList = result.records.map(function (message, index) {
                  var prevMessage = result.records[index - 1];
                  var showTime = !prevMessage || new Date(message.createTime) - new Date(prevMessage.createTime) > 300000; // 5分钟

                  return {
                    id: message.id,
                    type: message.messageType || 'text',
                    content: message.content,
                    isMine: message.senderId === _this.getCurrentUserId(),
                    avatar: message.senderAvatar || _this.otherUserAvatar,
                    timestamp: new Date(message.createTime),
                    showTime: showTime,
                    status: message.status || 'sent'
                  };
                }).reverse(); // 消息按时间正序显示

                // 标记消息已读
                _context.next = 8;
                return _this.markAllMessagesAsRead();
              case 8:
                _context.next = 11;
                break;
              case 10:
                // 使用模拟数据作为后备
                _this.messageList = _this.generateMockMessages();
              case 11:
                _context.next = 17;
                break;
              case 13:
                _context.prev = 13;
                _context.t0 = _context["catch"](0);
                console.error('加载消息失败:', _context.t0);
                // 使用模拟数据作为后备
                _this.messageList = _this.generateMockMessages();
              case 17:
                // 滚动到底部
                _this.$nextTick(function () {
                  _this.scrollToBottom();
                });
              case 18:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 13]]);
      }))();
    },
    // 生成模拟消息数据
    generateMockMessages: function generateMockMessages() {
      return [{
        id: 1,
        type: 'text',
        content: '你好！',
        isMine: false,
        avatar: 'https://picsum.photos/100/100?random=800',
        timestamp: new Date(Date.now() - 3600000),
        showTime: true,
        status: 'read'
      }, {
        id: 2,
        type: 'text',
        content: '你好，很高兴认识你！',
        isMine: true,
        timestamp: new Date(Date.now() - 3500000),
        showTime: false,
        status: 'read'
      }, {
        id: 3,
        type: 'image',
        content: 'https://picsum.photos/200/200?random=801',
        isMine: false,
        avatar: 'https://picsum.photos/100/100?random=800',
        timestamp: new Date(Date.now() - 1800000),
        showTime: true,
        status: 'read'
      }, {
        id: 4,
        type: 'voice',
        content: '',
        duration: 3,
        isMine: true,
        timestamp: new Date(Date.now() - 300000),
        showTime: false,
        status: 'read',
        isPlaying: false
      }];
      this.scrollToBottom();
    },
    // 获取当前用户ID
    getCurrentUserId: function getCurrentUserId() {
      // TODO: 从用户状态或本地存储获取当前用户ID
      return 1;
    },
    // 标记所有消息已读
    markAllMessagesAsRead: function markAllMessagesAsRead() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return (0, _socialApi.markMessageAsRead)({
                  userId: _this2.chatId,
                  conversationId: _this2.chatId
                });
              case 3:
                _context2.next = 8;
                break;
              case 5:
                _context2.prev = 5;
                _context2.t0 = _context2["catch"](0);
                console.error('标记消息已读失败:', _context2.t0);
              case 8:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 5]]);
      }))();
    },
    formatMessageTime: function formatMessageTime(timestamp) {
      var date = new Date(timestamp);
      var now = new Date();
      var diff = now - date;
      if (diff < 86400000) {
        // 今天
        return "".concat(date.getHours().toString().padStart(2, '0'), ":").concat(date.getMinutes().toString().padStart(2, '0'));
      } else {
        return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5 ").concat(date.getHours().toString().padStart(2, '0'), ":").concat(date.getMinutes().toString().padStart(2, '0'));
      }
    },
    scrollToBottom: function scrollToBottom() {
      var _this3 = this;
      this.$nextTick(function () {
        _this3.scrollTop = 999999;
      });
    },
    sendMessage: function sendMessage() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var messageContent, tempId, message, result;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (_this4.inputText.trim()) {
                  _context3.next = 2;
                  break;
                }
                return _context3.abrupt("return");
              case 2:
                messageContent = _this4.inputText.trim();
                tempId = Date.now();
                message = {
                  id: tempId,
                  type: 'text',
                  content: messageContent,
                  isMine: true,
                  timestamp: new Date(),
                  showTime: false,
                  status: 'sending'
                };
                _this4.messageList.push(message);
                _this4.inputText = '';
                _this4.scrollToBottom();
                _context3.prev = 8;
                _context3.next = 11;
                return (0, _socialApi.sendMessage)({
                  receiverId: _this4.chatId,
                  messageType: 'text',
                  content: messageContent
                });
              case 11:
                result = _context3.sent;
                if (result) {
                  // 更新消息状态和ID
                  message.id = result.id || tempId;
                  message.status = 'sent';

                  // 模拟已读状态
                  setTimeout(function () {
                    message.status = 'read';
                  }, 1000);
                } else {
                  message.status = 'failed';
                }
                _context3.next = 20;
                break;
              case 15:
                _context3.prev = 15;
                _context3.t0 = _context3["catch"](8);
                console.error('发送消息失败:', _context3.t0);
                message.status = 'failed';
                uni.showToast({
                  title: '发送失败',
                  icon: 'none'
                });
              case 20:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[8, 15]]);
      }))();
    },
    chooseImage: function chooseImage() {
      var _this5 = this;
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: function success(res) {
          var message = {
            id: Date.now(),
            type: 'image',
            content: res.tempFilePaths[0],
            isMine: true,
            timestamp: new Date(),
            showTime: false,
            status: 'sending'
          };
          _this5.messageList.push(message);
          _this5.showExtensions = false;
          _this5.scrollToBottom();

          // 模拟上传成功
          setTimeout(function () {
            message.status = 'sent';
          }, 1000);
        }
      });
    },
    previewImage: function previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    },
    startVoiceRecord: function startVoiceRecord() {
      this.voiceMode = true;
      this.showExtensions = false;
    },
    toggleVoiceMode: function toggleVoiceMode() {
      this.voiceMode = !this.voiceMode;
    },
    startRecord: function startRecord() {
      this.isRecording = true;
      // 开始录音逻辑
    },
    stopRecord: function stopRecord() {
      if (!this.isRecording) return;
      this.isRecording = false;

      // 模拟语音消息
      var message = {
        id: Date.now(),
        type: 'voice',
        content: '',
        duration: Math.floor(Math.random() * 10) + 1,
        isMine: true,
        timestamp: new Date(),
        showTime: false,
        status: 'sending',
        isPlaying: false
      };
      this.messageList.push(message);
      this.scrollToBottom();
      setTimeout(function () {
        message.status = 'sent';
      }, 500);
    },
    cancelRecord: function cancelRecord() {
      this.isRecording = false;
    },
    playVoice: function playVoice(message) {
      // 停止其他语音播放
      this.messageList.forEach(function (msg) {
        if (msg.type === 'voice') {
          msg.isPlaying = false;
        }
      });
      message.isPlaying = true;

      // 模拟播放完成
      setTimeout(function () {
        message.isPlaying = false;
      }, message.duration * 1000);
    },
    toggleEmojis: function toggleEmojis() {
      this.showEmojis = !this.showEmojis;
      this.showExtensions = false;
    },
    toggleExtensions: function toggleExtensions() {
      this.showExtensions = !this.showExtensions;
      this.showEmojis = false;
    },
    insertEmoji: function insertEmoji(emoji) {
      this.inputText += emoji;
    },
    onInputFocus: function onInputFocus() {
      var _this6 = this;
      this.showEmojis = false;
      this.showExtensions = false;
      // 滚动到底部，确保输入框可见
      this.$nextTick(function () {
        _this6.scrollToBottom();
      });
    },
    onInputBlur: function onInputBlur() {
      // 输入框失焦时的处理
      // 可以在这里添加自动保存草稿等功能
    },
    onInputChange: function onInputChange(value) {
      // 处理输入内容变化
      this.inputText = value;
      // 如果需要，可以在这里添加输入实时验证或字数统计
    },
    goBack: function goBack() {
      uni.navigateBack();
    },
    makeCall: function makeCall() {
      uni.makePhoneCall({
        phoneNumber: '10086'
      });
    },
    showMoreActions: function showMoreActions() {
      var _this7 = this;
      uni.showActionSheet({
        itemList: ['查看资料', '清空聊天记录', '举报'],
        success: function success(res) {
          switch (res.tapIndex) {
            case 0:
              _this7.viewUserProfile();
              break;
            case 1:
              _this7.clearChatHistory();
              break;
            case 2:
              _this7.reportUser();
              break;
          }
        }
      });
    },
    // 查看用户资料
    viewUserProfile: function viewUserProfile() {
      uni.navigateTo({
        url: "/pagesSub/social/user/profile?userId=".concat(this.chatId, "&name=").concat(this.chatName)
      });
    },
    // 清空聊天记录
    clearChatHistory: function clearChatHistory() {
      var _this8 = this;
      uni.showModal({
        title: '清空聊天记录',
        content: '确定要清空与该用户的所有聊天记录吗？此操作不可恢复。',
        confirmText: '清空',
        confirmColor: '#ff4757',
        success: function success(res) {
          if (res.confirm) {
            // 清空消息列表
            _this8.messageList = [];

            // 显示成功提示
            uni.showToast({
              title: '聊天记录已清空',
              icon: 'success',
              duration: 2000
            });

            // 这里可以调用后端API清空服务器端的聊天记录
            _this8.clearChatHistoryFromServer();
          }
        }
      });
    },
    // 从服务器清空聊天记录
    clearChatHistoryFromServer: function clearChatHistoryFromServer() {
      // TODO: 调用后端API清空聊天记录
      // 示例API调用
      /*
      uni.request({
        url: '/api/chat/clear',
        method: 'POST',
        data: {
          chatId: this.chatId
        },
        success: (res) => {
          console.log('服务器聊天记录已清空')
        },
        fail: (err) => {
          console.error('清空聊天记录失败:', err)
          uni.showToast({
            title: '清空失败，请重试',
            icon: 'error'
          })
        }
      })
      */
    },
    // 举报用户
    reportUser: function reportUser() {
      var _this9 = this;
      var reportReasons = ['发送垃圾信息', '发送不当内容', '骚扰他人', '诈骗行为', '其他违规行为'];
      uni.showActionSheet({
        itemList: reportReasons,
        success: function success(res) {
          var reason = reportReasons[res.tapIndex];
          _this9.confirmReport(reason);
        }
      });
    },
    // 确认举报
    confirmReport: function confirmReport(reason) {
      var _this10 = this;
      uni.showModal({
        title: '举报用户',
        content: "\u786E\u5B9A\u8981\u4E3E\u62A5\u8BE5\u7528\u6237\"".concat(reason, "\"\u5417\uFF1F\u6211\u4EEC\u4F1A\u8BA4\u771F\u5904\u7406\u60A8\u7684\u4E3E\u62A5\u3002"),
        confirmText: '举报',
        confirmColor: '#ff4757',
        success: function success(res) {
          if (res.confirm) {
            // 提交举报
            _this10.submitReport(reason);
          }
        }
      });
    },
    // 提交举报到服务器
    submitReport: function submitReport(reason) {
      // 显示加载提示
      uni.showLoading({
        title: '提交中...'
      });

      // TODO: 调用后端API提交举报
      // 模拟API调用
      setTimeout(function () {
        uni.hideLoading();
        uni.showToast({
          title: '举报已提交',
          icon: 'success',
          duration: 2000
        });

        // 实际API调用示例
        /*
        uni.request({
          url: '/api/report/submit',
          method: 'POST',
          data: {
            reportedUserId: this.chatId,
            reportedUserName: this.chatName,
            reason: reason,
            reportType: 'chat'
          },
          success: (res) => {
            uni.hideLoading()
            uni.showToast({
              title: '举报已提交',
              icon: 'success'
            })
          },
          fail: (err) => {
            uni.hideLoading()
            console.error('举报提交失败:', err)
            uni.showToast({
              title: '提交失败，请重试',
              icon: 'error'
            })
          }
        })
        */
      }, 1000);
    },
    loadMoreMessages: function loadMoreMessages() {
      // 加载更多历史消息
    },
    resendMessage: function resendMessage(message) {
      message.status = 'sending';
      setTimeout(function () {
        message.status = 'sent';
      }, 500);
    },
    chooseLocation: function chooseLocation() {
      this.showExtensions = false;
      this.$u.toast('位置功能开发中');
    },
    chooseFile: function chooseFile() {
      this.showExtensions = false;
      this.$u.toast('文件功能开发中');
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 824:
/*!********************************************************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true& ***!
  \********************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true& */ 825);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 825:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[818,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesSub/social/chat/detail.js.map