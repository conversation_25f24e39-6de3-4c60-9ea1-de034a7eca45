{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-swiper/u-swiper.vue?93ac", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-swiper/u-swiper.vue?1b0e", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-swiper/u-swiper.vue?dacf", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-swiper/u-swiper.vue?f2ee", "uni-app:///components/uview-ui/components/u-swiper/u-swiper.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-swiper/u-swiper.vue?ee77", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-swiper/u-swiper.vue?3399"], "names": ["name", "props", "list", "type", "default", "title", "indicator", "borderRadius", "interval", "mode", "height", "indicatorPos", "effect3d", "effect3dPreviousMargin", "autoplay", "duration", "circular", "imgMode", "bgColor", "current", "titleStyle", "watch", "data", "uCurrent", "computed", "justifyContent", "titlePaddingBottom", "tmp", "el<PERSON><PERSON><PERSON>", "methods", "listClick", "change", "animationfinish"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgM;AAChM,gBAAgB,8LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClCA;AAAA;AAAA;AAAA;AAAsvB,CAAgB,usBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmD1wB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA,gBAqBA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAJ;MACAG;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;QACA;MACA;IACA;EACA;EACAiB;IACA;IACAnB;MACA;IACA;IACA;IACA;IACAiB;MACA;IACA;EACA;EACAG;IACA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACA;IACAC;;MAEA;IAAA;EAGA;AACA;AAAA,4B;;;;;;;;;;;;ACrOA;AAAA;AAAA;AAAA;AAA66C,CAAgB,kxCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-swiper/u-swiper.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-swiper.vue?vue&type=template&id=10d34c9a&scoped=true&\"\nvar renderjs\nimport script from \"./u-swiper.vue?vue&type=script&lang=js&\"\nexport * from \"./u-swiper.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-swiper.vue?vue&type=style&index=0&id=10d34c9a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"10d34c9a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-swiper/u-swiper.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-swiper.vue?vue&type=template&id=10d34c9a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s0 =\n      _vm.title && item.title\n        ? _vm.__get_style([\n            {\n              \"padding-bottom\": _vm.titlePaddingBottom,\n            },\n            _vm.titleStyle,\n          ])\n        : null\n    return {\n      $orig: $orig,\n      s0: s0,\n    }\n  })\n  var g0 = _vm.mode == \"number\" ? _vm.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-swiper.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-swiper.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-swiper-wrap\" :style=\"{\r\n\t\tborderRadius: `${borderRadius}rpx`\r\n\t}\">\r\n\t\t<swiper :current=\"elCurrent\" @change=\"change\" @animationfinish=\"animationfinish\" :interval=\"interval\" :circular=\"circular\" :duration=\"duration\" :autoplay=\"autoplay\"\r\n\t\t :previous-margin=\"effect3d ? effect3dPreviousMargin + 'rpx' : '0'\" :next-margin=\"effect3d ? effect3dPreviousMargin + 'rpx' : '0'\"\r\n\t\t :style=\"{\r\n\t\t\t\theight: height + 'rpx',\r\n\t\t\t\tbackgroundColor: bgColor\r\n\t\t\t}\">\r\n\t\t\t<swiper-item class=\"u-swiper-item\" v-for=\"(item, index) in list\" :key=\"index\">\r\n\t\t\t\t<view class=\"u-list-image-wrap\" @tap.stop.prevent=\"listClick(index)\" :class=\"[uCurrent != index ? 'u-list-scale' : '']\" :style=\"{\r\n\t\t\t\t\t\tborderRadius: `${borderRadius}rpx`,\r\n\t\t\t\t\t\ttransform: effect3d && uCurrent != index ? 'scaleY(0.9)' : 'scaleY(1)',\r\n\t\t\t\t\t\tmargin: effect3d && uCurrent != index ? '0 20rpx' : 0,\r\n\t\t\t\t\t}\">\r\n\t\t\t\t\t<image class=\"u-swiper-image\" :src=\"item[name] || item\" :mode=\"imgMode\"></image>\r\n\t\t\t\t\t<view v-if=\"title && item.title\" class=\"u-swiper-title u-line-1\" :style=\"[{\r\n\t\t\t\t\t\t\t'padding-bottom': titlePaddingBottom\r\n\t\t\t\t\t\t}, titleStyle]\">\r\n\t\t\t\t\t\t{{ item.title }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t\t<view class=\"u-swiper-indicator\" :style=\"{\r\n\t\t\t\ttop: indicatorPos == 'topLeft' || indicatorPos == 'topCenter' || indicatorPos == 'topRight' ? '12rpx' : 'auto',\r\n\t\t\t\tbottom: indicatorPos == 'bottomLeft' || indicatorPos == 'bottomCenter' || indicatorPos == 'bottomRight' ? '12rpx' : 'auto',\r\n\t\t\t\tjustifyContent: justifyContent,\r\n\t\t\t\tpadding: `0 ${effect3d ? '74rpx' : '24rpx'}`\r\n\t\t\t}\">\r\n\t\t\t<block v-if=\"mode == 'rect'\">\r\n\t\t\t\t<view class=\"u-indicator-item-rect\" :class=\"{ 'u-indicator-item-rect-active': index == uCurrent }\" v-for=\"(item, index) in list\"\r\n\t\t\t\t :key=\"index\"></view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"mode == 'dot'\">\r\n\t\t\t\t<view class=\"u-indicator-item-dot\" :class=\"{ 'u-indicator-item-dot-active': index == uCurrent }\" v-for=\"(item, index) in list\"\r\n\t\t\t\t :key=\"index\"></view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"mode == 'round'\">\r\n\t\t\t\t<view class=\"u-indicator-item-round\" :class=\"{ 'u-indicator-item-round-active': index == uCurrent }\" v-for=\"(item, index) in list\"\r\n\t\t\t\t :key=\"index\"></view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"mode == 'number'\">\r\n\t\t\t\t<view class=\"u-indicator-item-number\">{{ uCurrent + 1 }}/{{ list.length }}</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * swiper 轮播图\r\n\t * @description 该组件一般用于导航轮播，广告展示等场景,可开箱即用\r\n\t * @tutorial https://www.uviewui.com/components/swiper.html\r\n\t * @property {Array} list 轮播图数据，见官网\"基本使用\"说明\r\n\t * @property {Boolean} title 是否显示标题文字，需要配合list参数，见官网说明（默认false）\r\n\t * @property {String} mode 指示器模式，见官网说明（默认round）\r\n\t * @property {String Number} height 轮播图组件高度，单位rpx（默认250）\r\n\t * @property {String} indicator-pos 指示器的位置（默认bottomCenter）\r\n\t * @property {Boolean} effect3d 是否开启3D效果（默认false）\r\n\t * @property {Boolean} autoplay 是否自动播放（默认true）\r\n\t * @property {String Number} interval 自动轮播时间间隔，单位ms（默认2500）\r\n\t * @property {Boolean} circular 是否衔接播放，见官网说明（默认true）\r\n\t * @property {String} bg-color 背景颜色（默认#f3f4f6）\r\n\t * @property {String Number} border-radius 轮播图圆角值，单位rpx（默认8）\r\n\t * @property {Object} title-style 自定义标题样式\r\n\t * @property {String Number} effect3d-previous-margin mode = true模式的情况下，激活项与前后项之间的距离，单位rpx（默认50）\r\n\t * @property {String} img-mode 图片的裁剪模式，详见image组件裁剪模式（默认aspectFill）\r\n\t * @event {Function} click 点击轮播图时触发\r\n\t * @example <u-swiper :list=\"list\" mode=\"dot\" indicator-pos=\"bottomRight\"></u-swiper>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-swiper\",\r\n\t\tprops: {\r\n\t\t\t// 轮播图的数据,格式如：[{image: 'xxxx', title: 'xxxx'}，{image: 'yyyy', title: 'yyyy'}]，其中title字段可选\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 是否显示title标题\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 用户自定义的指示器的样式\r\n\t\t\tindicator: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 圆角值\r\n\t\t\tborderRadius: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 8\r\n\t\t\t},\r\n\t\t\t// 隔多久自动切换\r\n\t\t\tinterval: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 3000\r\n\t\t\t},\r\n\t\t\t// 指示器的模式，rect|dot|number|round\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'round'\r\n\t\t\t},\r\n\t\t\t// list的高度，单位rpx\r\n\t\t\theight: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 250\r\n\t\t\t},\r\n\t\t\t// 指示器的位置，topLeft|topCenter|topRight|bottomLeft|bottomCenter|bottomRight\r\n\t\t\tindicatorPos: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'bottomCenter'\r\n\t\t\t},\r\n\t\t\t// 是否开启缩放效果\r\n\t\t\teffect3d: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 3D模式的情况下，激活item与前后item之间的距离，单位rpx\r\n\t\t\teffect3dPreviousMargin: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 50\r\n\t\t\t},\r\n\t\t\t// 是否自动播放\r\n\t\t\tautoplay: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 自动轮播时间间隔，单位ms\r\n\t\t\tduration: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 500\r\n\t\t\t},\r\n\t\t\t// 是否衔接滑动，即到最后一张时接着滑动，是否自动切换到第一张\r\n\t\t\tcircular: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 图片的裁剪模式 \r\n\t\t\timgMode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'aspectFill'\r\n\t\t\t},\r\n\t\t\t// 从list数组中读取的图片的属性名\r\n\t\t\tname: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'image'\r\n\t\t\t},\r\n\t\t\t// 背景颜色\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#f3f4f6'\r\n\t\t\t},\r\n\t\t\t// 初始化时，默认显示第几项\r\n\t\t\tcurrent: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 标题的样式，对象形式\r\n\t\t\ttitleStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault() {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 如果外部的list发生变化，判断长度是否被修改，如果前后长度不一致，重置uCurrent值，避免溢出\r\n\t\t\tlist(nVal, oVal) {\r\n\t\t\t\tif(nVal.length !== oVal.length) this.uCurrent = 0;\r\n\t\t\t},\r\n\t\t\t// 监听外部current的变化，实时修改内部依赖于此测uCurrent值，如果更新了current，而不是更新uCurrent，\r\n\t\t\t// 就会错乱，因为指示器是依赖于uCurrent的\r\n\t\t\tcurrent(n) {\r\n\t\t\t\tthis.uCurrent = n;\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuCurrent: this.current // 当前活跃的swiper-item的index\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tjustifyContent() {\r\n\t\t\t\tif (this.indicatorPos == 'topLeft' || this.indicatorPos == 'bottomLeft') return 'flex-start';\r\n\t\t\t\tif (this.indicatorPos == 'topCenter' || this.indicatorPos == 'bottomCenter') return 'center';\r\n\t\t\t\tif (this.indicatorPos == 'topRight' || this.indicatorPos == 'bottomRight') return 'flex-end';\r\n\t\t\t},\r\n\t\t\ttitlePaddingBottom() {\r\n\t\t\t\tlet tmp = 0;\r\n\t\t\t\tif (this.mode == 'none') return '12rpx';\r\n\t\t\t\tif (['bottomLeft', 'bottomCenter', 'bottomRight'].indexOf(this.indicatorPos) >= 0 && this.mode == 'number') {\r\n\t\t\t\t\ttmp = '60rpx';\r\n\t\t\t\t} else if (['bottomLeft', 'bottomCenter', 'bottomRight'].indexOf(this.indicatorPos) >= 0 && this.mode != 'number') {\r\n\t\t\t\t\ttmp = '40rpx';\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttmp = '12rpx';\r\n\t\t\t\t}\r\n\t\t\t\treturn tmp;\r\n\t\t\t},\r\n\t\t\t// 因为uni的swiper组件的current参数只接受Number类型，这里做一个转换\r\n\t\t\telCurrent() {\r\n\t\t\t\treturn Number(this.current);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tlistClick(index) {\r\n\t\t\t\tthis.$emit('click', index);\r\n\t\t\t},\r\n\t\t\tchange(e) {\r\n\t\t\t\tlet current = e.detail.current;\r\n\t\t\t\tthis.uCurrent = current;\r\n\t\t\t\t// 发出change事件，表示当前自动切换的index，从0开始\r\n\t\t\t\tthis.$emit('change', current);\r\n\t\t\t},\r\n\t\t\t// 头条小程序不支持animationfinish事件，改由change事件\r\n\t\t\t// 暂不监听此事件，因为不再给swiper绑定uCurrent属性\r\n\t\t\tanimationfinish(e) {\r\n\t\t\t\t// #ifndef MP-TOUTIAO\r\n\t\t\t\t// this.uCurrent = e.detail.current;\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\t\r\n\t.u-swiper-wrap {\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\ttransform: translateY(0);\r\n\t}\r\n\r\n\t.u-swiper-image {\r\n\t\twidth: 100%;\r\n\t\twill-change: transform;\r\n\t\theight: 100%;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: block;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef H5 */\r\n\t\tpointer-events: none;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.u-swiper-indicator {\r\n\t\tpadding: 0 24rpx;\r\n\t\tposition: absolute;\r\n\t\t@include vue-flex;\r\n\t\twidth: 100%;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.u-indicator-item-rect {\r\n\t\twidth: 26rpx;\r\n\t\theight: 8rpx;\r\n\t\tmargin: 0 6rpx;\r\n\t\ttransition: all 0.5s;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.u-indicator-item-rect-active {\r\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\r\n\t}\r\n\r\n\t.u-indicator-item-dot {\r\n\t\twidth: 14rpx;\r\n\t\theight: 14rpx;\r\n\t\tmargin: 0 6rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\ttransition: all 0.5s;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.u-indicator-item-dot-active {\r\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\r\n\t}\r\n\r\n\t.u-indicator-item-round {\r\n\t\twidth: 14rpx;\r\n\t\theight: 14rpx;\r\n\t\tmargin: 0 6rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\ttransition: all 0.5s;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.u-indicator-item-round-active {\r\n\t\twidth: 34rpx;\r\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\r\n\t}\r\n\r\n\t.u-indicator-item-number {\r\n\t\tpadding: 6rpx 16rpx;\r\n\t\tline-height: 1;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\t\tborder-radius: 100rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t}\r\n\r\n\t.u-list-scale {\r\n\t\ttransform-origin: center center;\r\n\t}\r\n\r\n\t.u-list-image-wrap {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tflex: 1;\r\n\t\ttransition: all 0.5s;\r\n\t\toverflow: hidden;\r\n\t\tbox-sizing: content-box;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.u-swiper-title {\r\n\t\tposition: absolute;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\tfont-size: 28rpx;\r\n\t\tpadding: 12rpx 24rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t}\r\n\r\n\t.u-swiper-item {\r\n\t\t@include vue-flex;\r\n\t\toverflow: hidden;\r\n\t\talign-items: center;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-swiper.vue?vue&type=style&index=0&id=10d34c9a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-swiper.vue?vue&type=style&index=0&id=10d34c9a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752818687639\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}