{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabbar/u-tabbar.vue?7114", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabbar/u-tabbar.vue?6bdb", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabbar/u-tabbar.vue?8f07", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabbar/u-tabbar.vue?6e7f", "uni-app:///components/uview-ui/components/u-tabbar/u-tabbar.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabbar/u-tabbar.vue?40ee", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabbar/u-tabbar.vue?af5a"], "names": ["props", "show", "type", "default", "value", "bgColor", "height", "iconSize", "midButtonSize", "activeColor", "inactiveColor", "midButton", "list", "beforeSwitch", "borderTop", "hideTabBar", "data", "midButtonLeft", "pageUrl", "created", "computed", "elIconPath", "elColor", "mounted", "methods", "clickHandler", "switchTab", "uni", "url", "getOffsetRight", "getMidButtonLeft"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgM;AAChM,gBAAgB,8LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,+OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAAsvB,CAAgB,usBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCkD1wB;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;QACA;MACA;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;YACA;UACA;YACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;UACA,mGACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACAZ,gFACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAA;kBACA;kBACA;gBACA,yBAEA;cAAA;gBAAA;gBAAA;cAAA;gBACA;kBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAa;MACA;MACA;MACA;MACA;QACAC;UACAC;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACpOA;AAAA;AAAA;AAAA;AAA66C,CAAgB,kxCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-tabbar/u-tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-tabbar.vue?vue&type=template&id=627f7c73&scoped=true&\"\nvar renderjs\nimport script from \"./u-tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-tabbar.vue?vue&type=style&index=0&id=627f7c73&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"627f7c73\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-tabbar/u-tabbar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar.vue?vue&type=template&id=627f7c73&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uBadge: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-badge/u-badge\" */ \"@/components/uview-ui/components/u-badge/u-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.show ? _vm.$u.addUnit(_vm.height) : null\n  var l0 = _vm.show\n    ? _vm.__map(_vm.list, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.elIconPath(index)\n        var m1 = _vm.elColor(index)\n        var m2 =\n          item.count || item.isDot\n            ? _vm.getOffsetRight(item.count, item.isDot)\n            : null\n        var m3 = _vm.elColor(index)\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n        }\n      })\n    : null\n  var g1 = _vm.show ? _vm.$u.addUnit(_vm.height) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"show\" class=\"u-tabbar\" @touchmove.stop.prevent=\"() => {}\">\r\n\t\t<view class=\"u-tabbar__content safe-area-inset-bottom\" :style=\"{\r\n\t\t\theight: $u.addUnit(height),\r\n\t\t\tbackgroundColor: bgColor,\r\n\t\t}\" :class=\"{\r\n\t\t\t'u-border-top': borderTop\r\n\t\t}\">\r\n\t\t\t<view class=\"u-tabbar__content__item\" v-for=\"(item, index) in list\" :key=\"index\" :class=\"{\r\n\t\t\t\t'u-tabbar__content__circle': midButton &&item.midButton\r\n\t\t\t}\" @tap.stop=\"clickHandler(index)\" :style=\"{\r\n\t\t\t\tbackgroundColor: bgColor\r\n\t\t\t}\">\r\n\t\t\t\t<view :class=\"[\r\n\t\t\t\t\tmidButton && item.midButton ? 'u-tabbar__content__circle__button' : 'u-tabbar__content__item__button'\r\n\t\t\t\t]\">\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t:size=\"midButton && item.midButton ? midButtonSize : iconSize\"\r\n\t\t\t\t\t\t:name=\"elIconPath(index)\"\r\n\t\t\t\t\t\timg-mode=\"scaleToFill\"\r\n\t\t\t\t\t\t:color=\"elColor(index)\"\r\n\t\t\t\t\t\t:custom-prefix=\"item.customIcon ? 'custom-icon' : 'uicon'\"\r\n\t\t\t\t\t></u-icon>\r\n\t\t\t\t\t<u-badge :count=\"item.count\" :is-dot=\"item.isDot\"\r\n\t\t\t\t\t\tv-if=\"item.count || item.isDot\"\r\n\t\t\t\t\t\t:offset=\"[-2, getOffsetRight(item.count, item.isDot)]\"\r\n\t\t\t\t\t></u-badge>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"u-tabbar__content__item__text\" :style=\"{\r\n\t\t\t\t\tcolor: elColor(index)\r\n\t\t\t\t}\">\r\n\t\t\t\t\t<text class=\"u-line-1\">{{item.text}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"midButton\" class=\"u-tabbar__content__circle__border\" :class=\"{\r\n\t\t\t\t'u-border': borderTop,\r\n\t\t\t}\" :style=\"{\r\n\t\t\t\tbackgroundColor: bgColor,\r\n\t\t\t\tleft: midButtonLeft\r\n\t\t\t}\">\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 这里加上一个48rpx的高度,是为了增高有凸起按钮时的防塌陷高度(也即按钮凸出来部分的高度) -->\r\n\t\t<view class=\"u-fixed-placeholder safe-area-inset-bottom\" :style=\"{\r\n\t\t\t\theight: `calc(${$u.addUnit(height)} + ${midButton ? 48 : 0}rpx)`,\r\n\t\t\t}\"></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\t// 显示与否\r\n\t\t\tshow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 通过v-model绑定current值\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 整个tabbar的背景颜色\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#ffffff'\r\n\t\t\t},\r\n\t\t\t// tabbar的高度，默认50px，单位任意，如果为数值，则为rpx单位\r\n\t\t\theight: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: '50px'\r\n\t\t\t},\r\n\t\t\t// 非凸起图标的大小，单位任意，数值默认rpx\r\n\t\t\ticonSize: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 40\r\n\t\t\t},\r\n\t\t\t// 凸起的图标的大小，单位任意，数值默认rpx\r\n\t\t\tmidButtonSize: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 90\r\n\t\t\t},\r\n\t\t\t// 激活时的演示，包括字体图标，提示文字等的演示\r\n\t\t\tactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#303133'\r\n\t\t\t},\r\n\t\t\t// 未激活时的颜色\r\n\t\t\tinactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#606266'\r\n\t\t\t},\r\n\t\t\t// 是否显示中部的凸起按钮\r\n\t\t\tmidButton: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 配置参数\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 切换前的回调\r\n\t\t\tbeforeSwitch: {\r\n\t\t\t\ttype: Function,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t\t// 是否显示顶部的横线\r\n\t\t\tborderTop: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 是否隐藏原生tabbar\r\n\t\t\thideTabBar: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 由于安卓太菜了，通过css居中凸起按钮的外层元素有误差，故通过js计算将其居中\r\n\t\t\t\tmidButtonLeft: '50%',\r\n\t\t\t\tpageUrl: '', // 当前页面URL\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 是否隐藏原生tabbar\r\n\t\t\tif(this.hideTabBar) uni.hideTabBar();\r\n\t\t\t// 获取引入了u-tabbar页面的路由地址，该地址没有路径前面的\"/\"\r\n\t\t\tlet pages = getCurrentPages();\r\n\t\t\t// 页面栈中的最后一个即为项为当前页面，route属性为页面路径\r\n\t\t\tthis.pageUrl = pages[pages.length - 1].route;\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\telIconPath() {\r\n\t\t\t\treturn (index) => {\r\n\t\t\t\t\t// 历遍u-tabbar的每一项item时，判断是否传入了pagePath参数，如果传入了\r\n\t\t\t\t\t// 和data中的pageUrl参数对比，如果相等，即可判断当前的item对应当前的tabbar页面，设置高亮图标\r\n\t\t\t\t\t// 采用这个方法，可以无需使用v-model绑定的value值\r\n\t\t\t\t\tlet pagePath = this.list[index].pagePath;\r\n\t\t\t\t\t// 如果定义了pagePath属性，意味着使用系统自带tabbar方案，否则使用一个页面用几个组件模拟tabbar页面的方案\r\n\t\t\t\t\t// 这两个方案对处理tabbar item的激活与否方式不一样\r\n\t\t\t\t\tif(pagePath) {\r\n\t\t\t\t\t\tif(pagePath == this.pageUrl || pagePath == '/' + this.pageUrl) {\r\n\t\t\t\t\t\t\treturn this.list[index].selectedIconPath;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\treturn this.list[index].iconPath;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 普通方案中，索引等于v-model值时，即为激活项\r\n\t\t\t\t\t\treturn index == this.value ? this.list[index].selectedIconPath : this.list[index].iconPath\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\telColor() {\r\n\t\t\t\treturn (index) => {\r\n\t\t\t\t\t// 判断方法同理于elIconPath\r\n\t\t\t\t\tlet pagePath = this.list[index].pagePath;\r\n\t\t\t\t\tif(pagePath) {\r\n\t\t\t\t\t\tif(pagePath == this.pageUrl || pagePath == '/' + this.pageUrl) return this.activeColor;\r\n\t\t\t\t\t\telse return this.inactiveColor;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn index == this.value ? this.activeColor : this.inactiveColor;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.midButton && this.getMidButtonLeft();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync clickHandler(index) {\r\n\t\t\t\tif(this.beforeSwitch && typeof(this.beforeSwitch) === 'function') {\r\n\t\t\t\t\t// 执行回调，同时传入索引当作参数\r\n\t\t\t\t\t// 在微信，支付宝等环境(H5正常)，会导致父组件定义的customBack()函数体中的this变成子组件的this\r\n\t\t\t\t\t// 通过bind()方法，绑定父组件的this，让this.customBack()的this为父组件的上下文\r\n\t\t\t\t\tlet beforeSwitch = this.beforeSwitch.bind(this.$u.$parent.call(this))(index);\r\n\t\t\t\t\t// 判断是否返回了promise\r\n\t\t\t\t\tif (!!beforeSwitch && typeof beforeSwitch.then === 'function') {\r\n\t\t\t\t\t\tawait beforeSwitch.then(res => {\r\n\t\t\t\t\t\t\t// promise返回成功，\r\n\t\t\t\t\t\t\tthis.switchTab(index);\r\n\t\t\t\t\t\t}).catch(err => {\r\n\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else if(beforeSwitch === true) {\r\n\t\t\t\t\t\t// 如果返回true\r\n\t\t\t\t\t\tthis.switchTab(index);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.switchTab(index);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 切换tab\r\n\t\t\tswitchTab(index) {\r\n\t\t\t\t// 发出事件和修改v-model绑定的值\r\n\t\t\t\tthis.$emit('change', index);\r\n\t\t\t\t// 如果有配置pagePath属性，使用uni.switchTab进行跳转\r\n\t\t\t\tif(this.list[index].pagePath) {\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl: this.list[index].pagePath\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果配置了papgePath属性，将不会双向绑定v-model传入的value值\r\n\t\t\t\t\t// 因为这个模式下，不再需要v-model绑定的value值了，而是通过getCurrentPages()适配\r\n\t\t\t\t\tthis.$emit('input', index);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 计算角标的right值\r\n\t\t\tgetOffsetRight(count, isDot) {\r\n\t\t\t\t// 点类型，count大于9(两位数)，分别设置不同的right值，避免位置太挤\r\n\t\t\t\tif(isDot) {\r\n\t\t\t\t\treturn -20;\r\n\t\t\t\t} else if(count > 9) {\r\n\t\t\t\t\treturn -40;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn -30;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 获取凸起按钮外层元素的left值，让其水平居中\r\n\t\t\tgetMidButtonLeft() {\r\n\t\t\t\tlet windowWidth = this.$u.sys().windowWidth;\r\n\t\t\t\t// 由于安卓中css计算left: 50%的结果不准确，故用js计算\r\n\t\t\t\tthis.midButtonLeft = (windowWidth / 2) + 'px';\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\t.u-fixed-placeholder {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: content-box;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.u-tabbar {\r\n\r\n\t\t&__content {\r\n\t\t\t@include vue-flex;\r\n\t\t\talign-items: center;\r\n\t\t\tposition: relative;\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\tz-index: 998;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tbox-sizing: content-box;\r\n\t\t\t/* #endif */\r\n\r\n\t\t\t&__circle__border {\r\n\t\t\t\tborder-radius: 100%;\r\n\t\t\t\twidth: 110rpx;\r\n\t\t\t\theight: 110rpx;\r\n\t\t\t\ttop: -48rpx;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: 4;\r\n\t\t\t\tbackground-color: #ffffff;\r\n\t\t\t\t// 由于安卓的无能，导致只有3个tabbar item时，此css计算方式有误差\r\n\t\t\t\t// 故使用js计算的形式来定位，此处不注释，是因为js计算有延后，避免出现位置闪动\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\ttransform: translateX(-50%);\r\n\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tborder-radius: 100px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&__item {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tpadding: 12rpx 0;\r\n\t\t\t\t@include vue-flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t&__button {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 14rpx;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&__text {\r\n\t\t\t\t\tcolor: $u-content-color;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tline-height: 28rpx;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 14rpx;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&__circle {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t@include vue-flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tz-index: 10;\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\theight: calc(100% - 1px);\r\n\t\t\t\t/* #endif */\r\n\r\n\t\t\t\t&__button {\r\n\t\t\t\t\twidth: 90rpx;\r\n\t\t\t\t\theight: 90rpx;\r\n\t\t\t\t\tborder-radius: 100%;\r\n\t\t\t\t\t@include vue-flex;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbackground-color: #ffffff;\r\n\t\t\t\t\ttop: -40rpx;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\tz-index: 6;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar.vue?vue&type=style&index=0&id=627f7c73&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar.vue?vue&type=style&index=0&id=627f7c73&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752818687454\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}