{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/lessonPackage/lessonPackagexq.vue?d6b3", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/lessonPackage/lessonPackagexq.vue?fd58", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/lessonPackage/lessonPackagexq.vue?47e7", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/lessonPackage/lessonPackagexq.vue?532c", "uni-app:///pages/mine/lessonPackage/lessonPackagexq.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/lessonPackage/lessonPackagexq.vue?a8b2", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/lessonPackage/lessonPackagexq.vue?8198"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "isH5", "type", "date_sj", "kcjsDetail", "kmLists", "coursePackageInfo", "id", "imgbaseUrl", "pageId", "videoToggle", "videoItem", "video", "jlIndex", "jlIndexerj", "currentTime", "controlsToggle", "speedState", "speedNum", "speedRate", "controlsToggle_tc", "speedState_tc", "speedNum_tc", "speedRate_tc", "created", "onShow", "onLoad", "onHide", "console", "onUnload", "methods", "speedTap", "handleFullScreen", "handleControlstoggle", "handleSetSpeedRate", "videoContext", "uni", "icon", "title", "duration", "speedTap_tc", "handleFullScreen_tc", "handleControlstoggle_tc", "handleSetSpeedRate_tc", "videoContext_tc", "gbVideoTap", "timeupdateVideo", "playVideo", "videoTap", "viewVideo", "viewing_status", "coursePackageData", "res", "that", "mlTap", "tabTap", "bindDateChange_sj", "userData", "userDetailApi", "navTo", "url", "setTimeout", "formatRichText", "match", "newContent"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACc;;;AAG5E;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,8sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC+GlwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;MACAC;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;AACA;EALA,CAMA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAD;IACA;MACA;IACA;EACA;;EACAE;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACAC;MACA;MACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACAC;MACA;MACA;MACAR;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAM;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACAnB;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAoB;MACApB;MACA;MACA;MACA;MACA;MACA;MACA;AACA;AACA;AACA;IACA;IACA;IACAqB;MACA;MACA;QAAA1C;QAAA2C;QAAAX;MAAA;QACAX;QACA,oBACA;MACA;IACA;IACA;IACAuB;MACAf;QACAE;MACA;MACA;MACA;QAAA/B;MAAA;QACAqB;QACA;UACA;AACA;AACA;AACA;AACA;UACA;UACA;UACA;;UAEA;YACAwB;UACA;UAEA;YACAA;YACA;cACA;gBACAA;cACA;YACA;UACA;UACA;YACAA;UACA;UACAC;UACAA;UACAjB;QACA;MACA;IACA;IACAkB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACArB;QACAE;MACA;MACA;MACAoB;QACA;UACA9B;UACAQ;QACA;MACA;IACA;IACAuB;MACA;QACAvB;UACAwB;QACA;QACA;MACA;MACA;MACA;QACAxB;UACAC;UACAC;QACA;QACAuB;UACAzB;YACAwB;UACA;QACA;MACA;QACAxB;UACAwB;QACA;MACA;IACA;IACAE;MACA;MACA;QACAC;QACAA;QACAA;QACA;MACA;MACA;MACAC;QACAD;QACA;MACA;MACA;MACAC;MACA;MACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3XA;AAAA;AAAA;AAAA;AAAi2C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAr3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/lessonPackage/lessonPackagexq.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/lessonPackage/lessonPackagexq.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./lessonPackagexq.vue?vue&type=template&id=f641207a&\"\nvar renderjs\nimport script from \"./lessonPackagexq.vue?vue&type=script&lang=js&\"\nexport * from \"./lessonPackagexq.vue?vue&type=script&lang=js&\"\nimport style0 from \"./lessonPackagexq.vue?vue&type=style&index=0&lang=less&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/lessonPackage/lessonPackagexq.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lessonPackagexq.vue?vue&type=template&id=f641207a&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 =\n    _vm.coursePackageInfo.id && _vm.type == 1\n      ? _vm.__map(_vm.kmLists, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var l0 = item.toggle\n            ? _vm.__map(item.catalog, function (itemerj, indexerj) {\n                var $orig = _vm.__get_orig(itemerj)\n                var g0 = item.catalog.length\n                return {\n                  $orig: $orig,\n                  g0: g0,\n                }\n              })\n            : null\n          var g1 = item.toggle ? item.catalog.length : null\n          return {\n            $orig: $orig,\n            l0: l0,\n            g1: g1,\n          }\n        })\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.speedNum = false\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      _vm.speedNum_tc = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lessonPackagexq.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lessonPackagexq.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"lessonPackagexq\" v-if=\"coursePackageInfo.id\">\r\n\t\t\r\n\t\t<view class=\"kcxq_video\" :class=\"speedState ? 'qpvideo' : ''\" v-if=\"coursePackageInfo.introduce_video\">\r\n\t\t\t<video :src=\"coursePackageInfo.isoss ? coursePackageInfo.introduce_video : imgbaseUrl + coursePackageInfo.introduce_video\" controls id=\"videoId\" @fullscreenchange=\"handleFullScreen\" @controlstoggle=\"handleControlstoggle\">\r\n\t\t\t\t\r\n\t\t\t\t<!-- 倍速按钮 -->\r\n\t\t\t\t<cover-view v-show=\"controlsToggle\" class=\"speed\">\r\n\t\t\t\t\t<!-- <cover-view @click=\"speedNum=true\" class=\"doubleSpeed\">倍速</cover-view> -->\r\n\t\t\t\t\t<cover-view @click=\"speedTap\" class=\"doubleSpeed\">倍速</cover-view>\r\n\t\t\t\t</cover-view>\r\n\t\t\t\t<!-- 倍速面板 -->\r\n\t\t\t\t<cover-view class=\"speedNumBox\" v-if=\"speedNum\">\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(0.5)\" :class=\"0.5 == speedRate ? 'activeClass' :'' \">0.5倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(0.8)\" :class=\"0.8 == speedRate ? 'activeClass' :'' \">0.8倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(1)\" :class=\"1 == speedRate ? 'activeClass' :'' \">1倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(1.25)\" :class=\"1.25 == speedRate ? 'activeClass' :'' \">1.25倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(1.5)\" :class=\"1.5 == speedRate ? 'activeClass' :'' \">1.5倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(2)\" :class=\"2 == speedRate ? 'activeClass' :'' \">2倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"speedNum = false\">取消</cover-view>\r\n\t\t\t\t</cover-view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t<!-- 0.5/0.8/1.0/1.25/1.5/2 -->\r\n\t\t\t</video>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"ord_nav kcxq_tab\">\r\n\t\t\t<view class=\"ord_nav_li\" :class=\"type == 0 ? 'ord_nav_li_ac' : ''\" @click=\"tabTap(0)\"><view><text>介绍</text><text></text></view></view>\r\n\t\t\t<view class=\"ord_nav_li\" :class=\"type == 1 ? 'ord_nav_li_ac' : ''\" @click=\"tabTap(1)\"><view><text>目录</text><text></text></view></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"kbxq_one\" style=\"margin-top:0;\" v-if=\"type == 0\">\r\n\t\t\t<view class=\"kcxq_two_t\"><view class=\"kcxq_two_t_n\"><text>课程介绍</text><text></text></view></view>\r\n\t\t\t<view class=\"kbxq_one_b\">\r\n\t\t\t\t<!-- <rich-text :nodes=\"kcjsDetail\"></rich-text> -->\r\n\t\t\t\t{{coursePackageInfo.introduce}}\r\n\t\t\t</view>\r\n\t\t\t<view class=\"kbxq_one_c\"><image v-for=\"(item,index) in coursePackageInfo.images\" :key=\"index\" :src=\"imgbaseUrl + item\" mode=\"widthFix\"></image></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"kbxq_one\" v-if=\"type == 0\">\r\n\t\t\t<view class=\"kcxq_two_t\"><view class=\"kcxq_two_t_n\"><text>适用人群</text><text></text></view></view>\r\n\t\t\t<view class=\"kbxq_one_b\">\r\n\t\t\t\t<!-- <rich-text :nodes=\"kcjsDetail\"></rich-text> -->\r\n\t\t\t\t{{coursePackageInfo.apply_crowd}}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"kbxq_one\" v-if=\"type == 0\">\r\n\t\t\t<view class=\"kcxq_two_t\"><view class=\"kcxq_two_t_n\"><text>讲师详情</text><text></text></view></view>\r\n\t\t\t<view class=\"kcxq_one_b\">\r\n\t\t\t\t<image :src=\"imgbaseUrl + coursePackageInfo.teacher.image\" class=\"kcxq_one_b_l\"></image>\r\n\t\t\t\t<view class=\"kcxq_one_b_r\">\r\n\t\t\t\t\t<view class=\"kcxq_one_b_r_l\"><view>{{coursePackageInfo.teacher.name}}</view><text>擅长舞种：{{coursePackageInfo.danceTable.name}}</text></view>\r\n\t\t\t\t\t<view class=\"kcxq_one_b_r_r\" @click=\"navTo('/pages/buy/coursePackage/teacherDetails?id=' + coursePackageInfo.teacher.id,'1')\">讲师详情<image src=\"/static/images/introduce_more.png\"></image></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- <view class=\"kbxq_ml\" v-if=\"type == 1\">\r\n\t\t\t<view class=\"kbxq_ml_li\" v-for=\"(item,index) in kmLists\" :key=\"index\">\r\n\t\t\t\t<view class=\"kbxq_ml_li_t\" @click=\"mlTap(index)\"><view>{{index+1}}.{{item.name}}</view><text :style=\"item.toggle ? 'border-bottom: 14rpx solid #999;border-top:none' : ''\"></text></view>\r\n\t\t\t\t<view class=\"kbxq_ml_li_b\" v-if=\"item.toggle\">\r\n\t\t\t\t\t<view class=\"kbxq_ml_li_b_li\" v-for=\"(itemerj,indexerj) in item.lists\" :key=\"indexerj\"><view>{{itemerj.name}}</view><text>已看</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<view class=\"kbxq_ml\" v-if=\"type == 1\">\r\n\t\t\t<view class=\"kbxq_ml_li\" v-for=\"(item,index) in kmLists\" :key=\"index\">\r\n\t\t\t\t<view class=\"kbxq_ml_li_t\" @click=\"mlTap(index)\">\r\n\t\t\t\t\t<view>{{index+1}}.{{item.name}}</view><text :style=\"item.toggle ? 'border-bottom: 14rpx solid #999;border-top:none' : ''\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"kbxq_ml_li_b\" v-if=\"item.toggle\">\r\n\t\t\t\t\t<view class=\"kbxq_ml_li_b_li\" v-for=\"(itemerj,indexerj) in item.catalog\" :key=\"indexerj\" v-if=\"item.catalog.length > 0\" @click=\"videoTap(itemerj,index,indexerj)\">\r\n\t\t\t\t\t\t<view>{{itemerj.name}}</view>\r\n\t\t\t\t\t\t<text :style=\"itemerj.view ? 'color:#999999' : ''\">{{itemerj.viewing_status == 1 ? '已看' : '未看'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"width: 100%;text-align:center;font-size: 26rpx;margin-top:30rpx;\" v-if=\"item.catalog.length == 0\">暂无目录</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"video_tanc\" :class=\"speedState_tc ? 'qpvideo' : ''\" v-if=\"videoToggle\">\r\n\t\t\t<video :src=\"videoItem.isoss ? videoItem.video : imgbaseUrl + videoItem.video\" controls id=\"videoId_tc\" @play=\"playVideo\" @timeupdate=\"timeupdateVideo\" @ended=\"playVideo\" @pause=\"playVideo\" @fullscreenchange=\"handleFullScreen_tc\" @controlstoggle=\"handleControlstoggle_tc\">\r\n\t\t\t\t<!-- 倍速按钮 -->\r\n\t\t\t\t<cover-view v-show=\"controlsToggle_tc\" class=\"speed\">\r\n\t\t\t\t\t<!-- <cover-view @click=\"speedNum=true\" class=\"doubleSpeed\">倍速</cover-view> -->\r\n\t\t\t\t\t<cover-view @click=\"speedTap_tc\" class=\"doubleSpeed\">倍速</cover-view>\r\n\t\t\t\t</cover-view>\r\n\t\t\t\t<!-- 倍速面板 -->\r\n\t\t\t\t<cover-view class=\"speedNumBox\" v-if=\"speedNum_tc\">\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(0.5)\" :class=\"0.5 == speedRate_tc ? 'activeClass' :'' \">0.5倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(0.8)\" :class=\"0.8 == speedRate_tc ? 'activeClass' :'' \">0.8倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(1)\" :class=\"1 == speedRate_tc ? 'activeClass' :'' \">1倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(1.25)\" :class=\"1.25 == speedRate_tc ? 'activeClass' :'' \">1.25倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(1.5)\" :class=\"1.5 == speedRate_tc ? 'activeClass' :'' \">1.5倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(2)\" :class=\"2 == speedRate_tc ? 'activeClass' :'' \">2倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"speedNum_tc = false\">取消</cover-view>\r\n\t\t\t\t</cover-view>\r\n\t\t\t</video>\r\n\t\t\t<image src=\"/static/images/icon56.png\" @click=\"gbVideoTap\"></image>\r\n\t\t</view>\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tmyPackagexqApi,\r\n\tvideoViewApi\r\n} from '@/config/http.achieve.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:false,//是否登录\r\n\t\t\tisH5:false,//是否是h5\r\n\t\t\ttype:0,\r\n\t\t\tdate_sj: '请选择',\r\n\t\t\tkcjsDetail:'<p style=\"color:#fff;background:red;font-size:14px;\">富文本内容</p>',\r\n\t\t\tkmLists:[],\r\n\t\t\tcoursePackageInfo:{id:0},\r\n\t\t\timgbaseUrl:\"\",\r\n\t\t\tpageId:0,\r\n\t\t\tvideoToggle:false,\r\n\t\t\tvideoItem:{video:''},\r\n\t\t\tjlIndex:0,\r\n\t\t\tjlIndexerj:0,\r\n\t\t\tcurrentTime: 0, // 记录实际观看的时长（秒）\r\n\t\t\t\r\n\t\t\tcontrolsToggle:false,//是否显示状态\r\n\t\t\tspeedState:false,//是否进入全屏\r\n\t\t\tspeedNum:false,//是否显示倍速\r\n\t\t\tspeedRate:0,//当前倍数\r\n\t\t\t\r\n\t\t\tcontrolsToggle_tc:false,//弹窗课包目录>是否显示状态\r\n\t\t\tspeedState_tc:false,//弹窗课包目录>是否进入全屏\r\n\t\t\tspeedNum_tc:false,//弹窗课包目录>是否显示倍速\r\n\t\t\tspeedRate_tc:0,//弹窗课包目录>当前倍数\r\n\t\t}\r\n\t},\r\n\tcreated(){\r\n\t\t/*uni.getSystemInfo({\r\n\t\t\tsuccess: function(res) {\r\n\t\t\t\tthis.videoWidth = res.screenWidth - 16;\r\n\t\t\t\tthis.clientHeight = res.screenHeight;\r\n\t\t\t}\r\n\t\t});*/\r\n\t},\r\n\tonShow() {\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t\tthis.coursePackageData();//课包详情\r\n\t},\r\n\tonLoad(options) {\r\n\t\t// this.userData();//个人信息\r\n\t\tthis.pageId = options.id\r\n\t},\r\n\tonHide(){\r\n\t\tconsole.log('onHide')\r\n\t},\r\n\tonUnload(){\r\n\t\tconsole.log('onUnload');\r\n\t\tif(this.currentTime > 0){\r\n\t\t\tthis.viewVideo();//视频观看\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t//点击倍数\r\n\t\tspeedTap(){\r\n\t\t\tthis.speedNum = true;\r\n\t\t},\r\n\t\t//监听进入全屏 和 退出全屏\r\n\t\thandleFullScreen(e){\r\n\t\t\t// console.log('监听进入全屏1',e);\r\n\t\t\t// console.log('监听进入全屏2',e.detail.fullScreen);\r\n\t\t\tthis.speedState = e.detail.fullScreen;\r\n\t\t\tthis.speedNum = false;\r\n\t\t},\r\n\t\t//2.控件（播放/暂停按钮、播放进度、时间）是显示状态\r\n\t\thandleControlstoggle(e){\r\n\t\t\t// console.log(e.detail.show);\r\n\t\t\tthis.controlsToggle = e.detail.show\r\n\t\t},\r\n\t\t//设置倍速速度\r\n\t\thandleSetSpeedRate(rate){\r\n\t\t\t let videoContext = uni.createVideoContext(\"videoId\");\r\n\t\t\t videoContext.playbackRate(rate);\r\n\t\t\t this.speedRate = rate;\r\n\t\t\t this.speedNum = false;\r\n\t\t\t uni.showToast({\r\n\t\t\t \ticon: 'none',\r\n\t\t\t \ttitle: '已切换至' + rate + '倍数',\r\n\t\t\t\tduration:2000\r\n\t\t\t });\r\n\t\t},\r\n\t\t//弹窗课包目录>点击倍数\r\n\t\tspeedTap_tc(){\r\n\t\t\tthis.speedNum_tc = true;\r\n\t\t},\r\n\t\t//弹窗课包目录>监听进入全屏 和 退出全屏\r\n\t\thandleFullScreen_tc(e){\r\n\t\t\t// console.log('监听进入全屏1',e);\r\n\t\t\t// console.log('监听进入全屏2',e.detail.fullScreen);\r\n\t\t\tthis.speedState_tc = e.detail.fullScreen;\r\n\t\t\tthis.speedNum_tc = false;\r\n\t\t},\r\n\t\t//弹窗课包目录>2.控件（播放/暂停按钮、播放进度、时间）是显示状态\r\n\t\thandleControlstoggle_tc(e){\r\n\t\t\t// console.log(e.detail.show);\r\n\t\t\tthis.controlsToggle_tc = e.detail.show\r\n\t\t},\r\n\t\t//设置倍速速度\r\n\t\thandleSetSpeedRate_tc(rate){\r\n\t\t\t let videoContext_tc = uni.createVideoContext(\"videoId_tc\");\r\n\t\t\t videoContext_tc.playbackRate(rate);\r\n\t\t\t this.speedRate_tc = rate;\r\n\t\t\t this.speedNum_tc = false;\r\n\t\t\t uni.showToast({\r\n\t\t\t \ticon: 'none',\r\n\t\t\t \ttitle: '已切换至' + rate + '倍数',\r\n\t\t\t\tduration:2000\r\n\t\t\t });\r\n\t\t},\r\n\t\t\r\n\t\t//关闭视频弹窗\r\n\t\tgbVideoTap(){\r\n\t\t\tthis.videoToggle = false;\r\n\t\t\tif(this.currentTime*1 > 0){\r\n\t\t\t\tthis.playVideo();\r\n\t\t\t}\r\n\t\t\tthis.currentTime = 0;\r\n\t\t},\r\n\t\t//检测播放时长\r\n\t\ttimeupdateVideo(e){\r\n\t\t\tthis.currentTime += 0.25\r\n\t\t\tthis.currentTime = this.currentTime+=0.25\r\n\t\t\t// console.log((this.currentTime/1.5).toFixed(2)*1,'33this.currentTime哈哈')\r\n\t\t},\r\n\t\t//检测视频是否开始播放\r\n\t\tplayVideo(e){\r\n\t\t\tconsole.log(e,'66')\r\n\t\t\tif(this.videoItem.viewing_status*1 == 0){}\r\n\t\t\tthis.kmLists[this.jlIndex].catalog[this.jlIndexerj].viewing_status = 1\r\n\t\t\tif(this.currentTime*1 > 0){\r\n\t\t\t\tthis.viewVideo();//视频观看\r\n\t\t\t}\r\n\t\t},\r\n\t\t//视频点击播放\r\n\t\tvideoTap(item,index,indexerj){\r\n\t\t\tconsole.log(item,'item')\r\n\t\t\tthis.videoToggle = true;\r\n\t\t\tthis.videoItem = item;\r\n\t\t\tthis.currentTime = 0;\r\n\t\t\tthis.jlIndex = index;\r\n\t\t\tthis.jlIndexerj = indexerj;\r\n\t\t\t/*if(item.viewing_status*1 == 0){\r\n\t\t\t\tthis.kmLists[index].catalog[indexerj].viewing_status = 1\r\n\t\t\t\tthis.viewVideo();//视频观看\r\n\t\t\t}*/\r\n\t\t},\r\n\t\t//视频观看\r\n\t\tviewVideo(){\r\n\t\t\tlet that = this;\r\n\t\t\tvideoViewApi({id:that.videoItem.id,viewing_status:1,duration:(that.currentTime/1.5).toFixed(2)*1}).then(res => {\r\n\t\t\t\tconsole.log('视频观看告诉后台',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//课包详情\r\n\t\tcoursePackageData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tmyPackagexqApi({id:that.pageId}).then(res => {\r\n\t\t\t\tconsole.log('课包详情',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t/*res.data.catalog[0].catalog[0] = {\r\n\t\t\t\t\t\t\"id\": 3, //目录\r\n\t\t\t\t\t\t\"name\": \"街舞的由来\", //目录名称\r\n\t\t\t\t\t\t\"video\": \"https://danceadmin.xinzhiyukeji.cn/storage/default/20241025/video(2)e4e0dfbba9b0b7a5e7c9e6319de310a431b24b95.mp4\" //视频\r\n\t\t\t\t\t}*/\r\n\t\t\t\t\t//res.data.catalog[0].catalog[0].video = 'https://www.runoob.com/try/demo_source/movie.mp4';\r\n\t\t\t\t\t//res.data.catalog[0].catalog[1].video = 'https://danceadmin.xinzhiyukeji.cn/storage/default/20241025/video(2)e4e0dfbba9b0b7a5e7c9e6319de310a431b24b95.mp4';\r\n\t\t\t\t\t// res.data.introduce_video = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734312077132_ces.mp4'\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(res.data.introduce_video){\r\n\t\t\t\t\t\tres.data.isoss = res.data.introduce_video.substring(0,5) == 'https' ? true : false\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tfor(var i=0;i<res.data.catalog.length;i++){\r\n\t\t\t\t\t\tres.data.catalog[i].toggle = false;\r\n\t\t\t\t\t\tfor(var j=0;j<res.data.catalog[i].catalog.length;j++){\r\n\t\t\t\t\t\t\tif(res.data.catalog[i].catalog[j].video){\r\n\t\t\t\t\t\t\t\tres.data.catalog[i].catalog[j].isoss = res.data.catalog[i].catalog[j].video.substring(0,5) == 'https' ? true : false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(res.data.catalog.length > 0){\r\n\t\t\t\t\t\tres.data.catalog[0].toggle = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.coursePackageInfo = res.data;\r\n\t\t\t\t\tthat.kmLists = res.data.catalog\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tmlTap(index){\r\n\t\t\tthis.kmLists[index].toggle = !this.kmLists[index].toggle\r\n\t\t},\r\n\t\ttabTap(index){\r\n\t\t\tthis.type = index;\r\n\t\t},\r\n\t\tbindDateChange_sj: function(e) {\r\n\t\t\tthis.date_sj = e.detail.value\r\n\t\t},\r\n\t\t//个人信息\r\n\t\tuserData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tuserDetailApi().then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tconsole.log('个人信息',res);\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tnavTo(url,ismd){\r\n\t\t\tif(ismd){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:url\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tif(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t}else{\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:url\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tformatRichText (html) {\r\n\t\t\t// 去掉img标签里的style、width、height属性\r\n\t\t\tlet newContent= html.replace(/<img[^>]*>/gi,function(match,capture){\r\n\t\t\t\tmatch = match.replace(/style=\"[^\"]+\"/gi, '').replace(/style='[^']+'/gi, '');\r\n\t\t\t\tmatch = match.replace(/width=\"[^\"]+\"/gi, '').replace(/width='[^']+'/gi, '');\r\n\t\t\t\tmatch = match.replace(/height=\"[^\"]+\"/gi, '').replace(/height='[^']+'/gi, '');\r\n\t\t\t\treturn match;\r\n\t\t\t});\r\n\t\t\t// 修改所有style里的width属性为max-width:100%\r\n\t\t\tnewContent = newContent.replace(/style=\"[^\"]+\"/gi,function(match,capture){\r\n\t\t\t\tmatch = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi, 'max-width:100%;');\r\n\t\t\t\treturn match;\r\n\t\t\t});\r\n\t\t\t// 去掉<br/>标签\r\n\t\t\tnewContent = newContent.replace(/<br[^>]*\\/>/gi, '');\r\n\t\t\t// img标签添加style属性：max-width:100%;height:auto\r\n\t\t\tnewContent = newContent.replace(/\\<img/gi, '<img style=\"max-width:100%;height:auto;display:block;margin:0px auto;\"');\r\n\t\t\treturn newContent;\r\n\t\t}\r\n\t},\r\n}\r\n</script>\r\n\r\n<style lang=\"less\">\r\npage{padding-bottom:0;background:#fff;}\r\n.lessonPackagexq{\r\n\toverflow:hidden;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lessonPackagexq.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lessonPackagexq.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752818685357\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}