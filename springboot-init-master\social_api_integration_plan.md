# 社交模块前后端对接计划

## 概述

本文档详细描述了社交模块前后端对接的计划和步骤。前端已完成基本页面开发，后端已有数据库设计和部分接口实现。本计划将确保前后端顺利对接，实现完整的社交功能。

## 数据库分析与更新

### 当前数据库状况

后端已设计了完善的数据库结构，包括：
- 帖子表（posts）
- 用户关系表（user_follows）
- 帖子互动表（post_likes, post_favorites, post_shares）
- 标签和话题表（tags, post_tags）
- 消息通知表（notifications）
- 私信系统表（private_conversations, private_messages）

### 需要更新的数据库表

1. **ba_user表扩展**
   - 需要添加 `bio` (个人简介) 字段
   - 需要添加 `dance_type` (舞种) 字段
   - 当前表缺少这些前端展示所需的字段

2. **user_stats表**
   - 需确保包含前端展示所需的统计数据字段

## API接口分析

### 已有可用接口

根据后端设计，以下接口可以直接使用：

1. **帖子相关**
   - 帖子发布接口
   - 帖子列表查询接口
   - 帖子详情查询接口

2. **用户关系**
   - 关注/取消关注接口
   - 获取关注列表/粉丝列表接口

3. **互动功能**
   - 点赞/取消点赞接口
   - 收藏/取消收藏接口
   - 分享记录接口

### 需要新增的接口

1. **用户资料**
   - 获取用户详情接口
   - 更新用户资料接口
   - 上传用户头像接口

2. **搜索功能**
   - 综合搜索接口（支持帖子、用户、话题）
   - 获取热门搜索词接口
   - 保存搜索历史接口

3. **话题标签**
   - 获取热门话题接口
   - 获取话题详情接口
   - 按话题筛选帖子接口

4. **内容分类**
   - 获取用户发布的帖子接口
   - 获取用户点赞的帖子接口
   - 获取用户收藏的帖子接口

5. **消息通知**
   - 获取未读消息数接口
   - 标记消息已读接口
   - 获取各类型消息列表接口

## 对接任务清单

### 第一阶段：数据库更新

1. **更新用户表结构**
   - 添加个人简介和舞种字段
   - 确保用户统计表结构完整

### 第二阶段：核心接口实现

2. **用户资料接口**
   - 实现获取用户详情API
   - 实现更新用户资料API
   - 实现头像上传API

3. **帖子接口**
   - 实现帖子列表API（支持分页、按话题筛选）
   - 实现帖子详情API
   - 实现发布帖子API

4. **互动接口**
   - 实现点赞/取消点赞API
   - 实现收藏/取消收藏API
   - 实现评论发布/获取API

### 第三阶段：功能性接口实现

5. **搜索接口**
   - 实现综合搜索API
   - 实现热门搜索词API

6. **话题接口**
   - 实现获取热门话题API
   - 实现话题详情API

7. **消息接口**
   - 实现获取未读消息数API
   - 实现消息列表API

### 第四阶段：前端对接

8. **首页对接**
   - 对接帖子列表API
   - 对接话题列表API

9. **个人主页对接**
   - 对接用户详情API
   - 对接用户帖子/点赞/收藏API

10. **互动功能对接**
    - 对接点赞/评论/收藏API
    - 对接关注/取消关注API

11. **搜索功能对接**
    - 对接搜索API
    - 对接热门搜索API

12. **消息功能对接**
    - 对接消息列表API
    - 对接未读消息数API

## API规范

所有API遵循以下统一格式：

```json
{
  "code": 0,        // 0表示成功，非0表示错误
  "data": {},       // 响应数据
  "message": "success" // 响应消息
}
```

## 详细API设计

### 1. 用户相关API

#### 1.1 获取用户详情

- **URL:** `/api/user/profile/{userId}`
- **方法:** GET
- **响应:**
```json
{
  "code": 0,
  "data": {
    "userId": "123",
    "nickname": "张小明",
    "avatar": "/static/images/avatar.png",
    "bio": "美食爱好者 | 旅行达人 | 摄影师",
    "danceType": "街舞",
    "postCount": 128,
    "followingCount": 256,
    "followerCount": 1024,
    "likeCount": 8547,
    "isFollowed": false
  },
  "message": "success"
}
```

#### 1.2 更新用户资料

- **URL:** `/api/user/profile/update`
- **方法:** POST
- **请求体:**
```json
{
  "nickname": "新昵称",
  "bio": "新的个人简介",
  "danceType": "新的舞种"
}
```
- **响应:**
```json
{
  "code": 0,
  "data": true,
  "message": "success"
}
```

### 2. 帖子相关API

#### 2.1 获取帖子列表

- **URL:** `/api/posts`
- **方法:** GET
- **参数:**
  - `page`: 页码
  - `size`: 每页数量
  - `topicId`: 话题ID（可选）
- **响应:**
```json
{
  "code": 0,
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1,
        "userId": 123,
        "username": "张小明",
        "userAvatar": "/static/images/avatar.png",
        "content": "帖子内容",
        "images": ["url1", "url2"],
        "topics": ["话题1", "话题2"],
        "likeCount": 10,
        "commentCount": 5,
        "shareCount": 2,
        "isLiked": false,
        "createTime": "2025-07-15 10:00:00"
      }
    ]
  },
  "message": "success"
}
```

#### 2.2 获取帖子详情

- **URL:** `/api/posts/{postId}`
- **方法:** GET
- **响应:**
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "userId": 123,
    "username": "张小明",
    "userAvatar": "/static/images/avatar.png",
    "content": "帖子内容",
    "images": ["url1", "url2"],
    "topics": ["话题1", "话题2"],
    "likeCount": 10,
    "commentCount": 5,
    "shareCount": 2,
    "isLiked": false,
    "createTime": "2025-07-15 10:00:00"
  },
  "message": "success"
}
```

### 3. 话题相关API

#### 3.1 获取热门话题

- **URL:** `/api/topics/hot`
- **方法:** GET
- **参数:**
  - `limit`: 返回数量
- **响应:**
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "街舞",
      "description": "分享街舞技巧和表演",
      "postCount": 1234,
      "isHot": true
    }
  ],
  "message": "success"
}
```

### 4. 搜索相关API

#### 4.1 综合搜索

- **URL:** `/api/search`
- **方法:** GET
- **参数:**
  - `keyword`: 搜索关键词
  - `type`: 搜索类型（post/user/topic/all）
  - `page`: 页码
  - `size`: 每页数量
- **响应:**
```json
{
  "code": 0,
  "data": {
    "posts": [],
    "users": [],
    "topics": []
  },
  "message": "success"
}
```

## 数据库更新SQL

需要执行以下SQL来更新用户表结构：

```sql
-- 添加个人简介字段
ALTER TABLE ba_user ADD COLUMN bio VARCHAR(500) DEFAULT '' COMMENT '个人简介';

-- 添加舞种字段
ALTER TABLE ba_user ADD COLUMN dance_type VARCHAR(50) DEFAULT '' COMMENT '舞种';
```

## 对接时间计划

1. 数据库更新：1天
2. 核心接口实现：3天
3. 功能性接口实现：2天
4. 前端对接：3天
5. 测试与优化：2天

总计：11天 