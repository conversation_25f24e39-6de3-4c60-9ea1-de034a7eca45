<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="SqlDialectMappings">
    <file url="file://$PROJECT_DIR$/sql/comment_tables.sql" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/sql/creat_init.sql" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/sql/fix_unique_constraint_issue.sql" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/sql/social_posts_system.sql" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/sql/stage_two_database_updates.sql" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/sql/topics.sql" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/sql/update_comment_for_topic.sql" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/sql/update_line6.sql" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/sql/vote_record_table.sql" dialect="MySQL" />
  </component>
</project>