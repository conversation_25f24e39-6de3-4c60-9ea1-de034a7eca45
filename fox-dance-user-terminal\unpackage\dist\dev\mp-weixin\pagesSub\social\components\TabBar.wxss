@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.tabbar-container.data-v-4fd9768a {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
  height: 150rpx;
}
/* 确保TabBar容器有足够的高度 */
.tabbar-container.data-v-4fd9768a  .u-tabbar {
  height: 100rpx;
  padding-bottom: 0;
}
.tabbar-container.data-v-4fd9768a  .u-tabbar__content {
  height: 100rpx;
}
.tabbar-container.data-v-4fd9768a  .u-tabbar__content__mid-button {
  background-color: #2979ff;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  top: -20rpx;
}
.tabbar-container.data-v-4fd9768a  .u-tabbar__content__mid-button__icon {
  color: #fff !important;
  width: 40rpx !important;
  height: 40rpx !important;
}

