# 社交模块 TabBar 单页面切换方案

## 概述

已将 TabBar 组件从页面跳转模式改为单页面内容切换模式，提升用户体验和性能。

## 主要改动

### 1. TabBar 组件修改 (`/components/TabBar.vue`)

- **移除页面跳转逻辑**：不再使用 `uni.navigateTo` 或 `uni.switchTab`
- **添加事件通信**：通过 `$emit('tab-change')` 通知父组件
- **支持当前状态**：添加 `currentTab` prop 来控制选中状态

```vue
<!-- 使用方式 -->
<TabBar :currentTab="currentTab" @tab-change="handleTabChange" />
```

### 2. 主容器页面 (`/main/index.vue`)

- **统一管理**：所有 TabBar 相关页面的容器
- **条件渲染**：使用 `v-if` 控制不同内容组件的显示
- **状态管理**：维护当前激活的选项卡状态

### 3. 各个页面组件修改

已移除以下页面中的 TabBar 组件：
- `/home/<USER>
- `/discover/index.vue` - 发现页面  
- `/message/index.vue` - 消息页面
- `/profile/index.vue` - 我的页面

## 使用方法

### 访问社交模块

```javascript
// 跳转到社交模块主页面
uni.navigateTo({
  url: '/pagesSub/social/main/index'
})

// 跳转到特定选项卡
uni.navigateTo({
  url: '/pagesSub/social/main/index?tab=1' // 0=首页, 1=发现, 3=消息, 4=我的
})
```

### 在主容器中处理切换

```javascript
methods: {
  handleTabChange(data) {
    const { index } = data
    this.currentTab = index
    // 可以在这里添加切换时的额外逻辑
  }
}
```

## 技术特点

1. **性能优化**：使用动态导入减少初始加载时间
2. **状态保持**：切换选项卡时保持页面状态
3. **微信小程序兼容**：完全兼容微信小程序环境
4. **uview 组件**：继续使用 uview-ui 组件库

## 注意事项

- 发布按钮（中间的 + 按钮）仍然使用页面跳转
- 各个页面组件现在作为子组件使用，不再独立导航
- 保持了原有的视觉样式和交互效果
