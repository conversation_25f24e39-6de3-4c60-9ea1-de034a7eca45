{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabs/u-tabs.vue?e707", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabs/u-tabs.vue?6680", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabs/u-tabs.vue?369c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabs/u-tabs.vue?ea09", "uni-app:///components/uview-ui/components/u-tabs/u-tabs.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabs/u-tabs.vue?5b5d", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-tabs/u-tabs.vue?1fab"], "names": ["name", "props", "isScroll", "type", "default", "list", "current", "height", "fontSize", "duration", "activeColor", "inactiveColor", "<PERSON><PERSON><PERSON><PERSON>", "barHeight", "gutter", "bgColor", "count", "offset", "bold", "activeItemStyle", "showBar", "barStyle", "itemWidth", "data", "scrollLeft", "tabQueryInfo", "componentWidth", "scrollBarLeft", "parentLeft", "id", "currentIndex", "barFirstTimeMove", "watch", "immediate", "handler", "computed", "tabBarStyle", "width", "transform", "opacity", "Object", "tabItemStyle", "padding", "flex", "style", "methods", "init", "tabRect", "clickTab", "getTabRect", "query", "size", "rect", "scrollByIndex", "setTimeout", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACgM;AAChM,gBAAgB,8LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAovB,CAAgB,qsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqBxwB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA1BA,gBA2BA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAJ;MACAG;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;QACA;MACA;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;QACA;MACA;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;QACA;MACA;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;EACA;EACAmB;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACA;IACA3B;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA2B;MACAC;QAAA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAC;QACAC;QACA;QACA;QACA;QACA/B;QACAgC;QACA;QACA;MACA;MACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACAlC;UACA;UACA;UACA;UACAmC;UACAC;UACAN;QACA;QACA;QACA;QACA;UACAO;UACA;UACAA;QACA;UACAA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAC;UACAC;UACAC;QACA;MACA;MACA;MACAF,WACA;QACA;QACA;QACA;MACA,aACA;IACA;IACA;IACAG;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC/SA;AAAA;AAAA;AAAA;AAA26C,CAAgB,gxCAAG,EAAC,C;;;;;;;;;;;ACA/7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-tabs/u-tabs.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-tabs.vue?vue&type=template&id=d9072e9a&scoped=true&\"\nvar renderjs\nimport script from \"./u-tabs.vue?vue&type=script&lang=js&\"\nexport * from \"./u-tabs.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-tabs.vue?vue&type=style&index=0&id=d9072e9a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d9072e9a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-tabs/u-tabs.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=template&id=d9072e9a&scoped=true&\"", "var components\ntry {\n  components = {\n    uBadge: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-badge/u-badge\" */ \"@/components/uview-ui/components/u-badge/u-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s0 = _vm.__get_style([_vm.tabItemStyle(index)])\n    return {\n      $orig: $orig,\n      s0: s0,\n    }\n  })\n  var s1 = _vm.showBar ? _vm.__get_style([_vm.tabBarStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-tabs\" :style=\"{\r\n\t\tbackground: bgColor\r\n\t}\">\r\n\t\t<!-- $u.getRect()对组件根节点无效，因为写了.in(this)，故这里获取内层接点尺寸 -->\r\n\t\t<view>\r\n\t\t\t<scroll-view scroll-x class=\"u-scroll-view\" :scroll-left=\"scrollLeft\" scroll-with-animation>\r\n\t\t\t\t<view class=\"u-scroll-box\" :id=\"id\" :class=\"{'u-tabs-scroll-flex': !isScroll}\">\r\n\t\t\t\t\t<view class=\"u-tab-item u-line-1\" :id=\"'u-tab-item-' + index\" v-for=\"(item, index) in list\" :key=\"index\" @tap=\"clickTab(index)\"\r\n\t\t\t\t\t :style=\"[tabItemStyle(index)]\">\r\n\t\t\t\t\t\t<u-badge :count=\"item[count] || item['count'] || 0\" :offset=\"offset\" size=\"mini\"></u-badge>\r\n\t\t\t\t\t\t{{ item[name] || item['name']}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"showBar\" class=\"u-tab-bar\" :style=\"[tabBarStyle]\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * tabs 标签\r\n\t * @description 该组件，是一个tabs标签组件，在标签多的时候，可以配置为左右滑动，标签少的时候，可以禁止滑动。 该组件的一个特点是配置为滚动模式时，激活的tab会自动移动到组件的中间位置。\r\n\t * @tutorial https://www.uviewui.com/components/tabs.html\r\n\t * @property {Boolean} is-scroll tabs是否可以左右拖动（默认true）\r\n\t * @property {Array} list 标签数组，元素为对象，如[{name: '推荐'}]\r\n\t * @property {String Number} current 指定哪个tab为激活状态（默认0）\r\n\t * @property {String Number} height 导航栏的高度，单位rpx（默认80）\r\n\t * @property {String Number} font-size tab文字大小，单位rpx（默认30）\r\n\t * @property {String Number} duration 滑块移动一次所需的时间，单位秒（默认0.5）\r\n\t * @property {String} active-color 滑块和激活tab文字的颜色（默认#2979ff）\r\n\t * @property {String} inactive-color tabs文字颜色（默认#303133）\r\n\t * @property {String Number} bar-width 滑块宽度，单位rpx（默认40）\r\n\t * @property {Object} active-item-style 活动tabs item的样式，对象形式\r\n\t * @property {Object} bar-style 底部滑块的样式，对象形式\r\n\t * @property {Boolean} show-bar 是否显示底部的滑块（默认true）\r\n\t * @property {String Number} bar-height 滑块高度，单位rpx（默认6）\r\n\t * @property {String Number} item-width 标签的宽度（默认auto）\r\n\t * @property {String Number} gutter 单个tab标签的左右内边距之和，单位rpx（默认40）\r\n\t * @property {String} bg-color tabs导航栏的背景颜色（默认#ffffff）\r\n\t * @property {String} name 组件内部读取的list参数中的属性名（tab名称），见官网说明（默认name）\r\n\t * @property {String} count 组件内部读取的list参数中的属性名（badge徽标数），同name属性的使用，见官网说明（默认count）\r\n\t * @property {Array} offset 设置badge徽标数的位置偏移，格式为 [x, y]，也即设置的为top和right的值，单位rpx（默认[5, 20]）\r\n\t * @property {Boolean} bold 激活选项的字体是否加粗（默认true）\r\n\t * @event {Function} change 点击标签时触发\r\n\t * @example <u-tabs ref=\"tabs\" :list=\"list\" :is-scroll=\"false\"></u-tabs>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-tabs\",\r\n\t\tprops: {\r\n\t\t\t// 导航菜单是否需要滚动，如只有2或者3个的时候，就不需要滚动了，此时使用flex平分tab的宽度\r\n\t\t\tisScroll: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t//需循环的标签列表\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 当前活动tab的索引\r\n\t\t\tcurrent: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 导航栏的高度和行高\r\n\t\t\theight: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 80\r\n\t\t\t},\r\n\t\t\t// 字体大小\r\n\t\t\tfontSize: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 30\r\n\t\t\t},\r\n\t\t\t// 过渡动画时长, 单位ms\r\n\t\t\tduration: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 0.5\r\n\t\t\t},\r\n\t\t\t// 选中项的主题颜色\r\n\t\t\tactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#2979ff'\r\n\t\t\t},\r\n\t\t\t// 未选中项的颜色\r\n\t\t\tinactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#303133'\r\n\t\t\t},\r\n\t\t\t// 菜单底部移动的bar的宽度，单位rpx\r\n\t\t\tbarWidth: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 40\r\n\t\t\t},\r\n\t\t\t// 移动bar的高度\r\n\t\t\tbarHeight: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 6\r\n\t\t\t},\r\n\t\t\t// 单个tab的左或有内边距（左右相同）\r\n\t\t\tgutter: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 30\r\n\t\t\t},\r\n\t\t\t// 导航栏的背景颜色\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#ffffff'\r\n\t\t\t},\r\n\t\t\t// 读取传入的数组对象的属性(tab名称)\r\n\t\t\tname: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'name'\r\n\t\t\t},\r\n\t\t\t// 读取传入的数组对象的属性(徽标数)\r\n\t\t\tcount: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'count'\r\n\t\t\t},\r\n\t\t\t// 徽标数位置偏移\r\n\t\t\toffset: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => {\r\n\t\t\t\t\treturn [5, 20]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 活动tab字体是否加粗\r\n\t\t\tbold: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 当前活动tab item的样式\r\n\t\t\tactiveItemStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault() {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 是否显示底部的滑块\r\n\t\t\tshowBar: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 底部滑块的自定义样式\r\n\t\t\tbarStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault() {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 标签的宽度\r\n\t\t\titemWidth: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 'auto'\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tscrollLeft: 0, // 滚动scroll-view的左边滚动距离\r\n\t\t\t\ttabQueryInfo: [], // 存放对tab菜单查询后的节点信息\r\n\t\t\t\tcomponentWidth: 0, // 屏幕宽度，单位为px\r\n\t\t\t\tscrollBarLeft: 0, // 移动bar需要通过translateX()移动的距离\r\n\t\t\t\tparentLeft: 0, // 父元素(tabs组件)到屏幕左边的距离\r\n\t\t\t\tid: this.$u.guid(), // id值\r\n\t\t\t\tcurrentIndex: this.current,\r\n\t\t\t\tbarFirstTimeMove: true, // 滑块第一次移动时(页面刚生成时)，无需动画，否则给人怪异的感觉\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 监听tab的变化，重新计算tab菜单的布局信息，因为实际使用中菜单可能是通过\r\n\t\t\t// 后台获取的（如新闻app顶部的菜单），获取返回需要一定时间，所以list变化时，重新获取布局信息\r\n\t\t\tlist(n, o) {\r\n\t\t\t\t// list变动时，重制内部索引，否则可能导致超出数组边界的情况\r\n\t\t\t\tif(n.length !== o.length) this.currentIndex = 0;\r\n\t\t\t\t// 用$nextTick等待视图更新完毕后再计算tab的局部信息，否则可能因为tab还没生成就获取，就会有问题\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.init();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcurrent: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(nVal, oVal) {\r\n\t\t\t\t\t// 视图更新后再执行移动操作\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.currentIndex = nVal;\r\n\t\t\t\t\t\tthis.scrollByIndex();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 移动bar的样式\r\n\t\t\ttabBarStyle() {\r\n\t\t\t\tlet style = {\r\n\t\t\t\t\twidth: this.barWidth + 'rpx',\r\n\t\t\t\t\ttransform: `translate(${this.scrollBarLeft}px, -100%)`,\r\n\t\t\t\t\t// 滑块在页面渲染后第一次滑动时，无需动画效果\r\n\t\t\t\t\t'transition-duration': `${this.barFirstTimeMove ? 0 : this.duration }s`,\r\n\t\t\t\t\t'background-color': this.activeColor,\r\n\t\t\t\t\theight: this.barHeight + 'rpx',\r\n\t\t\t\t\topacity: this.barFirstTimeMove ? 0 : 1,\r\n\t\t\t\t\t// 设置一个很大的值，它会自动取能用的最大值，不用高度的一半，是因为高度可能是单数，会有小数出现\r\n\t\t\t\t\t'border-radius': `${this.barHeight / 2}px`\r\n\t\t\t\t};\r\n\t\t\t\tObject.assign(style, this.barStyle);\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// tab的样式\r\n\t\t\ttabItemStyle() {\r\n\t\t\t\treturn (index) => {\r\n\t\t\t\t\tlet style = {\r\n\t\t\t\t\t\theight: this.height + 'rpx',\r\n\t\t\t\t\t\t'line-height': this.height + 'rpx',\r\n\t\t\t\t\t\t'font-size': this.fontSize + 'rpx',\r\n\t\t\t\t\t\t'transition-duration': `${this.duration}s`,\r\n\t\t\t\t\t\tpadding: this.isScroll ? `0 ${this.gutter}rpx` : '',\r\n\t\t\t\t\t\tflex: this.isScroll ? 'auto' : '1',\r\n\t\t\t\t\t\twidth: this.$u.addUnit(this.itemWidth)\r\n\t\t\t\t\t};\r\n\t\t\t\t\t// 字体加粗\r\n\t\t\t\t\tif (index == this.currentIndex && this.bold) style.fontWeight = 'bold';\r\n\t\t\t\t\tif (index == this.currentIndex) {\r\n\t\t\t\t\t\tstyle.color = this.activeColor;\r\n\t\t\t\t\t\t// 给选中的tab item添加外部自定义的样式\r\n\t\t\t\t\t\tstyle = Object.assign(style, this.activeItemStyle);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tstyle.color = this.inactiveColor;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn style;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 设置一个init方法，方便多处调用\r\n\t\t\tasync init() {\r\n\t\t\t\t// 获取tabs组件的尺寸信息\r\n\t\t\t\tlet tabRect = await this.$uGetRect('#' + this.id);\r\n\t\t\t\t// tabs组件距离屏幕左边的宽度\r\n\t\t\t\tthis.parentLeft = tabRect.left;\r\n\t\t\t\t// tabs组件的宽度\r\n\t\t\t\tthis.componentWidth = tabRect.width;\r\n\t\t\t\tthis.getTabRect();\r\n\t\t\t},\r\n\t\t\t// 点击某一个tab菜单\r\n\t\t\tclickTab(index) {\r\n\t\t\t\t// 点击当前活动tab，不触发事件\r\n\t\t\t\tif(index == this.currentIndex) return ;\r\n\t\t\t\t// 发送事件给父组件\r\n\t\t\t\tthis.$emit('change', index);\r\n\t\t\t},\r\n\t\t\t// 查询tab的布局信息\r\n\t\t\tgetTabRect() {\r\n\t\t\t\t// 创建节点查询\r\n\t\t\t\tlet query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t// 历遍所有tab，这里是执行了查询，最终使用exec()会一次性返回查询的数组结果\r\n\t\t\t\tfor (let i = 0; i < this.list.length; i++) {\r\n\t\t\t\t\t// 只要size和rect两个参数\r\n\t\t\t\t\tquery.select(`#u-tab-item-${i}`).fields({\r\n\t\t\t\t\t\tsize: true,\r\n\t\t\t\t\t\trect: true\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// 执行查询，一次性获取多个结果\r\n\t\t\t\tquery.exec(\r\n\t\t\t\t\tfunction(res) {\r\n\t\t\t\t\t\tthis.tabQueryInfo = res;\r\n\t\t\t\t\t\t// 初始化滚动条和移动bar的位置\r\n\t\t\t\t\t\tthis.scrollByIndex();\r\n\t\t\t\t\t}.bind(this)\r\n\t\t\t\t);\r\n\t\t\t},\r\n\t\t\t// 滚动scroll-view，让活动的tab处于屏幕的中间位置\r\n\t\t\tscrollByIndex() {\r\n\t\t\t\t// 当前活动tab的布局信息，有tab菜单的width和left(为元素左边界到父元素左边界的距离)等信息\r\n\t\t\t\tlet tabInfo = this.tabQueryInfo[this.currentIndex];\r\n\t\t\t\tif (!tabInfo) return;\r\n\t\t\t\t// 活动tab的宽度\r\n\t\t\t\tlet tabWidth = tabInfo.width;\r\n\t\t\t\t// 活动item的左边到tabs组件左边的距离，用item的left减去tabs的left\r\n\t\t\t\tlet offsetLeft = tabInfo.left - this.parentLeft;\r\n\t\t\t\t// 将活动的tabs-item移动到屏幕正中间，实际上是对scroll-view的移动\r\n\t\t\t\tlet scrollLeft = offsetLeft - (this.componentWidth - tabWidth) / 2;\r\n\t\t\t\tthis.scrollLeft = scrollLeft < 0 ? 0 : scrollLeft;\r\n\t\t\t\t// 当前活动item的中点点到左边的距离减去滑块宽度的一半，即可得到滑块所需的移动距离\r\n\t\t\t\tlet left = tabInfo.left + tabInfo.width / 2 - this.parentLeft;\r\n\t\t\t\t// 计算当前活跃item到组件左边的距离\r\n\t\t\t\tthis.scrollBarLeft = left - uni.upx2px(this.barWidth) / 2;\r\n\t\t\t\t// 第一次移动滑块的时候，barFirstTimeMove为true，放到延时中将其设置false\r\n\t\t\t\t// 延时是因为scrollBarLeft作用于computed计算时，需要一个过程需，否则导致出错\r\n\t\t\t\tif(this.barFirstTimeMove == true) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.barFirstTimeMove = false;\r\n\t\t\t\t\t}, 100)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init();\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\r\n\tview,\r\n\tscroll-view {\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t::-webkit-scrollbar,\r\n\t::-webkit-scrollbar,\r\n\t::-webkit-scrollbar {\r\n\t\tdisplay: none;\r\n\t\twidth: 0 !important;\r\n\t\theight: 0 !important;\r\n\t\t-webkit-appearance: none;\r\n\t\tbackground: transparent;\r\n\t}\r\n\t/* #endif */\r\n\r\n\t.u-scroll-box {\r\n\t\tposition: relative;\r\n\t\t/* #ifdef MP-TOUTIAO */\r\n\t\twhite-space: nowrap;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t/* #ifdef H5 */\r\n\t// 通过样式穿透，隐藏H5下，scroll-view下的滚动条\r\n\tscroll-view ::v-deep ::-webkit-scrollbar {\r\n\t\tdisplay: none;\r\n\t\twidth: 0 !important;\r\n\t\theight: 0 !important;\r\n\t\t-webkit-appearance: none;\r\n\t\tbackground: transparent;\r\n\t}\r\n\t/* #endif */\r\n\r\n\t.u-scroll-view {\r\n\t\twidth: 100%;\r\n\t\twhite-space: nowrap;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.u-tab-item {\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: inline-block;\r\n\t\t/* #endif */\r\n\t\ttext-align: center;\r\n\t\ttransition-property: background-color, color;\r\n\t}\r\n\r\n\t.u-tab-bar {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t}\r\n\r\n\t.u-tabs-scroll-flex {\r\n\t\t@include vue-flex;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=style&index=0&id=d9072e9a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=style&index=0&id=d9072e9a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752725568750\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}