package com.yupi.springbootinit.controller;

import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.model.dto.notification.NotificationQueryRequest;
import com.yupi.springbootinit.model.vo.NotificationVO;
import com.yupi.springbootinit.service.NotificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 消息通知接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/notifications")
@Slf4j
@Api(tags = "消息通知接口")
public class NotificationController {

    @Resource
    private NotificationService notificationService;

    /**
     * 获取未读消息数
     */
    @GetMapping("/unread-count")
    @ApiOperation(value = "获取未读消息数")
    public BaseResponse<Map<String, Integer>> getUnreadCount(HttpServletRequest request) {
        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            Map<String, Integer> unreadCount = notificationService.getUnreadCount(currentUserId);
            log.info("获取未读消息数成功 - userId: {}, unreadCount: {}", currentUserId, unreadCount);
            return ResultUtils.success(unreadCount);

        } catch (Exception e) {
            log.error("获取未读消息数失败 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取未读消息数失败");
        }
    }

    /**
     * 获取消息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取消息列表")
    public BaseResponse<List<NotificationVO>> getNotificationList(@RequestParam(defaultValue = "all") String type,
                                                                 @RequestParam(defaultValue = "1") Integer current,
                                                                 @RequestParam(defaultValue = "20") Integer size,
                                                                 HttpServletRequest request) {
        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            NotificationQueryRequest queryRequest = new NotificationQueryRequest();
            queryRequest.setUserId(currentUserId);
            queryRequest.setType(type);
            queryRequest.setCurrent(current);
            queryRequest.setSize(size);

            List<NotificationVO> notifications = notificationService.getNotificationList(queryRequest);
            log.info("获取消息列表成功 - userId: {}, type: {}, count: {}", 
                    currentUserId, type, notifications.size());
            return ResultUtils.success(notifications);

        } catch (Exception e) {
            log.error("获取消息列表失败 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取消息列表失败");
        }
    }

    /**
     * 获取系统消息
     */
    @GetMapping("/system")
    @ApiOperation(value = "获取系统消息")
    public BaseResponse<List<NotificationVO>> getSystemNotifications(@RequestParam(defaultValue = "1") Integer current,
                                                                    @RequestParam(defaultValue = "20") Integer size,
                                                                    HttpServletRequest request) {
        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            List<NotificationVO> notifications = notificationService.getSystemNotifications(currentUserId, current, size);
            log.info("获取系统消息成功 - userId: {}, count: {}", currentUserId, notifications.size());
            return ResultUtils.success(notifications);

        } catch (Exception e) {
            log.error("获取系统消息失败 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取系统消息失败");
        }
    }

    /**
     * 获取点赞消息
     */
    @GetMapping("/likes")
    @ApiOperation(value = "获取点赞消息")
    public BaseResponse<List<NotificationVO>> getLikeNotifications(@RequestParam(defaultValue = "1") Integer current,
                                                                  @RequestParam(defaultValue = "20") Integer size,
                                                                  HttpServletRequest request) {
        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            List<NotificationVO> notifications = notificationService.getLikeNotifications(currentUserId, current, size);
            log.info("获取点赞消息成功 - userId: {}, count: {}", currentUserId, notifications.size());
            return ResultUtils.success(notifications);

        } catch (Exception e) {
            log.error("获取点赞消息失败 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取点赞消息失败");
        }
    }

    /**
     * 获取关注消息
     */
    @GetMapping("/follows")
    @ApiOperation(value = "获取关注消息")
    public BaseResponse<List<NotificationVO>> getFollowNotifications(@RequestParam(defaultValue = "1") Integer current,
                                                                    @RequestParam(defaultValue = "20") Integer size,
                                                                    HttpServletRequest request) {
        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            List<NotificationVO> notifications = notificationService.getFollowNotifications(currentUserId, current, size);
            log.info("获取关注消息成功 - userId: {}, count: {}", currentUserId, notifications.size());
            return ResultUtils.success(notifications);

        } catch (Exception e) {
            log.error("获取关注消息失败 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取关注消息失败");
        }
    }

    /**
     * 获取评论消息
     */
    @GetMapping("/comments")
    @ApiOperation(value = "获取评论消息")
    public BaseResponse<List<NotificationVO>> getCommentNotifications(@RequestParam(defaultValue = "1") Integer current,
                                                                     @RequestParam(defaultValue = "20") Integer size,
                                                                     HttpServletRequest request) {
        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            List<NotificationVO> notifications = notificationService.getCommentNotifications(currentUserId, current, size);
            log.info("获取评论消息成功 - userId: {}, count: {}", currentUserId, notifications.size());
            return ResultUtils.success(notifications);

        } catch (Exception e) {
            log.error("获取评论消息失败 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取评论消息失败");
        }
    }

    /**
     * 标记消息已读
     */
    @PutMapping("/read")
    @ApiOperation(value = "标记消息已读")
    public BaseResponse<Boolean> markAsRead(@RequestParam(required = false) Long notificationId,
                                          @RequestParam(required = false) String type,
                                          HttpServletRequest request) {
        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            boolean result;
            if (notificationId != null) {
                // 标记单个消息已读
                result = notificationService.markAsRead(notificationId, currentUserId);
                log.info("标记单个消息已读 - notificationId: {}, userId: {}, result: {}", 
                        notificationId, currentUserId, result);
            } else if (type != null) {
                // 标记某类型所有消息已读
                result = notificationService.markTypeAsRead(currentUserId, type);
                log.info("标记类型消息已读 - type: {}, userId: {}, result: {}", 
                        type, currentUserId, result);
            } else {
                // 标记所有消息已读
                result = notificationService.markAllAsRead(currentUserId);
                log.info("标记所有消息已读 - userId: {}, result: {}", currentUserId, result);
            }

            if (result) {
                return ResultUtils.success(true);
            } else {
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "标记消息已读失败");
            }

        } catch (Exception e) {
            log.error("标记消息已读异常 - notificationId: {}, type: {}, error: {}", 
                    notificationId, type, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "标记消息已读失败");
        }
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{notificationId}")
    @ApiOperation(value = "删除消息")
    public BaseResponse<Boolean> deleteNotification(@PathVariable Long notificationId,
                                                   HttpServletRequest request) {
        if (notificationId == null || notificationId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "消息ID无效");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            boolean result = notificationService.deleteNotification(notificationId, currentUserId);
            if (result) {
                log.info("删除消息成功 - notificationId: {}, userId: {}", notificationId, currentUserId);
                return ResultUtils.success(true);
            } else {
                log.warn("删除消息失败 - notificationId: {}, userId: {}", notificationId, currentUserId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "删除消息失败");
            }

        } catch (Exception e) {
            log.error("删除消息异常 - notificationId: {}, error: {}", notificationId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "删除消息失败");
        }
    }

    /**
     * 清空某类型消息
     */
    @DeleteMapping("/clear/{type}")
    @ApiOperation(value = "清空某类型消息")
    public BaseResponse<Boolean> clearNotifications(@PathVariable String type,
                                                   HttpServletRequest request) {
        if (type == null || type.trim().isEmpty()) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "消息类型不能为空");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            boolean result = notificationService.clearNotifications(type, currentUserId);
            if (result) {
                log.info("清空消息成功 - type: {}, userId: {}", type, currentUserId);
                return ResultUtils.success(true);
            } else {
                log.warn("清空消息失败 - type: {}, userId: {}", type, currentUserId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "清空消息失败");
            }

        } catch (Exception e) {
            log.error("清空消息异常 - type: {}, error: {}", type, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "清空消息失败");
        }
    }
}
