<template>
	<view class="coursePackage">
		
		<view class="les_search">
			<view class="les_search_l"><image src="/static/images/search.png"></image><input type="text" placeholder-style="color:#999999" placeholder="搜索课包名称"  v-model="keywords" confirm-type="search" @confirm="searchTap" /></view>
			<view class="les_search_r" @click="searchTap">搜索</view>
		</view>
		
		<view class="cour_one"  :class="jbToggle || wuzToggle || laosToggle ? 'stor_thr_c_fixed' : ''">
			<view class="cour_one_n">
				<!-- <view :class="type == 0 ? 'cour_one_ac' : ''" @click="navTap(0)">级别</view>
				<view :class="type == 1 ? 'cour_one_ac' : ''" @click="navTap(1)">舞种</view>
				<view :class="type == 2 ? 'cour_one_ac' : ''" @click="navTap(2)">讲师</view> -->
				<view class="stor_thr_c_li" :class="jbToggle ? 'stor_thr_c_li_ac' : ''" @click="jbStartTap">{{jibText == '' ? '级别' : jibText}}<text></text></view>
				<view class="stor_thr_c_li" :class="wuzToggle ? 'stor_thr_c_li_ac' : ''" @click="wuzStartTap">{{wuzText == '' ? '舞种' : wuzText}}<text></text></view>
				<view class="stor_thr_c_li" :class="laosToggle ? 'stor_thr_c_li_ac' : ''" @click="laosStartTap">{{laosText == '' ? '老师' : laosText}}<text></text></view>
			</view>
			<view class="gg_rgba" v-if="jbToggle || wuzToggle || laosToggle" @click="gbTcTap"></view>
			<!-- 级别 go -->
			<view class="teaxzTanc" v-if="jbToggle">
				<view class="teaxzTanc_t">
					<view v-for="(item,index) in jibLists" :key="index" :class="jibIndex == index ? 'teaxzTanc_t_ac' : ''" @click="jibTap(index)">{{item.name}}</view>
				</view>
				<view class="teaxzTanc_b"><view @click="jibReact">重置</view><text @click="jibSubTap">提交</text></view>
			</view>
			<!-- 级别 end -->
			<!-- 舞种 go -->
			<view class="teaxzTanc" v-if="wuzToggle">
				<view class="teaxzTanc_t">
					<view v-for="(item,index) in wuzLists" :key="index" :class="wuzIndex == index ? 'teaxzTanc_t_ac' : ''" @click="wuzTap(index)">{{item.name}}</view>
				</view>
				<view class="teaxzTanc_b"><view @click="wuzReact">重置</view><text @click="wuzSubTap">提交</text></view>
			</view>
			<!-- 舞种 end -->
			<!-- 老师 go -->
			<view class="teaxzTanc" v-if="laosToggle">
				<view class="teaxzTanc_t">
					<view v-for="(item,index) in laosLists" :key="index" :class="laosIndex == index ? 'teaxzTanc_t_ac' : ''" @click="laosTap(index)">{{item.name}}</view>
				</view>
				<view class="teaxzTanc_b"><view @click="laosReact">重置</view><text @click="laosSubTap">提交</text></view>
			</view>
			<!-- 老师 end -->
		</view>
		
		<view class="cour_two">
			<view class="cour_two_li" v-for="(item,index) in coursePackageLists" :key="index" @click="navTo('/pages/buy/coursePackage/myCoursexq?id=' + item.id)">
				<view class="cour_two_li_l"><image :src="imgbaseUrl + item.image" mode="aspectFill"></image></view>
				<view class="cour_two_li_r">
					<view class="cour_two_li_r_a">{{item.name}}</view>
					<view class="cour_two_li_r_b" v-if="item.levelTable">{{item.levelTable.name}}/{{item.danceTable.name}}</view>
					<view class="cour_two_li_r_c">课程时长：{{item.duration*1}}分钟</view>
					<view class="cour_two_li_r_d">讲师:{{item.teacher ? item.teacher.name : '-'}}</view>
					<view class="cour_two_li_r_e">
						<view class="cour_two_li_r_e_l">已售{{item.sales_volume*1}}<text>¥{{item.price*1}}</text></view>
						<view class="cour_two_li_r_e_r">详情</view>
					</view>
				</view>
			</view>
			<view class="gg_loding" v-if="!zanwsj">
				<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
					<view></view>
					<text>加载中</text>
				</view>
				<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
			</view>
			<view class="gg_zwsj" v-if="zanwsj">
				<view class="gg_zwsj_w">
					<image src="/static/images/wusj.png" mode="widthFix"></image>
					<text>暂无数据</text>
				</view>
			</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	lscxCategoryApi,
	CoursePackageListsApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			type:0,
			
			keywords:'',
			keywords_cunc:'',
			imgbaseUrl:'',//图片地址
			
			jibLists:[],
			jibIndex:-1,
			jibText:'',
			jbToggle:false,
			
			wuzLists:[],
			wuzIndex:-1,
			wuzText:'',
			wuzToggle:false,
			
			laosLists:[],
			laosIndex:-1,
			laosText:'',
			laosToggle:false,
			
			coursePackageLists:[],
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			isLogined:false,
		}
	},
	onShow() {
		
	},
	methods: {
		//搜索
		searchTap(){
			this.keywords_cunc = this.keywords;
			this.page = 1;
			this.coursePackageLists = [];
			this.coursePackageData();//课包
		},
		onLoadData(){
			this.isLogined = uni.getStorageSync('token') ? true : false;
			this.imgbaseUrl = this.$baseUrl;
			this.page = 1;
			this.coursePackageLists = [];
			this.coursePackageData();//课包
			this.categoryData();//老师分类
		},
		navTap(index){
			this.type = index;
		},
		//课包列表
		coursePackageData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			CoursePackageListsApi({
				page:that.page,
				size:10,
				level_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,
				dance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,
				teacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,
				name:that.keywords_cunc,
			}).then(res => {
				console.log('课包列表',res)
				if (res.code == 1) {
					var obj = res.data.data;
					that.coursePackageLists = that.coursePackageLists.concat(obj);
					that.zanwsj = that.coursePackageLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.coursePackageLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottomData() {
			console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.coursePackageData();
				}
			}
		},
		onReachBottom() {
			// console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.coursePackageData();
				}
			}
		},
		onPullDownRefresh: function() {
		    // console.log('我被下拉了');
		    this.page = 1;
			this.coursePackageLists = [];
			this.coursePackageData();//课包列表
		},
		//老师分类
		categoryData(){
			let that = this;
			lscxCategoryApi({}).then(res => {
				console.log('老师分类',res)
				if (res.code == 1) {
					that.jibLists = res.data.level;
					that.wuzLists = res.data.dance;
					that.laosLists = res.data.teacher;
				}
			})
		},
		//关闭所有弹窗
		gbTcTap(){
			this.jbToggle = false;
			this.wuzToggle = false;
			this.laosToggle = false;
		},
		//级别弹窗开启
		jbStartTap(){
			this.jbToggle = !this.jbToggle;
			this.wuzToggle = false;
			this.laosToggle = false;
		},
		//级别选择
		jibTap(index){
			this.jibIndex = index;
		},
		//级别提交
		jibSubTap(){
			if(this.jibIndex == -1){
				this.jibText = ''
			}else{
				this.jibText = this.jibLists[this.jibIndex].name
			}
			this.jbToggle = false;
			this.page = 1;
			this.coursePackageLists = [];
			this.coursePackageData();//课包
		},
		//级别重置
		jibReact(){
			this.jibIndex = -1;
		},
		//舞种弹窗开启
		wuzStartTap(){
			this.jbToggle = false;
			this.wuzToggle = !this.wuzToggle;
			this.laosToggle = false;
		},
		//舞种选择
		wuzTap(index){
			this.wuzIndex = index;
		},
		//舞种提交
		wuzSubTap(){
			if(this.wuzIndex == -1){
				this.wuzText = ''
			}else{
				this.wuzText = this.wuzLists[this.wuzIndex].name
			}
			this.wuzToggle = false;
			this.page = 1;
			this.coursePackageLists = [];
			this.coursePackageData();//课包
		},
		//舞种重置
		wuzReact(){
			this.wuzIndex = -1;
		},
		//老师弹窗开启
		laosStartTap(){			
			this.jbToggle = false;
			this.wuzToggle = false;
			this.laosToggle = !this.laosToggle;
		},
		//老师选择
		laosTap(index){
			this.laosIndex = index;
		},
		//老师提交
		laosSubTap(){
			if(this.laosIndex == -1){
				this.laosText = ''
			}else{
				this.laosText = this.laosLists[this.laosIndex].name
			}
			this.laosToggle = false;
			this.page = 1;
			this.coursePackageLists = [];
			this.coursePackageData();//课包
		},
		//老师重置
		laosReact(){
			this.laosIndex = -1;
		},
		//打开图片
		openImg(idx, imgs) {
			let arr = []
			for (let i = 0; i < imgs.length; i++) {
				arr.push(this.imgbaseUrl + imgs[i])
			}
			console.log(idx, imgs);
			uni.previewImage({
				current: idx,
				urls: arr
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.coursePackage{overflow: hidden;}
page{padding-bottom: 0;background:#F6F6F6;}
</style>