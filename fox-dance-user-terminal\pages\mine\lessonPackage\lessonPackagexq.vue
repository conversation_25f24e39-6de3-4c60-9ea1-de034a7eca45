<template>
	<view class="lessonPackagexq" v-if="coursePackageInfo.id">
		
		<view class="kcxq_video" :class="speedState ? 'qpvideo' : ''" v-if="coursePackageInfo.introduce_video">
			<video :src="coursePackageInfo.isoss ? coursePackageInfo.introduce_video : imgbaseUrl + coursePackageInfo.introduce_video" controls id="videoId" @fullscreenchange="handleFullScreen" @controlstoggle="handleControlstoggle">
				
				<!-- 倍速按钮 -->
				<cover-view v-show="controlsToggle" class="speed">
					<!-- <cover-view @click="speedNum=true" class="doubleSpeed">倍速</cover-view> -->
					<cover-view @click="speedTap" class="doubleSpeed">倍速</cover-view>
				</cover-view>
				<!-- 倍速面板 -->
				<cover-view class="speedNumBox" v-if="speedNum">
					<cover-view class="number" @click.stop="handleSetSpeedRate(0.5)" :class="0.5 == speedRate ? 'activeClass' :'' ">0.5倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(0.8)" :class="0.8 == speedRate ? 'activeClass' :'' ">0.8倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(1)" :class="1 == speedRate ? 'activeClass' :'' ">1倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(1.25)" :class="1.25 == speedRate ? 'activeClass' :'' ">1.25倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(1.5)" :class="1.5 == speedRate ? 'activeClass' :'' ">1.5倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(2)" :class="2 == speedRate ? 'activeClass' :'' ">2倍速</cover-view>
					<cover-view class="number" @click.stop="speedNum = false">取消</cover-view>
				</cover-view>
							
				<!-- 0.5/0.8/1.0/1.25/1.5/2 -->
			</video>
		</view>
		
		<view class="ord_nav kcxq_tab">
			<view class="ord_nav_li" :class="type == 0 ? 'ord_nav_li_ac' : ''" @click="tabTap(0)"><view><text>介绍</text><text></text></view></view>
			<view class="ord_nav_li" :class="type == 1 ? 'ord_nav_li_ac' : ''" @click="tabTap(1)"><view><text>目录</text><text></text></view></view>
		</view>
		
		<view class="kbxq_one" style="margin-top:0;" v-if="type == 0">
			<view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>课程介绍</text><text></text></view></view>
			<view class="kbxq_one_b">
				<!-- <rich-text :nodes="kcjsDetail"></rich-text> -->
				{{coursePackageInfo.introduce}}
			</view>
			<view class="kbxq_one_c"><image v-for="(item,index) in coursePackageInfo.images" :key="index" :src="imgbaseUrl + item" mode="widthFix"></image></view>
		</view>
		
		<view class="kbxq_one" v-if="type == 0">
			<view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>适用人群</text><text></text></view></view>
			<view class="kbxq_one_b">
				<!-- <rich-text :nodes="kcjsDetail"></rich-text> -->
				{{coursePackageInfo.apply_crowd}}
			</view>
		</view>
		
		<view class="kbxq_one" v-if="type == 0">
			<view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>讲师详情</text><text></text></view></view>
			<view class="kcxq_one_b">
				<image :src="imgbaseUrl + coursePackageInfo.teacher.image" class="kcxq_one_b_l"></image>
				<view class="kcxq_one_b_r">
					<view class="kcxq_one_b_r_l"><view>{{coursePackageInfo.teacher.name}}</view><text>擅长舞种：{{coursePackageInfo.danceTable.name}}</text></view>
					<view class="kcxq_one_b_r_r" @click="navTo('/pages/buy/coursePackage/teacherDetails?id=' + coursePackageInfo.teacher.id,'1')">讲师详情<image src="/static/images/introduce_more.png"></image></view>
				</view>
			</view>
		</view>
		
		<!-- <view class="kbxq_ml" v-if="type == 1">
			<view class="kbxq_ml_li" v-for="(item,index) in kmLists" :key="index">
				<view class="kbxq_ml_li_t" @click="mlTap(index)"><view>{{index+1}}.{{item.name}}</view><text :style="item.toggle ? 'border-bottom: 14rpx solid #999;border-top:none' : ''"></text></view>
				<view class="kbxq_ml_li_b" v-if="item.toggle">
					<view class="kbxq_ml_li_b_li" v-for="(itemerj,indexerj) in item.lists" :key="indexerj"><view>{{itemerj.name}}</view><text>已看</text></view>
				</view>
			</view>
		</view> -->
		
		<view class="kbxq_ml" v-if="type == 1">
			<view class="kbxq_ml_li" v-for="(item,index) in kmLists" :key="index">
				<view class="kbxq_ml_li_t" @click="mlTap(index)">
					<view>{{index+1}}.{{item.name}}</view><text :style="item.toggle ? 'border-bottom: 14rpx solid #999;border-top:none' : ''"></text>
				</view>
				<view class="kbxq_ml_li_b" v-if="item.toggle">
					<view class="kbxq_ml_li_b_li" v-for="(itemerj,indexerj) in item.catalog" :key="indexerj" v-if="item.catalog.length > 0" @click="videoTap(itemerj,index,indexerj)">
						<view>{{itemerj.name}}</view>
						<text :style="itemerj.view ? 'color:#999999' : ''">{{itemerj.viewing_status == 1 ? '已看' : '未看'}}</text>
					</view>
					<view style="width: 100%;text-align:center;font-size: 26rpx;margin-top:30rpx;" v-if="item.catalog.length == 0">暂无目录</view>
				</view>
			</view>
		</view>
		
		<view class="video_tanc" :class="speedState_tc ? 'qpvideo' : ''" v-if="videoToggle">
			<video :src="videoItem.isoss ? videoItem.video : imgbaseUrl + videoItem.video" controls id="videoId_tc" @play="playVideo" @timeupdate="timeupdateVideo" @ended="playVideo" @pause="playVideo" @fullscreenchange="handleFullScreen_tc" @controlstoggle="handleControlstoggle_tc">
				<!-- 倍速按钮 -->
				<cover-view v-show="controlsToggle_tc" class="speed">
					<!-- <cover-view @click="speedNum=true" class="doubleSpeed">倍速</cover-view> -->
					<cover-view @click="speedTap_tc" class="doubleSpeed">倍速</cover-view>
				</cover-view>
				<!-- 倍速面板 -->
				<cover-view class="speedNumBox" v-if="speedNum_tc">
					<cover-view class="number" @click.stop="handleSetSpeedRate_tc(0.5)" :class="0.5 == speedRate_tc ? 'activeClass' :'' ">0.5倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate_tc(0.8)" :class="0.8 == speedRate_tc ? 'activeClass' :'' ">0.8倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate_tc(1)" :class="1 == speedRate_tc ? 'activeClass' :'' ">1倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate_tc(1.25)" :class="1.25 == speedRate_tc ? 'activeClass' :'' ">1.25倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate_tc(1.5)" :class="1.5 == speedRate_tc ? 'activeClass' :'' ">1.5倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate_tc(2)" :class="2 == speedRate_tc ? 'activeClass' :'' ">2倍速</cover-view>
					<cover-view class="number" @click.stop="speedNum_tc = false">取消</cover-view>
				</cover-view>
			</video>
			<image src="/static/images/icon56.png" @click="gbVideoTap"></image>
		</view>
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>

<script>
import {
	myPackagexqApi,
	videoViewApi
} from '@/config/http.achieve.js'

export default {
	data() {
		return {
			isLogined:false,//是否登录
			isH5:false,//是否是h5
			type:0,
			date_sj: '请选择',
			kcjsDetail:'<p style="color:#fff;background:red;font-size:14px;">富文本内容</p>',
			kmLists:[],
			coursePackageInfo:{id:0},
			imgbaseUrl:"",
			pageId:0,
			videoToggle:false,
			videoItem:{video:''},
			jlIndex:0,
			jlIndexerj:0,
			currentTime: 0, // 记录实际观看的时长（秒）
			
			controlsToggle:false,//是否显示状态
			speedState:false,//是否进入全屏
			speedNum:false,//是否显示倍速
			speedRate:0,//当前倍数
			
			controlsToggle_tc:false,//弹窗课包目录>是否显示状态
			speedState_tc:false,//弹窗课包目录>是否进入全屏
			speedNum_tc:false,//弹窗课包目录>是否显示倍速
			speedRate_tc:0,//弹窗课包目录>当前倍数
		}
	},
	created(){
		/*uni.getSystemInfo({
			success: function(res) {
				this.videoWidth = res.screenWidth - 16;
				this.clientHeight = res.screenHeight;
			}
		});*/
	},
	onShow() {
		this.imgbaseUrl = this.$baseUrl;
		this.isLogined = uni.getStorageSync('token') ? true : false;
		this.coursePackageData();//课包详情
	},
	onLoad(options) {
		// this.userData();//个人信息
		this.pageId = options.id
	},
	onHide(){
		console.log('onHide')
	},
	onUnload(){
		console.log('onUnload');
		if(this.currentTime > 0){
			this.viewVideo();//视频观看
		}
	},
	methods: {
		//点击倍数
		speedTap(){
			this.speedNum = true;
		},
		//监听进入全屏 和 退出全屏
		handleFullScreen(e){
			// console.log('监听进入全屏1',e);
			// console.log('监听进入全屏2',e.detail.fullScreen);
			this.speedState = e.detail.fullScreen;
			this.speedNum = false;
		},
		//2.控件（播放/暂停按钮、播放进度、时间）是显示状态
		handleControlstoggle(e){
			// console.log(e.detail.show);
			this.controlsToggle = e.detail.show
		},
		//设置倍速速度
		handleSetSpeedRate(rate){
			 let videoContext = uni.createVideoContext("videoId");
			 videoContext.playbackRate(rate);
			 this.speedRate = rate;
			 this.speedNum = false;
			 uni.showToast({
			 	icon: 'none',
			 	title: '已切换至' + rate + '倍数',
				duration:2000
			 });
		},
		//弹窗课包目录>点击倍数
		speedTap_tc(){
			this.speedNum_tc = true;
		},
		//弹窗课包目录>监听进入全屏 和 退出全屏
		handleFullScreen_tc(e){
			// console.log('监听进入全屏1',e);
			// console.log('监听进入全屏2',e.detail.fullScreen);
			this.speedState_tc = e.detail.fullScreen;
			this.speedNum_tc = false;
		},
		//弹窗课包目录>2.控件（播放/暂停按钮、播放进度、时间）是显示状态
		handleControlstoggle_tc(e){
			// console.log(e.detail.show);
			this.controlsToggle_tc = e.detail.show
		},
		//设置倍速速度
		handleSetSpeedRate_tc(rate){
			 let videoContext_tc = uni.createVideoContext("videoId_tc");
			 videoContext_tc.playbackRate(rate);
			 this.speedRate_tc = rate;
			 this.speedNum_tc = false;
			 uni.showToast({
			 	icon: 'none',
			 	title: '已切换至' + rate + '倍数',
				duration:2000
			 });
		},
		
		//关闭视频弹窗
		gbVideoTap(){
			this.videoToggle = false;
			if(this.currentTime*1 > 0){
				this.playVideo();
			}
			this.currentTime = 0;
		},
		//检测播放时长
		timeupdateVideo(e){
			this.currentTime += 0.25
			this.currentTime = this.currentTime+=0.25
			// console.log((this.currentTime/1.5).toFixed(2)*1,'33this.currentTime哈哈')
		},
		//检测视频是否开始播放
		playVideo(e){
			console.log(e,'66')
			if(this.videoItem.viewing_status*1 == 0){}
			this.kmLists[this.jlIndex].catalog[this.jlIndexerj].viewing_status = 1
			if(this.currentTime*1 > 0){
				this.viewVideo();//视频观看
			}
		},
		//视频点击播放
		videoTap(item,index,indexerj){
			console.log(item,'item')
			this.videoToggle = true;
			this.videoItem = item;
			this.currentTime = 0;
			this.jlIndex = index;
			this.jlIndexerj = indexerj;
			/*if(item.viewing_status*1 == 0){
				this.kmLists[index].catalog[indexerj].viewing_status = 1
				this.viewVideo();//视频观看
			}*/
		},
		//视频观看
		viewVideo(){
			let that = this;
			videoViewApi({id:that.videoItem.id,viewing_status:1,duration:(that.currentTime/1.5).toFixed(2)*1}).then(res => {
				console.log('视频观看告诉后台',res)
				if (res.code == 1) {
				}
			})
		},
		//课包详情
		coursePackageData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			myPackagexqApi({id:that.pageId}).then(res => {
				console.log('课包详情',res)
				if (res.code == 1) {
					/*res.data.catalog[0].catalog[0] = {
						"id": 3, //目录
						"name": "街舞的由来", //目录名称
						"video": "https://danceadmin.xinzhiyukeji.cn/storage/default/20241025/video(2)e4e0dfbba9b0b7a5e7c9e6319de310a431b24b95.mp4" //视频
					}*/
					//res.data.catalog[0].catalog[0].video = 'https://www.runoob.com/try/demo_source/movie.mp4';
					//res.data.catalog[0].catalog[1].video = 'https://danceadmin.xinzhiyukeji.cn/storage/default/20241025/video(2)e4e0dfbba9b0b7a5e7c9e6319de310a431b24b95.mp4';
					// res.data.introduce_video = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734312077132_ces.mp4'
					
					if(res.data.introduce_video){
						res.data.isoss = res.data.introduce_video.substring(0,5) == 'https' ? true : false
					}
					
					for(var i=0;i<res.data.catalog.length;i++){
						res.data.catalog[i].toggle = false;
						for(var j=0;j<res.data.catalog[i].catalog.length;j++){
							if(res.data.catalog[i].catalog[j].video){
								res.data.catalog[i].catalog[j].isoss = res.data.catalog[i].catalog[j].video.substring(0,5) == 'https' ? true : false
							}
						}
					}
					if(res.data.catalog.length > 0){
						res.data.catalog[0].toggle = true;
					}
					that.coursePackageInfo = res.data;
					that.kmLists = res.data.catalog
					uni.hideLoading();
				}
			})
		},
		mlTap(index){
			this.kmLists[index].toggle = !this.kmLists[index].toggle
		},
		tabTap(index){
			this.type = index;
		},
		bindDateChange_sj: function(e) {
			this.date_sj = e.detail.value
		},
		//个人信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userDetailApi().then(res => {
				if (res.code == 0) {
					console.log('个人信息',res);
					uni.hideLoading();
				}
			})
		},
		navTo(url,ismd){
			if(ismd){
				uni.navigateTo({
					url:url
				});
				return false;
			}
			var that = this;
			if(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
			}else{
				uni.navigateTo({
					url:url
				})
			}
		},
		formatRichText (html) {
			// 去掉img标签里的style、width、height属性
			let newContent= html.replace(/<img[^>]*>/gi,function(match,capture){
				match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '');
				match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '');
				match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '');
				return match;
			});
			// 修改所有style里的width属性为max-width:100%
			newContent = newContent.replace(/style="[^"]+"/gi,function(match,capture){
				match = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi, 'max-width:100%;');
				return match;
			});
			// 去掉<br/>标签
			newContent = newContent.replace(/<br[^>]*\/>/gi, '');
			// img标签添加style属性：max-width:100%;height:auto
			newContent = newContent.replace(/\<img/gi, '<img style="max-width:100%;height:auto;display:block;margin:0px auto;"');
			return newContent;
		}
	},
}
</script>

<style lang="less">
page{padding-bottom:0;background:#fff;}
.lessonPackagexq{
	overflow:hidden;
}
</style>
