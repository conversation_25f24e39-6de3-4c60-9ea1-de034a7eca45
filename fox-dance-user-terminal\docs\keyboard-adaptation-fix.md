# 微信小程序键盘适配修复文档

## 问题描述
在微信小程序的评论功能中，当用户点击底部固定的输入框组件时，弹出的虚拟键盘会遮挡住输入框的一部分内容，导致用户无法看到完整的输入区域。

## 解决方案

### 1. 核心原理
- 使用 `uni.onKeyboardHeightChange()` API 监听键盘高度变化
- 动态调整输入框容器的 `bottom` 样式属性
- 禁用 textarea 的 `adjust-position` 属性，手动控制位置

### 2. 修复的文件

#### 2.1 comment.vue
**数据属性添加：**
```javascript
data() {
  return {
    keyboardHeight: 0, // 键盘高度
    inputContainerBottom: 0, // 输入框容器底部距离
    isKeyboardShow: false, // 键盘是否显示
    // ... 其他属性
  }
}
```

**键盘监听方法：**
```javascript
setupKeyboardListener() {
  // #ifdef MP-WEIXIN
  uni.onKeyboardHeightChange(res => {
    console.log('键盘高度变化:', res.height);
    this.keyboardHeight = res.height;
    this.isKeyboardShow = res.height > 0;
    
    if (res.height > 0) {
      // 键盘弹出，调整输入框位置
      this.inputContainerBottom = res.height;
    } else {
      // 键盘收起，恢复输入框位置
      this.inputContainerBottom = 0;
    }
  });
  // #endif
}
```

**模板动态样式：**
```html
<view class="input-container" :style="{ bottom: inputContainerBottom + 'px' }">
  <comment-input @focus="onInputFocus" @blur="onInputBlur" />
</view>
```

**CSS过渡动画：**
```scss
.input-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background: #ffffff;
  transition: bottom 0.3s ease-in-out; // 添加过渡动画
}
```

#### 2.2 comment-detail.vue
与 comment.vue 相同的修复方案，确保两个页面的键盘适配行为一致。

#### 2.3 CommentInput.vue
**关键配置：**
```html
<textarea 
  :adjust-position="false"  // 禁用自动调整位置
  @focus="onFocus"
  @blur="onBlur"
  // ... 其他属性
/>
```

### 3. 生命周期管理
```javascript
onShow() {
  // 页面显示时，确保只有一个有效的键盘监听
  uni.offKeyboardHeightChange();
  this.setupKeyboardListener();
},

onHide() {
  // 页面隐藏时移除监听
  uni.offKeyboardHeightChange();
},

onUnload() {
  // 页面卸载时移除监听
  uni.offKeyboardHeightChange();
}
```

### 4. 焦点处理优化
```javascript
onInputFocus(e) {
  this.isKeyboardShow = true;
  
  // #ifdef MP-WEIXIN
  // 延时获取键盘高度，因为键盘弹出需要时间
  setTimeout(() => {
    if (this.keyboardHeight === 0) {
      // 如果监听器没有获取到键盘高度，使用默认值
      this.keyboardHeight = 280; // 微信小程序默认键盘高度
      this.inputContainerBottom = this.keyboardHeight;
    }
  }, 300);
  // #endif
},

onInputBlur(e) {
  this.isKeyboardShow = false;
  
  // 延时重置，确保键盘完全收起
  setTimeout(() => {
    if (!this.isKeyboardShow) {
      this.keyboardHeight = 0;
      this.inputContainerBottom = 0;
    }
  }, 100);
}
```

## 技术要点

### 1. 条件编译
使用 `#ifdef MP-WEIXIN` 确保键盘监听代码只在微信小程序环境下执行。

### 2. 防抖处理
通过 `isKeyboardShow` 状态和延时处理，避免键盘快速弹出收起时的状态混乱。

### 3. 默认值兜底
当键盘高度监听失效时，使用默认高度 280px 作为兜底方案。

### 4. 平滑动画
通过 CSS `transition` 属性，让输入框位置变化更加平滑自然。

## 测试验证

### 测试场景
1. 点击输入框，键盘弹出时输入框应完整显示在键盘上方
2. 键盘收起时，输入框应平滑回到底部
3. 快速点击输入框多次，不应出现位置错乱
4. 页面切换时，键盘监听应正确清理和重建

### 兼容性
- ✅ 微信小程序：完整支持键盘适配
- ✅ H5浏览器：降级处理，使用浏览器默认行为
- ✅ 其他平台：条件编译确保不影响其他平台

## 注意事项

1. **必须禁用 adjust-position**：textarea 的 `adjust-position` 属性必须设为 `false`，否则会与手动控制冲突
2. **生命周期管理**：必须在页面隐藏/卸载时清理键盘监听，避免内存泄漏
3. **延时处理**：键盘弹出需要时间，必须使用延时获取准确的键盘高度
4. **状态同步**：确保 `isKeyboardShow` 状态与实际键盘状态同步

## 效果展示

修复后的效果：
- 🎯 键盘弹出时，输入框完整显示在键盘上方
- 🎯 键盘收起时，输入框平滑回到底部
- 🎯 用户可以清楚看到输入的内容
- 🎯 交互体验流畅自然
