@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.message-detail-container.data-v-01825956 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}
.header.data-v-01825956 {
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: 25px;
}
.header-content.data-v-01825956 {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}
.header-left.data-v-01825956, .header-right.data-v-01825956 {
  width: 80rpx;
  display: flex;
  justify-content: flex-start;
}
.header-title.data-v-01825956 {
  flex: 1;
  display: flex;
  justify-content: center;
}
.title-text.data-v-01825956 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.content-scroll.data-v-01825956 {
  flex: 1;
}
.message-detail.data-v-01825956 {
  background: #fff;
  margin: 32rpx;
  border-radius: 24rpx;
  overflow: hidden;
}
.message-header.data-v-01825956 {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f5f5f5;
}
.message-icon.data-v-01825956 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.icon-system.data-v-01825956 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.icon-activity.data-v-01825956 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.icon-security.data-v-01825956 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.header-info.data-v-01825956 {
  flex: 1;
}
.message-title.data-v-01825956 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}
.message-time.data-v-01825956 {
  font-size: 24rpx;
  color: #999;
}
.message-body.data-v-01825956 {
  padding: 32rpx;
  line-height: 1.6;
  font-size: 28rpx;
  color: #333;
}
.action-buttons.data-v-01825956 {
  padding: 0 32rpx 32rpx;
  display: flex;
  gap: 24rpx;
}
.action-btn.data-v-01825956 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-btn.primary.data-v-01825956 {
  background: #2979ff;
}
.action-btn.primary .btn-text.data-v-01825956 {
  color: #fff;
}
.action-btn.secondary.data-v-01825956 {
  background: #f5f5f5;
  border: 2rpx solid #e4e7ed;
}
.action-btn.secondary .btn-text.data-v-01825956 {
  color: #666;
}
.btn-text.data-v-01825956 {
  font-size: 28rpx;
  font-weight: 500;
}
.attachments.data-v-01825956 {
  padding: 32rpx;
  border-top: 2rpx solid #f5f5f5;
}
.section-title.data-v-01825956 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}
.attachment-item.data-v-01825956 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.attachment-name.data-v-01825956 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-left: 16rpx;
}

