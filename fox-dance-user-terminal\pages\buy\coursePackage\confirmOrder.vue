<template>
	<view class="confirmOrder" :style="{ '--qjbutton-color': qjbutton }" v-if="buyDetailInfo.id">
		
		<view class="xsk_one">
			<view class="xsk_one_title">{{buyDetailInfo.name}}</view>
			<view class="xsk_one_li">
				<view class="xsk_one_li_l">课程时长</view>
				<view class="xsk_one_li_r">{{buyDetailInfo.duration}}分钟</view>
			</view>
			<view class="xsk_one_li">
				<view class="xsk_one_li_l">授课讲师</view>
				<view class="xsk_one_li_r">{{buyDetailInfo.teacher.name}}</view>
			</view>
		</view>
		
		<view class="xsk_one">
			<view class="xsk_one_li">
				<view class="xsk_one_li_l">昵称</view>
				<view class="xsk_one_li_r">{{buyDetailInfo.nickname}}</view>
			</view>
			<view class="xsk_one_li">
				<view class="xsk_one_li_l">手机号</view>
				<view class="xsk_one_li_r">{{buyDetailInfo.mobile}}</view>
			</view>
			<view class="xsk_one_li" @click="yhqTap">
				<view class="xsk_one_li_l">优惠券</view>
				<view class="xsk_one_li_r" style="color:#FF6D5C">{{yhqInfo.discount_price == '' ? '选择优惠券' : yhqInfo.type*1 == 1 ? ('无门槛优惠券' + yhqInfo.discount_price+'元') : ('平台现金券' + yhqInfo.discount_price+'元')}}<text v-if="yhqInfo.discount_price != ''" style="color:#999;margin-left:20rpx;" @click.stop="delyhqTap">删除</text><image src="/static/images/right_more.png"></image></view>
			</view>
		</view>
		
		<view class="memk_six">
			<view class="memk_six_a"><image src="/static/images/icon29.png"></image>线上课购买说明</view>
			<view class="memk_six_b">{{buyDetailInfo.purchase_description}}</view>
		</view>
		
		<view class="xsk_xy" @click="xyToggle = !xyToggle"><image :src="xyToggle ? '/static/images/xz-1.png' : '/static/images/xz.png'"></image>阅读并同意<text @click.stop="navTo('/pages/login/xieYi?type=3')">《用户授权协议》</text>和<text @click.stop="navTo('/pages/login/xieYi?type=4')">《平台服务协议》</text></view>
		
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<view class="peodex_foo kc_foo">
			<view class="peodex_foo_l">应支付：<text>￥{{totalPrice}}</text></view>
			<view class="peodex_foo_r" @click="buyTap">购买</view>
		</view>
		
		<!-- 优惠券 go -->
		<view class="gg_rgba" v-if="yhqToggle" @click="yhqToggle = false"></view>
		<view class="thq_tanc" v-if="yhqToggle">
			<view class="thq_tanc_t"><text>优惠券</text><image src="/static/images/popup_close.png" @click="yhqToggle = false"></image></view>
			<view class="thq_tanc_b">
				
				<view class="cou_con_li" v-for="(item,index) in buyDetailInfo.coupon" :key="index">
					<view class="cou_con_li_l">
						<view class="cou_con_li_l_a">课包通用</view>
						<view class="cou_con_li_l_b">￥<text>{{item.discount_price*1}}</text></view>
						<view class="cou_con_li_l_c">{{item.type*1 == 1 ? '无门槛' : '满'+item.full_price+'可用'}}</view>
					</view>
					<view class="cou_con_li_r">
						<view class="cou_con_li_r_a">{{item.type*1 == 1 ? '无门槛优惠券' : '平台现金券'}}</view>
						<view class="cou_con_li_r_b">有效期:{{item.effective_stage}}</view>
						<view class="cou_con_li_r_c">每次仅能使用一张</view>
						<view class="cou_con_li_r_d" @click="goyhqTap(item)">去使用</view>
					</view>
				</view>
				
			</view>
		</view>
		<!-- 优惠券 end -->
		
	</view>
</template>


<script>
import {
	buyDetailApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			yhqText:'',
			xyToggle:false,
			couponLists:[],
			yhqToggle:false,
			buyDetailInfo:{id:0},
			yhqInfo:{discount_price:0,id:0},
			totalPrice:0,
			qjbutton:'#131315',
		}
	},
	onShow() {
		
	},
	onLoad(option) {
		this.buyDetailData(option.id);//获取购买详情
		this.qjbutton = uni.getStorageSync('storeInfo').button
	},
	methods: {
		//优惠券
		yhqTap(){
			if(this.buyDetailInfo.coupon.length == 0){
				uni.showToast({
					icon: 'none',
					title: '暂无可用优惠券',
					duration:2000
				});
				return false;
			}
			this.yhqToggle = true;
		},
		//使用优惠券
		goyhqTap(item){
			// this.yhqText = item;
			this.yhqInfo = item;
			this.yhqToggle = false;
			this.totalPrice = (this.buyDetailInfo.price - this.yhqInfo.discount_price*1).toFixed(2)*1
		},
		//删除使用优惠券
		delyhqTap(){
			this.totalPrice = this.buyDetailInfo.price
			this.yhqInfo = {discount_price:'',id:0};
		},
		//获取购买详情
		buyDetailData(id){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			buyDetailApi({
				id:id
			}).then(res => {
				console.log('获取购买详情',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.totalPrice = res.data.price*1
					that.buyDetailInfo = res.data;
				}
			})
		},
		buyTap(){
			if(!this.xyToggle){
				uni.showToast({
					title: '请先阅读并同意《用户授权协议》和《平台服务协议》',
					icon: 'none',
					duration: 1000
				})
				return false;
			}
			uni.redirectTo({
				url:'/pages/buy/coursePackage/orderPayment?id=' + this.buyDetailInfo.id + '&couponid=' + this.yhqInfo.id + '&price=' + this.totalPrice 
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.confirmOrder{overflow: hidden;}
page{padding-bottom: 0;}
</style>