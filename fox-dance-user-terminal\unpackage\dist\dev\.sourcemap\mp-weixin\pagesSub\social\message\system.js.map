{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/system.vue?8338", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/system.vue?a561", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/system.vue?f78a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/system.vue?ba89", "uni-app:///pagesSub/social/message/system.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/system.vue?c3cf", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/system.vue?3fed"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "isRefreshing", "hasMore", "page", "pageSize", "messageList", "id", "type", "title", "content", "createTime", "isRead", "onLoad", "methods", "markAllAsRead", "uni", "success", "msg", "getIconName", "system", "activity", "security", "formatTime", "openMessageDetail", "message", "url", "onRefresh", "loadMore", "loadMessages", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAquB,CAAgB,qsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6DzvB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,cACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAL;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAL;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAL;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAA;MAAA;MACA;QACA;QACA;MACA;MAEAC;QACAP;QACAC;QACAO;UACA;YACA;cACAC;YACA;YACA;UACA;QACA;MACA;IACA;IAEAC;MACA;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACAC;;MAEA;MACAT;QACAU;MACA;IACA;IAEAC;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBAAA;gBAAA;cAAA;gBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjMA;AAAA;AAAA;AAAA;AAAg5C,CAAgB,gxCAAG,EAAC,C;;;;;;;;;;;ACAp6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/message/system.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/message/system.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./system.vue?vue&type=template&id=65989434&scoped=true&\"\nvar renderjs\nimport script from \"./system.vue?vue&type=script&lang=js&\"\nexport * from \"./system.vue?vue&type=script&lang=js&\"\nimport style0 from \"./system.vue?vue&type=style&index=0&id=65989434&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"65989434\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/message/system.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./system.vue?vue&type=template&id=65989434&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.messageList && _vm.messageList.length > 0\n  var l0 = g0\n    ? _vm.__map(_vm.messageList, function (message, __i0__) {\n        var $orig = _vm.__get_orig(message)\n        var m0 = _vm.getIconName(message.type)\n        var m1 = _vm.formatTime(message.createTime)\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var g1 = _vm.hasMore && _vm.messageList && _vm.messageList.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./system.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./system.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"system-message-container\">\n    <!-- 一键已读按钮 -->\n    <view class=\"read-all-section\">\n      <view class=\"read-all-btn\" @click=\"markAllAsRead\">\n        <u-icon name=\"checkmark-circle\" size=\"20\" color=\"#2979ff\"></u-icon>\n        <text class=\"read-all-text\">一键已读</text>\n      </view>\n    </view>\n\n    <!-- 消息列表 -->\n    <scroll-view \n      class=\"message-list\"\n      scroll-y\n      refresher-enabled\n      :refresher-triggered=\"isRefreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @scrolltolower=\"loadMore\"\n    >\n      <view v-if=\"messageList && messageList.length > 0\">\n        <view\n          v-for=\"message in messageList\"\n          :key=\"message.id\"\n          class=\"message-card\"\n          @click=\"openMessageDetail(message)\"\n        >\n          <!-- 消息图标 -->\n          <view class=\"message-icon\" :class=\"'icon-' + message.type\">\n            <u-icon :name=\"getIconName(message.type)\" size=\"20\" color=\"#fff\"></u-icon>\n          </view>\n          \n          <!-- 消息内容 -->\n          <view class=\"message-content\">\n            <view class=\"message-header\">\n              <text class=\"message-title\">{{ message.title }}</text>\n              <text class=\"message-time\">{{ formatTime(message.createTime) }}</text>\n            </view>\n            <text class=\"message-desc\">{{ message.content }}</text>\n          </view>\n          \n          <!-- 未读标识 -->\n          <view v-if=\"!message.isRead\" class=\"unread-dot\"></view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-else class=\"empty-state\">\n        <image src=\"/static/images/empty-message.png\" class=\"empty-image\"></image>\n        <text class=\"empty-text\">暂无系统消息</text>\n      </view>\n\n      <!-- 加载更多 -->\n      <view v-if=\"hasMore && messageList && messageList.length > 0\" class=\"load-more\">\n        <u-loading mode=\"flower\" size=\"24\"></u-loading>\n        <text class=\"load-text\">加载中...</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'SystemMessage',\n  data() {\n    return {\n      isRefreshing: false,\n      hasMore: true,\n      page: 1,\n      pageSize: 20,\n      messageList: [\n        {\n          id: 1,\n          type: 'system',\n          title: '系统维护通知',\n          content: '系统将于今晚23:00-次日1:00进行维护升级，期间可能影响部分功能使用，请您提前做好准备。',\n          createTime: new Date(Date.now() - 3600000),\n          isRead: false\n        },\n        {\n          id: 2,\n          type: 'activity',\n          title: '舞蹈大赛开始报名',\n          content: '2024年度舞蹈大赛正式开始报名，丰厚奖品等你来拿！报名截止时间：本月底。',\n          createTime: new Date(Date.now() - 7200000),\n          isRead: true\n        },\n        {\n          id: 3,\n          type: 'security',\n          title: '账户安全提醒',\n          content: '检测到您的账户在新设备上登录，如非本人操作，请及时修改密码并联系客服。',\n          createTime: new Date(Date.now() - 86400000),\n          isRead: false\n        },\n        {\n          id: 4,\n          type: 'system',\n          title: '新功能上线',\n          content: '视频剪辑功能已上线，快来体验全新的创作工具，让您的舞蹈视频更加精彩！',\n          createTime: new Date(Date.now() - 172800000),\n          isRead: true\n        }\n      ]\n    }\n  },\n  onLoad() {\n    this.loadMessages()\n  },\n  methods: {\n    markAllAsRead() {\n      const unreadCount = this.messageList.filter(msg => !msg.isRead).length\n      if (unreadCount === 0) {\n        this.$u.toast('暂无未读消息')\n        return\n      }\n\n      uni.showModal({\n        title: '确认操作',\n        content: `确定要将所有${unreadCount}条未读消息标记为已读吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            this.messageList.forEach(msg => {\n              msg.isRead = true\n            })\n            this.$u.toast('已全部标记为已读')\n          }\n        }\n      })\n    },\n\n    getIconName(type) {\n      const iconMap = {\n        system: 'setting',\n        activity: 'volume',\n        security: 'shield'\n      }\n      return iconMap[type] || 'bell'\n    },\n\n    formatTime(time) {\n      const now = new Date()\n      const diff = now - time\n      const minutes = Math.floor(diff / 60000)\n      const hours = Math.floor(diff / 3600000)\n      const days = Math.floor(diff / 86400000)\n\n      if (minutes < 60) {\n        return `${minutes}分钟前`\n      } else if (hours < 24) {\n        return `${hours}小时前`\n      } else {\n        return `${days}天前`\n      }\n    },\n\n    openMessageDetail(message) {\n      // 标记为已读\n      message.isRead = true\n      \n      // 跳转到消息详情页\n      uni.navigateTo({\n        url: `/pagesSub/social/message/detail?id=${message.id}`\n      })\n    },\n\n    onRefresh() {\n      this.isRefreshing = true\n      this.page = 1\n      this.loadMessages().finally(() => {\n        this.isRefreshing = false\n      })\n    },\n\n    loadMore() {\n      if (!this.hasMore) return\n      this.page++\n      this.loadMessages()\n    },\n\n    async loadMessages() {\n      try {\n        // 模拟API请求\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        \n        if (this.page >= 3) {\n          this.hasMore = false\n        }\n      } catch (error) {\n        console.error('加载消息失败:', error)\n        this.$u.toast('加载失败，请重试')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.system-message-container {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f5f5f5;\n}\n\n.read-all-section {\n  background: #fff;\n  padding: 24rpx 32rpx;\n  border-bottom: 2rpx solid #e4e7ed;\n}\n\n.read-all-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 72rpx;\n  background: #f8f9fa;\n  border: 2rpx solid #e4e7ed;\n  border-radius: 36rpx;\n  transition: all 0.2s ease;\n}\n\n.read-all-btn:active {\n  background: #e9ecef;\n  transform: scale(0.98);\n}\n\n.read-all-text {\n  font-size: 28rpx;\n  color: #2979ff;\n  margin-left: 12rpx;\n  font-weight: 500;\n}\n\n.message-list {\n  flex: 1;\n  padding: 32rpx 0;\n}\n\n.message-card {\n  display: flex;\n  align-items: flex-start;\n  padding: 32rpx;\n  margin: 0 32rpx 24rpx;\n  background: #fff;\n  border-radius: 24rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  position: relative;\n}\n\n.message-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n  flex-shrink: 0;\n}\n\n.icon-system {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.icon-activity {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.icon-security {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.message-content {\n  flex: 1;\n}\n\n.message-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16rpx;\n}\n\n.message-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  flex: 1;\n  margin-right: 16rpx;\n}\n\n.message-time {\n  font-size: 24rpx;\n  color: #999;\n  white-space: nowrap;\n}\n\n.message-desc {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  line-clamp: 2;\n  overflow: hidden;\n}\n\n.unread-dot {\n  position: absolute;\n  top: 32rpx;\n  right: 32rpx;\n  width: 16rpx;\n  height: 16rpx;\n  background: #ff4757;\n  border-radius: 50%;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 32rpx;\n}\n\n.empty-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 32rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 32rpx;\n}\n\n.load-text {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 16rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./system.vue?vue&type=style&index=0&id=65989434&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./system.vue?vue&type=style&index=0&id=65989434&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752818685444\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}