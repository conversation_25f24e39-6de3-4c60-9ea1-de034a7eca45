<template>
	<view class="myCoursexq">
		<view class="kcxq_video">
			<video src="https://www.runoob.com/try/demo_source/movie.mp4" controls></video>
		</view>
		
		<view class="kcxq_one">
			<view class="kcxq_one_a">拉丁舞练习<text>入门</text></view>
			<view class="kcxq_one_b">
				<image src="/static/images/toux.png" class="kcxq_one_b_l"></image>
				<view class="kcxq_one_b_r">
					<view class="kcxq_one_b_r_l"><view>LINDA</view><text>五年经验</text></view>
					<view class="kcxq_one_b_r_r" @click="navTo('/pages/index/teacherDetail')">老师详情<image src="/static/images/introduce_more.png"></image></view>
				</view>
			</view>
			<view class="kcxq_one_c">
				<view>上课时间：2024-8-14 11:00</view>
				<view>课程时长：60分钟</view>
				<view>上课地址：河南省郑州市中原区街道123<image src="/static/images/icon18.png"></image></view>
			</view>
		</view>
		
		<view class="kcxq_two">
			<view class="kcxq_two_xf">已预约23人、前方3人在等位</view>
			<view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>学生</text><text></text></view></view>
			<view class="kcxq_two_b">
				<view v-for="(item,index) in 15" :key="index"><image src="/static/images/toux.png"></image><text>学生</text></view>
			</view>
		</view>
		
		<view class="kcxq_two kcxq_thr">
			<view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>本节回顾</text><text></text></view></view>
			<view class="kcxq_thr_b">
				本节课程用到的音乐链接
				<video src="https://www.runoob.com/try/demo_source/movie.mp4" controls style="display: block;width: 100%;"></video>
			</view>
		</view>	
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<view class="kcxq_foo">
			<view class="kcxq_foo_l">
				<view @click="homeTap"><image src="/static/tabbar/tab_home.png"></image>首页</view>
			</view>
			<view class="kcxq_foo_r">
				<view class="back" @click="navTo('/pages/Schedule/confirmOrder')">预约</view>
			</view>
		</view>
	</view>
</template>


<script>
export default {
	data() {
		return {
			isLogined:true,
			navLists:['全部','等位中','待开课','授课中','已完成'],
			
		}
	},
	onShow() {
		
	},
	methods: {
		homeTap(){
			uni.switchTab({
				url:'/pages/index/index'
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
page{padding-bottom: 0;}
</style>