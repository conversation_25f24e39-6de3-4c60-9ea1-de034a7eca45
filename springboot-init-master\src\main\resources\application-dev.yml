# 开发环境配置文件
server:
  port: 8102

spring:
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************
    username: admin_foxdance_c
    password: btxzGpwj9kyy7CKF
    
  # Redis配置（开发环境暂时禁用）
  # redis:
  #   host: localhost
  #   port: 6379
  #   password: 
  #   database: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: isDelete
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.yupi.springbootinit.mapper: debug
    com.yupi.springbootinit.service: debug
    com.yupi.springbootinit.controller: debug
