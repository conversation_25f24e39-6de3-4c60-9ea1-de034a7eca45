<view data-event-opts="{{[['tap',[['goPostDetail',['$event']]]]]}}" class="post-card data-v-92e3cfc8" bindtap="__e"><view class="cover-container data-v-92e3cfc8"><image class="cover-image data-v-92e3cfc8" src="{{post.coverImage||post.images&&post.images[0]||defaultCover}}" mode="aspectFill" data-event-opts="{{[['error',[['onImageError',['$event']]]],['load',[['onImageLoad',['$event']]]]]}}" binderror="__e" bindload="__e"></image><block wx:if="{{imageError}}"><view class="image-placeholder data-v-92e3cfc8"><u-icon vue-id="1b356c6a-1" name="image" color="#ccc" size="32" class="data-v-92e3cfc8" bind:__l="__l"></u-icon><text class="placeholder-text data-v-92e3cfc8">图片加载失败</text></view></block></view><view class="post-title data-v-92e3cfc8"><text class="title-text data-v-92e3cfc8">{{post.title||post.content||'无标题'}}</text></view><view class="post-footer data-v-92e3cfc8"><view data-event-opts="{{[['tap',[['goUserProfile',['$event']]]]]}}" class="user-info data-v-92e3cfc8" catchtap="__e"><u-avatar vue-id="1b356c6a-2" src="{{post.userAvatar}}" size="24" class="data-v-92e3cfc8" bind:__l="__l"></u-avatar><text class="username data-v-92e3cfc8">{{post.username}}</text></view><view data-event-opts="{{[['tap',[['toggleLike',['$event']]]]]}}" class="like-info data-v-92e3cfc8" catchtap="__e"><u-icon vue-id="1b356c6a-3" name="{{post.isLiked?'heart-fill':'heart'}}" color="{{post.isLiked?'#ff4757':'#999'}}" size="16" class="data-v-92e3cfc8" bind:__l="__l"></u-icon><text class="like-count data-v-92e3cfc8">{{$root.m0}}</text></view></view></view>