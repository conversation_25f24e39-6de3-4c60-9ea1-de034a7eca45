package com.yupi.springbootinit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yupi.springbootinit.config.CosClientConfig;
import com.yupi.springbootinit.mapper.*;
import com.yupi.springbootinit.model.dto.CommentDTO;
import com.yupi.springbootinit.model.dto.ReplyDTO;
import com.yupi.springbootinit.model.dto.UserDTO;
import com.yupi.springbootinit.model.entity.*;
import com.yupi.springbootinit.service.CacheService;
import com.yupi.springbootinit.service.CommentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 评论服务实现类
 */
@Service
@Slf4j
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {

    @Resource
    private CommentMapper commentMapper;

    @Resource
    private CommentReplyMapper commentReplyMapper;

    @Resource
    private CacheService cacheService;

    @Resource
    private CommentLikeMapper commentLikeMapper;

    @Resource
    private BaUserMapper baUserMapper;
    
    @Resource
    private CosClientConfig cosClientConfig;

    @Override
    public List<CommentDTO> getCommentList(String contentId, String filter, Long userId) {
        log.info("获取评论列表服务 - contentId: {}, filter: {}, userId: {}", contentId, filter, userId);
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Comment::getContentId, contentId)
                    .eq(Comment::getIsDelete, 0);

            // 根据筛选条件排序
            if ("new".equals(filter)) {
                queryWrapper.orderByDesc(Comment::getCreatedAt);
                log.info("按最新排序");
            } else if ("hot".equals(filter)) {
                queryWrapper.orderByDesc(Comment::getLikes);
                log.info("按热度排序");
            } else if ("my".equals(filter) && userId != null) {
                queryWrapper.eq(Comment::getUserId, userId);
                queryWrapper.orderByDesc(Comment::getCreatedAt);
                log.info("查询我的评论");
            } else {
                // 默认按热度排序
                queryWrapper.orderByDesc(Comment::getLikes);
                log.info("使用默认排序(热度)");
            }

            // 查询全部数据
            List<Comment> commentList = commentMapper.selectList(queryWrapper);
            log.info("评论查询结果 - 总条数: {}", commentList.size());

            // 转换为DTO
            List<CommentDTO> commentDTOList = new ArrayList<>();

            for (Comment comment : commentList) {
                log.debug("处理评论 - commentId: {}, userId: {}", comment.getId(), comment.getUserId());
                CommentDTO commentDTO = convertToDTO(comment, userId);
                
                // 获取评论的前两条回复作为预览
                List<ReplyDTO> previewReplies = getPreviewReplies(comment.getId(), userId, 2);
                log.debug("获取到评论预览回复 - commentId: {}, 回复数: {}", comment.getId(), previewReplies.size());
                commentDTO.setReplies(previewReplies);
                
                commentDTOList.add(commentDTO);
            }

            log.info("获取评论列表服务完成 - 总评论数: {}", commentDTOList.size());
            return commentDTOList;
        } catch (Exception e) {
            log.error("获取评论列表服务异常 - contentId: {}, 错误信息: {}", contentId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Page<CommentDTO> getCommentListWithPage(String contentId, String filter, Long userId, Integer current, Integer pageSize) {
        log.info("获取评论列表服务(分页) - contentId: {}, filter: {}, userId: {}, current: {}, pageSize: {}",
                contentId, filter, userId, current, pageSize);

        long startTime = System.currentTimeMillis();

        try {
            // 解析话题ID（简化版本，支持 "topic_123" 格式）
            Long topicId = null;
            if (contentId != null && contentId.startsWith("topic_")) {
                try {
                    topicId = Long.parseLong(contentId.substring(6));
                } catch (NumberFormatException e) {
                    log.warn("无法解析话题ID - contentId: {}", contentId);
                }
            }

            // 使用多级缓存查询评论列表
            if (topicId != null) {
                return cacheService.getCommentListWithCache(topicId, filter, userId, current, pageSize, () -> {
                    // 数据库查询逻辑（作为缓存未命中时的数据加载器）
                    return queryCommentListFromDatabase(contentId, filter, userId, current, pageSize);
                });
            } else {
                // 如果无法解析topicId，直接查询数据库
                return queryCommentListFromDatabase(contentId, filter, userId, current, pageSize);
            }

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("获取评论列表服务异常 - contentId: {}, 耗时: {}ms, 错误信息: {}",
                    contentId, (endTime - startTime), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从数据库查询评论列表（原有逻辑）
     */
    private Page<CommentDTO> queryCommentListFromDatabase(String contentId, String filter, Long userId, Integer current, Integer pageSize) {
        log.debug("从数据库查询评论列表 - contentId: {}, filter: {}", contentId, filter);

        // 构建查询条件
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Comment::getContentId, contentId)
                .eq(Comment::getIsDelete, 0);

        // 根据筛选条件排序
        if ("new".equals(filter)) {
            queryWrapper.orderByDesc(Comment::getCreatedAt);
            log.debug("按最新排序");
        } else if ("hot".equals(filter)) {
            queryWrapper.orderByDesc(Comment::getLikes);
            log.debug("按热度排序");
        } else if ("my".equals(filter) && userId != null) {
            queryWrapper.eq(Comment::getUserId, userId);
            queryWrapper.orderByDesc(Comment::getCreatedAt);
            log.debug("查询我的评论");
        } else {
            // 默认按热度排序
            queryWrapper.orderByDesc(Comment::getLikes);
            log.debug("使用默认排序(热度)");
        }

        // 分页查询
        Page<Comment> commentPage = new Page<>(current, pageSize);
        Page<Comment> resultPage = commentMapper.selectPage(commentPage, queryWrapper);

        log.info("评论分页查询结果 - 总条数: {}, 当前页: {}, 每页大小: {}, 总页数: {}",
                resultPage.getTotal(), resultPage.getCurrent(), resultPage.getSize(), resultPage.getPages());

        // 转换为DTO（批量优化）
        List<CommentDTO> commentDTOList = resultPage.getRecords().stream()
                .map(comment -> {
                    CommentDTO commentDTO = convertToDTOOptimized(comment, userId);

                    // 获取评论的前两条回复作为预览（异步优化）
                    List<ReplyDTO> previewReplies = getPreviewRepliesOptimized(comment.getId(), userId, 2);
                    commentDTO.setReplies(previewReplies);

                    return commentDTO;
                })
                .collect(Collectors.toList());

        // 创建结果分页对象
        Page<CommentDTO> resultDTOPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        resultDTOPage.setRecords(commentDTOList);
        resultDTOPage.setPages(resultPage.getPages());

        log.info("从数据库查询评论列表完成 - 总评论数: {}, 返回数: {}",
                resultDTOPage.getTotal(), commentDTOList.size());

        return resultDTOPage;
    }

    /**
     * 从数据库查询评论列表（基于话题ID）
     */
    private Page<CommentDTO> queryCommentListFromDatabaseByTopicId(Long topicId, String filter, Long userId, Integer current, Integer pageSize) {
        log.debug("从数据库查询评论列表 - topicId: {}, filter: {}", topicId, filter);

        // 构建查询条件
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Comment::getTopicId, topicId)
                .eq(Comment::getIsDelete, 0);

        // 根据筛选条件排序（优化：添加二级排序确保稳定性）
        if ("new".equals(filter)) {
            queryWrapper.orderByDesc(Comment::getCreatedAt)
                       .orderByDesc(Comment::getId); // 二级排序：ID降序
            log.debug("按最新排序（二级排序：ID）");
        } else if ("hot".equals(filter)) {
            queryWrapper.orderByDesc(Comment::getLikes)
                       .orderByDesc(Comment::getCreatedAt)  // 二级排序：创建时间
                       .orderByDesc(Comment::getId);        // 三级排序：ID
            log.debug("按热度排序（二级排序：创建时间，三级排序：ID）");
        } else if ("my".equals(filter) && userId != null) {
            queryWrapper.eq(Comment::getUserId, userId)
                       .orderByDesc(Comment::getCreatedAt)
                       .orderByDesc(Comment::getId); // 二级排序：ID降序
            log.debug("查询我的评论（二级排序：ID）");
        } else {
            // 默认按热度排序
            queryWrapper.orderByDesc(Comment::getLikes)
                       .orderByDesc(Comment::getCreatedAt)  // 二级排序：创建时间
                       .orderByDesc(Comment::getId);        // 三级排序：ID
            log.debug("使用默认排序(热度)（二级排序：创建时间，三级排序：ID）");
        }

        // 分页查询
        Page<Comment> commentPage = new Page<>(current, pageSize);
        Page<Comment> resultPage = commentMapper.selectPage(commentPage, queryWrapper);

        log.info("评论分页查询结果 - topicId: {}, filter: {}, 总条数: {}, 当前页: {}, 每页大小: {}, 总页数: {}, 实际返回: {}",
                topicId, filter, resultPage.getTotal(), resultPage.getCurrent(), resultPage.getSize(),
                resultPage.getPages(), resultPage.getRecords().size());

        // 数据完整性检查：验证是否有重复ID
        List<Long> commentIds = resultPage.getRecords().stream()
                .map(Comment::getId)
                .collect(Collectors.toList());
        Set<Long> uniqueIds = new HashSet<>(commentIds);

        if (commentIds.size() != uniqueIds.size()) {
            log.error("❌ 数据库查询返回重复评论 - topicId: {}, filter: {}, 总数: {}, 唯一ID数: {}",
                    topicId, filter, commentIds.size(), uniqueIds.size());
            // 记录重复的ID
            Map<Long, Long> idCounts = commentIds.stream()
                    .collect(Collectors.groupingBy(id -> id, Collectors.counting()));
            idCounts.entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .forEach(entry -> log.error("重复评论ID: {}, 出现次数: {}", entry.getKey(), entry.getValue()));
        }

        // 转换为DTO（批量优化）- 增强数据完整性检查
        List<CommentDTO> commentDTOList = resultPage.getRecords().stream()
                .filter(comment -> comment != null && comment.getId() != null) // 过滤无效数据
                .map(comment -> {
                    try {
                        CommentDTO commentDTO = convertToDTOOptimized(comment, userId);

                        // 验证转换结果
                        if (commentDTO == null || commentDTO.getId() == null) {
                            log.warn("⚠️ 评论DTO转换失败 - commentId: {}", comment.getId());
                            return null;
                        }

                        // 获取评论的前两条回复作为预览（异步优化）
                        List<ReplyDTO> previewReplies = getPreviewRepliesOptimized(comment.getId(), userId, 2);
                        commentDTO.setReplies(previewReplies);

                        return commentDTO;
                    } catch (Exception e) {
                        log.error("❌ 评论DTO转换异常 - commentId: {}, error: {}", comment.getId(), e.getMessage(), e);
                        return null;
                    }
                })
                .filter(dto -> dto != null) // 过滤转换失败的DTO
                .collect(Collectors.toList());

        // 验证转换后的数据完整性
        if (commentDTOList.size() != resultPage.getRecords().size()) {
            log.warn("⚠️ DTO转换数据丢失 - 原始: {}, 转换后: {}",
                    resultPage.getRecords().size(), commentDTOList.size());
        }

        // 创建结果分页对象
        Page<CommentDTO> resultDTOPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        resultDTOPage.setRecords(commentDTOList);
        resultDTOPage.setPages(resultPage.getPages());

        log.info("从数据库查询评论列表完成 - topicId: {}, 总评论数: {}, 返回数: {}",
                topicId, resultDTOPage.getTotal(), commentDTOList.size());

        return resultDTOPage;
    }

    @Override
    public Map<String, Object> getTopicCommentStats(Long topicId, Long userId) {
        log.info("🔢 获取话题评论统计 - topicId: {}, userId: {}", topicId, userId);

        Map<String, Object> stats = new HashMap<>();

        try {
            // 统计热门评论总数（所有评论）
            LambdaQueryWrapper<Comment> hotWrapper = new LambdaQueryWrapper<>();
            hotWrapper.eq(Comment::getTopicId, topicId)
                     .eq(Comment::getIsDelete, 0);
            Long hotTotal = commentMapper.selectCount(hotWrapper);

            // 统计最新评论总数（与热门相同，因为都是所有评论）
            Long newTotal = hotTotal;

            // 统计我的评论总数
            LambdaQueryWrapper<Comment> myWrapper = new LambdaQueryWrapper<>();
            myWrapper.eq(Comment::getTopicId, topicId)
                    .eq(Comment::getUserId, userId)
                    .eq(Comment::getIsDelete, 0);
            Long myTotal = commentMapper.selectCount(myWrapper);

            stats.put("hotTotal", hotTotal);
            stats.put("newTotal", newTotal);
            stats.put("myTotal", myTotal);

            log.info("🔢 话题评论统计完成 - topicId: {}, 热门: {}, 最新: {}, 我的: {}",
                    topicId, hotTotal, newTotal, myTotal);

        } catch (Exception e) {
            log.error("获取话题评论统计异常 - topicId: {}, userId: {}, 错误: {}",
                    topicId, userId, e.getMessage(), e);
            // 返回默认值
            stats.put("hotTotal", 0L);
            stats.put("newTotal", 0L);
            stats.put("myTotal", 0L);
        }

        return stats;
    }

    @Override
    public CommentDTO getCommentDetail(Long commentId, Long userId) {
        log.info("获取评论详情服务 - commentId: {}, userId: {}", commentId, userId);

        try {
            // 使用缓存查询评论详情
            return cacheService.getCommentDetailWithCache(commentId, userId, () -> {
                // 数据库查询逻辑（作为缓存未命中时的数据加载器）
                return queryCommentDetailFromDatabase(commentId, userId);
            });
        } catch (Exception e) {
            log.error("获取评论详情服务异常 - commentId: {}, 错误信息: {}", commentId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从数据库查询评论详情（原有逻辑）
     */
    private CommentDTO queryCommentDetailFromDatabase(Long commentId, Long userId) {
        log.debug("从数据库查询评论详情 - commentId: {}", commentId);

        Comment comment = commentMapper.selectById(commentId);
        if (comment == null) {
            log.warn("评论不存在 - commentId: {}", commentId);
            return null;
        }

        if (comment.getIsDelete() == 1) {
            log.warn("评论已删除 - commentId: {}", commentId);
            return null;
        }

        CommentDTO commentDTO = convertToDTO(comment, userId);
        log.info("从数据库查询评论详情完成 - commentId: {}", commentId);
        return commentDTO;
    }

    /**
     * 调试方法：直接查询数据库中的评论数据
     */
    public Map<String, Object> debugDatabaseQuery(String contentId) {
        log.info("🔍 调试数据库查询 - contentId: {}", contentId);

        Map<String, Object> debugInfo = new HashMap<>();

        try {
            // 1. 查询总数
            LambdaQueryWrapper<Comment> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.eq(Comment::getContentId, contentId)
                       .eq(Comment::getIsDelete, 0);
            Long totalCount = commentMapper.selectCount(countWrapper);
            debugInfo.put("totalCount", totalCount);

            // 2. 查询前5条记录
            LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Comment::getContentId, contentId)
                       .eq(Comment::getIsDelete, 0)
                       .orderByDesc(Comment::getCreatedAt)
                       .last("LIMIT 5");
            List<Comment> comments = commentMapper.selectList(queryWrapper);
            debugInfo.put("sampleComments", comments);
            debugInfo.put("sampleCount", comments.size());

            // 3. 检查是否有任何评论数据
            LambdaQueryWrapper<Comment> allWrapper = new LambdaQueryWrapper<>();
            allWrapper.eq(Comment::getIsDelete, 0)
                     .last("LIMIT 10");
            List<Comment> allComments = commentMapper.selectList(allWrapper);
            debugInfo.put("allCommentsCount", allComments.size());
            debugInfo.put("allCommentsSample", allComments.stream()
                    .map(c -> "contentId: " + c.getContentId() + ", topicId: " + c.getTopicId())
                    .collect(Collectors.toList()));

            log.info("🔍 数据库查询结果 - contentId: {}, 总数: {}, 样本数: {}, 全部评论数: {}",
                    contentId, totalCount, comments.size(), allComments.size());

        } catch (Exception e) {
            log.error("调试数据库查询异常 - contentId: {}, 错误: {}", contentId, e.getMessage(), e);
            debugInfo.put("error", e.getMessage());
        }

        return debugInfo;
    }

    /**
     * 创建测试评论数据
     */
    public int createTestCommentData(Long topicId) {
        log.info("🔧 创建测试评论数据 - topicId: {}", topicId);

        try {
            String contentId = "topic_" + topicId;
            int count = 0;

            // 创建5条测试评论
            for (int i = 1; i <= 5; i++) {
                Comment comment = new Comment();
                comment.setContentId(contentId);
                comment.setTopicId(topicId);
                comment.setUserId(123L + i); // 测试用户ID
                comment.setContent("这是话题" + topicId + "的第" + i + "条测试评论内容");
                comment.setLikes(i * 10); // 不同的点赞数
                // 时间字段由MyBatis-Plus自动填充
                comment.setIsDelete(0);

                commentMapper.insert(comment);
                count++;

                log.info("✅ 创建测试评论 - id: {}, contentId: {}, content: {}",
                        comment.getId(), comment.getContentId(), comment.getContent());
            }

            log.info("🎉 成功创建 {} 条测试评论数据", count);
            return count;

        } catch (Exception e) {
            log.error("创建测试评论数据失败 - topicId: {}, 错误: {}", topicId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Page<CommentDTO> getCommentListByTopicIdWithPage(Long topicId, String filter, Long userId, Integer current, Integer pageSize) {
        log.info("根据话题ID获取评论列表服务(分页) - topicId: {}, filter: {}, userId: {}, current: {}, pageSize: {}",
                topicId, filter, userId, current, pageSize);

        long startTime = System.currentTimeMillis();

        try {
            // 直接使用话题ID查询，不再依赖contentId
            return cacheService.getCommentListWithCache(topicId, filter, userId, current, pageSize, () -> {
                // 数据库查询逻辑（作为缓存未命中时的数据加载器）
                return queryCommentListFromDatabaseByTopicId(topicId, filter, userId, current, pageSize);
            });

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("根据话题ID获取评论列表服务异常 - topicId: {}, 耗时: {}ms, 错误信息: {}",
                    topicId, (endTime - startTime), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createComment(String contentId, String content, Long userId, Long topicId) {
        log.info("创建评论服务 - contentId: {}, content: {}, userId: {}", contentId, content, userId);
        
        try {
            // 获取用户信息
            BaUser user = baUserMapper.selectById(userId);
            
            Comment comment = new Comment();
            comment.setContentId(contentId);
            comment.setContent(content);
            comment.setUserId(userId);
            comment.setLikes(0);
            comment.setReplyCount(0);
            comment.setTopicId(topicId);

            // 根据topicId判断内容类型
            if (topicId != null) {
                comment.setContentType("topic");
            } else {
                // 如果没有topicId，判断是否为帖子评论
                if (contentId != null && contentId.matches("\\d+")) {
                    comment.setContentType("post");
                } else {
                    comment.setContentType("general");
                }
            }
            
            // 设置用户昵称和头像
            if (user != null) {
                comment.setNickname(user.getNickname());
                comment.setAvatar(user.getAvatar());
            }
            
            // 时间字段由MyBatis-Plus自动填充，不需要手动设置
            comment.setIsDelete(0);
            
            log.debug("准备插入评论数据: {}", comment);
            commentMapper.insert(comment);

            // 清除相关话题的评论缓存
            if (topicId != null) {
                cacheService.evictTopicCommentCache(topicId);
                log.info("已清除话题评论缓存 - topicId: {}", topicId);
            }

            log.info("创建评论服务完成 - 新评论ID: {}", comment.getId());
            return comment.getId();
        } catch (Exception e) {
            log.error("创建评论服务异常 - contentId: {}, userId: {}, 错误信息: {}", contentId, userId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LikeResult likeComment(Long commentId, Long userId, String action) {
        log.info("点赞/取消点赞评论服务 - commentId: {}, userId: {}, action: {}", commentId, userId, action);
        
        try {
            Comment comment = commentMapper.selectById(commentId);
            if (comment == null) {
                log.warn("评论不存在 - commentId: {}", commentId);
                throw new RuntimeException("评论不存在");
            }
            
            if (comment.getIsDelete() == 1) {
                log.warn("评论已删除 - commentId: {}", commentId);
                throw new RuntimeException("评论不存在");
            }

            // 使用自定义方法查询点赞记录（包括已删除的）
            CommentLike like = commentLikeMapper.findByCommentIdAndUserId(commentId, userId);
            log.debug("查询点赞记录(包括已删除) - commentId: {}, userId: {}, 点赞记录: {}", commentId, userId, like);

            boolean isLiked = false;
            int likes = comment.getLikes();

            if ("like".equals(action)) {
                // 添加空值检查
                if (like == null || like.getIsDelete() == null) {
                    // 没有点赞记录，创建新记录
                    CommentLike commentLike = new CommentLike();
                    commentLike.setCommentId(commentId);
                    commentLike.setUserId(userId);
                    // 创建时间由MyBatis-Plus自动填充
                    commentLike.setIsDelete(0);
                    commentLikeMapper.insert(commentLike);
                    log.debug("创建点赞记录 - commentId: {}, userId: {}", commentId, userId);

                    // 更新评论点赞数 - 只更新likes字段，避免时间字段冲突
                    likes = likes + 1;
                    UpdateWrapper<Comment> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("id", commentId)
                                .eq("is_delete", 0)
                                .set("likes", likes);
                    commentMapper.update(null, updateWrapper);
                    log.debug("更新评论点赞数 - commentId: {}, 新点赞数: {}", commentId, likes);
                    isLiked = true;
                } else if (like.getIsDelete() == 1) {
                    // 有已删除的点赞记录，恢复记录
                    int rows = commentLikeMapper.restoreByCommentIdAndUserId(commentId, userId);
                    log.debug("恢复已删除的点赞记录 - commentId: {}, userId: {}, 影响行数: {}", commentId, userId, rows);

                    // 更新评论点赞数 - 只更新likes字段，避免时间字段冲突
                    likes = likes + 1;
                    UpdateWrapper<Comment> updateWrapper2 = new UpdateWrapper<>();
                    updateWrapper2.eq("id", commentId)
                                 .eq("is_delete", 0)
                                 .set("likes", likes);
                    commentMapper.update(null, updateWrapper2);
                    log.debug("更新评论点赞数 - commentId: {}, 新点赞数: {}", commentId, likes);
                    isLiked = true;
                } else {
                    log.debug("用户已点赞，无需重复操作 - commentId: {}, userId: {}", commentId, userId);
                    isLiked = true;
                }
            } else if ("unlike".equals(action)) {
                // 添加空值检查
                if (like != null && like.getIsDelete() != null && like.getIsDelete() == 0) {
                    // 有点赞记录且未删除，标记为删除
                    int rows = commentLikeMapper.softDeleteByCommentIdAndUserId(commentId, userId);
                    log.debug("软删除点赞记录 - commentId: {}, userId: {}, 影响行数: {}", commentId, userId, rows);

                    // 更新评论点赞数 - 只更新likes字段，避免时间字段冲突
                    likes = Math.max(0, likes - 1);
                    UpdateWrapper<Comment> updateWrapper3 = new UpdateWrapper<>();
                    updateWrapper3.eq("id", commentId)
                                 .eq("is_delete", 0)
                                 .set("likes", likes);
                    commentMapper.update(null, updateWrapper3);
                    log.debug("更新评论点赞数 - commentId: {}, 新点赞数: {}", commentId, likes);
                    isLiked = false;
                } else {
                    log.debug("用户未点赞，无需取消 - commentId: {}, userId: {}", commentId, userId);
                    isLiked = false;
                }
            }

            // 清除评论详情缓存（因为点赞状态发生变化）
            cacheService.evictCommentCache(commentId);
            log.debug("已清除评论详情缓存 - commentId: {}", commentId);

            // 清除话题评论列表缓存（因为评论点赞数发生变化，影响列表显示）
            if (comment.getTopicId() != null) {
                cacheService.evictTopicCommentCache(comment.getTopicId());
                log.debug("已清除话题评论列表缓存 - topicId: {}", comment.getTopicId());
            }

            log.info("点赞/取消点赞评论服务完成 - commentId: {}, 点赞数: {}, 是否已点赞: {}", commentId, likes, isLiked);
            return new LikeResult(likes, isLiked);
        } catch (Exception e) {
            log.error("点赞/取消点赞评论服务异常 - commentId: {}, userId: {}, 错误信息: {}", commentId, userId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteComment(Long commentId, Long userId) {
        log.info("删除评论服务 - commentId: {}, userId: {}", commentId, userId);
        
        try {
            // 查询评论
            Comment comment = commentMapper.selectById(commentId);
            if (comment == null) {
                log.warn("评论不存在 - commentId: {}", commentId);
                return false;
            }
            
            // 检查是否已删除
            if (comment.getIsDelete() == 1) {
                log.warn("评论已删除 - commentId: {}", commentId);
                return false;
            }
            
            // 检查是否是评论者本人
            if (!comment.getUserId().equals(userId)) {
                log.warn("非评论者本人，无权删除 - commentId: {}, commentUserId: {}, currentUserId: {}", 
                        commentId, comment.getUserId(), userId);
                return false;
            }
            
            // 使用直接的软删除方法
            int rows = commentMapper.softDeleteById(commentId);

            if (rows > 0) {
                // 清除相关缓存
                cacheService.evictCommentCache(commentId);
                if (comment.getTopicId() != null) {
                    cacheService.evictTopicCommentCache(comment.getTopicId());
                }
                cacheService.evictUserCommentCache(userId);
                log.info("已清除评论相关缓存 - commentId: {}, topicId: {}", commentId, comment.getTopicId());
            }

            log.info("删除评论服务完成 - commentId: {}, 影响行数: {}", commentId, rows);
            return rows > 0;
        } catch (Exception e) {
            log.error("删除评论服务异常 - commentId: {}, userId: {}, 错误信息: {}", commentId, userId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 将评论实体转换为DTO（优化版本）
     */
    private CommentDTO convertToDTOOptimized(Comment comment, Long userId) {
        log.debug("转换评论实体到DTO(优化版) - commentId: {}", comment.getId());

        try {
            CommentDTO dto = new CommentDTO();
            BeanUtils.copyProperties(comment, dto);

            // 设置基础字段
            dto.setCreatedAt(comment.getCreatedAt());
            dto.setReplyCount(comment.getReplyCount());
            dto.setNickname(comment.getNickname());
            dto.setAvatar(comment.getAvatar());
            dto.setIsOwner(userId != null && userId.equals(comment.getUserId()));

            // 批量查询用户信息（减少数据库查询）
            BaUser user = baUserMapper.selectById(comment.getUserId());
            if (user != null) {
                UserDTO userDTO = new UserDTO();
                userDTO.setId(user.getId());
                userDTO.setNickname(user.getNickname());

                // 优化头像URL处理
                String avatar = processAvatarUrl(user.getAvatar());
                userDTO.setAvatar(avatar);
                userDTO.setLevel(user.getLevel());

                dto.setUser(userDTO);

                // 填充缺失字段
                if (StringUtils.isBlank(dto.getNickname())) {
                    dto.setNickname(user.getNickname());
                }
                if (StringUtils.isBlank(dto.getAvatar())) {
                    dto.setAvatar(avatar);
                }
            }

            // 优化点赞状态查询
            if (userId != null) {
                boolean isLiked = checkUserLikedComment(comment.getId(), userId);
                dto.setIsLiked(isLiked);
            } else {
                dto.setIsLiked(false);
            }

            return dto;
        } catch (Exception e) {
            log.error("转换评论实体到DTO(优化版)异常 - commentId: {}, 错误信息: {}", comment.getId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 将评论实体转换为DTO
     */
    private CommentDTO convertToDTO(Comment comment, Long userId) {
        log.debug("转换评论实体到DTO - commentId: {}", comment.getId());
        
        try {
            CommentDTO dto = new CommentDTO();
            BeanUtils.copyProperties(comment, dto);
            
            // 设置创建时间
            dto.setCreatedAt(comment.getCreatedAt());
            
            // 设置回复数量
            dto.setReplyCount(comment.getReplyCount());
            
            // 设置nickname和avatar（优先使用表中存储的值）
            dto.setNickname(comment.getNickname());
            dto.setAvatar(comment.getAvatar());
            
            // 设置当前用户是否是评论的发布者
            dto.setIsOwner(userId != null && userId.equals(comment.getUserId()));
            
            // 查询用户信息
            BaUser user = baUserMapper.selectById(comment.getUserId());
            if (user != null) {
                log.debug("获取到评论用户信息 - userId: {}, userName: {}", user.getId(), user.getNickname());
                UserDTO userDTO = new UserDTO();
                userDTO.setId(user.getId());
                userDTO.setNickname(user.getNickname());

                
                // 处理头像URL
                String avatar = user.getAvatar();
                if (StringUtils.isNotBlank(avatar)) {
                    // 如果是相对路径，拼接COS基础URL
                    if (avatar.startsWith("/")) {
                        avatar = cosClientConfig.getBaseUrl() + avatar;
                        log.debug("处理用户头像URL - 原始URL: {}, 处理后URL: {}", user.getAvatar(), avatar);
                    }
                }
                userDTO.setAvatar(avatar);
                
//                // 设置用户等级 - 默认为1
//                Integer userLevel = 0;
//                userDTO.setLevel(userLevel);
                userDTO.setLevel(user.getLevel());
                log.info("用户等级: {}", user.getLevel());
                
                dto.setUser(userDTO);
                
                // 如果表中没有nickname和avatar，则使用用户信息中的值
                if (StringUtils.isBlank(dto.getNickname())) {
                    dto.setNickname(user.getNickname());
                }
                
                if (StringUtils.isBlank(dto.getAvatar())) {
                    dto.setAvatar(avatar);
                }
            } else {
                log.warn("未找到评论用户信息 - userId: {}", comment.getUserId());
            }
            
            // 查询当前用户是否已点赞
            if (userId != null) {
                LambdaQueryWrapper<CommentLike> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CommentLike::getCommentId, comment.getId())
                        .eq(CommentLike::getUserId, userId);
                long count = commentLikeMapper.selectCount(queryWrapper);
                dto.setIsLiked(count > 0);
                log.debug("查询用户是否点赞 - commentId: {}, userId: {}, isLiked: {}", comment.getId(), userId, count > 0);
            } else {
                dto.setIsLiked(false);
            }
            
            return dto;
        } catch (Exception e) {
            log.error("转换评论实体到DTO异常 - commentId: {}, 错误信息: {}", comment.getId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取评论的预览回复
     */
    private List<ReplyDTO> getPreviewReplies(Long commentId, Long userId, int limit) {
        log.debug("获取评论预览回复 - commentId: {}, limit: {}", commentId, limit);
        
        try {
            LambdaQueryWrapper<CommentReply> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CommentReply::getCommentId, commentId)
                    .eq(CommentReply::getIsDelete, 0)
                    .orderByDesc(CommentReply::getCreatedAt)
                    .last("LIMIT " + limit);
            
            List<CommentReply> replies = commentReplyMapper.selectList(queryWrapper);
            log.debug("获取到评论回复数据 - commentId: {}, 回复数: {}", commentId, replies.size());
            
            return replies.stream().map(reply -> {
                ReplyDTO dto = new ReplyDTO();
                BeanUtils.copyProperties(reply, dto);
                dto.setCreatedAt(reply.getCreatedAt());
                
                // 设置nickname和avatar（优先使用表中存储的值）
                dto.setNickname(reply.getNickname());
                dto.setAvatar(reply.getAvatar());
                
                // 设置回复用户信息
                BaUser replyUser = baUserMapper.selectById(reply.getUserId());
                if (replyUser != null) {
                    log.debug("获取到回复用户信息 - userId: {}, userName: {}", replyUser.getId(), replyUser.getNickname());
                    UserDTO userDTO = new UserDTO();
                    userDTO.setId(replyUser.getId());
                    userDTO.setNickname(replyUser.getNickname());
                    
                    // 处理头像URL
                    String avatar = replyUser.getAvatar();
                    if (StringUtils.isNotBlank(avatar)) {
                        // 如果是相对路径，拼接COS基础URL
                        if (avatar.startsWith("/")) {
                            avatar = cosClientConfig.getBaseUrl() + avatar;
                            log.debug("处理用户头像URL - 原始URL: {}, 处理后URL: {}", replyUser.getAvatar(), avatar);
                        }
                    }
                    userDTO.setAvatar(avatar);
                    
                    // 设置用户等级 - 默认为1
                    userDTO.setLevel(1);
                    
                    dto.setUser(userDTO);
                    
                    // 如果表中没有nickname和avatar，则使用用户信息中的值
                    if (StringUtils.isBlank(dto.getNickname())) {
                        dto.setNickname(replyUser.getNickname());
                    }
                    
                    if (StringUtils.isBlank(dto.getAvatar())) {
                        dto.setAvatar(avatar);
                    }
                } else {
                    log.warn("未找到回复用户信息 - userId: {}", reply.getUserId());
                }
                
                // 设置被回复用户信息
                if (reply.getReplyToId() != null) {
                    BaUser replyToUser = baUserMapper.selectById(reply.getReplyToId());
                    if (replyToUser != null) {
                        log.debug("获取到被回复用户信息 - userId: {}, userName: {}", replyToUser.getId(), replyToUser.getNickname());
                        UserDTO replyToDTO = new UserDTO();
                        replyToDTO.setId(replyToUser.getId());
                        replyToDTO.setNickname(replyToUser.getNickname());
                        
                        // 处理头像URL
                        String avatar = replyToUser.getAvatar();
                        if (StringUtils.isNotBlank(avatar)) {
                            // 如果是相对路径，拼接COS基础URL
                            if (avatar.startsWith("/")) {
                                avatar = cosClientConfig.getBaseUrl() + avatar;
                                log.debug("处理用户头像URL - 原始URL: {}, 处理后URL: {}", replyToUser.getAvatar(), avatar);
                            }
                        }
                        replyToDTO.setAvatar(avatar);
                        
                        // 设置用户等级 - 默认为1
                        replyToDTO.setLevel(1);
                        
                        dto.setReplyTo(replyToDTO);
                    } else {
                        log.warn("未找到被回复用户信息 - userId: {}", reply.getReplyToId());
                    }
                }
                
                return dto;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取评论预览回复异常 - commentId: {}, 错误信息: {}", commentId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<CommentDTO> getCommentListByTopicId(Long topicId, String filter, Long userId) {
        log.info("获取话题评论列表服务 - topicId: {}, filter: {}, userId: {}", topicId, filter, userId);
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Comment::getTopicId, topicId)
                    .eq(Comment::getIsDelete, 0);

            // 根据筛选条件排序
            if ("new".equals(filter)) {
                queryWrapper.orderByDesc(Comment::getCreatedAt);
                log.info("按最新排序");
            } else if ("hot".equals(filter)) {
                queryWrapper.orderByDesc(Comment::getLikes);
                log.info("按热度排序");
            } else if ("my".equals(filter) && userId != null) {
                queryWrapper.eq(Comment::getUserId, userId);
                queryWrapper.orderByDesc(Comment::getCreatedAt);
                log.info("查询我的评论");
            } else {
                // 默认按热度排序
                queryWrapper.orderByDesc(Comment::getLikes);
                log.info("使用默认排序(热度)");
            }

            // 查询全部数据
            List<Comment> commentList = commentMapper.selectList(queryWrapper);
            log.info("话题评论查询结果 - 总条数: {}", commentList.size());

            // 转换为DTO
            List<CommentDTO> commentDTOList = new ArrayList<>();

            for (Comment comment : commentList) {
                log.debug("处理评论 - commentId: {}, userId: {}", comment.getId(), comment.getUserId());
                CommentDTO commentDTO = convertToDTO(comment, userId);
                
                // 获取评论的前两条回复作为预览
                List<ReplyDTO> previewReplies = getPreviewReplies(comment.getId(), userId, 2);
                log.debug("获取到评论预览回复 - commentId: {}, 回复数: {}", comment.getId(), previewReplies.size());
                commentDTO.setReplies(previewReplies);
                
                commentDTOList.add(commentDTO);
            }

            log.info("获取话题评论列表服务完成 - 总评论数: {}", commentDTOList.size());
            return commentDTOList;
        } catch (Exception e) {
            log.error("获取话题评论列表服务异常 - topicId: {}, 错误信息: {}", topicId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTopicComment(Long topicId, String content, Long userId) {
        log.info("创建话题评论服务 - topicId: {}, content: {}, userId: {}", topicId, content, userId);
        
        try {
            // 获取用户信息
            BaUser user = baUserMapper.selectById(userId);
            
            Comment comment = new Comment();
            comment.setContentId("topic_" + topicId); // 设置content_id为话题ID格式
            comment.setTopicId(topicId);
            comment.setContent(content);
            comment.setUserId(userId);
            comment.setLikes(0);
            comment.setReplyCount(0);
            
            // 设置用户昵称和头像
            if (user != null) {
                comment.setNickname(user.getNickname());
                comment.setAvatar(user.getAvatar());
            }
            
            // 时间字段由MyBatis-Plus自动填充，不需要手动设置
            comment.setIsDelete(0);
            
            log.debug("准备插入话题评论数据: {}", comment);
            commentMapper.insert(comment);

            // 清除话题评论列表缓存（新评论创建后，所有筛选条件的缓存都需要更新）
            cacheService.evictTopicCommentCache(topicId);
            log.info("已清除话题评论列表缓存 - topicId: {}", topicId);

            log.info("创建话题评论服务完成 - 新评论ID: {}", comment.getId());
            return comment.getId();
        } catch (Exception e) {
            log.error("创建话题评论服务异常 - topicId: {}, userId: {}, 错误信息: {}", topicId, userId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 优化的头像URL处理
     */
    private String processAvatarUrl(String avatar) {
        if (StringUtils.isNotBlank(avatar) && avatar.startsWith("/")) {
            return cosClientConfig.getBaseUrl() + avatar;
        }
        return avatar;
    }

    /**
     * 优化的用户点赞状态检查
     */
    private boolean checkUserLikedComment(Long commentId, Long userId) {
        LambdaQueryWrapper<CommentLike> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommentLike::getCommentId, commentId)
                .eq(CommentLike::getUserId, userId)
                .eq(CommentLike::getIsDelete, 0);
        return commentLikeMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 优化的预览回复获取
     */
    private List<ReplyDTO> getPreviewRepliesOptimized(Long commentId, Long userId, int limit) {
        log.debug("获取评论预览回复(优化版) - commentId: {}, limit: {}", commentId, limit);

        try {
            LambdaQueryWrapper<CommentReply> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CommentReply::getCommentId, commentId)
                    .eq(CommentReply::getIsDelete, 0)
                    .orderByDesc(CommentReply::getCreatedAt)
                    .last("LIMIT " + limit);

            List<CommentReply> replies = commentReplyMapper.selectList(queryWrapper);

            return replies.stream().map(reply -> {
                ReplyDTO dto = new ReplyDTO();
                BeanUtils.copyProperties(reply, dto);
                dto.setCreatedAt(reply.getCreatedAt());
                dto.setNickname(reply.getNickname());
                dto.setAvatar(reply.getAvatar());

                // 简化用户信息处理
                BaUser replyUser = baUserMapper.selectById(reply.getUserId());
                if (replyUser != null) {
                    UserDTO userDTO = new UserDTO();
                    userDTO.setId(replyUser.getId());
                    userDTO.setNickname(replyUser.getNickname());
                    userDTO.setAvatar(processAvatarUrl(replyUser.getAvatar()));
                    userDTO.setLevel(replyUser.getLevel());
                    dto.setUser(userDTO);
                }

                return dto;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取评论预览回复(优化版)异常 - commentId: {}, 错误信息: {}", commentId, e.getMessage(), e);
            return new ArrayList<>(); // 返回空列表而不是抛异常
        }
    }

    @Override
    public Page<CommentDTO> getCommentListByStoreIdWithPage(Long storeId, String filter, Long userId, Integer current, Integer pageSize) {
        log.info("根据店铺ID获取评论列表服务(分页) - storeId: {}, filter: {}, userId: {}, current: {}, pageSize: {}",
                storeId, filter, userId, current, pageSize);

        long startTime = System.currentTimeMillis();

        try {
            // 暂时直接查询数据库，后续可以添加缓存
            return queryCommentListFromDatabaseByStoreId(storeId, filter, userId, current, pageSize);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("根据店铺ID获取评论列表服务异常 - storeId: {}, 耗时: {}ms, 错误信息: {}",
                    storeId, (endTime - startTime), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从数据库查询评论列表（基于店铺ID）
     */
    private Page<CommentDTO> queryCommentListFromDatabaseByStoreId(Long storeId, String filter, Long userId, Integer current, Integer pageSize) {
        log.debug("从数据库查询店铺评论列表 - storeId: {}, filter: {}", storeId, filter);

        // 构建查询条件
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Comment::getStoreId, storeId)
                .eq(Comment::getIsDelete, 0);

        // 根据筛选条件排序
        if ("new".equals(filter)) {
            queryWrapper.orderByDesc(Comment::getCreatedAt)
                       .orderByDesc(Comment::getId);
            log.debug("按最新排序（二级排序：ID）");
        } else if ("hot".equals(filter)) {
            queryWrapper.orderByDesc(Comment::getLikes)
                       .orderByDesc(Comment::getCreatedAt)
                       .orderByDesc(Comment::getId);
            log.debug("按热度排序（二级排序：创建时间，三级排序：ID）");
        } else if ("my".equals(filter) && userId != null) {
            queryWrapper.eq(Comment::getUserId, userId)
                       .orderByDesc(Comment::getCreatedAt)
                       .orderByDesc(Comment::getId);
            log.debug("查询我的评论（二级排序：ID）");
        } else {
            // 默认按热度排序
            queryWrapper.orderByDesc(Comment::getLikes)
                       .orderByDesc(Comment::getCreatedAt)
                       .orderByDesc(Comment::getId);
            log.debug("使用默认排序(热度)（二级排序：创建时间，三级排序：ID）");
        }

        // 分页查询
        Page<Comment> page = new Page<>(current, pageSize);
        Page<Comment> commentPage = this.page(page, queryWrapper);

        log.debug("数据库查询结果 - 总数: {}, 当前页: {}, 返回数量: {}",
                commentPage.getTotal(), commentPage.getCurrent(), commentPage.getRecords().size());

        // 转换为DTO并设置点赞状态
        List<CommentDTO> commentDTOs = commentPage.getRecords().stream().map(comment -> {
            CommentDTO dto = convertToDTO(comment, userId);
            return dto;
        }).collect(Collectors.toList());

        // 创建结果分页对象
        Page<CommentDTO> resultPage = new Page<>();
        resultPage.setRecords(commentDTOs);
        resultPage.setTotal(commentPage.getTotal());
        resultPage.setCurrent(commentPage.getCurrent());
        resultPage.setSize(commentPage.getSize());
        resultPage.setPages(commentPage.getPages());

        return resultPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStoreComment(Long storeId, String content, Long userId) {
        log.info("创建店铺评论服务 - storeId: {}, content: {}, userId: {}", storeId, content, userId);

        try {
            // 获取用户信息
            BaUser user = baUserMapper.selectById(userId);

            Comment comment = new Comment();
            // 为店铺评论生成content_id
            comment.setContentId("store_" + storeId + "_" + System.currentTimeMillis());
            comment.setStoreId(storeId);
            comment.setContent(content);
            comment.setUserId(userId);
            comment.setTopicId(9L);
            comment.setLikes(0);
            comment.setReplyCount(0);

            // 设置用户昵称和头像
            if (user != null) {
                comment.setNickname(user.getNickname());
                comment.setAvatar(user.getAvatar());
            }

            log.info("店铺评论数据准备完成 - contentId: {}, storeId: {}, userId: {}",
                    comment.getContentId(), storeId, userId);

            // 保存评论
            boolean saved = this.save(comment);
            if (!saved) {
                throw new RuntimeException("保存店铺评论失败");
            }

            log.info("店铺评论创建成功 - commentId: {}, contentId: {}, storeId: {}, userId: {}",
                    comment.getId(), comment.getContentId(), storeId, userId);

            // 暂时不清除缓存，后续可以添加店铺评论缓存功能
            // cacheService.evictStoreCommentCache(storeId);

            return comment.getId();

        } catch (Exception e) {
            log.error("创建店铺评论服务异常 - storeId: {}, userId: {}, 错误信息: {}", storeId, userId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Map<String, Object> getStoreCommentStats(Long storeId, Long userId) {
        log.info("获取店铺评论统计 - storeId: {}, userId: {}", storeId, userId);

        try {
            Map<String, Object> stats = new HashMap<>();

            // 获取热门评论总数
            LambdaQueryWrapper<Comment> hotWrapper = new LambdaQueryWrapper<>();
            hotWrapper.eq(Comment::getStoreId, storeId)
                     .eq(Comment::getIsDelete, 0);
            long hotTotal = this.count(hotWrapper);

            // 获取最新评论总数（与热门相同）
            long newTotal = hotTotal;

            // 获取我的评论总数
            long myTotal = 0;
            if (userId != null) {
                LambdaQueryWrapper<Comment> myWrapper = new LambdaQueryWrapper<>();
                myWrapper.eq(Comment::getStoreId, storeId)
                        .eq(Comment::getUserId, userId)
                        .eq(Comment::getIsDelete, 0);
                myTotal = this.count(myWrapper);
            }

            stats.put("hotTotal", hotTotal);
            stats.put("newTotal", newTotal);
            stats.put("myTotal", myTotal);

            log.info("店铺评论统计结果 - storeId: {}, 热门: {}, 最新: {}, 我的: {}",
                    storeId, hotTotal, newTotal, myTotal);

            return stats;

        } catch (Exception e) {
            log.error("获取店铺评论统计异常 - storeId: {}, userId: {}, 错误信息: {}",
                    storeId, userId, e.getMessage(), e);
            throw e;
        }
    }
}
