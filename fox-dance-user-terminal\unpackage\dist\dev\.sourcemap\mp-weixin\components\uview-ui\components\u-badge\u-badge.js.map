{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-badge/u-badge.vue?8b7f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-badge/u-badge.vue?2c01", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-badge/u-badge.vue?07f6", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-badge/u-badge.vue?67fe", "uni-app:///components/uview-ui/components/u-badge/u-badge.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-badge/u-badge.vue?fab7", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-badge/u-badge.vue?115d"], "names": ["name", "props", "type", "default", "size", "isDot", "count", "overflowCount", "showZero", "offset", "absolute", "fontSize", "color", "bgColor", "isCenter", "computed", "boxStyle", "style", "showText", "show"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgM;AAChM,gBAAgB,8LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7BA;AAAA;AAAA;AAAA;AAAqvB,CAAgB,ssBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmBzwB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,gBAiBA;EACAA;EACAC;IACA;IACAC;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;IACA;IACA;IACAK;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACA;IACAC;MACA;MACA;QACAC;QACAA;QACA;QACAA;MACA;QACAA;QACAA;QACAA;MACA;MACA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAC;MACA,+BACA;QACA,oFACA;MACA;IACA;IACA;IACAC;MACA;MACA,iEACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACxIA;AAAA;AAAA;AAAA;AAA46C,CAAgB,ixCAAG,EAAC,C;;;;;;;;;;;ACAh8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-badge/u-badge.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-badge.vue?vue&type=template&id=40f94fea&scoped=true&\"\nvar renderjs\nimport script from \"./u-badge.vue?vue&type=script&lang=js&\"\nexport * from \"./u-badge.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-badge.vue?vue&type=style&index=0&id=40f94fea&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"40f94fea\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-badge/u-badge.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-badge.vue?vue&type=template&id=40f94fea&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.show\n    ? _vm.__get_style([\n        {\n          top: _vm.offset[0] + \"rpx\",\n          right: _vm.offset[1] + \"rpx\",\n          fontSize: _vm.fontSize + \"rpx\",\n          position: _vm.absolute ? \"absolute\" : \"static\",\n          color: _vm.color,\n          backgroundColor: _vm.bgColor,\n        },\n        _vm.boxStyle,\n      ])\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-badge.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-badge.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"show\" class=\"u-badge\" :class=\"[\r\n\t\t\tisDot ? 'u-badge-dot' : '', \r\n\t\t\tsize == 'mini' ? 'u-badge-mini' : '',\r\n\t\t\ttype ? 'u-badge--bg--' + type : ''\r\n\t\t]\" :style=\"[{\r\n\t\t\ttop: offset[0] + 'rpx',\r\n\t\t\tright: offset[1] + 'rpx',\r\n\t\t\tfontSize: fontSize + 'rpx',\r\n\t\t\tposition: absolute ? 'absolute' : 'static',\r\n\t\t\tcolor: color,\r\n\t\t\tbackgroundColor: bgColor\r\n\t\t}, boxStyle]\"\r\n\t>\r\n\t\t{{showText}}\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * badge 角标\r\n\t * @description 本组件一般用于展示头像的地方，如个人中心，或者评论列表页的用户头像展示等场所。\r\n\t * @tutorial https://www.uviewui.com/components/badge.html\r\n\t * @property {String Number} count 展示的数字，大于 overflowCount 时显示为 ${overflowCount}+，为0且show-zero为false时隐藏\r\n\t * @property {Boolean} is-dot 不展示数字，只有一个小点（默认false）\r\n\t * @property {Boolean} absolute 组件是否绝对定位，为true时，offset参数才有效（默认true）\r\n\t * @property {String Number} overflow-count 展示封顶的数字值（默认99）\r\n\t * @property {String} type 使用预设的背景颜色（默认error）\r\n\t * @property {Boolean} show-zero 当数值为 0 时，是否展示 Badge（默认false）\r\n\t * @property {String} size Badge的尺寸，设为mini会得到小一号的Badge（默认default）\r\n\t * @property {Array} offset 设置badge的位置偏移，格式为 [x, y]，也即设置的为top和right的值，单位rpx。absolute为true时有效（默认[20, 20]）\r\n\t * @property {String} color 字体颜色（默认#ffffff）\r\n\t * @property {String} bgColor 背景颜色，优先级比type高，如设置，type参数会失效\r\n\t * @property {Boolean} is-center 组件中心点是否和父组件右上角重合，优先级比offset高，如设置，offset参数会失效（默认false）\r\n\t * @example <u-badge type=\"error\" count=\"7\"></u-badge>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-badge',\r\n\t\tprops: {\r\n\t\t\t// primary,warning,success,error,info\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'error'\r\n\t\t\t},\r\n\t\t\t// default, mini\r\n\t\t\tsize: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'default'\r\n\t\t\t},\r\n\t\t\t//是否是圆点\r\n\t\t\tisDot: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 显示的数值内容\r\n\t\t\tcount: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t},\r\n\t\t\t// 展示封顶的数字值\r\n\t\t\toverflowCount: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 99\r\n\t\t\t},\r\n\t\t\t// 当数值为 0 时，是否展示 Badge\r\n\t\t\tshowZero: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 位置偏移\r\n\t\t\toffset: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => {\r\n\t\t\t\t\treturn [20, 20]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 是否开启绝对定位，开启了offset才会起作用\r\n\t\t\tabsolute: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 字体大小\r\n\t\t\tfontSize: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: '24'\r\n\t\t\t},\r\n\t\t\t// 字体演示\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#ffffff'\r\n\t\t\t},\r\n\t\t\t// badge的背景颜色\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 是否让badge组件的中心点和父组件右上角重合，配置的话，offset将会失效\r\n\t\t\tisCenter: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 是否将badge中心与父组件右上角重合\r\n\t\t\tboxStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\tif(this.isCenter) {\r\n\t\t\t\t\tstyle.top = 0;\r\n\t\t\t\t\tstyle.right = 0;\r\n\t\t\t\t\t// Y轴-50%，意味着badge向上移动了badge自身高度一半，X轴50%，意味着向右移动了自身宽度一半\r\n\t\t\t\t\tstyle.transform = \"translateY(-50%) translateX(50%)\";\r\n\t\t\t\t} else {\r\n\t\t\t\t\tstyle.top = this.offset[0] + 'rpx';\r\n\t\t\t\t\tstyle.right = this.offset[1] + 'rpx';\r\n\t\t\t\t\tstyle.transform = \"translateY(0) translateX(0)\";\r\n\t\t\t\t}\r\n\t\t\t\t// 如果尺寸为mini，后接上scale()\r\n\t\t\t\tif(this.size == 'mini') {\r\n\t\t\t\t\tstyle.transform = style.transform + \" scale(0.8)\";\r\n\t\t\t\t}\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// isDot类型时，不显示文字\r\n\t\t\tshowText() {\r\n\t\t\t\tif(this.isDot) return '';\r\n\t\t\t\telse {\r\n\t\t\t\t\tif(this.count > this.overflowCount) return `${this.overflowCount}+`;\r\n\t\t\t\t\telse return this.count;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 是否显示组件\r\n\t\t\tshow() {\r\n\t\t\t\t// 如果count的值为0，并且showZero设置为false，不显示组件\r\n\t\t\t\tif(this.count == 0 && this.showZero == false) return false;\r\n\t\t\t\telse return true;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\t\r\n\t.u-badge {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: inline-flex;\r\n\t\t/* #endif */\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tline-height: 24rpx;\r\n\t\tpadding: 4rpx 8rpx;\r\n\t\tborder-radius: 100rpx;\r\n\t\tz-index: 9;\r\n\t\t\r\n\t\t&--bg--primary {\r\n\t\t\tbackground-color: $u-type-primary;\r\n\t\t}\r\n\t\t\r\n\t\t&--bg--error {\r\n\t\t\tbackground-color: $u-type-error;\r\n\t\t}\r\n\t\t\r\n\t\t&--bg--success {\r\n\t\t\tbackground-color: $u-type-success;\r\n\t\t}\r\n\t\t\r\n\t\t&--bg--info {\r\n\t\t\tbackground-color: $u-type-info;\r\n\t\t}\r\n\t\t\r\n\t\t&--bg--warning {\r\n\t\t\tbackground-color: $u-type-warning;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.u-badge-dot {\r\n\t\theight: 16rpx;\r\n\t\twidth: 16rpx;\r\n\t\tborder-radius: 100rpx;\r\n\t\tline-height: 1;\r\n\t}\r\n\t\r\n\t.u-badge-mini {\r\n\t\ttransform: scale(0.8);\r\n\t\ttransform-origin: center center;\r\n\t}\r\n\t\r\n\t// .u-primary {\r\n\t// \tbackground: $u-type-primary;\r\n\t// \tcolor: #fff;\r\n\t// }\r\n\t\r\n\t// .u-error {\r\n\t// \tbackground: $u-type-error;\r\n\t// \tcolor: #fff;\r\n\t// }\r\n\t\r\n\t// .u-warning {\r\n\t// \tbackground: $u-type-warning;\r\n\t// \tcolor: #fff;\r\n\t// }\r\n\t\r\n\t// .u-success {\r\n\t// \tbackground: $u-type-success;\r\n\t// \tcolor: #fff;\r\n\t// }\r\n\t\r\n\t// .u-black {\r\n\t// \tbackground: #585858;\r\n\t// \tcolor: #fff;\r\n\t// }\r\n\t\r\n\t.u-info {\r\n\t\tbackground-color: $u-type-info;\r\n\t\tcolor: #fff;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-badge.vue?vue&type=style&index=0&id=40f94fea&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-badge.vue?vue&type=style&index=0&id=40f94fea&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752725568790\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}