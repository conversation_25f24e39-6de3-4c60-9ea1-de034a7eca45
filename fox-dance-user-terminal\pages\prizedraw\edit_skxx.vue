<template>
	<view class="confirmOrder edit_skxx" :style="{ '--qjbutton-color': qjbutton }" v-if="productxq.id">
		 
		<view class="hbdhCon_a">
			<image src='/static/images/icon62.png' class="hbdhCon_a_bj"></image>
			<view class="hbdhCon_a_n"  @click="UploadImg">
				<image :src="payment_code == null || payment_code == '' ? '/static/images/icon70.png' : imgbaseUrl + payment_code" class="hbdhCon_a_l" mode="aspectFill"></image>
				<view class="hbdhCon_a_r">{{payment_code == null || payment_code == '' ? '点击添加微信收款码' : '点击更换微信收款码'}}</view>
			</view>
		</view>
		
		<view class="hbdhCon_b">
			<view class="hbdhCon_b_t">
				<view>设为默认收款账户</view>
				<text>每次兑换会默认使用该账户</text>
			</view>
			<u-switch :active-color="qjbutton" size="40" v-model="switchVal" @change="change"></u-switch>
		</view>
		
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<view class="peodex_foo">
			<view class="peodex_foo_r" @click="bcSubTap">保存</view>
		</view>
		
		
	</view>
</template>


<script>
import {
	upImg,
	userInfoApi,
	paymentcodeApi,
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			imgbaseUrl:'',
			isLogined:true,
			productxq:{id:1},
			imgbaseUrl:'',
			switchVal: true,
			payment_code:'',
			qjbutton:'#131315',
		}
	},
	onShow() {
		
	},
	onLoad(option) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.imgbaseUrl = this.$baseUrl;
		this.userData();//获取收款信息
		this.switchVal = uni.getStorageSync('skxxMr') == 1 ? true : false
	},
	methods: {
		//获取收款信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userInfoApi({}).then(res => {
				if (res.code == 1) {
					console.log('个人信息',res);
					that.payment_code = res.data.payment_code;
					uni.hideLoading();
				}
			})
		},
		//提交收款信息
		bcSubTap(){
			if(this.payment_code == null || this.payment_code == ''){
				uni.showToast({
					title: '请传收款信息',
					icon: 'none',
					duration: 2000
				})
				return false;
			}
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			paymentcodeApi({
				payment_code:that.payment_code
			}).then(res => {
				if (res.code == 1) {
					uni.setStorageSync('skxxMr',this.switchVal ? 1 : 2)
					uni.hideLoading();
					uni.showToast({
						title: '保存成功',
						icon: 'success',
						duration: 2000
					})
					setTimeout(function(){
						uni.navigateBack({})
					},1500)
				}
			})
		},
		// 更换头像
		UploadImg() {
			let that = this
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					const tempFilePaths = res.tempFilePaths[0]
					uni.showLoading({
						title:'加载中'
					})
					upImg(tempFilePaths, 'file',{driver:'cos'} ).then(ress => {
						console.log('上传图片',ress)
						if (ress.code == 1) {
							uni.hideLoading();
							that.payment_code = ress.data.file.url
							console.log(that.payment_code,'that.payment_code')
						}
					})
		
				}
			})
		
		},
		change(e) {
			console.log('change', );
			this.switchVal = e
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.confirmOrder{overflow: hidden;}
page{padding-bottom: 0;}
</style>