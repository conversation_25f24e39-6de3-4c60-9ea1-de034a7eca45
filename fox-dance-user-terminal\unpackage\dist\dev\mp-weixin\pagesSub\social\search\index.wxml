<view class="search-container data-v-2813d7e8"><view class="search-bar data-v-2813d7e8"><u-search vue-id="4b30b3c4-1" placeholder="搜索帖子、用户或话题" show-action="{{true}}" action-text="搜索" value="{{keyword}}" data-event-opts="{{[['^search',[['onSearch']]],['^custom',[['onSearch']]],['^input',[['__set_model',['','keyword','$event',[]]]]]]}}" bind:search="__e" bind:custom="__e" bind:input="__e" class="data-v-2813d7e8" bind:__l="__l"></u-search></view><block wx:if="{{searched}}"><scroll-view class="search-results data-v-2813d7e8" scroll-y="{{true}}"><block wx:if="{{$root.g0>0}}"><view class="results-list data-v-2813d7e8"><block wx:for="{{results}}" wx:for-item="post" wx:for-index="__i0__" wx:key="id"><post-card class="post-card-item data-v-2813d7e8" vue-id="{{'4b30b3c4-2-'+__i0__}}" post="{{post}}" bind:__l="__l"></post-card></block></view></block><block wx:else><u-empty vue-id="4b30b3c4-3" mode="search" text="没有找到相关内容" class="data-v-2813d7e8" bind:__l="__l"></u-empty></block></scroll-view></block><block wx:else><view class="discovery-section data-v-2813d7e8"><block wx:if="{{$root.g1>0}}"><view class="history-section data-v-2813d7e8"><view class="section-header data-v-2813d7e8"><text class="section-title data-v-2813d7e8">搜索历史</text><u-icon vue-id="4b30b3c4-4" name="trash" color="#999" size="20" data-event-opts="{{[['^click',[['clearHistory']]]]}}" bind:click="__e" class="data-v-2813d7e8" bind:__l="__l"></u-icon></view><view class="tags-container data-v-2813d7e8"><block wx:for="{{searchHistory}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-tag vue-id="{{'4b30b3c4-5-'+index}}" text="{{item}}" type="info" data-event-opts="{{[['^click',[['onTagClick',['$0'],[[['searchHistory','',index]]]]]]]}}" bind:click="__e" class="data-v-2813d7e8" bind:__l="__l"></u-tag></block></view></view></block><view class="hot-searches-section data-v-2813d7e8"><view class="section-header data-v-2813d7e8"><text class="section-title data-v-2813d7e8">热门搜索</text></view><view class="tags-container data-v-2813d7e8"><block wx:for="{{hotSearches}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-tag vue-id="{{'4b30b3c4-6-'+index}}" text="{{item}}" type="warning" plain="{{true}}" data-event-opts="{{[['^click',[['onTagClick',['$0'],[[['hotSearches','',index]]]]]]]}}" bind:click="__e" class="data-v-2813d7e8" bind:__l="__l"></u-tag></block></view></view></view></block></view>