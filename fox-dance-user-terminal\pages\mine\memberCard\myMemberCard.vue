<template>
	<view class="myMemberCard">
		<view class="mucars_one">
			<view class="mucars_one_li">
				<picker @change="bindPickerChange_lb" :value="index_lb" :range="array_lb">
					<view class="uni-input">{{array_lb[index_lb]}}<text></text></view>
				</picker> 
			</view>
			<view class="mucars_one_li">
				<picker @change="bindPickerChange_md" :value="index_md" :range="array_md">
					<view class="uni-input">{{array_md[index_md]}}<text></text></view>
				</picker>
			</view>
		</view>
		<view class="mycards_thr">
			<view class="mycards_thr_li" v-for="(item,index) in cardsLists" :key="index" @click="navTo('/pages/mine/memberCard/myMemberCardxq?id=' + item.id)">
				<!-- https://file.foxdance.com.cn/storage/default/20250417/3IrxlLQQb7Wc9302dcd60cd7d0cef3b399122418a1926274929a44c.jpg -->
				<image :src="imgbaseUrl + item.image" class="mycards_thr_li_bj"></image>
				<view class="mycards_thr_li_zt" v-if="!xzhyk">{{item.status == 0 ? '未激活' : item.status == 1 ? '使用中' : item.status == 2 ? '请假中' : item.status == 3 ? '已耗尽' : item.status == 4 ? '已过期' : item.status == 5 ? '已转让' : item.status == 6 ? '已退款' : item.status == 7 ? '已转卡' : ''}}</view>
				<view class="mycards_thr_li_c">
					<view class="mycards_thr_li_c_l"><image :src="imgbaseUrl + avatar" mode="aspectFill"></image></view>
					<view class="mycards_thr_li_c_r">
						<view class="mycards_thr_li_c_r_a">{{item.contract_name}}</view>
						<view class="mycards_thr_li_c_r_b" v-if="item.type*1 == 0">剩余<text>{{item.surplus_frequency}}</text>次</view>
						<view class="mycards_thr_li_c_r_b" v-else>{{item.status > 0 ? item.become_time + '到期' : '未激活'}}</view>
					</view>
				</view>
				<view class="mycards_thr_li_c_r_f">
					<view class="mycards_thr_li_c_r_f_l">使用期限:{{item.status == 0 ? '未激活' : item.activation_time + ' - ' + item.become_time}}</view>
					<view class="mycards_thr_li_c_r_f_r" v-if="xzhyk" @click.stop="xzhykTap(item)">选择</view>
					<view class="mycards_thr_li_c_r_f_r" v-else="xzhyk">详情</view>
				</view>
			</view>
		</view>
		<view class="mycards_two" v-if="false">
			<view class="mycards_two_li" v-for="(item,index) in cardsLists" :key="index" @click="navTo('/pages/mine/memberCard/myMemberCardxq?id=' + item.id)">
				<view class="mycards_two_li_t">
					<image :src="imgbaseUrl + avatar" mode="aspectFill" class="mycards_two_li_t_l"></image>
					<!-- 会员卡类型:0=次卡,1=年卡,2=月卡 -->
					<view class="mycards_two_li_t_r">
						<view v-if="item.contract_name">会员名称:{{item.contract_name}}</view>
						<view v-else>会员ID:{{item.out_trade_no}}</view>
						<text>{{item.type*1 == 0 ? '次卡' : '时长卡'}}：{{item.status > 0 ? item.become_time + '到期　' : '未激活　'}}　<template v-if="item.type*1 == 0">剩余{{item.surplus_frequency}}次</template></text>
						<text>到期时间:{{item.status == 0 ? '未激活' : item.activation_time + ' ~ ' + item.become_time}}</text>
					</view>
				</view>
				<view class="mycards_two_li_t_b" v-if="xzhyk" @click.stop="xzhykTap(item)">选择</view>
				<view class="mycards_two_li_t_b" v-else="xzhyk">详情</view>
				<!-- <view class="mycards_two_li_t_zt" v-if="item.status == 1 && !xzhyk">请假中</view> -->
				<view class="mycards_two_li_t_zt" v-if="!xzhyk">{{item.status == 0 ? '未激活' : item.status == 1 ? '使用中' : item.status == 2 ? '请假中' : item.status == 3 ? '已耗尽' : item.status == 4 ? '已过期' : item.status == 5 ? '已转让' : item.status == 6 ? '已退款' : item.status == 7 ? '已转卡' : ''}}</view>
				
				<!-- status：状态:0=待激活,1=使用中,2=请假中,3=已耗尽,4=已过期,5=已转让,6=已退款,7=已转卡 -->
				
				<view class="mycards_two_li_t_zt" v-if="xzhyk">剩余{{item.leave_frequency}}次</view>
			</view>
		</view>
		<view class="gg_loding" v-if="!zanwsj">
			<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
				<view></view>
				<text>加载中</text>
			</view>
			<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
		</view>
		<view class="gg_zwsj" v-if="zanwsj">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无数据</text>
			</view>
		</view>
		
		
	</view>
</template>


<script>
import {
	myCardApi,
	storeListsApi,
	userInfoApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			navBg:'',
			avatar:'',
			zsewmToggle:false,//专属二维码
			cardsLists:[],
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			isLogined:false,
			imgbaseUrl:'',//图片地址
			
			array_lb: ['卡种类别', '次卡', '时长卡'],//卡种类别
			index_lb: 0,//卡种类别
			array_md: ['适用门店'],//适用门店
			array_md_cunc:[],////适用门店存储
			index_md: 0,//适用门店
			
			xzhyk:false,
		}
	},
	onPageScroll(e) {
		const top = uni.upx2px(100)
		const {
			scrollTop
		} = e
		let percent = scrollTop / top > 1 ? 1 : scrollTop / top
		this.navBg = percent
	},
	onLoad(option) {
		this.avatar = option.avatar;
		this.xzhyk = option.xzhyk ? true : false;
	},
	onShow() {
		// this.isLogined = uni.getStorageSync('token') ? true : false;
		if(this.isLogined){
			// this.userData();//个人信息
		}
		this.imgbaseUrl = this.$baseUrl;
		this.page = 1;
		this.cardsLists = [];
		this.cardsData();//我的会员卡
		this.storeData();//门店列表
		this.userData();//个人信息
	},
	methods: {
		//选择会员卡
		xzhykTap(item){
			uni.setStorageSync('selectCards',item)
			uni.navigateBack({})
		},
		//个人信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userInfoApi({}).then(res => {
				console.log('个人中心',res)
				if (res.code == 1) {
					that.loding = true;
					that.avatar = res.data.avatar;
					uni.hideLoading();
				}
			})
		},
		//卡种类别
		bindPickerChange_lb: function(e) {
			console.log('picker发送选择改变，携带值为', e.detail.value)
			this.index_lb = e.detail.value
			this.page = 1;
			this.cardsLists = [];
			this.cardsData();//我的会员卡
		},
		//适用门店
		bindPickerChange_md: function(e) {
			console.log('picker发送选择改变，携带值为', e.detail.value)
			this.index_md = e.detail.value;
			this.page = 1;
			this.cardsLists = [];
			this.cardsData();//我的会员卡
		},
		//门店列表
		storeData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			storeListsApi({
				type:1,
				longitude:uni.getStorageSync('postion').longitude,
				latitude:uni.getStorageSync('postion').latitude,
				limit:9999,
			}).then(res => {
				console.log('门店列表',res)
				if (res.code == 1) {
					var array_md = ['适用门店']
					for(var i=0;i<res.data.data.length;i++){
						array_md.push(res.data.data[i].name)
					}
					that.array_md = array_md;
					that.array_md_cunc = res.data.data;
					uni.hideLoading();
				}
			})
		},
		//我的会员卡
		cardsData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			myCardApi({
				page:that.page,
				size:10,
				type:that.index_lb,
				store_id:that.index_md == 0 ? 0 : that.array_md_cunc[that.index_md-1].id,
			}).then(res => {
				console.log('我的会员卡2',res)
				if (res.code == 1) {
					var obj = res.data.data;
					for(var i=0;i<obj.length;i++){
						// console.log(obj[i].activation_time.split('-')[0] + '/' + obj[i].activation_time.split('-')[1] + '/' + obj[i].activation_time.split('-')[2],'sss')
						obj[i].activation_time = obj[i].activation_time.split('-')[0] + '/' + obj[i].activation_time.split('-')[1] + '/' + obj[i].activation_time.split('-')[2]
						obj[i].become_time = obj[i].become_time.split('-')[0] + '/' + obj[i].become_time.split('-')[1] + '/' + obj[i].become_time.split('-')[2]
					}
					that.cardsLists = that.cardsLists.concat(obj);
					that.zanwsj = that.cardsLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.cardsLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottom() {
			// console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.cardsData();
				}
			}
		},
		onPullDownRefresh: function() {
		    // console.log('我被下拉了');
		    this.page = 1;
			this.cardsLists = [];
			this.cardsData();//我的会员卡
		},
		navTo(url){
			if(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
			}else{
				uni.navigateTo({
					url:url
				})
			}
		},
	}
}
</script>

<style scoped lang="scss">
	
</style>