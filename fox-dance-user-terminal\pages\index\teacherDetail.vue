<template>
	<view class="teacherDetail" :style="{ '--qjbutton-color': qjbutton,'--qjziti-color': qjziti }" v-if="loding">
		
		<view class="lsxq_head">
			<view class="lsxq_head_l">
				<view class="stor_thr_c_n">
					<view class="stor_thr_c_li" :class="jbToggle ? 'stor_thr_c_li_ac' : ''" @click="jbStartTap">{{jibText == '' ? '级别' : jibText}}<text></text></view>
					<view class="stor_thr_c_li" :class="wuzToggle ? 'stor_thr_c_li_ac' : ''" @click="wuzStartTap">{{wuzText == '' ? '舞种' : wuzText}}<text></text></view>
					<view class="stor_thr_c_li" :class="laosToggle ? 'stor_thr_c_li_ac' : ''" @click="laosStartTap">{{laosText == '' ? '老师' : laosText}}<text></text></view>
				</view>
			</view>
			<view class="lsxq_head_r"><text></text><image src="/static/images/icon36.png" @click="searchToggle = true"></image></view>
			
			<view class="les_search" v-if="searchToggle">
				<view class="les_search_l"><image src="/static/images/search.png" @click="searchTap(keywords)"></image><input type="text" placeholder-style="color:#999999" placeholder="请输入课程名称" v-model="keywords" confirm-type="search" @confirm="searchTap(keywords)" /></view>
				<view class="les_search_r" @click="searchToggle = false">清除</view>
			</view>
			
		</view>
		 
		<view class="gg_rgba" v-if="jbToggle || wuzToggle || laosToggle" @click="gbTcTap"></view>
		<!-- 级别 go -->
		<view class="teaxzTanc" v-if="jbToggle">
			<view class="teaxzTanc_t">
				<view v-for="(item,index) in jibLists" :key="index" :class="jibIndex == index ? 'teaxzTanc_t_ac' : ''" @click="jibTap(index)">{{item.name}}</view>
			</view>
			<view class="teaxzTanc_b"><view @click="jibReact">重置</view><text @click="jibSubTap">提交</text></view>
		</view>
		<!-- 级别 end -->
		<!-- 舞种 go -->
		<view class="teaxzTanc" v-if="wuzToggle">
			<view class="teaxzTanc_t">
				<view v-for="(item,index) in wuzLists" :key="index" :class="wuzIndex == index ? 'teaxzTanc_t_ac' : ''" @click="wuzTap(index)">{{item.name}}</view>
			</view>
			<view class="teaxzTanc_b"><view @click="wuzReact">重置</view><text @click="wuzSubTap">提交</text></view>
		</view>
		<!-- 舞种 end -->
		<!-- 老师 go -->
		<view class="teaxzTanc" v-if="laosToggle">
			<view class="teaxzTanc_t">
				<view v-for="(item,index) in laosLists" :key="index" :class="laosIndex == index ? 'teaxzTanc_t_ac' : ''" @click="laosTap(index)">{{item.name}}</view>
			</view>
			<view class="teaxzTanc_b"><view @click="laosReact">重置</view><text @click="laosSubTap">提交</text></view>
		</view>
		<!-- 老师 end -->
		
		
		
		<view class="tea_ban">
			<view class="tea_ban_a">{{teacherList[uswiperIndex].name}}</view>
			<!-- <view class="tea_ban_b">
				<view class="tea_ban_b_t">
					
					<view class="banner">
						<swiper class="banner-container" circular="true" interval="3000" 
							duration="1000"  previous-margin="200rpx" next-margin="200rpx" @change="cIndex" >
							<block v-for="(item,index) in teachBan" :key="index">
								<swiper-item>{{item.name}}
									<image class="slide-image" :class="[ currentIndex === index ? 'active':'' ]" src="/static/images/icon23.jpg" ></image>
								</swiper-item>
							</block>
						</swiper>
					</view>
					
				</view>
				<view class="tea_ban_b_yd"></view>
			</view> -->
			<view class="tea_ban_uswiper">
				<u-swiper :list="teacherList" mode="none" :autoplay="false" :current="uswiperIndex" :effect3d="true" :effect3d-previous-margin="200" @change="changeSwiper"></u-swiper>
			</view>
			<image src="/static/images/icon47.png" class="tea_ban_yd"></image>
		</view>
		
		<view class="ord_nav tea_one">
			<view class="ord_nav_li" :class="type == 0 ? 'ord_nav_li_ac' : ''" @click="navTap(0)"><view><text>任课详情</text><text></text></view></view>
			<view class="ord_nav_li" :class="type == 1 ? 'ord_nav_li_ac' : ''" @click="navTap(1)"><view><text>老师评价</text><text></text></view></view>
		</view>
		
		<view class="kcxq_video" :class="speedState ? 'qpvideo' : ''" v-if="type == 0 && teacherList[uswiperIndex].masterpiece != ''">
			<video :src="teacherList[uswiperIndex].isoss ? teacherList[uswiperIndex].masterpiece : imgbaseUrl + teacherList[uswiperIndex].masterpiece" controls id="videoId" @fullscreenchange="handleFullScreen" @controlstoggle="handleControlstoggle">
				<!-- 倍速按钮 -->
				<cover-view v-show="controlsToggle" class="speed">
					<!-- <cover-view @click="speedNum=true" class="doubleSpeed">倍速</cover-view> -->
					<cover-view @click="speedTap" class="doubleSpeed">倍速</cover-view>
				</cover-view>
				<!-- 倍速面板 -->
				<cover-view class="speedNumBox" v-if="speedNum">
					<cover-view class="number" @click.stop="handleSetSpeedRate(0.5)" :class="0.5 == speedRate ? 'activeClass' :'' ">0.5倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(0.8)" :class="0.8 == speedRate ? 'activeClass' :'' ">0.8倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(1)" :class="1 == speedRate ? 'activeClass' :'' ">1倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(1.25)" :class="1.25 == speedRate ? 'activeClass' :'' ">1.25倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(1.5)" :class="1.5 == speedRate ? 'activeClass' :'' ">1.5倍速</cover-view>
					<cover-view class="number" @click.stop="handleSetSpeedRate(2)" :class="2 == speedRate ? 'activeClass' :'' ">2倍速</cover-view>
					<cover-view class="number" @click.stop="speedNum = false">取消</cover-view>
				</cover-view>
			</video>
		</view>
		
		<view class="kcxq_one">
			<view class="kcxq_one_b" style="margin-top:0;">
				<image src="/static/images/toux.png" class="kcxq_one_b_l"></image>
				<view class="kcxq_one_b_r">
					<view class="kcxq_one_b_r_l"><view>{{teacherList[uswiperIndex].name}}</view><text v-if="teacherList[uswiperIndex].work_year*1 > 0">{{teacherList[uswiperIndex].work_year}}年经验</text></view>
				</view>
			</view>
			<view class="kcxq_one_c">
				<view>擅长舞种：{{teacherList[uswiperIndex].skilled_dance}}</view>
				<view>课程级别：{{teacherList[uswiperIndex].level_name}}</view>
				<view v-if="teacherList[uswiperIndex].section_number*1 > 0">最近任课：{{teacherList[uswiperIndex].day}}天{{teacherList[uswiperIndex].section_number}}节</view>
				<view v-else>最近暂无任课</view>
			</view>
		</view>
		
		<view class="lspjCon" v-if="type == 1">
			<view class="lspjCon_t">
				<view>老师评分<text>{{teacherList[uswiperIndex].score}}</text></view>
			</view>
			<view class="lspjCon_b">
				<text v-for="(item,index) in teacherList[uswiperIndex].evaluate" :key="index">{{item}}</text>
			</view>
		</view>
		
		<view class="rlxz_con" v-if="type == 0 && array_md_cunc.length > 0">
			<view class="rlxz_con_l">
				<scroll-view scroll-x="true" :scroll-left="scrollLeft" @scroll="scrollJt">
					<view class="rlxz_con_l_li" :class="sjsxIndex == index ? 'rlxz_con_l_li_ac' : ''" v-for="(item,index) in sjsxLists" :key="index" @click="sjsxTap(index,item)">
						<view>{{item.week}}</view><view>{{item.day}}</view><text></text>
					</view>
				</scroll-view>
			</view>
			<view class="rlxz_con_r">
				<image src="/static/images/icon53.png"></image>
				<picker mode="date" :value="date_sx" :start="startDate" :end="endDate" @change="bindDateChange_sx">
					<view class="uni-input">{{date_sx}}</view>
				</picker>
			</view>
		</view>
		
		<view class="md_xz md_xzCon" v-if="type == 0 && array_md_cunc.length > 0">
			<image src="/static/images/icon52-3.png" class="md_xz_bj"></image>
			<view class="md_xz_title">{{array_md[index_md]}}</view>
			<image src="/static/images/icon52-4.png" class="md_xz_xt"></image>
			<picker @change="bindPickerChange_md" :value="index_md" :range="array_md">
				<view class="uni-input">{{array_md[index_md]}}</view>
			</picker>
		</view>
		
		
		
		<!-- <view class="teaCon" v-if="type == 0">
			<view class="teaCon_li" v-for="(item,index) in 4" :key="index">
				<view class="teaCon_li_a">拉丁舞练习</view>
				<view class="teaCon_li_b">
					<image src="/static/images/icon23.jpg" mode="aspectFill" class="teaCon_li_b_l"></image>
					<view class="teaCon_li_b_c">
						<view class="teaCon_li_b_c_a">16:00-17:00</view>
						<view class="teaCon_li_b_c_b">上课老师：LINDA</view>
						<view class="teaCon_li_b_c_c"><text>入门</text><text>拉丁舞</text></view>
					</view>
					<view class="teaCon_li_b_r" @click="navTo('/pages/Schedule/Schedulexq')">预约</view>
				</view>
				<view class="teaCon_li_c">
					<view class="teaCon_li_c_l">
						<image src="/static/images/toux.png" v-for="(item,index) in 6" :key="index"></image>
					</view>
					<view class="teaCon_li_c_r">已预约：<text>23</text>人;<text>3</text>人在等位</view>
				</view>
			</view>
		</view> -->
		
		
		<view class="teaCon" v-if="type == 0">
			<view class="teaCon_li" v-for="(item,index) in storeCourseLists" :key="index" @click="storesxqTap(item)">
				<view class="teaCon_li_a">{{item.course.name}}</view>
				<view class="teaCon_li_b">
					<image :src="imgbaseUrl + item.teacher.image" mode="aspectFill" class="teaCon_li_b_l"></image>
					<view class="teaCon_li_b_c">
						<view class="teaCon_li_b_c_a">{{item.start_time}}-{{item.end_time}}</view>
						<view class="teaCon_li_b_c_b">上课老师：{{item.teacher.name}}</view>
						<view class="teaCon_li_b_c_b" v-if="item.frequency*1 > 0">次卡消耗：{{item.frequency*1}}次</view>
						<view class="teaCon_li_b_c_c"><text v-if="item.level_name">{{item.level_name}}</text><text>{{item.dance_name}}</text></view>
					</view>
					
					
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-if="item.status == 1" @click.stop>待开课</view>
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 2" @click.stop>授课中</view>
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 3" @click.stop>已完成</view>
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 4" @click.stop>等位中</view>
					<!-- <view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 6" @click.stop>未开始预约</view> -->
					<view class="teaCon_li_b_r yysj" style="background:#BEBEBE" v-else-if="item.status == 6" @click.stop><text>{{item.start_reservation}}</text><text>开始预约</text></view>
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 7" @click.stop>截止预约</view>
					<!-- 未开启等位并且课程预约爆满 (课程预约满员时开启等位:0=关闭,1=开启)-->
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.equivalent*1 == 0 && (item.appointment_number*1 >= item.maximum_reservation*1)" @click.stop="kqhyts">预约</view>
					<view class="teaCon_li_b_r" :style="item.member == 0 ? 'background:#BEBEBE' : ''" v-else-if="item.member == 0" @click.stop="ljtkToggle = true">预约</view>
					<!-- 开启等位 -->
					<view class="teaCon_li_b_r" v-else @click.stop="yypdTo(item,'/pages/Schedule/Schedulexq?id' + item.id)">{{item.waiting_number*1 > 0 ? '去排队' : '预约'}}</view>
						
						
				</view>
				<view class="teaCon_li_c">
					<view class="teaCon_li_c_l">
						<!-- /static/images/toux.png -->
						<image :src="imgbaseUrl + item.avatar" v-for="(item,index) in item.appointment_people" :key="index" mode="aspectFit"></image>
					</view>
					<view class="teaCon_li_c_r">已预约：<text>{{item.appointment_number}}</text>人;<template v-if="item.waiting_number*1 > 0"><text>{{item.waiting_number}}</text>人在等位</template></view>
				</view>
			</view>
		</view>
		
		<view class="gg_zwsj" style="margin-bottom:60rpx;" v-if="storeCourseLists.length == 0 && type == 0">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无课程</text>
			</view>
		</view>
		
		<!-- 提示预约弹窗 go -->
		<view class="yytnCon" v-if="ljtkToggle"><view class="yytnCon_n"><image :src="imgbaseUrlOss + '/userreport/icon55.png'"></image><text @click="ljktTap"></text></view><image src="/static/images/icon56.png" @click="ljtkToggle = false"></image></view>
		<!-- 提示预约弹窗 end -->
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	lscxCategoryApi,
	TeachersIntroductionApi,
	searchStoreApi,
	storeListsApi,
	storeCourseApi
} from '@/config/http.achieve.js'
export default {
	data() {
		const currentDate = this.getDate({
			format: true
		})
		return {
			isLogined:true,
			loding:false,
			jibLists:[],
			jibIndex:-1,
			jibText:'',
			jbToggle:false,
			imgbaseUrlOss:'',
			wuzLists:[],
			wuzIndex:-1,
			wuzText:'',
			wuzToggle:false,
			
			laosLists:[],
			laosIndex:-1,
			laosText:'',
			laosToggle:false,
			
			keywords:'',
			keywords_cunc:'',
			searchToggle:false,//搜索是否显示
			
			currentIndex:3,
			type:0,
			sjsxLists:[],//时间筛选
			sjsxIndex:0,
			scrollLeft:0,
			date_sx: currentDate,
			array_md: [],
			array_md_cunc: [],
			index_md: 0,
			dateText:'',//时间
			
			uswiperIndex:0,
			teacherList:[],
			ljtkToggle:false,
			
			storeCourseLists:[],
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			
			imgbaseUrl:'',
			kcxqTeachId:0,//课程详情
			
			controlsToggle:false,//是否显示状态
			speedState:false,//是否进入全屏
			speedNum:false,//是否显示倍速
			speedRate:0,//当前倍数
			
			qjbutton:'#131315',
			qjziti:'#F8F8FA'
		}
	},
	onShow() {
		this.imgbaseUrlOss = this.$baseUrlOss;
		this.isLogined = uni.getStorageSync('token') ? true : false;
	},
	onLoad(option) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.qjziti = uni.getStorageSync('storeInfo').written_words
		this.imgbaseUrl = this.$baseUrl;
		this.dateText = this.getFormattedCurrentDate();
		this.dateDatasx(this.getFormattedCurrentDate());//获取最近15日
		this.kcxqTeachId = option.id ? option.id : 0;// option.id 是从课程详情进入
		this.teachersData();//老师列表>通过老师查找门店>在通过门店和老师共同查找课程
		this.categoryData();//老师分类
	},
	computed: {
		startDate() {
			return this.getDate('start');
		},
		endDate() {
			return this.getDate('end');
		}
	},
	methods: {
		//点击倍数
		speedTap(){
			this.speedNum = true;
		},
		//监听进入全屏 和 退出全屏
		handleFullScreen(e){
			// console.log('监听进入全屏1',e);
			// console.log('监听进入全屏2',e.detail.fullScreen);
			this.speedState = e.detail.fullScreen;
			this.speedNum = false;
		},
		//2.控件（播放/暂停按钮、播放进度、时间）是显示状态
		handleControlstoggle(e){
			// console.log(e.detail.show);
			this.controlsToggle = e.detail.show
		},
		//设置倍速速度
		handleSetSpeedRate(rate){
			 let videoContext = uni.createVideoContext("videoId");
			 videoContext.playbackRate(rate);
			 this.speedRate = rate;
			 this.speedNum = false;
			 uni.showToast({
			 	icon: 'none',
			 	title: '已切换至' + rate + '倍数',
				duration:2000
			 });
		},
		//swiper
		changeSwiper(index){
			console.log(index,'index')
			this.uswiperIndex = index;
			this.index_md = 0;
			this.searchStore();//通过老师搜索可选择门店
		},
		//通过老师搜索可选择门店
		searchStore(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			searchStoreApi({
			// storeListsApi({
				teacher_id:that.teacherList[that.uswiperIndex].id
			}).then(res => {
				console.log('通过老师搜索可选择门店',res)
				if (res.code == 1) {
					uni.hideLoading();
					var obj = res.data;
					var array_md = [];
					for(var i=0;i<obj.length;i++){
						array_md.push(obj[i].name)
					}
					that.array_md = array_md;
					that.array_md_cunc = obj;
					if(res.data.length == 0){
						that.page = 1;
						that.storeCourseLists = [];//该老师在所有门店下均无课程
					}else{
						that.page = 1;
						that.storeCourseLists = [];//门店课程
						that.storeCourseData();//门店课程
					}
					
				}
			})
		},
		//门店课程
		/*storeCourseData(){
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			storeCourseApi({
				id:that.array_md_cunc.length == 0 ? 0 : that.array_md_cunc[that.index_md].id,
				level_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,
				dance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,
				teacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,
				// date:'2024-10-30',
				date:that.dateText,
				name:that.keywords_cunc,
			}).then(res => {
				console.log('门店课程',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.storeCourseLists = res.data;
				}
			})
		},*/
		//门店课程
		storeCourseData(){
			
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			storeCourseApi({
				page:that.page,
				id:that.array_md_cunc.length == 0 ? 0 : that.array_md_cunc[that.index_md].id,
				level_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,
				dance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,
				teacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,
				// date:'2024-10-30',
				date:that.dateText,
				name:that.keywords_cunc,
			}).then(res => {
				console.log('门店课程',res)
				/*if (res.code == 1) {
					uni.hideLoading();
					that.storeCourseLists = res.data.data;
				}*/
				if (res.code == 1) {
					var obj = res.data.data;
					that.storeCourseLists = that.storeCourseLists.concat(obj);
					that.zanwsj = that.storeCourseLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.storeCourseLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
		},
		onReachBottom() {
			//console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.storeCourseData();//门店课程
				}
			}
		},
		onPullDownRefresh: function() {
		    console.log('我被下拉了');
		    this.page = 1;
		    this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
		},
		//详情跳转
		storesxqTap(item){
			console.log(this.isLogined,'this.isLogined')
			if(!this.isLogined){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
				return false;
			}
			// 未开启会员并且后端设置了必须开通会员方可查看详情
			if(item.course.view_type*1 == 0 && item.member == 0){
				this.ljtkToggle = true
			}else{
				uni.navigateTo({
					// url:'/pages/Schedule/Schedulexq?id=' + item.id
					url:'/pages/mine/myCourse/myCoursexq?id=' + item.id
				})
			}
		},
		//预约约课/排队
		yypdTo(item){
			if(!this.isLogined){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
				return false;
			}
			
			uni.navigateTo({
				url:'/pages/Schedule/confirmOrder?id=' + item.id  + '&storeid=' + this.array_md_cunc[this.index_md].id
			})
		},
		//预约爆满
		kqhyts(){
			uni.showToast({
				title: '预约课程已满',
				icon: 'none',
				duration: 1000
			})
		},
		//立即开通会员
		ljktTap(){
			this.ljtkToggle = false;
			uni.switchTab({
				url:'/pages/buy/buy'
			})
		},
		//搜索跳转
		searchTap(keywords){
			this.keywords_cunc = this.keywords;
			this.page = 1;
			this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
			/*uni.navigateTo({
				url:'/pages/Schedule/searchResults?keywords=' + keywords
			})*/
		},
		//选择门店
		bindPickerChange_md: function(e) {
			console.log('picker发送选择改变，携带值为', e.detail.value)
			this.index_md = e.detail.value;
			this.page = 1;
			this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
		},
		//日期转化
		dateDatasx(date){
			let startDate = date;
			let daysToAdd = 15;
			this.sjsxLists = this.getDateArrayWithWeekday(startDate, daysToAdd);
			this.scrollLeft = 1;
		},
		//指定日期往后推迟xx日
		getDateArrayWithWeekday(startDateStr, daysToAdd) {
		      let dateArray = [];
		      let weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
		      let startDate = new Date(startDateStr);
		      for (let i = 0; i < daysToAdd; i++) {
		        let newDate = new Date(startDate);
		        newDate.setDate(startDate.getDate() + i);
		        let year = newDate.getFullYear();
		        let month = (newDate.getMonth() + 1).toString().padStart(2, '0');
		        let day = newDate.getDate().toString().padStart(2, '0');
		        let weekdayIndex = newDate.getDay();
		        let weekday = weekdays[weekdayIndex];
		        let formattedDate = `${year}-${month}-${day}（${weekday}）`;
		        dateArray.push({week:weekday,day:`${month}-${day}`,date:`${year}-${month}-${day}`});
		      }
		      return dateArray;
		},
		//获取当前日期
		getFormattedCurrentDate() {
		  let currentDate = new Date();
		  let year = currentDate.getFullYear();
		  let month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
		  let day = currentDate.getDate().toString().padStart(2, '0');
		  return `${year}-${month}-${day}`;
		},
		cIndex(e) {
			this.currentIndex = e.detail.current;			
		},
		scrollJt(e){
		},
		bindDateChange_sx: function(e) {
			var that = this;
			this.date_sx = e.detail.value;
			this.dateDatasx(this.date_sx);
			this.sjsxIndex = 0;
			that.dateText = e.detail.value;
			this.page = 1;
			this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
			setTimeout(function(){
				that.scrollLeft = 0;
			},0)
			
		},
		getDate(type) {
			const date = new Date();
			let year = date.getFullYear();
			let month = date.getMonth() + 1;
			let day = date.getDate();

			if (type === 'start') {
				year = year;
			} else if (type === 'end') {
				year = year + 1;
			}
			month = month > 9 ? month : '0' + month;
			day = day > 9 ? day : '0' + day;
			return `${year}-${month}-${day}`;
		},
		//时间筛选选择
		sjsxTap(index,item){
			this.sjsxIndex = index;
			this.dateText = item.date;
			this.page = 1;
			this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
		},
		navTap(index){
			this.type = index;
		},
		//老师列表
		teachersData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			TeachersIntroductionApi({
				page:1,
				limit:99999,
				// level_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,
				// dance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,
				// teacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,
			}).then(res => {
				console.log('老师列表',res)
				if (res.code == 1) {
					for(var i=0;i<res.data.data.length;i++){
						res.data.data[i].image = that.imgbaseUrl + res.data.data[i].image;
						// res.data.data[1].masterpiece = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734312077132_ces.mp4'
						if(res.data.data[i].masterpiece){
							res.data.data[i].isoss = res.data.data[i].masterpiece.substring(0,5) == 'https' ? true : false
						}
						//是从课程详情进入找到指定老师
						if(that.kcxqTeachId != 0){
							if(res.data.data[i].id == that.kcxqTeachId){
								that.uswiperIndex = i
							}
						}
					}
					// console.log(that.uswiperIndex,'that.uswiperIndex')
					that.teacherList = res.data.data;
					that.loding = true;
					uni.hideLoading();
					that.searchStore();//通过老师搜索可选择门店
				}
			})
		},
		//老师分类
		categoryData(){
			let that = this;
			lscxCategoryApi({}).then(res => {
				console.log('老师分类',res)
				if (res.code == 1) {
					that.jibLists = res.data.level;
					that.wuzLists = res.data.dance;
					that.laosLists = res.data.teacher;
				}
			})
		},
		//关闭所有弹窗
		gbTcTap(){
			this.jbToggle = false;
			this.wuzToggle = false;
			this.laosToggle = false;
		},
		//级别弹窗开启
		jbStartTap(){
			this.jbToggle = !this.jbToggle;
			this.wuzToggle = false;
			this.laosToggle = false;
		},
		//级别选择
		jibTap(index){
			this.jibIndex = index;
		},
		//级别提交
		jibSubTap(){
			if(this.jibIndex == -1){
				this.jibText = ''
			}else{
				this.jibText = this.jibLists[this.jibIndex].name
			}
			this.jbToggle = false;
			// this.storeCourseData();//门店课程
			
			if(this.array_md_cunc == 0){
				this.page = 1;
				this.storeCourseLists = [];//该老师在所有门店下均无课程
				uni.showLoading({
					title: '加载中'
				});
				setTimeout(function(){
					uni.hideLoading();
				},500)
			}else{
				this.page = 1;
				this.storeCourseLists = [];//门店课程
				this.storeCourseData();//门店课程
			}
		},
		//级别重置
		jibReact(){
			this.jibIndex = -1;
		},
		//舞种弹窗开启
		wuzStartTap(){
			this.jbToggle = false;
			this.wuzToggle = !this.wuzToggle;
			this.laosToggle = false;
		},
		//舞种选择
		wuzTap(index){
			this.wuzIndex = index;
		},
		//舞种提交
		wuzSubTap(){
			if(this.wuzIndex == -1){
				this.wuzText = ''
			}else{
				this.wuzText = this.wuzLists[this.wuzIndex].name
			}
			this.wuzToggle = false;
			if(this.array_md_cunc == 0){
				this.storeCourseLists = [];//该老师在所有门店下均无课程
				uni.showLoading({
					title: '加载中'
				});
				setTimeout(function(){
					uni.hideLoading();
				},500)
			}else{
				this.page = 1;
				this.storeCourseLists = [];//门店课程
				this.storeCourseData();//门店课程
			}
		},
		//舞种重置
		wuzReact(){
			this.wuzIndex = -1;
		},
		//老师弹窗开启
		laosStartTap(){			
			this.jbToggle = false;
			this.wuzToggle = false;
			this.laosToggle = !this.laosToggle;
		},
		//老师选择
		laosTap(index){
			this.laosIndex = index;
		},
		//老师提交
		laosSubTap(){
			console.log(this.laosIndex,'this.laosIndex')
			if(this.laosIndex == -1){
				this.laosText = ''
			}else{
				this.laosText = this.laosLists[this.laosIndex].name
			}
			this.uswiperIndex = this.laosIndex == -1 ? 0 : this.laosIndex;
			this.laosToggle = false;
			if(this.array_md_cunc == 0){
				this.storeCourseLists = [];//该老师在所有门店下均无课程
				uni.showLoading({
					title: '加载中'
				});
				setTimeout(function(){
					uni.hideLoading();
				},500)
			}else{
				this.page = 1;
				this.storeCourseLists = [];//门店课程
				this.storeCourseData();//门店课程
			}
		},
		//老师重置
		laosReact(){
			this.laosIndex = -1;
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.teacherDetail{overflow: hidden;}
page{padding-bottom: 0;}
</style>