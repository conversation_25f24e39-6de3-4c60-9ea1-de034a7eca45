<template>
  <view>
    <!-- TabBar 仅在卡片不显示时渲染 -->
    <view class="tab-bar" :class="{
			'tab-bar-exit': showBox && !isTabBarReturning,
			'tab-bar-enter': isTabBarReturning,
			'tab-bar-icon-visible': showBox
		}">
      <view class="tab_bgi " :class="noIndex == 2 ? 'tab_bgi2' : ''">
        <!-- <image src="/static/images/<EMAIL>" mode="scaleToFill"></image> -->
      </view>
      <view v-for="(item, index) in list" :key="index" class="tab-bar-item"
            :class="{ 'tab-fixed-visible': index === 2 && showBox }" @click="tabbarChange(item, index)">
        <view class="" v-if="index != 2">
          <!-- <image class="tab_img" :src="current == index ? item.selectedIconPath : item.iconPath"
            mode="aspectFit">
          </image> -->
          <u-icon :name="current == index ? item.icon + '-fill' : item.icon" :color="selColor"
                  size="40"></u-icon>
          <view class="tab_text" :style="{ color: current == index ? '#131315' : '#131315' }">
            {{ item.text }}
          </view>
        </view>
        <view class="" v-if="index == 2">
          <image class="tab_img" :src="current == index ? item.selectedIconPath : item.iconPath"
                 mode="aspectFit" style="width: 104rpx;height: 104rpx;margin-top: -110rpx;">
          </image>
        </view>
      </view>
    </view>

    <!-- 全屏底层白色背景 -->
    <view class="fullscreen-background" v-if="showBox" @click="closePreview">
      <!-- 顶部 -->
      <view class="header-container">
        <view class="header-left">
          <image src="/static/icon/home.png" mode="aspectFit" class="header-image home-icon"
                 @click.stop="onHomeClick"></image>
        </view>
        <image src="/static/tabbar/tab_fox1.png" mode="aspectFit" class="header-image"></image>
        <view class="header-right">
          <image src="/static/icon/个人中心.png" mode="aspectFit" class="header-image"></image>
        </view>
      </view>
      <!-- 底部 -->
      <view class="bottom-hint">
        <view class="fox-logo-container">
          <view class="fox-line"></view>
          <text class="fox-text">FOX DANCE STUDIO</text>
          <view class="fox-line"></view>
        </view>
        <text class="fox-subtext">随便点一下，可能会发现新大陆</text>
      </view>
    </view>

    <!-- 卡片容器 -->
    <view class="card-container-with-buttons" :class="{ 'show': showBox && !isCardExiting, 'exit': isCardExiting }"
          @touchmove.stop @touchstart.stop @touchend.stop catchtouchmove="true">
      <!-- 横向卡片布局容器 -->
      <swiper class="card-swiper" :current="currentPage" @change="onSwiperChange" :previous-margin="'30rpx'"
              :next-margin="'30rpx'" :duration="300" :circular="true" :skip-hidden-item-layout="true"
              :indicator-dots="false" :class="{ 'card-pulling': isCardPulling }" @touchmove.stop @touchstart.stop
              @touchend.stop>
        <swiper-item v-for="(item, index) in products" :key="index" class="card-swiper-item">
          <view class="card-preview-container card-item"
                :class="{ 'active-card': currentPage === index, 'near-active': Math.abs(currentPage - index) === 1 }"
                :data-index="index">
            <preview-card :title="item.name" :tag="item.tag" :image="item.image"
                          :targetPage="item.targetPage" @pulling="handleCardPulling" />
          </view>
        </swiper-item>
      </swiper>

      <!-- 分页指示器，固定在页面中 -->
      <view class="page-dots">
        <!-- 使用内联样式固定位置 -->
        <view v-for="i in products.length" :key="i" class="page-dot"
              :style="{ backgroundColor: currentPage === i ? 'rgb(232,124,174)' : 'rgba(0,0,0,0.2)' }"
              @tap="handleDotTap(i - 1)">
        </view>
      </view>
    </view>

  </view>
</template>

<script>
// 导入PreviewCard组件
import PreviewCard from './PreviewCard.vue'

export default {
  name: 'tabbar',
  components: {
    PreviewCard
  },
  props: {
    current: {
      type: [Number],
      default: 0
    }
  },
  data() {
    return {
      list: [{
        iconPath: "/static/tabbar/tab_home.png",
        selectedIconPath: "/static/tabbar/tab_home_x.png",
        icon: 'home',
        text: '首页',
        count: 0,
        isDot: false,
        customIcon: false,
        pagePath: "/pages/index/index"
      },
        {
          iconPath: "/static/tabbar/tab_buy.png",
          selectedIconPath: "/static/tabbar/tab_buy_x.png",
          icon: 'bag',
          text: '购买',
          count: 0,
          isDot: false,
          customIcon: false,
          pagePath: "/pages/buy/buy"
        },
        {
          iconPath: "/static/tabbar/tab_fox1.png",
          selectedIconPath: "/static/tabbar/tab_fox1.png",
          text: '作品',
          count: 0,
          isDot: false,
          customIcon: false,
          pagePath: "/pages/index/index"
        },
        {
          iconPath: "/static/tabbar/tab_schedule.png",
          selectedIconPath: "/static/tabbar/tab_schedule_x.png",
          icon: 'tags',
          text: '约课',
          count: 0,
          isDot: false,
          customIcon: false,
          pagePath: "/pages/Schedule/Schedule"
        },
        {
          iconPath: "/static/tabbar/tab_mine.png",
          selectedIconPath: "/static/tabbar/tab_mine_x.png",
          icon: 'account',
          text: '我的',
          count: 0,
          isDot: false,
          customIcon: false,
          pagePath: "/pages/mine/mine"
        },
      ],

      bottomHeight2: 4,
      bottomHeight: 10,
      ecology: '',
      noIndex: 0,
      showBox: false,
      showFixed: false,
      selColor: uni.getStorageSync('storeInfo') ? uni.getStorageSync('storeInfo').button : '#131315',

      // 卡片相关数据
      currentPage: 0,  // 当前页面索引
      showBoundaryHint: false,
      totalCards: 2,  // 总卡片数量

      // 商品数据
      products: [
        { id: 1, name: '新店投票', tag: 'Fox - New store voting', image: 'https://dance-**********.cos.ap-guangzhou.myqcloud.com/static/images/vote-card-image.jpg', targetPage: '/pagesSub/switch/vote' },
        { id: 2, name: '敬请期待', tag: 'Fox Dance Studio', image: 'https://dance-**********.cos.ap-guangzhou.myqcloud.com/static/images/vote-card-image.jpg', targetPage: '/pages/index/index' }
      ],

      // 页面滚动锁定标志
      pageScrollLocked: false,
      // TabBar显示状态
      isTabBarHidden: false,
      // TabBar返回动画标记
      isTabBarReturning: false,

      // 滚动状态
      scrollLeft: 0,
      cardWidth: 0,
      windowWidth: 0,

      // 触摸和滑动状态追踪
      isScrolling: false,
      lastScrollLeft: 0,
      scrollTimer: null,
      touchEndTimer: null,

      // 过渡动画状态
      isTransitioning: false,

      // 卡片触摸相关状态
      touchStartX: 0,
      touchStartY: 0,
      touchStartTime: 0,
      touchMoved: false,
      isSwiping: false,
      swipeThreshold: 10, // 滑动阈值
      cardTouchTimer: null,

      // 副卡相关状态
      rightCardOffsetX: 0,
      leftCardOffsetX: 0,
      rightCardScale: 0.85,
      leftCardScale: 0.85,
      rightCardOpacity: 0.7,
      leftCardOpacity: 0.7,

      // 添加卡片下拉状态
      isCardPulling: false,
      pullDistance: 0,

      // 添加关闭状态
      isClosing: false,

      // 添加卡片退场状态
      isCardExiting: false
    }
  },
  computed: {
    // 卡片容器样式
    cardContainerStyle() {
      if (!this.showBox) return {}

      return {
        transform: `translateY(-20px)`,
        transition: 'transform 450ms cubic-bezier(0.4, 0, 0.2, 1)'
      }
    }
  },
  created() {
    let that = this;
    uni.getSystemInfo({
      success(res) {
        if (res.safeAreaInsets.bottom) {
          that.bottomHeight = res.safeAreaInsets.bottom
          that.bottomHeight2 = res.safeAreaInsets.bottom - 7;
        }
        // 获取屏幕宽度用于卡片定位
        that.windowWidth = res.windowWidth;
        that.cardWidth = res.windowWidth * 0.8; // 卡片宽度为窗口的80%
      }
    })

    // 全局触摸事件处理
    uni.$on('touchmove', (e) => {
      if (this.showBox || this.pageScrollLocked) {
        // 如果卡片显示或页面被锁定，阻止所有触摸移动
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
        // 如果页面被锁定，则滚动到顶部
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0
        });
        return false;
      }
    });
  },
  mounted() {
    // 在组件挂载后，确保初始卡片居中显示
    this.$nextTick(() => {
      setTimeout(() => {
        if (this.showBox) {
          this.scrollToCard(this.currentPage);
        }
      }, 300);
    });
  },
  beforeDestroy() {
    // 移除全局触摸事件监听
    uni.$off('touchmove');
  },
  methods: {
    // 轮播图切换事件
    onSwiperChange(e) {
      this.currentPage = e.detail.current;
    },

    // 切换完门店执行切换按钮颜色
    setColor(ecology) {
      try {
        this.ecology = ecology ? ecology : '敬请期待~'
        const storeInfo = uni.getStorageSync('storeInfo')
        this.selColor = (storeInfo && storeInfo.button) ? storeInfo.button : '#131315'
      } catch (error) {
        console.warn('setColor error:', error)
        this.ecology = '敬请期待~'
        this.selColor = '#131315'
      }
    },
    tabbarChange(item, index) {
      this.noIndex = index
      if (index == 2) {
        // 如果当前是约课页面(current === 3)或购买页面(current === 1)，禁止点击中间Tab进入卡片预览
        if(this.current === 3 || this.current === 1) {
          // 在约课页面或购买页面，禁止打开卡片预览
          let pageName = this.current === 3 ? '约课' : '购买'
          uni.showToast({
            title: `${pageName}页面不可用`,
            icon: 'none',
            duration: 2000
          })
          return
        }

        this.noIndex = index
        // 入场阶段：TabBar和卡片同步动画
        this.showBox = true
        this.showFixed = true
        this.isTabBarReturning = false
        // 阻止页面滚动
        this.preventScroll(true)

        // 尝试使用微信小程序API控制弹性效果
        try {
          // 微信小程序环境
          // #ifdef MP-WEIXIN
          wx.setPageStyle({
            style: {
              overflow: 'hidden'
            }
          })
          // #endif
        } catch(e) {
          // 忽略错误
        }

        // 修改：保留图标可见
        setTimeout(() => {
          this.isTabBarHidden = false
        }, 400)

        return
      } else {
        uni.switchTab({
          url: item.pagePath
        })
      }
    },

    // 关闭预览
    closePreview() {
      // 退场阶段：卡片先开始退场
      this.isCardExiting = true;

      // 延迟150ms启动TabBar返回动画
      setTimeout(() => {
        // 明确设置TabBar返回动画状态
        this.isTabBarReturning = true;
        // TabBar可见
        this.isTabBarHidden = false;
      }, 150);

      // 在动画完成后(400ms)关闭卡片和背景
      setTimeout(() => {
        // 重置卡片状态
        this.isCardExiting = false;

        // 恢复页面滚动
        this.preventScroll(false);

        // 尝试使用微信小程序API恢复滚动效果
        try {
          // 微信小程序环境
          // #ifdef MP-WEIXIN
          wx.setPageStyle({
            style: {
              overflow: 'auto'
            }
          })
          // #endif
        } catch(e) {
          // 忽略错误
        }

        // 延迟重置其他状态，确保动画完成
        setTimeout(() => {
          this.showBox = false;
          this.showFixed = false; // 重置固定图标状态

          // 再延迟一小段时间后重置TabBar状态
          setTimeout(() => {
            this.isTabBarReturning = false;
          }, 200);
        }, 100);
      }, 400);

      this.currentPage = 0;
      this.noIndex = this.current;
    },

    // 添加一个强制关闭的方法
    closePreviewForce() {
      // 直接修改状态关闭预览
      this.showBox = false;
      this.preventScroll(false);

      // 延迟重置其他状态
      setTimeout(() => {
        this.isTabBarHidden = false;
        this.isTabBarReturning = true;

        setTimeout(() => {
          this.currentPage = 0;
          this.noIndex = this.current;
          this.isTabBarReturning = false;
        }, 200);
      }, 50);
    },

    // 改进防止滚动的方法
    preventScroll(prevent) {
      // 通过页面状态控制滚动
      this.pageScrollLocked = prevent;

      // 使用全局变量控制滚动状态
      getApp().globalData = getApp().globalData || {};
      getApp().globalData.pageScrollLocked = prevent;

      // 尝试使用微信小程序原生API控制滚动
      try {
        // #ifdef MP-WEIXIN
        wx.setPageStyle({
          style: {
            overflow: prevent ? 'hidden' : 'auto'
          }
        })
        // #endif
      } catch(e) {
        // 忽略错误
      }
    },

    // 更强的触摸阻止函数
    preventTouchMove(e) {
      if (this.pageScrollLocked) {
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
        return false;
      }
    },

    // 添加卡片下拉处理方法
    handleCardPulling(data) {
      // 根据下拉距离动态调整容器的样式或类
      // 这里我们可以设置一个标志，表示卡片正在被下拉
      this.isCardPulling = true;
      this.pullDistance = data.distance;
    },

    // 专门处理home图标点击的方法
    onHomeClick() {
      this.showBox = false;
      this.showFixed = false; // 重置固定图标状态
      setTimeout(() => {
        this.preventScroll(false);
        this.isTabBarHidden = false;
        this.isTabBarReturning = true;
        this.currentPage = 0;
        this.noIndex = this.current;
      }, 50);
    },

    // 添加点击指示点切换页面的方法
    handleDotTap(index) {
      this.currentPage = index;
    }
  }
}
</script>

<style lang="scss">
/* 全屏底层白色背景 */
.fullscreen-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background-color: #FFFFFF;
  z-index: 997;
  animation: fadeIn 300ms ease;
  touch-action: none;
  overflow: visible;
}

/* 底部提示样式 */
.bottom-hint {
  width: 100%;
  position: absolute;
  bottom: 80rpx;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.fox-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  width: 100%;
  padding: 10rpx 0;
}

.fox-line {
  width: 125rpx;
  height: 1px;
  background-color: #CCCCCC;
  margin: 0 16rpx;
}

.fox-text {
  font-size: 26rpx;
  color: #666666;
  letter-spacing: 2rpx;
  font-weight: 500;
}

.fox-subtext {
  font-size: 22rpx;
  color: #999999;
  letter-spacing: 0.5rpx;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* 整体卡片容器 - 包含卡片和按钮 */
.card-container-with-buttons {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -45%);
  width: 100%;
  margin-top: 280rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 999;
  will-change: transform, opacity;
  height: auto;
  opacity: 0;
  pointer-events: none;
  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
  padding-bottom: 80rpx;
  touch-action: none;
  overflow: visible;
  transform: translate(-50%, -45%) translateY(20px);
  /* 添加初始向下偏移 */
}

.card-container-with-buttons.show {
  opacity: 1;
  transform: translate(-50%, -50%) translateY(0);
  /* 向上淡入浮现 */
  pointer-events: auto;
}

/* swiper样式 */
.card-swiper {
  width: 100%;
  height: 100vh;
  touch-action: pan-x pan-y;
  overflow: visible;
  /* 确保卡片可以超出容器 */
}

.card-swiper-item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  top: -270rpx;
  box-sizing: border-box;
  transform: scale(1);
  padding-bottom: 30rpx;
  /* 为阴影添加额外空间 */
  overflow: visible;
  /* 确保卡片可以超出容器 */
}

.card-item {
  width: 82%;
  height: 100%;
  max-height: 900rpx;
  display: inline-block;
  border-radius: 66rpx;
  box-shadow: none;
  /* 移除阴影效果 */
  transition: all 0.3s ease;
  transform: scale(0.85) translateY(5rpx);
  opacity: 0.9;
  position: relative;
  /* 确保为绝对定位的子元素提供参考 */
  z-index: 100;
  /* 添加中等级别的z-index */
  overflow: visible;
  /* 改为visible使阴影可以超出容器 */
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
}

.card-item.active-card {
  transform: scale(1) translateY(-15rpx);
  opacity: 1;
  box-shadow: none;
  z-index: 101;
}

.card-item.near-active {
  transform: scale(0.9) translateY(0);
  opacity: 0.9;
  box-shadow: none;
}

/* 修改swiper组件样式，确保其不会裁剪子元素 */
.card-swiper .wx-swiper-dots {
  position: relative;
  z-index: 98;
}

/* TabBar样式，包含入场和退场动画 */
.tab-bar {
  .tab_bgi {
    position: absolute;
    z-index: -1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-image: url('@/static/tabbar/tab_big1.png');
    background-size: 100% 100%;
  }

  display: flex;
  justify-content: center;
  align-items: end;
  width: 702rpx;
  height: 170rpx;
  height: 98rpx;
  border-radius: 66rpx;
  z-index: 996;
  position: fixed;
  bottom: 56rpx;
  left: 50%;
  transform: translateX(-50%) translateY(0);
  opacity: 1;
  transition: transform 400ms ease-in-out,
  opacity 400ms ease-in-out;
  will-change: transform,
  opacity;

  .tab-bar-item {
    flex: 1;
    height: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    &:nth-child(4) {
      padding-top: 0;
    }

    .tab_img {
      width: 44rpx;
      height: 44rpx;
      display: block;
      margin: auto;
    }

    .tab_text {
      margin-top: 2rpx;
      color: #333;
      font-size: 26rpx;
      color: #945048;
      line-height: 30rpx;
      text-align: center;
    }
  }
}

/* TabBar退出动画 */
.tab-bar-exit {
  transform: translateX(-50%) translateY(100%);
  /* 向下淡出位移 */
  opacity: 0;
  transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);
  /* 确保动画持续400ms */
  will-change: transform, opacity;
}

/* TabBar返回动画 */
.tab-bar-enter {
  transform: translateX(-50%) translateY(0);
  /* 向上淡入复位 */
  opacity: 1;
  transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);
  /* 确保动画持续400ms */
  will-change: transform, opacity;
}

/* 添加图标可见的样式 */
.tab-bar-icon-visible .tab-bar-item:nth-child(3) {
  opacity: 1 !important;
  z-index: 998;
}

/* 固定显示的tab图标 */
.tab-fixed-visible {
  position: relative;
  z-index: 998;
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* 卡片通用样式 */
.card-preview-container {
  background: #FFF;
  border-radius: 66rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transform-style: preserve-3d;
  user-select: none;
  touch-action: pan-x pan-y;
  overflow: visible;
  /* 改为visible使阴影可以超出容器 */
  position: relative;
  z-index: 1000;
  /* 确保显示在白色背景之上 */
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 156rpx;
  left: 0;
  right: 0;
  width: 100%;
  height: 100rpx;
  z-index: 998;
}

.header-image {
  width: 100rpx;
  height: 100rpx;
}

.header-left image {
  width: 48rpx;
  height: 48rpx;
  padding-left: 20rpx;
}

.header-right image {
  width: 48rpx;
  height: 48rpx;
  padding-right: 20rpx;
}

/* 简单稳定的分页指示器样式 */
.page-dots {
  position: fixed;
  bottom: 620rpx;
  left: 0;
  width: 100%;
  height: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: -1;
}

.page-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin: 0 10rpx;
}

/* 卡片退场动画 */
.card-container-with-buttons.exit {
  opacity: 0;
  transform: translate(-50%, -45%) translateY(20px);
  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
}
</style>