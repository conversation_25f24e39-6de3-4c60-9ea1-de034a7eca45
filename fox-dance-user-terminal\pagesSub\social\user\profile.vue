<template>
  <view class="user-profile-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <u-icon name="arrow-left" size="24" color="#333" @click="goBack"></u-icon>
        <text class="title">用户主页</text>
        <u-icon name="more-dot-fill" size="24" color="#333" @click="showMoreActions"></u-icon>
      </view>
    </view>

    <scroll-view class="content" scroll-y>
      <!-- 用户信息 -->
      <view class="user-section">
        <view class="user-info">
          <u-avatar :src="userInfo.avatar" size="80"></u-avatar>
          <view class="user-details">
            <text class="nickname">{{ userInfo.nickname }}</text>
            <text class="user-id">ID: {{ userInfo.userId }}</text>
            <text class="bio">{{ userInfo.bio || '这个人很懒，什么都没有留下...' }}</text>
          </view>
          <u-button 
            :type="userInfo.isFollowed ? 'default' : 'primary'" 
            size="small" 
            :text="userInfo.isFollowed ? '已关注' : '关注'"
            @click="toggleFollow"
          ></u-button>
        </view>

        <!-- 数据统计 -->
        <view class="stats-section">
          <view class="stat-item">
            <text class="stat-number">{{ userInfo.postCount }}</text>
            <text class="stat-label">帖子</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userInfo.followingCount }}</text>
            <text class="stat-label">关注</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userInfo.followersCount }}</text>
            <text class="stat-label">粉丝</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userInfo.likeCount }}</text>
            <text class="stat-label">获赞</text>
          </view>
        </view>
      </view>

      <!-- 用户帖子 -->
      <view class="posts-section">
        <view class="section-header">
          <text class="section-title">TA的帖子</text>
        </view>
        
        <view class="post-grid">
          <PostCard
            v-for="post in userPosts"
            :key="post.id"
            :post="post"
            class="post-card-item"
            @click="goPostDetail"
            @user-click="goUserProfile"
            @like="onPostLike"
          />
        </view>

        <!-- 空状态 -->
        <view v-if="!userPosts.length" class="empty-state">
          <u-icon name="file-text" color="#ccc" size="60"></u-icon>
          <text class="empty-text">暂无帖子</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import PostCard from '../components/PostCard.vue'
import { getUserProfile, getPostList, followUser, unfollowUser, checkFollowStatus, likePost, unlikePost } from '@/utils/socialApi.js'

export default {
  name: 'UserProfile',
  components: {
    PostCard
  },
  data() {
    return {
      userId: '',
      userInfo: {
        userId: '123456',
        nickname: '用户昵称',
        avatar: 'https://picsum.photos/100/100?random=999',
        bio: '分享生活的美好瞬间 ✨',
        postCount: 28,
        followingCount: 156,
        followersCount: 324,
        likeCount: 1248,
        isFollowed: false
      },
      userPosts: []
    }
  },
  onLoad(options) {
    this.userId = options.id
    this.loadUserInfo()
    this.loadUserPosts()
  },
  methods: {
    async loadUserInfo() {
      try {
        // 加载用户资料
        const userProfile = await getUserProfile(this.userId)
        if (userProfile) {
          this.userInfo = {
            id: userProfile.id,
            userId: userProfile.userId || this.userId,
            nickname: userProfile.nickname || '用户' + this.userId,
            avatar: userProfile.avatar || 'https://picsum.photos/200/200?random=' + this.userId,
            bio: userProfile.bio || '这个人很懒，什么都没有留下...',
            danceType: userProfile.danceType || '街舞',
            postCount: userProfile.postCount || 0,
            followingCount: userProfile.followingCount || 0,
            followersCount: userProfile.followersCount || 0,
            likeCount: userProfile.likeCount || 0,
            isFollowed: false
          }
        }

        // 检查关注状态
        const followStatus = await checkFollowStatus(this.userId)
        this.userInfo.isFollowed = followStatus.isFollowed || false

      } catch (error) {
        console.error('加载用户信息失败:', error)
        // 使用默认用户信息
      }
    },

    async loadUserPosts() {
      try {
        const result = await getPostList({
          current: 1,
          size: 20,
          userId: this.userId,
          sortField: 'createTime',
          sortOrder: 'desc'
        })

        if (result && result.records) {
          this.userPosts = result.records.map(post => ({
            id: post.id,
            title: post.title || '',
            coverImage: post.coverImage || (post.images && post.images[0]) || 'https://picsum.photos/400/400?random=' + post.id,
            username: post.userNickname || this.userInfo.nickname,
            userAvatar: post.userAvatar || this.userInfo.avatar,
            content: post.content,
            likeCount: post.likeCount || 0,
            commentCount: post.commentCount || 0,
            isLiked: post.isLiked || false,
            createTime: new Date(post.createTime)
          }))
        } else {
          this.userPosts = []
        }
      } catch (error) {
        console.error('加载用户帖子失败:', error)
        // 使用模拟数据作为后备
        const posts = []
        for (let i = 0; i < 6; i++) {
          posts.push({
            id: Date.now() + i,
            title: `用户帖子 ${i + 1}`,
            username: this.userInfo.nickname,
            userAvatar: this.userInfo.avatar,
            content: `这是用户发布的第${i + 1}条帖子`,
            coverImage: `https://picsum.photos/300/400?random=${Date.now() + i}`,
            likeCount: Math.floor(Math.random() * 500),
            commentCount: Math.floor(Math.random() * 50),
            isLiked: Math.random() > 0.7,
            createTime: new Date(Date.now() - Math.random() * 86400000 * 30)
          })
        }
        this.userPosts = posts
      }
    },

    async toggleFollow() {
      try {
        if (this.userInfo.isFollowed) {
          // 取消关注
          await unfollowUser(this.userId)
          this.userInfo.isFollowed = false
          this.userInfo.followersCount = Math.max(0, this.userInfo.followersCount - 1)
          uni.showToast({
            title: '取消关注',
            icon: 'none'
          })
        } else {
          // 关注用户
          await followUser(this.userId)
          this.userInfo.isFollowed = true
          this.userInfo.followersCount += 1
          uni.showToast({
            title: '关注成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('关注操作失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },

    async onPostLike(post) {
      try {
        if (post.isLiked) {
          // 取消点赞
          await unlikePost(post.id)
          post.isLiked = false
          post.likeCount = Math.max(0, post.likeCount - 1)
        } else {
          // 点赞
          await likePost(post.id)
          post.isLiked = true
          post.likeCount += 1
        }

        // 更新帖子列表中的数据
        const index = this.userPosts.findIndex(p => p.id === post.id)
        if (index !== -1) {
          this.$set(this.userPosts, index, { ...post })
        }

      } catch (error) {
        console.error('点赞操作失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },

    goPostDetail(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      })
    },

    goUserProfile(post) {
      // 如果是当前用户，不需要跳转
      if (post.userId === this.userId) return
      
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${post.userId || post.id}`
      })
    },

    goBack() {
      uni.navigateBack()
    },

    showMoreActions() {
      uni.showActionSheet({
        itemList: ['举报用户', '拉黑用户'],
        success: (res) => {
          console.log('更多操作:', res.tapIndex)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.user-profile-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.content {
  margin-top: calc(44px + var(--status-bar-height));
  padding: 16px;
  width: auto;
}

.user-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.user-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.user-details {
  flex: 1;
  margin-left: 16px;
  margin-right: 12px;
}

.nickname {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.user-id {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 8px;
}

.bio {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  display: block;
}

.stats-section {
  display: flex;
  justify-content: space-around;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.posts-section {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.post-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.post-card-item {
  width: calc(50% - 4px);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-top: 12px;
}
</style>
