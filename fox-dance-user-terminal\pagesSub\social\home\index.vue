<template>
  <view class="home-container">
    <view class="header">
      <view class="header-content">
        <view class="logo">
          <text class="logo-text">社区</text>
        </view>
        <view class="header-actions">
          <u-icon name="search" size="24" color="#666" @click="goSearch"></u-icon>
        </view>
      </view>
    </view>

    <!-- 话题标签栏 -->
    <view class="topic-tabs-container">
      <u-sticky bgColor="#fff">
        <u-tabs
          :list="topicList"
          :current="currentTopicIndex"
          @change="selectTopic"
          :scrollable="true"
          activeColor="#2979ff"
          inactiveColor="#666"
          fontSize="28"
          lineColor="#2979ff"
          lineWidth="40"
          lineHeight="6"
          height="80"
          itemStyle="padding: 0 32rpx;"
        ></u-tabs>
      </u-sticky>
    </view>

    <!-- 帖子网格列表 -->
    <scroll-view
      class="post-list"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view class="post-grid">
        <PostCard
          v-for="post in postList"
          :key="post.id"
          :post="post"
          class="post-card-item"
          @click="goPostDetail"
          @user-click="goUserProfile"
          @like="onPostLike"
        />
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="loading">
        <u-loading mode="circle" size="24"></u-loading>
        <text class="load-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view v-if="!postList.length && !loading" class="empty-state">
        <u-icon name="file-text" color="#ccc" size="120rpx"></u-icon>
        <text class="empty-text">{{ getEmptyText() }}</text>
        <text class="empty-desc">{{ getEmptyDesc() }}</text>
      </view>
    </scroll-view>

  </view>
</template>

<script>
import PostCard from '../components/PostCard.vue'
import { getPostList, getHotTags, likePost, unlikePost } from '@/utils/socialApi.js'

export default {
  name: 'SocialHome',
  components: {
    PostCard
  },
  data() {
    return {
      postList: [],
      loading: false,
      refreshing: false,
      page: 1,
      pageSize: 10,
      currentTopic: 'all',
      currentTopicIndex: 0,
      topicList: [
        { name: '全部', id: 'all' },
        { name: '街舞', id: 'street-dance' },
        { name: '现代舞', id: 'modern-dance' },
        { name: '芭蕾', id: 'ballet' },
        { name: '拉丁舞', id: 'latin-dance' },
        { name: '爵士舞', id: 'jazz-dance' },
        { name: '民族舞', id: 'folk-dance' },
        { name: '古典舞', id: 'classical-dance' },
        { name: '舞蹈教学', id: 'dance-teaching' },
        { name: '舞蹈比赛', id: 'dance-competition' }
      ],
      hasMore: true
    }
  },
  onLoad() {
    this.loadHotTags()
    this.loadPosts()
  },
  methods: {
    // 加载热门话题
    async loadHotTags() {
      try {
        const hotTags = await getHotTags(10)
        if (hotTags && hotTags.length > 0) {
          // 保留"全部"选项，添加热门话题
          const allOption = this.topicList[0]
          this.topicList = [allOption, ...hotTags.map(tag => ({
            name: tag.tagName || tag.name,
            id: tag.tagId || tag.id
          }))]
        }
      } catch (error) {
        console.error('加载热门话题失败:', error)
        // 使用默认话题列表
      }
    },

    async loadPosts(refresh = false) {
      if (this.loading) return

      this.loading = true
      try {
        const params = {
          current: refresh ? 1 : this.page,
          size: this.pageSize,
          sortField: 'createTime',
          sortOrder: 'desc'
        }

        // 如果选择了特定话题，添加话题筛选
        if (this.currentTopic !== 'all') {
          params.tagId = this.currentTopic
        }

        const result = await getPostList(params)

        if (result && result.records) {
          const posts = result.records.map(post => ({
            id: post.id,
            title: post.title || '',
            username: post.userNickname || '用户' + post.userId,
            userAvatar: post.userAvatar || 'https://picsum.photos/100/100?random=' + post.userId,
            content: post.content,
            coverImage: post.coverImage || (post.images && post.images[0]) || 'https://picsum.photos/300/400?random=' + post.id,
            images: post.images || [],
            topics: post.tags || [],
            topicId: this.currentTopic,
            likeCount: post.likeCount || 0,
            commentCount: post.commentCount || 0,
            isLiked: post.isLiked || false,
            createTime: new Date(post.createTime)
          }))

          if (refresh) {
            this.postList = posts
            this.page = 2
          } else {
            this.postList = [...this.postList, ...posts]
            this.page++
          }

          // 检查是否还有更多数据
          this.hasMore = result.records.length >= this.pageSize
        } else {
          // 如果API调用失败，使用模拟数据
          const mockPosts = this.generateMockPosts()
          if (refresh) {
            this.postList = mockPosts
            this.page = 2
          } else {
            this.postList = [...this.postList, ...mockPosts]
            this.page++
          }
        }

      } catch (error) {
        console.error('加载帖子失败:', error)
        // 使用模拟数据作为后备
        const mockPosts = this.generateMockPosts()
        if (refresh) {
          this.postList = mockPosts
          this.page = 2
        } else {
          this.postList = [...this.postList, ...mockPosts]
          this.page++
        }
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    generateMockPosts() {
      const posts = []
      const topicData = {
        life: { titles: ['今日穿搭分享', '生活小确幸', '宠物日常', '家居装饰'], name: '生活' },
        food: { titles: ['美食探店记录', '美妆教程', '手工制作'], name: '美食' },
        travel: { titles: ['旅行日记', '风景随拍'], name: '旅行' },
        photography: { titles: ['摄影作品', '风景随拍'], name: '摄影' },
        fitness: { titles: ['健身打卡'], name: '健身' },
        fashion: { titles: ['今日穿搭分享', '美妆教程'], name: '时尚' },
        music: { titles: ['音乐推荐'], name: '音乐' },
        movie: { titles: ['电影观后感'], name: '电影' },
        reading: { titles: ['读书笔记', '学习心得'], name: '读书' }
      }

      // 如果选择了特定话题，只生成该话题的帖子
      let availableTopics = Object.keys(topicData)
      if (this.currentTopic !== 'all') {
        availableTopics = [this.currentTopic]
      }

      for (let i = 0; i < this.pageSize; i++) {
        const randomTopicKey = availableTopics[Math.floor(Math.random() * availableTopics.length)]
        const topicInfo = topicData[randomTopicKey]
        const randomTitle = topicInfo.titles[Math.floor(Math.random() * topicInfo.titles.length)]
        const randomId = Math.floor(Math.random() * 1000)

        posts.push({
          id: Date.now() + i,
          title: randomTitle,
          username: `用户${randomId}`,
          userAvatar: `https://picsum.photos/100/100?random=${randomId}`,
          content: `${randomTitle} - 分享我的生活点滴，希望大家喜欢！`,
          coverImage: `https://picsum.photos/300/400?random=${Date.now() + i}`,
          images: [`https://picsum.photos/300/400?random=${Date.now() + i}`],
          topics: [topicInfo.name],
          topicId: randomTopicKey,
          likeCount: Math.floor(Math.random() * 2000),
          commentCount: Math.floor(Math.random() * 100),
          isLiked: Math.random() > 0.7,
          createTime: new Date(Date.now() - Math.random() * 86400000 * 7)
        })
      }
      return posts
    },

    onRefresh() {
      this.refreshing = true
      this.page = 1
      this.postList = []
      this.loadPosts(true)
    },

    loadMore() {
      if (!this.loading) {
        this.page++
        this.loadPosts()
      }
    },

    formatTime(time) {
      const now = new Date()
      const diff = now - new Date(time)
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      return `${days}天前`
    },

    async onPostLike(post) {
      try {
        if (post.isLiked) {
          // 取消点赞
          await unlikePost(post.id)
          post.isLiked = false
          post.likeCount = Math.max(0, post.likeCount - 1)
        } else {
          // 点赞
          await likePost(post.id)
          post.isLiked = true
          post.likeCount += 1
        }

        // 更新帖子列表中的数据
        const index = this.postList.findIndex(p => p.id === post.id)
        if (index !== -1) {
          this.$set(this.postList, index, { ...post })
        }

      } catch (error) {
        console.error('点赞操作失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },

    goPostDetail(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      })
    },

    goUserProfile(post) {
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${post.userId || post.id}`
      })
    },

    goSearch() {
      uni.navigateTo({
        url: '/pagesSub/social/search/index'
      })
    },

    selectTopic(index) {
      if (this.currentTopicIndex === index) return

      this.currentTopicIndex = index
      this.currentTopic = this.topicList[index].id
      this.page = 1
      this.postList = []

      // 重新加载帖子
      this.loadPosts(true)
    },

    // 格式化时间
    formatTime(dateString) {
      if (!dateString) return ''

      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date

      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`

      return date.toLocaleDateString()
    },

    getEmptyText() {
      const currentTopicName = this.topicList.find(topic => topic.id === this.currentTopic)?.name || '全部'
      return this.currentTopic === 'all' ? '暂无帖子' : `暂无${currentTopicName}相关帖子`
    },

    getEmptyDesc() {
      return this.currentTopic === 'all' ? '快来发布第一条帖子吧' : '换个话题看看其他内容吧'
    }
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 100px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 88rpx;

  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;

}

.logo-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #2979ff;
}

.topic-tabs-container {
  position: fixed;
  top: calc(88rpx + var(--status-bar-height));
  left: 0;
  right: 0;
  z-index: 99;
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;
}

/* uview tabs组件样式优化 */
.topic-tabs-container ::v-deep .u-tabs {
  background: #fff;
}

.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item {
  padding: 0 32rpx !important;
}

.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item__text {
  font-size: 28rpx !important;
  font-weight: 500;
}

.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__line {
  border-radius: 6rpx;
}

.post-list {
  margin-top: calc(168rpx + var(--status-bar-height));
  margin: 230rpx 26rpx;
  width: auto;
}

.post-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 14rpx;
  padding-bottom: 40rpx;
}

.post-card-item {
  width: calc(50% - 8rpx);
  margin-bottom: 16rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 160rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin: 32rpx 0 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #ccc;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.load-text {
  margin-left: 16rpx;
  color: #999;
  font-size: 28rpx;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .post-list {
    padding: 12rpx;
  }

  .post-grid {
    gap: 12rpx;
  }

  .post-card-item {
    width: calc(50% - 6rpx);
  }
}

@media screen and (min-width: 768px) {
  .post-grid {
    gap: 24rpx;
  }

  .post-card-item {
    width: calc(33.33% - 16rpx);
  }
}

@media screen and (min-width: 1024px) {
  .post-list {
    padding: 32rpx 64rpx;
  }

  .post-card-item {
    width: calc(25% - 18rpx);
  }
}
</style>
