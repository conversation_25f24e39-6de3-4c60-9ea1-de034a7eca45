<template>
  <view class="reply-skeleton">
    <!-- 用户头像骨架 -->
    <view class="skeleton-avatar">
      <view class="skeleton-circle"></view>
    </view>
    
    <!-- 回复内容骨架 -->
    <view class="skeleton-content">
      <!-- 用户名和回复对象骨架 -->
      <view class="skeleton-header">
        <view class="skeleton-line skeleton-name"></view>
        <view class="skeleton-line skeleton-reply-to"></view>
        <view class="skeleton-line skeleton-time"></view>
      </view>
      
      <!-- 回复文本骨架 -->
      <view class="skeleton-text">
        <view class="skeleton-line skeleton-text-line1"></view>
        <view class="skeleton-line skeleton-text-line2"></view>
      </view>
      
      <!-- 操作按钮骨架 -->
      <view class="skeleton-actions">
        <view class="skeleton-line skeleton-action"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ReplySkeleton'
}
</script>

<style lang="scss" scoped>
.reply-skeleton {
  padding: 28rpx 0;
  border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);
  display: flex;
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

.skeleton-avatar {
  margin-right: 24rpx;
  
  .skeleton-circle {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeletonShimmer 1.5s infinite;
  }
}

.skeleton-content {
  flex: 1;
}

.skeleton-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  gap: 12rpx;
  flex-wrap: wrap;
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonShimmer 1.5s infinite;
  border-radius: 6rpx;
}

.skeleton-name {
  width: 100rpx;
  height: 28rpx;
}

.skeleton-reply-to {
  width: 80rpx;
  height: 24rpx;
}

.skeleton-time {
  width: 60rpx;
  height: 20rpx;
}

.skeleton-text {
  margin-bottom: 16rpx;
}

.skeleton-text-line1 {
  width: 90%;
  height: 28rpx;
  margin-bottom: 10rpx;
}

.skeleton-text-line2 {
  width: 70%;
  height: 28rpx;
}

.skeleton-actions {
  display: flex;
  gap: 16rpx;
}

.skeleton-action {
  width: 60rpx;
  height: 24rpx;
}

/* 骨架屏动画 */
@keyframes skeletonShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes skeletonPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 小红书风格优化 */
.reply-skeleton {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 107, 135, 0.08),
      transparent
    );
    animation: skeletonSweep 2.5s infinite;
  }
}

@keyframes skeletonSweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .skeleton-avatar .skeleton-circle {
    width: 64rpx;
    height: 64rpx;
  }
  
  .skeleton-text-line1,
  .skeleton-text-line2 {
    height: 26rpx;
  }
}
</style>
