<template>
	<view class="membershipCard">
		<view class="memk_one">
			<view :class="cardType == 0 ? 'memk_one_ac' : ''" @click="cardTab(0)">次卡<text></text></view>
			<view :class="cardType == 1 ? 'memk_one_ac' : ''" @click="cardTab(1)">时长卡<text></text></view>
		</view>
		<template v-if="loding">
		<view class="memk_ban">
			<!-- <swiper class="swiper" autoplay="1500" :indicator-dots="true" :circular='true'
				indicator-active-color="#ffffff" indicator-color="#cccccc">
				<swiper-item class="swiper-wrap" v-for="(item,index) in 3" :key="index">
					<image src="/static/images/index1111.png" mode="aspectFill" @click="goBannerTap(item.url)"></image>
				</swiper-item>
			</swiper> -->
			<image :src="cardType == 0 ? imgbaseUrl + cardsInfo.secondary_card_image : imgbaseUrl + cardsInfo.duration_chart" mode="aspectFill" class="memk_ban_bj"></image>
			<view class="memk_ban_n"><view class="memk_ban_n_v" @click="xzmdTap"><view>{{mdText}}</view><text></text></view></view>
		</view>
		
		<view class="memk_two">
			<view class="memk_two_t"><view class="kcxq_two_t_n"><text>尊享特权</text><text style="display:none;"></text></view></view>
			<view class="memk_two_b">
				<view class="memk_two_b_li" v-for="(item,index) in cardsInfo.privilege" :key="index">
					<view class="memk_two_b_li_l"><template v-if="index <= 8">0</template>{{index+1}}</view>
					<view class="memk_two_b_li_r"><view>{{item}}</view></view>
				</view>
			</view>
		</view>
		<!-- 0=次卡,1=年卡,2=月卡 -->
		<view class="memk_thr" v-if="cardsInfo.card.length > 0">
			<view class="memk_thr_li" v-for="(item,index) in cardsInfo.card" :class="hyType == index ? 'memk_thr_li_ac' : ''" :key="index" @click="yhTap(index)">
				<view class="memk_thr_li_a"><text>{{item.number}}</text>{{item.type*1 == 0 ? '次' : item.type*1 == 1 ? '年' : item.type*1 == 2 ? '个月' : ''}}</view>
				<view class="memk_thr_li_b">{{item.average_price}}</view>
				<!-- <view class="memk_thr_li_b">{{item.proportion*1}}元/{{item.type*1 == 0 ? '次' : item.type*1 == 1 ? '年' : item.type*1 == 2 ? '月' : ''}}</view> -->
				<view class="memk_thr_li_c"><text>￥{{item.price*1}}</text></view>
				<image src="/static/images/icon28-1.png" class="memk_thr_li_xz"></image>
			</view>
		</view>
		
		<view class="memk_fou" @click="ljktTap">立即开通</view>
		<view class="memk_fiv">开通会员前请阅读<text @click.stop="navTo('/pages/login/xieYi?type=99')">《会员服务协议》</text></view>
		<view class="memk_six">
			<view class="memk_six_a"><image src="/static/images/icon29.png"></image>约课说明</view>
			<view class="memk_six_b">{{cardsInfo.appointment_instructions}}</view>
		</view>
		</template>
		
		<view class="xytc" v-if="xyToggle">
			<view class="xytcCon">
				<view class="xytcCon_a">会员服务协议提示</view>
				<view class="xytcCon_b">欢迎使用Fox舞蹈小程序!为了更好的保您的个人权益，在使用本产品前，请先阅读并同意以下内容:</view>
				<!-- <view class="xytcCon_c"><view><rich-text :nodes="xyCont"></rich-text></view></view> -->
				<view class="xytcCon_c">
					<scroll-view
					    class="scroll-view"
					    :style="{ height: scrollViewHeight + 'px' }"
					    scroll-y
					    @scroll="onScroll"
						id="myScrollView"
					    ref="scrollViewRef"
					  >
					    <!-- 这里放置需要滚动的内容 -->
						<view><rich-text :nodes="xyCont"></rich-text></view>
					  </scroll-view>
				</view>
				<view class="xytcCon_b">如您同意以上内容，请点击同意并继续，开始使用我们的产品和服务!</view>
				<view class="xytcCon_f">
					<view class="ty" @click="tyTap" :style="isScrollToBottom ? '' : 'opacity:.7'">{{isScrollToBottom ? '同意并继续' : '请仔细阅读下滑查看完毕'}}</view>
					<view class="noty" @click="xyGbTap">不同意</view>
				</view>
			</view>
		</view>
		
	</view>
</template>


<script>
import {
	cardsApi,
	buyCardsApi
} from '@/config/http.achieve.js'
import util from '@/utils/utils.js';
export default {
	data() {
		return {
			cardType:0,
			isLogined:false,
			loding:false,
			tqLists:[
				{name:'特权'},
				{name:'特权特权特权'},
				{name:'特权特权特权特权特权'},
				{name:'特权'},
			],
			hyLists:[
				{},
				{},
				{},
				{},
				{},
			],
			hyType:0,
			imgbaseUrl:'',//图片地址
			cardsInfo:{},
			ck_selectStores:[],//次卡
			sck_selectStores:[],//时长卡
			mdText:'',
			
			xyCont:'',
			xyToggle:false,
			scrollViewHeight: 300, // 可根据实际情况调整滚动视图的高度
			isScrollToBottom: false // 标记是否滚动到底部
		}
	},
	onShow() {
		console.log('走吗1五')
	},
	methods: {
		onScroll(e) {
			var that = this;
		  this.$nextTick(() => {
				
			  const query = uni.createSelectorQuery().in(this);
			  query.select('.scroll-view').boundingClientRect();
			  query.select('.scroll-view').scrollOffset();
			  query.exec((res) => {
				  console.log(res,'res')
				if (res && res[0] && res[1]) {
				  const scrollViewHeight = res[0].height;
				  const scrollTop = res[1].scrollTop;
				  const scrollHeight = e.detail.scrollHeight;
				  if (scrollTop + scrollViewHeight >= scrollHeight-20) {
					console.log('已经滚动到底部');
					that.isScrollToBottom = true;
				  }
				}
			  });
			
		  });
		},
		//立即开通
		ljktTap(){
			if(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
				return false;
			}
			if(this.cardsInfo.card.length == 0){
				uni.showToast({
					icon: 'none',
					title: '暂无要开通的卡'
				});
				return false;
			}
			//次卡
			if(this.cardType == 0){
				if(uni.getStorageSync('ck_selectStores')){
					//取选择门店
					var mdid = [];
					for(var i=0;i<uni.getStorageSync('ck_selectStores').length;i++){
						mdid.push(uni.getStorageSync('ck_selectStores')[i].id)
					}
					var card_id = mdid.join(',')
				}else{
					//取默认门店
					var card_id = uni.getStorageSync('storeInfo').id
				}
			}
			
			//时长卡
			if(this.cardType == 1){
				if(uni.getStorageSync('sck_selectStores')){
					//取选择门店
					var mdid = [];
					for(var j=0;j<uni.getStorageSync('sck_selectStores').length;j++){
						mdid.push(uni.getStorageSync('sck_selectStores')[j].id)
					}
					var card_id = mdid.join(',')
				}else{
					//取默认门店
					var card_id = uni.getStorageSync('storeInfo').id
				}
			}
			this.xyToggle = true;
			/*var price = this.cardsInfo.card[this.hyType].price
			var ids = this.cardsInfo.card[this.hyType].id
			uni.navigateTo({
				url:'/pages/buy/cardsPayment?price=' + price + '&id=' + ids + '&storeid=' + card_id
			})*/
		},
		//同意弹窗
		tyTap(){
			if(!this.isScrollToBottom){
				return false;
			}
			//次卡
			if(this.cardType == 0){
				if(uni.getStorageSync('ck_selectStores')){
					//取选择门店
					var mdid = [];
					for(var i=0;i<uni.getStorageSync('ck_selectStores').length;i++){
						mdid.push(uni.getStorageSync('ck_selectStores')[i].id)
					}
					var card_id = mdid.join(',')
				}else{
					//取默认门店
					var card_id = uni.getStorageSync('storeInfo').id
				}
			}
			
			//时长卡
			if(this.cardType == 1){
				if(uni.getStorageSync('sck_selectStores')){
					//取选择门店
					var mdid = [];
					for(var j=0;j<uni.getStorageSync('sck_selectStores').length;j++){
						mdid.push(uni.getStorageSync('sck_selectStores')[j].id)
					}
					var card_id = mdid.join(',')
				}else{
					//取默认门店
					var card_id = uni.getStorageSync('storeInfo').id
				}
			}
			this.xyToggle = false;
			this.isScrollToBottom = false;
			var price = this.cardsInfo.card[this.hyType].price
			var ids = this.cardsInfo.card[this.hyType].id
			uni.navigateTo({
				url:'/pages/buy/cardsPayment?price=' + price + '&id=' + ids + '&storeid=' + card_id
			})
		},
		//选择门店
		xzmdTap(){
			uni.navigateTo({
				url:'/pages/buy/selectStores?type=' + this.cardType
			})
		},
		onLoadData(){
			this.imgbaseUrl = this.$baseUrl;
			this.isLogined = uni.getStorageSync('token') ? true : false;
			this.mdTextData();//赋值
			this.cardsData();//会员卡
		},
		//赋值
		mdTextData(){
			if(this.cardType == 0){
				if(uni.getStorageSync('ck_selectStores')){
					this.mdText = uni.getStorageSync('ck_selectStores').length == 1 ? uni.getStorageSync('ck_selectStores')[0].name : uni.getStorageSync('ck_selectStores')[0].name + ' 等' + uni.getStorageSync('ck_selectStores').length + '个门店'
				}else{
					this.mdText = '选择门店'
				}
			}
			
			if(this.cardType == 1){
				if(uni.getStorageSync('sck_selectStores')){
					this.mdText = uni.getStorageSync('sck_selectStores').length == 1 ? uni.getStorageSync('sck_selectStores')[0].name : uni.getStorageSync('sck_selectStores')[0].name + ' 等' + uni.getStorageSync('sck_selectStores').length + '个门店'
				}else{
					this.mdText = '选择门店'
				}
			}
		},
		//会员卡
		cardsData(){
			uni.showLoading({
				title: '加载中'
			});
			
			//次卡
			if(this.cardType == 0){
				if(uni.getStorageSync('ck_selectStores')){
					//取选择门店
					var mdid = [];
					for(var i=0;i<uni.getStorageSync('ck_selectStores').length;i++){
						mdid.push(uni.getStorageSync('ck_selectStores')[i].id)
					}
					var card_id = mdid.join(',')
				}else{
					//取默认门店
					var card_id = uni.getStorageSync('storeInfo').id
				}
			}
			
			//时长卡
			if(this.cardType == 1){
				if(uni.getStorageSync('sck_selectStores')){
					//取选择门店
					var mdid = [];
					for(var j=0;j<uni.getStorageSync('sck_selectStores').length;j++){
						mdid.push(uni.getStorageSync('sck_selectStores')[j].id)
					}
					var card_id = mdid.join(',')
				}else{
					//取默认门店
					var card_id = uni.getStorageSync('storeInfo').id
				}
			}
			
			let that = this;
			cardsApi({
				card_id:card_id,
				type:that.cardType*1 + 1
			}).then(res => {
				console.log('会员卡',res)
				if (res.code == 1) {
					that.loding = true;
					that.cardsInfo = res.data;
					// res.data.member_service_agreement =  "<p style='margin-top:100px'>会议会员服务协议</p><p style='margin-top:100px'>会议会员服务协议</p><p style='margin-top:100px'>会议会员服务协议</p><p style='margin-top:100px'>会议会员服务协议</p>"
					
					that.xyCont = util.formatRichText(res.data.member_service_agreement)
					uni.hideLoading();
				}
			})
		},
		//协议关闭
		xyGbTap(){
			this.xyToggle = false;
			this.isScrollToBottom = false;
		},
		cardTab(index){
			this.cardType = index;
			this.mdTextData();//赋值
			this.cardsData();//会员卡
		},
		yhTap(index){
			this.hyType = index;
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
page{background:#F6F6F6;}
</style>