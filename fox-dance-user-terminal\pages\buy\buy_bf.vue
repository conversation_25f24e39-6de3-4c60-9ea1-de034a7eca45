<template>
	<view class="buy" :style="{ '--qjbutton-color': qjbutton }">
		
			<view class="buy_one">
				<view :class="bigType == 0 ? 'buy_one_ac' : ''" @click="bigTab(0)">会员卡</view>
				<view :class="bigType == 1 ? 'buy_one_ac' : ''" @click="bigTab(1)">课包</view>
				<view :class="bigType == 2 ? 'buy_one_ac' : ''" @click="bigTab(2)">周边商城</view>
			</view>
			
			<!-- 会员卡 -->
			<membershipCard v-show="bigType == 0" ref="membershipCardRef" />
			<!-- 会员卡 -->
			
			<!-- 课包 -->
			<coursePackage v-show="bigType == 1" ref="coursePackageRef" />
			<!-- 课包 -->
			
			<!-- 积分商城 -->
			<pointsMall v-show="bigType == 2" ref="pointsMallRef" />
			<!-- 积分商城 -->

			<tabbar ref="tabbar" :current="1"></tabbar>
		
	</view>
</template>


<script>
import tabbar from '@/components/tabbar.vue'
import membershipCard from './membershipCard'
import coursePackage from '../buy/coursePackage/coursePackage.vue'
import pointsMall from '../buy/pointsMall/pointsMall.vue'

export default {
	components: {
		tabbar,
		membershipCard,
		coursePackage,
		pointsMall
	},                                                                          
	data() {
		return {
			bigType:0,
			bgColor:'#fff',
			qjbutton:'#131315',
		}
	},
	onShow() {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.$refs.tabbar.setColor();
		console.log(this.qjbutton,'this.qjbutton')
		uni.hideTabBar()
		if(this.bigType == 0){
			this.$refs.membershipCardRef.onLoadData();
		}
		if(this.bigType == 1){
			this.$refs.coursePackageRef.onLoadData();
		}
		if(this.bigType == 2){
			this.$refs.pointsMallRef.onLoadData();
		}
		if(uni.getStorageSync('qbtz')){
			uni.removeStorageSync('qbtz')
			this.bigType = 1;
			this.$refs.coursePackageRef.onLoadData();
		}
	},
	onLoad() {
		uni.hideTabBar()
	},
	onPageScroll(e) {
		if(this.bigType == 2){
			this.$refs.pointsMallRef.onPageScrollData(e.scrollTop)
		}
	},
	onReachBottom() {
		console.log('到底了');
		if(this.bigType == 1){
			this.$refs.coursePackageRef.onReachBottomData();
		}
		if(this.bigType == 2){
			this.$refs.pointsMallRef.onReachBottomData();
		}
	},
	methods: {
		bigTab(index){
			this.bigType = index;
			if(this.bigType == 0){
				this.$refs.membershipCardRef.onLoadData();
			}
			if(this.bigType == 1){
				this.$refs.coursePackageRef.onLoadData();
			}
			if(this.bigType == 2){
				this.$refs.pointsMallRef.onLoadData();
			}
		}
	}
}
</script>

<style lang="scss">
.buy{overflow:hidden;}
</style>