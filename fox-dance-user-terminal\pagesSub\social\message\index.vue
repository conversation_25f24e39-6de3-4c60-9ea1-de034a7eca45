<template>
  <view class="message-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <text class="title">消息</text>
        <view class="header-actions">
          <u-icon name="search" size="24" color="#333" @click="goSearch"></u-icon>
          <u-icon name="plus" size="24" color="#333" @click="startNewChat"></u-icon>
        </view>
      </view>
    </view>

    <!-- 功能入口 -->
    <view class="quick-actions">
      <view class="action-item" @click="goSystemMessages">
        <view class="action-icon system">
          <u-icon name="bell" color="#fff" size="20"></u-icon>
        </view>
        <text class="action-text">系统消息</text>
        <view v-if="systemUnreadCount" class="unread-badge">{{ systemUnreadCount }}</view>
      </view>
      
      <view class="action-item" @click="goLikeMessages">
        <view class="action-icon like">
          <u-icon name="heart" color="#fff" size="20"></u-icon>
        </view>
        <text class="action-text">赞和评论</text>
        <view v-if="likeUnreadCount" class="unread-badge">{{ likeUnreadCount }}</view>
      </view>
      
      <view class="action-item" @click="goFollowMessages">
        <view class="action-icon follow">
          <u-icon name="account-fill" color="#fff" size="20"></u-icon>
        </view>
        <text class="action-text">新粉丝</text>
        <view v-if="followUnreadCount" class="unread-badge">{{ followUnreadCount }}</view>
      </view>
    </view>

    <scroll-view 
      class="chat-list" 
      scroll-y 
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <!-- 聊天列表 -->
      <view 
        v-for="chat in chatList" 
        :key="chat.id"
        class="chat-item"
        @click="openChat(chat)"
        @longpress="showChatActions(chat)"
      >
        <view class="chat-avatar">
          <u-avatar :src="chat.avatar" size="50"></u-avatar>
          <view v-if="chat.isOnline" class="online-dot"></view>
        </view>
        
        <view class="chat-content">
          <view class="chat-header">
            <text class="chat-name">{{ chat.name }}</text>
            <text class="chat-time">{{ formatTime(chat.lastMessageTime) }}</text>
          </view>
          
          <view class="chat-preview">
            <view class="message-preview">
              <text v-if="chat.lastMessageType === 'text'" class="preview-text">
                {{ chat.lastMessage }}
              </text>
              <text v-else-if="chat.lastMessageType === 'image'" class="preview-text">
                [图片]
              </text>
              <text v-else-if="chat.lastMessageType === 'voice'" class="preview-text">
                [语音]
              </text>
              <text v-else class="preview-text">
                {{ chat.lastMessage }}
              </text>
            </view>
            
            <view class="chat-status">
              <view v-if="chat.unreadCount" class="unread-count">
                {{ chat.unreadCount > 99 ? '99+' : chat.unreadCount }}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="!chatList.length && !loading" class="empty-state">
        <u-icon name="chat" color="#ccc" size="60"></u-icon>
        <text class="empty-text">暂无消息</text>
        <text class="empty-desc">开始与朋友聊天吧</text>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <u-loading mode="circle" size="24"></u-loading>
        <text class="loading-text">加载中...</text>
      </view>
    </scroll-view>

  </view>
</template>

<script>
import { getConversations, getUnreadCount, markMessageAsRead } from '@/utils/socialApi.js'

export default {
  name: 'SocialMessage',
  data() {
    return {
      chatList: [],
      loading: false,
      refreshing: false,
      systemUnreadCount: 3,
      likeUnreadCount: 12,
      followUnreadCount: 5
    }
  },
  onLoad() {
    this.loadUnreadCounts()
    this.loadChatList()
  },
  onShow() {
    // 页面显示时刷新数据
    this.loadChatList()
  },
  methods: {
    // 加载未读消息统计
    async loadUnreadCounts() {
      try {
        const unreadData = await getUnreadCount()
        if (unreadData) {
          this.systemUnreadCount = unreadData.systemCount || 0
          this.likeUnreadCount = unreadData.likeCount || 0
          this.followUnreadCount = unreadData.followCount || 0
        }
      } catch (error) {
        console.error('加载未读消息统计失败:', error)
        // 使用默认值
      }
    },

    async loadChatList() {
      this.loading = true
      try {
        const result = await getConversations({
          current: 1,
          size: 50
        })

        if (result && result.length > 0) {
          this.chatList = result.map(conversation => ({
            id: conversation.id,
            userId: conversation.otherUserId,
            name: conversation.otherUserNickname || '用户' + conversation.otherUserId,
            avatar: conversation.otherUserAvatar || 'https://picsum.photos/100/100?random=' + conversation.otherUserId,
            lastMessage: conversation.lastMessageContent || '',
            lastMessageTime: new Date(conversation.lastMessageTime),
            lastMessageType: 'text',
            unreadCount: conversation.unreadCount || 0,
            isOnline: false,
            isMuted: false
          }))
        } else {
          // 使用模拟数据作为后备
          this.chatList = this.generateMockChatList()
        }
      } catch (error) {
        console.error('加载聊天列表失败:', error)
        // 使用模拟数据作为后备
        this.chatList = this.generateMockChatList()
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    generateMockChatList() {
      return [
        {
          id: 1,
          name: '小美',
          avatar: 'https://picsum.photos/100/100?random=700',
          lastMessage: '今天的穿搭真好看！',
          lastMessageTime: new Date(Date.now() - 300000), // 5分钟前
          lastMessageType: 'text',
          unreadCount: 2,
          isOnline: true,
          isMuted: false
        },
        {
          id: 2,
          name: '旅行达人',
          avatar: 'https://picsum.photos/100/100?random=701',
          lastMessage: '分享一下这次旅行的照片',
          lastMessageTime: new Date(Date.now() - 1800000), // 30分钟前
          lastMessageType: 'image',
          unreadCount: 0,
          isOnline: false,
          isMuted: false
        },
        {
          id: 3,
          name: '美食家',
          avatar: 'https://picsum.photos/100/100?random=702',
          lastMessage: '这家餐厅真的很不错',
          lastMessageTime: new Date(Date.now() - 3600000), // 1小时前
          lastMessageType: 'text',
          unreadCount: 1,
          isOnline: true,
          isMuted: true
        },
        {
          id: 4,
          name: '摄影师',
          avatar: 'https://picsum.photos/100/100?random=703',
          lastMessage: '发了一段语音',
          lastMessageTime: new Date(Date.now() - 7200000), // 2小时前
          lastMessageType: 'voice',
          unreadCount: 0,
          isOnline: false,
          isMuted: false
        },
        {
          id: 5,
          name: '健身达人',
          avatar: 'https://picsum.photos/100/100?random=704',
          lastMessage: '明天一起去健身房吗？',
          lastMessageTime: new Date(Date.now() - 86400000), // 1天前
          lastMessageType: 'text',
          unreadCount: 0,
          isOnline: false,
          isMuted: false
        }
      ]
    },

    formatTime(time) {
      const now = new Date()
      const diff = now - new Date(time)
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      
      const date = new Date(time)
      return `${date.getMonth() + 1}/${date.getDate()}`
    },

    onRefresh() {
      this.refreshing = true
      this.loadChatList()
    },

    async openChat(chat) {
      try {
        // 标记消息已读
        if (chat.unreadCount > 0) {
          await markMessageAsRead({
            conversationId: chat.id,
            userId: chat.userId
          })

          // 清除未读数
          chat.unreadCount = 0

          // 更新聊天列表
          const index = this.chatList.findIndex(c => c.id === chat.id)
          if (index !== -1) {
            this.$set(this.chatList, index, { ...chat })
          }
        }

        uni.navigateTo({
          url: `/pagesSub/social/chat/detail?id=${chat.userId}&name=${encodeURIComponent(chat.name)}`
        })
      } catch (error) {
        console.error('打开聊天失败:', error)
        // 即使标记已读失败，也要跳转到聊天页面
        uni.navigateTo({
          url: `/pagesSub/social/chat/detail?id=${chat.userId}&name=${encodeURIComponent(chat.name)}`
        })
      }
    },

    showChatActions(chat) {
      const actions = ['置顶', chat.isMuted ? '取消免打扰' : '免打扰', '删除聊天']
      
      uni.showActionSheet({
        itemList: actions,
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.toggleChatTop(chat)
              break
            case 1:
              this.toggleChatMute(chat)
              break
            case 2:
              this.deleteChat(chat)
              break
          }
        }
      })
    },

    toggleChatTop(chat) {
      // 置顶/取消置顶逻辑
      this.$u.toast('置顶成功')
    },

    toggleChatMute(chat) {
      chat.isMuted = !chat.isMuted
      this.$u.toast(chat.isMuted ? '已开启免打扰' : '已关闭免打扰')
    },

    deleteChat(chat) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个聊天吗？',
        success: (res) => {
          if (res.confirm) {
            const index = this.chatList.findIndex(item => item.id === chat.id)
            if (index > -1) {
              this.chatList.splice(index, 1)
              this.$u.toast('删除成功')
            }
          }
        }
      })
    },

    goSearch() {
      uni.navigateTo({
        url: '/pagesSub/social/search/chat'
      })
    },

    startNewChat() {
      uni.navigateTo({
        url: '/pagesSub/social/contact/select'
      })
    },

    goSystemMessages() {
      uni.navigateTo({
        url: '/pagesSub/social/message/system'
      })
    },

    goLikeMessages() {
      uni.navigateTo({
        url: '/pagesSub/social/message/likes'
      })
    },

    goFollowMessages() {
      uni.navigateTo({
        url: '/pagesSub/social/message/followers'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.message-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 100px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.quick-actions {
  margin-top: calc(44px + var(--status-bar-height));
  background: #fff;
  padding: 16px;
  display: flex;
  justify-content: space-around;
  border-bottom: 8px solid #f8f9fa;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.action-icon {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.action-icon.system {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-icon.like {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-icon.follow {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.action-text {
  font-size: 12px;
  color: #666;
}

.unread-badge {
  position: absolute;
  top: -2px;
  right: 8px;
  background: #ff4757;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.chat-list {
  background: #fff;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.chat-item:last-child {
  border-bottom: none;
}

.chat-avatar {
  position: relative;
  margin-right: 12px;
}

.online-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #52c41a;
  border: 2px solid #fff;
  border-radius: 6px;
}

.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.chat-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.chat-time {
  font-size: 12px;
  color: #999;
}

.chat-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.message-preview {
  flex: 1;
  min-width: 0;
}

.preview-text {
  font-size: 14px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.chat-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unread-count {
  background: #ff4757;
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin: 16px 0 8px;
}

.empty-desc {
  font-size: 14px;
  color: #ccc;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading-text {
  margin-left: 8px;
  color: #999;
  font-size: 14px;
}
</style>
