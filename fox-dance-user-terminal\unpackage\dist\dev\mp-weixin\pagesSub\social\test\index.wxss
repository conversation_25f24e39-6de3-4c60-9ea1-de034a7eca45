@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.test-container.data-v-8496c0dc {
  min-height: 100vh;
  background: #f8f9fa;
}
.header.data-v-8496c0dc {
  background: #2979ff;
  padding: 25px 16px 16px;
  text-align: center;
}
.title.data-v-8496c0dc {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}
.content.data-v-8496c0dc {
  padding: 20px 16px;
}
.test-section.data-v-8496c0dc {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.section-title.data-v-8496c0dc {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: block;
}
.test-item.data-v-8496c0dc {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}
.test-label.data-v-8496c0dc {
  font-size: 14px;
  color: #666;
  min-width: 80px;
}
.nav-buttons.data-v-8496c0dc {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.status-list.data-v-8496c0dc {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.status-item.data-v-8496c0dc {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
}
.status-item.success.data-v-8496c0dc {
  background: rgba(82, 196, 26, 0.1);
}
.status-text.data-v-8496c0dc {
  font-size: 14px;
  color: #333;
}

