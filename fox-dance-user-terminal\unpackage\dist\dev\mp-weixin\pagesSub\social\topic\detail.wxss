@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.topic-detail-container.data-v-5790f86e {
  min-height: 100vh;
  background: #f8f9fa;
}
.header.data-v-5790f86e {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: transparent;
  padding: 25px 32rpx 0;
}
.nav-bar.data-v-5790f86e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
}
.nav-title.data-v-5790f86e {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}
.topic-header.data-v-5790f86e {
  height: 500rpx;
  background-size: cover;
  background-position: center;
  position: relative;
}
.topic-overlay.data-v-5790f86e {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 80rpx 32rpx 40rpx;
}
.topic-content.data-v-5790f86e {
  color: #fff;
}
.topic-name.data-v-5790f86e {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  display: block;
}
.topic-desc.data-v-5790f86e {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 24rpx;
  opacity: 0.9;
  display: block;
}
.topic-stats.data-v-5790f86e {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
}
.stat-item.data-v-5790f86e {
  font-size: 24rpx;
  opacity: 0.8;
}
.topic-actions.data-v-5790f86e {
  display: flex;
  gap: 24rpx;
}
.posts-container.data-v-5790f86e {
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  min-height: calc(100vh - 500rpx);
}
.filter-bar.data-v-5790f86e {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.posts-list.data-v-5790f86e {
  padding: 0 32rpx;
}
.load-more.data-v-5790f86e {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  gap: 16rpx;
}
.load-text.data-v-5790f86e {
  font-size: 28rpx;
  color: #999;
}
.no-more.data-v-5790f86e {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}
.no-more-text.data-v-5790f86e {
  font-size: 28rpx;
  color: #999;
}
.empty-state.data-v-5790f86e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}
.empty-text.data-v-5790f86e {
  font-size: 32rpx;
  color: #999;
  margin: 32rpx 0 16rpx;
}
.empty-desc.data-v-5790f86e {
  font-size: 28rpx;
  color: #ccc;
  margin-bottom: 40rpx;
}

