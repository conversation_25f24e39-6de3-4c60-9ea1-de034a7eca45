<template>
	<view class="productDetails winningrecordxq" :style="{ '--qjbutton-color': qjbutton }" v-if="goodsDetail.id">
		
		<view class="pro_ban">
			<swiper class="swiper" circular :autoplay="true" @change="swiperChange">
				<swiper-item v-for="(item,index) in goodsDetail.images" :key="index">
					<image :src="imgbaseUrl + item" mode="aspectFill"></image>
				</swiper-item>
			</swiper>
			<view class="pro_ban_xf"><text>{{swiperIndex+1}}/{{goodsDetail.images.length}}</text></view>
		</view>
		
		<view class="pro_one">
			<view class="pro_one_b">
				<view class="pro_one_b_t">{{goodsDetail.name}}</view>
			</view>
		</view>
		
		<!-- <view class="pro_two">送至<text>中原国家广告产业园</text><image src="/static/images/icon18.png"></image></view> -->
		<view class="pro_thr" style="margin-bottom:0;">
			<!-- <view class="pro_thr_t">商品信息</view> -->
			<view class="pro_thr_c" v-if="goodsDetail.parameter.length > 0">
				<view class="pro_thr_c_li" v-for="(item,index) in goodsDetail.parameter" :key="index"><view>{{item.key}}</view><text>{{item.value}}</text></view>
			</view>
		</view>
		
		<view class="pro_fou" :style="status != 0 ? 'margin-bottom:20rpx' : ''">
			<rich-text :nodes="kcjsDetail"></rich-text>
		</view>
		
		<view class="peode_foo" v-if="status == 0"><view @click="dhTap()">立即兑换</view></view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	mallListsxqApi,
	userInfoApi,
} from '@/config/http.achieve.js'
import util from '@/utils/utils.js';
export default {
	data() {
		return {
			loding:false,
			isLogined:true,
			score:0,
			imgbaseUrl:'',
			swiperIndex:0,
			bannerLists:[],
			kcjsDetail:'',
			goodsDetail:{id:0},
			jpid:0,
			qjbutton:'#131315',
			status:0
		}
	},
	onShow() {
		this.isLogined = uni.getStorageSync('token') ? true : false;
		if(this.isLogined){
			this.userData();//个人信息
		}
		this.imgbaseUrl = this.$baseUrl;
	},
	onLoad(option) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.jpid = option.jpid
		this.status = option.status
		this.goodsData(option.id);//商品详情
	},
	methods: {
		//商品详情
		goodsData(id){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			mallListsxqApi({
				goods_id:1
			}).then(res => {
				console.log('商品详情',res)
				if (res.code == 1) {
					that.kcjsDetail = util.formatRichText(res.data.details)
					that.goodsDetail = res.data;
					uni.hideLoading();
				}
			})
		},
		//个人信息
		userData(){
			/*uni.showLoading({
				title: '加载中'
			});*/
			let that = this;
			userInfoApi({}).then(res => {
				console.log('个人中心',res)
				if (res.code == 1) {
					that.score = res.data.score;
					// uni.hideLoading();
				}
			})
		},
		//兑换
		dhTap(){
			if(this.isLogined){
				/*if(this.score < this.goodsDetail.redeem_points*1){
					uni.showToast({
						icon: 'none',
						title: '积分不足',
						duration:2000
					});
					return false;
				}*/
				// var productxq = JSON.stringify({id:this.goodsDetail.id,name:this.goodsDetail.name,image:this.goodsDetail.images[0],redeem_points:this.goodsDetail.redeem_points*1})
				// var productxq = ''
				uni.setStorageSync('dhspGoods',{image:this.goodsDetail.images[0],name:this.goodsDetail.name,jpid:this.jpid})
				uni.redirectTo({
					url:'/pages/prizedraw/confirmOrder'
				})
				
			}else{
				uni.showToast({
					icon: 'none',
					title: '请先登录',
					duration:2000
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
			}
		},
		// swiper 监听
		swiperChange(e){
			this.swiperIndex = e.detail.current
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.productDetails{overflow: hidden;}
page{padding-bottom: 0;}
</style>