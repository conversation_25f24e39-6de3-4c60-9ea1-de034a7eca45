<template>
	<view class="login">
		<u-navbar :is-back="true" title=" " back-icon-color="#fff" back-icon-size="42"
			:background="{background:'transparent'}" :border-bottom="false">

		</u-navbar>
		<view class="acount-login">
			<image class="logo" src="/static/logo.png" mode="heightFix"></image>
			<view>
				<view class="input flex">
					<image src="/static/login/login_mobile.png" mode="scaleToFill"></image>
					<input v-model="mobile" class="flex-1" type="text" placeholder="请输入手机号" placeholder-class="color" />
				</view>
				<view class="input flex">
					<image src="/static/login/login_code.png" mode="scaleToFill"></image>
					<input v-model="code" maxlength="5" class="flex-1" type="number" placeholder="请输入验证码"
						placeholder-class="color" />
					<view class="show" style="width: auto;height: auto;color: #F2861A;" @click="getCode">
						{{codeText}}
					</view>
				</view>
				<view class="input flex">
					<image src="/static/login/login_passward.png" mode="scaleToFill"></image>
					<input v-if="isType == false" v-model="password" maxlength="12" type="text"
						placeholder="请输入新密码(6~12位字母+数字)" placeholder-class="color" style="width: 600rpx;" />
					<input v-if="isType ==  true" v-model="password" maxlength="12" type="password"
						placeholder="请输入新密码(6~12位字母+数字)" placeholder-class="color" style="width: 600rpx;" />
					<view class="show flex-col row-center col-center" @click="showPass">
						<image v-if="isType == true" src="/static/login/login_pass.png" mode="scaleToFill"></image>
						<image v-if="isType == false" src="/static/login/login_pass_x.png" mode="scaleToFill"
							class="show"></image>
					</view>
				</view>
			</view>
			<view class="btn" style="" @click="loginFun">确认</view>
			
		</view>


	</view>
</template>

<script>
	import {
		sendCode,
		forgetPass
	} from '@/config/http.achieve.js'
	export default {
		data() {
			return {
				isAgreement: true,
				password: '',
				mobile: '',
				code: '',
				isType: true,
				codeText: '获取验证码',
				countdown: 60,
				timer: null,
				//选择身份
				showId: false,
			};
		},

		onLoad(option) {


			// this.getLoginCode()
		},
		methods: {
			IsAgree() {
				this.isAgreement = !this.isAgreement
			},

			// 是否显示密码
			showPass() {
				this.isType = !this.isType
				// if (this.type == 'text') {
				// 	this.type = 'password'
				// } else {
				// 	this.type = 'text'
				// }
			},
			checkId(type) {
				confirmId({
					identity: type
				}).then(res => {
					if (res.code == 1) {
						if (type == 1) {
							uni.setStorageSync('identity', 'user')
						} else if (type == 2) {
							uni.setStorageSync('identity', 'master')
						}

						uni.switchTab({
							url: '/pages/index/index'
						})
					}
				})
			},
			codeChange(tip) {
				this.codeTips = tip
			},
			navTo(url) {
				uni.navigateTo({
					url: url
				})
			},
			goLogin() {
				uni.navigateBack()
			},

			getCode() {
				if (this.mobile == '') {
					this.$toast({
						title: '请先输入手机号'
					})
					return
				}
				let testphone = new RegExp(/^1(3|4|5|6|7|8|9)[0-9]{9}$/)
				if (!testphone.test(this.mobile)) {
					this.$toast({
						title: '手机号格式不正确'
					})
					return
				}
				if (this.codeText != '获取验证码') {
					this.$toast({
						title: `请${this.countdown}秒后重新获取`
					})
					return
				}
				uni.showLoading({
					title: '验证码发送中...',
					icon: 'loading'
				})
				sendCode({
					mobile: this.mobile,
					type: 2
				}).then(res => {
					if (res.code == 1) {
						uni.hideLoading()
						this.$toast({
							title: '验证码发送成功',
							icon: 'success'
						})
						if (this.timer) return;
						this.timer = setInterval(() => {
							if (this.countdown > 0) {
								this.countdown--;
								this.codeText = `${this.countdown}秒后重新获取`;
							} else {
								this.resetCountdown();
							}
						}, 1000);
					}
				})

			},
			resetCountdown() {
				clearInterval(this.timer);
				this.timer = null;
				this.countdown = 60;
				this.codeText = '获取验证码';
			},
			// 账号登录
			async loginFun() {
				if (!this.isAgreement) return this.$toast({
					title: '请先勾选\"已阅读并同意《服务协议》和《隐私协议》\"'
				})

				if (!this.mobile) {
					this.$toast({
						title: '请输入手机号'
					});
					return;
				}
				if (!this.password) {
					this.$toast({
						title: '请输入密码'
					});
					return;
				}
				forgetPass({
					mobile: this.mobile,
					password: this.password,
					code: this.code
				}).then(res => {
					if (res.code == 1) {
						uni.setStorageSync('info', res.data)
						if (res.data.identity) {
							if (res.data.identity == 1) {
								uni.setStorageSync('identity', 'user')
							} else if (res.data.identity == 2) {
								uni.setStorageSync('identity', 'master')
							} else {
								uni.setStorageSync('identity', 'master')
							}
							uni.switchTab({
								url: '/pages/index/index'
							})
						} else {
							this.showId = true
						}
					} else {
						this.$toast({
							title: res.msg
						})
					}
				})
			}
		},

	}
</script>
<style lang="scss">
	.color {
		color: #bfbfbf;
	}

	page {
		background-color: #fff !important;
		padding: 0;

		.login {
			// background-image: url(/static/login/login_top_bgi.png);
			background-size: 100% auto;
			background-repeat: no-repeat;
			min-height: 100vh;
			display: flex;
			flex-direction: column;

			.acount-login {
				padding-top: 100rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				box-sizing: border-box;
				min-height: 0;
				flex: 1;

				.logo {
					width: 180rpx;
					height: 180rpx;
					// box-shadow: 0px 10rpx 30rpx 0px rgba(0,110,216,0.1);
					border-radius: 60rpx;
					margin-bottom: 50rpx;
				}

				.input {
					width: 670rpx;
					border-bottom: 2rpx solid #F6F6F6;
					// padding-bottom: 40rpx;
					// margin-top: 49rpx;
					position: relative;

					.show {
						position: absolute;
						z-index: 11;
						height: 100%;
						width: 50rpx;
						right: 0;

						image {
							width: 36rpx;
							height: 22rpx;

						}
					}

					input {
						flex: 1;
						box-sizing: border-box;
						padding-left: 30rpx;
						font-weight: 400;
						font-size: 32rpx;
						color: #333;
						height: 110rpx;
						line-height: 110rpx;
					}

					image {
						width: 29rpx;
						height: 33rpx;
					}
				}
			}
		}
	}

	.btn {
		margin: 120rpx auto 0;
		width: 686rpx;
		height: 88rpx;
		border-radius: 44rpx;
	}

	.box {
		.white {
			width: 530rpx;
			height: 588rpx;
			background: #FFFFFF;
			border-radius: 20rpx;
			box-sizing: border-box;
			padding: 60rpx 35rpx;

			.title {
				text-align: center;
				font-weight: bold;
				font-size: 36rpx;
				color: #333333;
				line-height: 36rpx;
			}

			.tishi {
				margin-top: 30rpx;
				text-align: center;
				font-weight: 500;
				font-size: 24rpx;
				color: #999999;
				line-height: 30rpx;
			}

			.list {
				margin-top: 60rpx;

				.li {
					box-sizing: border-box;
					padding: 19rpx 27rpx 12rpx 40rpx;
					// width: 460rpx;
					// height: 140rpx;
					background: #FFFFFF;
					box-shadow: 0px 0px 14rpx 0px rgba(0, 0, 0, 0.1);
					border-radius: 20rpx;

					.li_l {
						font-weight: 500;
						font-size: 30rpx;
						color: #333333;
						line-height: 29rpx;

					}

					.li_r {
						image {
							width: 81rpx;
							height: 100rpx;
						}
					}
				}

				.active {
					box-shadow: 0px 0px 14rpx 0px rgba(242, 134, 26, 0.2);
					border: 2rpx solid #F2861A;


					// box-shadow: 0px 0px 14rpx 0px rgba(255, 0, 0, 0.1);
				}
			}
		}

		.close {
			margin-top: 60rpx;

			image {
				width: 63rpx;
				height: 63rpx;
				margin: 0 auto;
			}
		}

	}

	.Id {
		/deep/.u-mode-center-box {
			background-color: transparent !important;
		}
	}
</style>