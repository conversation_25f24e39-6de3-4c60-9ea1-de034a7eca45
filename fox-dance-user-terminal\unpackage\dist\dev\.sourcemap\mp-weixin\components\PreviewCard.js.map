{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/PreviewCard.vue?cc79", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/PreviewCard.vue?ae1c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/PreviewCard.vue?a475", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/PreviewCard.vue?ae0d", "uni-app:///components/PreviewCard.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/PreviewCard.vue?7767", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/PreviewCard.vue?f55a"], "names": ["name", "props", "title", "type", "default", "tag", "image", "targetPage", "computed", "cardStyle", "transform", "opacity", "imageStyle", "data", "touchStartY", "isPulling", "pullT<PERSON>eshold", "pullDistance", "animationData", "animation", "navigating", "imageAnimationData", "imageScale", "created", "duration", "timingFunction", "delay", "transform<PERSON><PERSON>in", "console", "methods", "handleTouchStart", "startY", "handleTouchMove", "currentY", "scaleValue", "opacityValue", "translateY", "scale", "step", "e", "distance", "setImageTransform", "imageAnimation", "handleTouchEnd", "resetPosition", "setTimeout", "resetImageScale", "navigateToVote", "uni", "url", "success", "fail", "navigateTo", "resetAfterNavigation", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AACuL;AACvL,gBAAgB,8LAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4sB,CAAgB,0sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2BhuB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;EACA;;EACAI;IACA;IACAC;MACA;QACA;QACA;QACA;UACAC;UACAC;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;UACAF;QACA;MACA;MACA;IACA;EACA;EACAG;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACA;EACAC;IACA;IACA;MACA;QACA;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;;MAEA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;QACA;MACA;;MAEA;MACA;MACA;MACA;MAEA;QACA;QACA;UACA;YAAAP;UAAA;UACA;QACA;MACA;QACAI;MACA;IACA;IAEAI;MACA;MACA;;MAEA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;QACA;MACA;;MAEA;;MAEA;MACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;UAAA;UACA;UACAC;UACAC;;UAEA;UACAb;;UAEA;UACA;QACA;QAEA;UACA;UACA;YACA;YACA,eACAc,8BACAC,kBACA1B,sBACA2B;cAAAd;YAAA;YACA;UACA;QACA;UACAI;QACA;;QAEA;QACAW;;QAEA;QACA;UACAC;UACAH;UACAf;QACA;MACA;IACA;IAEA;IACAmB;MAAA;MACA;QACA;QACA;UACA;UACA;YACAjB;YACAC;YACAE;UACA;;UAEA;UACAe;;UAEA;UACA;YACA;UACA;QACA;UACA;UACA;QACA;MACA;QACAd;MACA;IACA;IAEAe;MACA;MACA;MAEA;QACA;UACA;UACAf;UACA;QACA;UACA;UACA;QACA;;QAEA;QACA;MACA;QACAA;QACA;QACA;MACA;IACA;IAEAgB;MAAA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACAC;QACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QACAtB;QACAC;QACAE;MACA;MAEAe;;MAEA;MACA;QACA;MACA;IACA;IAEAK;MAAA;MACA;MACA;MAEA;QACA;QACA;UACA;UACA;QACA;;QAEA;QACA;;QAEA;QACAF;UACA;UACA;YACAjB;YACAoB;cACAC;cAAA;cACAC;gBACAtB;gBACA;gBACAiB;kBACA;gBACA;cACA;cACAM;gBACAvB;gBACA;gBACA;cACA;YACA;UACA;;UAEA;UACAwB;QACA;MACA;QACAxB;QACA;QACAoB;UACAC;QACA;MACA;IACA;IAEAI;MACA;MACA;QAAA7B;MAAA;MACA;MAEA;MACA;MACA;IACA;EACA;EACA8B;IACA;IACA;MACA;QAAA9B;MAAA;MACA;IACA;;IAEA;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChWA;AAAA;AAAA;AAAA;AAA+hC,CAAgB,y/BAAG,EAAC,C;;;;;;;;;;;ACAnjC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/PreviewCard.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./PreviewCard.vue?vue&type=template&id=b981d626&scoped=true&\"\nvar renderjs\nimport script from \"./PreviewCard.vue?vue&type=script&lang=js&\"\nexport * from \"./PreviewCard.vue?vue&type=script&lang=js&\"\nimport style0 from \"./PreviewCard.vue?vue&type=style&index=0&id=b981d626&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b981d626\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/PreviewCard.vue\"\nexport default component.exports", "export * from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PreviewCard.vue?vue&type=template&id=b981d626&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PreviewCard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PreviewCard.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"product-card\"\r\n        @touchstart=\"handleTouchStart\"\r\n        @touchmove=\"handleTouchMove\"\r\n        @touchend=\"handleTouchEnd\"\r\n        :animation=\"animationData\"\r\n        :style=\"cardStyle\">\r\n    <view class=\"product-image-container\">\r\n      <image class=\"product-image\" :src=\"image\" mode=\"aspectFill\" :animation=\"imageAnimationData\"\r\n             :style=\"imageStyle\"></image>\r\n    </view>\r\n    <view class=\"product-info\">\r\n      <text class=\"product-tag\">{{ tag || 'Fox Dance Studio' }}</text>\r\n      <text class=\"product-name\">{{ title || '敬请期待' }}</text>\r\n    </view>\r\n\r\n    <!-- 下拉提示区域 -->\r\n    <view class=\"swipe-hint\">\r\n      <text class=\"swipe-hint-text\">下拉查看详情</text>\r\n      <view class=\"arrow-container\">\r\n        <image class=\"arrow-down\" src=\"/static/icon/下拉.svg\" mode=\"aspectFit\" :class=\"{'pulse': isPulling}\"></image>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'PreviewCard',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    tag: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    image: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    targetPage: {\r\n      type: String,\r\n      default: '/pagesSub/switch/vote' // 默认导航到vote页面\r\n    }\r\n  },\r\n  computed: {\r\n    // 使用计算属性替代内联样式\r\n    cardStyle() {\r\n      if (!this.animationData && this.pullDistance > 0) {\r\n        const scale = 1 + (this.pullDistance / this.pullThreshold) * 0.1;\r\n        const opacity = 1 - (this.pullDistance / this.pullThreshold) * 0.3;\r\n        return {\r\n          transform: `translateY(${this.pullDistance}px) scale(${scale})`,\r\n          opacity: opacity\r\n        }\r\n      }\r\n      return {}\r\n    },\r\n    imageStyle() {\r\n      if (!this.imageAnimationData && this.imageScale > 1) {\r\n        return {\r\n          transform: `scale(${this.imageScale})`\r\n        }\r\n      }\r\n      return {}\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      touchStartY: 0,\r\n      isPulling: false,\r\n      pullThreshold: 80, // 降低触发导航的下拉阈值，原值为150\r\n      pullDistance: 0,\r\n      animationData: {}, // 动画实例数据对象\r\n      animation: null,    // 动画实例\r\n      navigating: false,   // 是否正在导航中\r\n      imageAnimationData: {}, // 图片动画数据对象\r\n      imageScale: 1.0 // 图片缩放比例\r\n    }\r\n  },\r\n  // 在组件创建时初始化动画实例\r\n  created() {\r\n    // 创建动画实例，确保阴影效果能够正确跟随\r\n    try {\r\n      if (typeof uni.createAnimation === 'function') {\r\n        this.animation = uni.createAnimation({\r\n          duration: 300,\r\n          timingFunction: 'ease-out',\r\n          delay: 0,\r\n          transformOrigin: '50% 50% 0'\r\n        });\r\n      }\r\n    } catch (e) {\r\n      console.error('创建动画实例失败:', e);\r\n    }\r\n  },\r\n  methods: {\r\n    handleTouchStart(e) {\r\n      // 如果正在导航，不处理触摸事件\r\n      if (this.navigating) return;\r\n\r\n      // 获取触摸位置，兼容不同平台事件对象结构\r\n      let startY;\r\n      if (e.touches && e.touches[0]) {\r\n        startY = e.touches[0].clientY;\r\n      } else if (e.changedTouches && e.changedTouches[0]) {\r\n        startY = e.changedTouches[0].clientY;\r\n      } else if (e.detail) {\r\n        startY = e.detail.y;\r\n      } else {\r\n        return; // 无法获取触摸位置，不处理\r\n      }\r\n\r\n      this.touchStartY = startY;\r\n      this.isPulling = false;\r\n      this.pullDistance = 0;\r\n      this.imageScale = 1.0;\r\n\r\n      try {\r\n        // 重置动画\r\n        if (this.animation && typeof this.animation.translateY === 'function') {\r\n          this.animation.translateY(0).scale(1).opacity(1).step({duration: 0});\r\n          this.animationData = this.animation.export();\r\n        }\r\n      } catch (e) {\r\n        console.error('重置动画失败:', e);\r\n      }\r\n    },\r\n\r\n    handleTouchMove(e) {\r\n      // 如果正在导航，不处理触摸事件\r\n      if (this.navigating) return;\r\n\r\n      // 获取触摸位置，兼容不同平台事件对象结构\r\n      let currentY;\r\n      if (e.touches && e.touches[0]) {\r\n        currentY = e.touches[0].clientY;\r\n      } else if (e.changedTouches && e.changedTouches[0]) {\r\n        currentY = e.changedTouches[0].clientY;\r\n      } else if (e.detail) {\r\n        currentY = e.detail.y;\r\n      } else {\r\n        return; // 无法获取触摸位置，不处理\r\n      }\r\n\r\n      const diffY = currentY - this.touchStartY;\r\n\r\n      // 只有下拉时才有效\r\n      if (diffY > 0) {\r\n        this.isPulling = true;\r\n        // 计算下拉距离，使用衰减函数使拉动感觉更自然\r\n        this.pullDistance = Math.min(diffY * 0.5, this.pullThreshold);\r\n\r\n        // 使用动画实例设置transform和opacity\r\n        let scaleValue = 1.0;\r\n        let opacityValue = 1.0;\r\n        let imageScale = 1.0;\r\n\r\n        // 当下拉超过阈值的一定比例时，开始缩放和改变透明度\r\n        if (this.pullDistance > this.pullThreshold * 0.5) { // 降低动画触发阈值，原值为0.7\r\n          const progress = (this.pullDistance - this.pullThreshold * 0.5) / (this.pullThreshold * 0.5);\r\n          scaleValue = 1 + progress * 0.1;\r\n          opacityValue = 1 - progress * 0.3;\r\n\r\n          // 计算图片缩放比例，与卡片保持一致\r\n          imageScale = scaleValue;\r\n\r\n          // 应用图片缩放效果\r\n          this.setImageTransform(imageScale);\r\n        }\r\n\r\n        try {\r\n          // 检查动画API是否可用\r\n          if (this.animation && typeof this.animation.translateY === 'function') {\r\n            // 设置动画并导出 - 确保z-index在变换过程中保持较高值\r\n            this.animation\r\n                .translateY(this.pullDistance)\r\n                .scale(scaleValue)\r\n                .opacity(opacityValue)\r\n                .step({duration: 0});\r\n            this.animationData = this.animation.export();\r\n          }\r\n        } catch(e) {\r\n          console.error('应用动画失败:', e);\r\n        }\r\n\r\n        // 阻止默认滚动\r\n        e.preventDefault && e.preventDefault();\r\n\r\n        // 通知父组件我们正在下拉，可能需要增加容器高度\r\n        this.$emit('pulling', {\r\n          distance: this.pullDistance,\r\n          scale: scaleValue,\r\n          imageScale: imageScale\r\n        });\r\n      }\r\n    },\r\n\r\n    // 添加设置图片transform的方法\r\n    setImageTransform(scale) {\r\n      try {\r\n        // 检查动画API是否存在\r\n        if (typeof uni.createAnimation === 'function') {\r\n          // 创建一个动画实例专门用于图片\r\n          const imageAnimation = uni.createAnimation({\r\n            duration: 0,\r\n            timingFunction: 'ease-out',\r\n            transformOrigin: '50% 50% 0'\r\n          });\r\n\r\n          // 应用缩放动画，但保持图片不超出容器\r\n          imageAnimation.scale(scale).step();\r\n\r\n          // 将动画应用到图片上\r\n          this.$nextTick(() => {\r\n            this.imageAnimationData = imageAnimation.export();\r\n          });\r\n        } else {\r\n          // 如果动画API不可用，可以使用样式直接设置\r\n          this.imageScale = scale;\r\n        }\r\n      } catch (e) {\r\n        console.error('创建图片动画失败:', e);\r\n      }\r\n    },\r\n\r\n    handleTouchEnd() {\r\n      // 如果正在导航，不处理触摸事件\r\n      if (this.navigating || !this.isPulling) return;\r\n\r\n      try {\r\n        if (this.pullDistance >= this.pullThreshold * 0.6) {\r\n          // 触发导航到vote页面\r\n          console.log('下拉距离满足条件，准备导航');\r\n          this.navigateToVote();\r\n        } else {\r\n          // 没有达到阈值，恢复原位\r\n          this.resetPosition();\r\n        }\r\n\r\n        // 无论如何，都通知父组件触摸已结束\r\n        this.$emit('touchEnded');\r\n      } catch (e) {\r\n        console.error('触摸结束处理失败:', e);\r\n        // 发生错误时重置位置\r\n        this.resetPosition();\r\n      }\r\n    },\r\n\r\n    resetPosition() {\r\n      // 设置恢复原位的动画\r\n      this.animation.translateY(0).scale(1).opacity(1).step();\r\n      this.animationData = this.animation.export();\r\n\r\n      // 重置图片缩放\r\n      this.resetImageScale();\r\n\r\n      // 动画结束后重置状态\r\n      setTimeout(() => {\r\n        this.isPulling = false;\r\n        this.pullDistance = 0;\r\n      }, 300);\r\n    },\r\n\r\n    resetImageScale() {\r\n      // 创建动画恢复到原始大小\r\n      const imageAnimation = uni.createAnimation({\r\n        duration: 300,\r\n        timingFunction: 'ease-out',\r\n        transformOrigin: '50% 50% 0'\r\n      });\r\n\r\n      imageAnimation.scale(1).step();\r\n\r\n      // 应用动画\r\n      this.$nextTick(() => {\r\n        this.imageAnimationData = imageAnimation.export();\r\n      });\r\n    },\r\n\r\n    navigateToVote() {\r\n      // 标记正在导航中\r\n      this.navigating = true;\r\n\r\n      try {\r\n        // 创建缩放淡出的动画\r\n        if (this.animation && typeof this.animation.translateY === 'function') {\r\n          this.animation.translateY(this.pullDistance + 30).scale(1.2).opacity(0).step();\r\n          this.animationData = this.animation.export();\r\n        }\r\n\r\n        // 重置图片缩放，避免导航后图片还是放大状态\r\n        this.resetImageScale();\r\n\r\n        // 等待动画完成后导航\r\n        setTimeout(() => {\r\n          // 正常情况下使用路由导航\r\n          const navigateTo = () => {\r\n            console.log('准备导航到:', this.targetPage);\r\n            uni.navigateTo({\r\n              url: this.targetPage, // 使用prop传入的目标页面\r\n              success: () => {\r\n                console.log('导航成功');\r\n                // 导航成功后重置\r\n                setTimeout(() => {\r\n                  this.resetAfterNavigation();\r\n                }, 100);\r\n              },\r\n              fail: (err) => {\r\n                console.error('导航失败', err);\r\n                // 导航失败也需要重置\r\n                this.resetAfterNavigation();\r\n              }\r\n            });\r\n          };\r\n\r\n          // 执行导航\r\n          navigateTo();\r\n        }, 300);\r\n      } catch (e) {\r\n        console.error('导航过程中出错:', e);\r\n        // 出错时也进行导航\r\n        uni.navigateTo({\r\n          url: this.targetPage\r\n        });\r\n      }\r\n    },\r\n\r\n    resetAfterNavigation() {\r\n      // 重置动画和状态\r\n      this.animation.translateY(0).scale(1).opacity(1).step({duration: 0});\r\n      this.animationData = this.animation.export();\r\n\r\n      this.isPulling = false;\r\n      this.pullDistance = 0;\r\n      this.navigating = false;\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 重置动画\r\n    if (this.animation) {\r\n      this.animation.translateY(0).scale(1).opacity(1).step({duration: 0});\r\n      this.animationData = this.animation.export();\r\n    }\r\n\r\n    // 重置状态\r\n    this.isPulling = false;\r\n    this.pullDistance = 0;\r\n    this.navigating = false;\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.product-card {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 66rpx;\r\n  overflow: visible; /* 改为visible使阴影可以超出容器 */\r\n  background-color: #fff;\r\n  position: relative;\r\n  /* 添加阴影效果 */\r\n  box-shadow: 0 25rpx 50rpx rgba(0,0,0,0.25);\r\n  /* 确保阴影能在动画过程中正确显示 */\r\n  transform-style: preserve-3d;\r\n  backface-visibility: hidden;\r\n  /* 增加z-index确保下拉时不被其他元素遮挡 */\r\n  z-index: 1000;\r\n}\r\n\r\n.product-image-container {\r\n  width: 85%;\r\n  height: 45%;\r\n  margin: 49rpx auto 0;\r\n  overflow: hidden;\r\n  position: relative;\r\n  border-radius: 66rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.product-image {\r\n  width: 100%; /* 设置为100%宽度填满容器 */\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border-radius: 66rpx;\r\n  margin: 0;\r\n  background-color: #fff;\r\n  transition: transform 0.1s ease;\r\n  transform-origin: center center;\r\n}\r\n\r\n.product-info {\r\n  padding: 20rpx 30rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.product-name {\r\n  font-size: 54rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  display: block;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.product-tag {\r\n  font-size: 23rpx;\r\n  font-weight: 500;\r\n  color: #666;\r\n  padding: 20rpx;\r\n}\r\n\r\n/* 下拉提示样式 */\r\n.swipe-hint {\r\n  margin-top: auto;\r\n  margin-bottom: 56rpx;\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.swipe-hint-text {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  opacity: 0.8;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.arrow-container {\r\n  height: 30rpx;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.arrow-down {\r\n  width: 50rpx;\r\n  height: 50rpx;\r\n  animation: bounce 1.5s infinite ease-in-out;\r\n}\r\n\r\n/* 微信小程序兼容动画 */\r\n.arrow-down.pulse {\r\n  animation: pulse 0.8s infinite ease-in-out;\r\n}\r\n\r\n@keyframes bounce {\r\n  0% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(6rpx);\r\n  }\r\n  100% {\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PreviewCard.vue?vue&type=style&index=0&id=b981d626&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PreviewCard.vue?vue&type=style&index=0&id=b981d626&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752725566895\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}