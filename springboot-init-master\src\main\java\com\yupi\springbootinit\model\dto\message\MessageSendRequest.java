package com.yupi.springbootinit.model.dto.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 消息发送请求
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@ApiModel(description = "消息发送请求")
public class MessageSendRequest implements Serializable {

    /**
     * 发送者ID（由系统设置）
     */
    @ApiModelProperty(value = "发送者ID", example = "1")
    private Long senderId;

    /**
     * 接收者ID
     */
    @ApiModelProperty(value = "接收者ID", example = "2", required = true)
    private Long receiverId;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容", example = "你好，很高兴认识你！", required = true)
    private String content;

    /**
     * 消息类型：1-文字，2-图片，3-语音，4-视频，5-文件
     */
    @ApiModelProperty(value = "消息类型", example = "1")
    private Integer messageType;

    /**
     * 媒体文件URL（图片、语音、视频、文件等）
     */
    @ApiModelProperty(value = "媒体文件URL", example = "https://example.com/image.jpg")
    private String mediaUrl;

    /**
     * 媒体文件大小（字节）
     */
    @ApiModelProperty(value = "媒体文件大小", example = "1024")
    private Long mediaSize;

    /**
     * 媒体文件时长（秒，用于语音和视频）
     */
    @ApiModelProperty(value = "媒体文件时长", example = "30")
    private Integer mediaDuration;

    /**
     * 引用消息ID（回复消息时使用）
     */
    @ApiModelProperty(value = "引用消息ID", example = "123")
    private Long replyToMessageId;

    private static final long serialVersionUID = 1L;

    // 手动添加getter/setter方法
    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getMessageType() {
        return messageType;
    }

    public void setMessageType(Integer messageType) {
        this.messageType = messageType;
    }

    public String getMediaUrl() {
        return mediaUrl;
    }

    public void setMediaUrl(String mediaUrl) {
        this.mediaUrl = mediaUrl;
    }

    public Long getMediaSize() {
        return mediaSize;
    }

    public void setMediaSize(Long mediaSize) {
        this.mediaSize = mediaSize;
    }

    public Integer getMediaDuration() {
        return mediaDuration;
    }

    public void setMediaDuration(Integer mediaDuration) {
        this.mediaDuration = mediaDuration;
    }

    public Long getReplyToMessageId() {
        return replyToMessageId;
    }

    public void setReplyToMessageId(Long replyToMessageId) {
        this.replyToMessageId = replyToMessageId;
    }
}
