package com.yupi.springbootinit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yupi.springbootinit.model.dto.UserDTO;
import com.yupi.springbootinit.model.dto.user.UserProfileUpdateRequest;
import com.yupi.springbootinit.model.entity.BaUser;

/**
 * 用户服务
 */
public interface BaUserService extends IService<BaUser> {
    
    /**
     * 减少用户剩余投票次数
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean decrementRemainingVotes(Long userId);
    
    /**
     * 获取用户信息，包括头像和等级
     *
     * @param userId 用户ID
     * @return 用户信息DTO
     */
    UserDTO getUserInfo(Long userId);

    /**
     * 更新用户资料（只更新非空字段）
     *
     * @param userId 用户ID
     * @param updateRequest 更新请求
     * @return 是否成功
     */
    boolean updateUserProfile(Long userId, UserProfileUpdateRequest updateRequest);
}