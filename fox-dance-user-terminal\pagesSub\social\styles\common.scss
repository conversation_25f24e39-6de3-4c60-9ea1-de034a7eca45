// 社交模块通用样式

// 颜色变量
$primary-color: #2979ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4757;
$text-color: #333333;
$text-color-secondary: #666666;
$text-color-light: #999999;
$border-color: #e4e7ed;
$bg-color: #f8f9fa;
$white: #ffffff;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;

// 圆角变量
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-round: 50%;

// 阴影变量
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$shadow-md: 0 2px 8px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);

// 字体大小
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-md: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 20px;

// 通用类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

// 文字颜色
.text-primary {
  color: $primary-color;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-error {
  color: $error-color;
}

.text-secondary {
  color: $text-color-secondary;
}

.text-light {
  color: $text-color-light;
}

// 背景颜色
.bg-primary {
  background-color: $primary-color;
}

.bg-white {
  background-color: $white;
}

.bg-gray {
  background-color: $bg-color;
}

// 间距
.m-xs { margin: $spacing-xs; }
.m-sm { margin: $spacing-sm; }
.m-md { margin: $spacing-md; }
.m-lg { margin: $spacing-lg; }
.m-xl { margin: $spacing-xl; }

.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.ml-xs { margin-left: $spacing-xs; }
.ml-sm { margin-left: $spacing-sm; }
.ml-md { margin-left: $spacing-md; }
.ml-lg { margin-left: $spacing-lg; }
.ml-xl { margin-left: $spacing-xl; }

.mr-xs { margin-right: $spacing-xs; }
.mr-sm { margin-right: $spacing-sm; }
.mr-md { margin-right: $spacing-md; }
.mr-lg { margin-right: $spacing-lg; }
.mr-xl { margin-right: $spacing-xl; }

.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-md { padding: $spacing-md; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }

.pt-xs { padding-top: $spacing-xs; }
.pt-sm { padding-top: $spacing-sm; }
.pt-md { padding-top: $spacing-md; }
.pt-lg { padding-top: $spacing-lg; }
.pt-xl { padding-top: $spacing-xl; }

.pb-xs { padding-bottom: $spacing-xs; }
.pb-sm { padding-bottom: $spacing-sm; }
.pb-md { padding-bottom: $spacing-md; }
.pb-lg { padding-bottom: $spacing-lg; }
.pb-xl { padding-bottom: $spacing-xl; }

.pl-xs { padding-left: $spacing-xs; }
.pl-sm { padding-left: $spacing-sm; }
.pl-md { padding-left: $spacing-md; }
.pl-lg { padding-left: $spacing-lg; }
.pl-xl { padding-left: $spacing-xl; }

.pr-xs { padding-right: $spacing-xs; }
.pr-sm { padding-right: $spacing-sm; }
.pr-md { padding-right: $spacing-md; }
.pr-lg { padding-right: $spacing-lg; }
.pr-xl { padding-right: $spacing-xl; }

// 圆角
.rounded-sm {
  border-radius: $border-radius-sm;
}

.rounded-md {
  border-radius: $border-radius-md;
}

.rounded-lg {
  border-radius: $border-radius-lg;
}

.rounded-xl {
  border-radius: $border-radius-xl;
}

.rounded-full {
  border-radius: $border-radius-round;
}

// 阴影
.shadow-sm {
  box-shadow: $shadow-sm;
}

.shadow-md {
  box-shadow: $shadow-md;
}

.shadow-lg {
  box-shadow: $shadow-lg;
}

// 卡片样式
.card {
  background: $white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
  padding: $spacing-lg;
}

// 按钮样式
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-lg;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: $primary-color;
  color: $white;
}

.btn-secondary {
  background: $bg-color;
  color: $text-color;
}

.btn-outline {
  background: transparent;
  border: 1px solid $border-color;
  color: $text-color;
}

// 输入框样式
.input {
  width: 100%;
  padding: $spacing-md;
  border: 1px solid $border-color;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  background: $white;
}

.input:focus {
  border-color: $primary-color;
  outline: none;
}

// 头像样式
.avatar {
  border-radius: $border-radius-round;
  overflow: hidden;
}

// 标签样式
.tag {
  display: inline-block;
  padding: $spacing-xs $spacing-sm;
  background: rgba($primary-color, 0.1);
  color: $primary-color;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
}

// 徽章样式
.badge {
  display: inline-block;
  padding: 2px 6px;
  background: $error-color;
  color: $white;
  border-radius: 10px;
  font-size: $font-size-xs;
  min-width: 16px;
  text-align: center;
}

// 分割线
.divider {
  height: 1px;
  background: $border-color;
  margin: $spacing-lg 0;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  color: $text-color-light;
}

// 加载状态
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  color: $text-color-light;
}

// 响应式适配
@media screen and (max-width: 375px) {
  .card {
    padding: $spacing-md;
  }
  
  .btn {
    padding: $spacing-xs $spacing-md;
    font-size: $font-size-sm;
  }
}

// 安全区域适配
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
