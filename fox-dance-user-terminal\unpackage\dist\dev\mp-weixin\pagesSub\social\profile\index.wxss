@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.profile-container.data-v-4df458ff {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 100rpx;
}
.header-section.data-v-4df458ff {
  position: relative;
  background: #fff;
}
.header-bg.data-v-4df458ff {
  height: 400rpx;
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
}
.header-actions.data-v-4df458ff {
  position: absolute;
  top: 60rpx;
  right: 32rpx;
  display: flex;
  gap: 32rpx;
  z-index: 10;
}
.user-info-section.data-v-4df458ff {
  padding: 40rpx 50rpx;
  background: #f8f9fa;
}
.user-info-content.data-v-4df458ff {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 50rpx;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.user-avatar-container.data-v-4df458ff {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;
  position: absolute;
  top: 340rpx;
  left: 9%;
}
.user-info-row.data-v-4df458ff {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}
.user-details.data-v-4df458ff {
  flex: 1;
  text-align: left;
}
.edit-section.data-v-4df458ff {
  flex-shrink: 0;
  margin-left: 20rpx;
  display: flex;
  align-items: flex-start;
}
.edit-link.data-v-4df458ff {
  font-size: 28rpx;
  font-weight: 500;
  border: 1rpx solid #2979ff;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  margin: 10rpx;
  color: #2979ff;
}
.nickname.data-v-4df458ff {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}
.user-id.data-v-4df458ff {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 16rpx;
}
.dance-type.data-v-4df458ff {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.bio.data-v-4df458ff {
  font-size: 28rpx;
  color: #999;
  line-height: 1.4;
  display: block;
}
.stats-row.data-v-4df458ff {
  display: flex;
  justify-content: center;
  gap: 80rpx;
  margin-bottom: 0;
}
.stat-item.data-v-4df458ff {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-number.data-v-4df458ff {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.stat-label.data-v-4df458ff {
  font-size: 24rpx;
  color: #999;
}
.tabs-container.data-v-4df458ff {
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}
.content.data-v-4df458ff {
  background: #fff;
  min-height: 60vh;
}
.posts-content.data-v-4df458ff {
  padding: 32rpx;
}
.post-grid.data-v-4df458ff {
  display: flex;
  flex-wrap: wrap;
  gap: 14rpx;
}
.post-card-item.data-v-4df458ff {
  width: calc(50% - 8rpx);
}
.empty-state.data-v-4df458ff {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

