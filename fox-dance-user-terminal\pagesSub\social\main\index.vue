<template>
  <view class="social-main-container">
    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 首页内容 -->
      <HomePage v-if="currentTab === 0" />
      
      <!-- 发现页面内容 -->
      <DiscoverPage v-if="currentTab === 1" />

      <PublishPage v-if="currentTab === 2" />
      
      <!-- 消息页面内容 -->
      <MessagePage v-if="currentTab === 3" />
      
      <!-- 我的页面内容 -->
      <ProfilePage v-if="currentTab === 4" />
    </view>

    <!-- 底部导航 -->
    <TabBar :currentTab="currentTab" @tab-change="handleTabChange" />
  </view>
</template>

<script>
import TabBar from '../components/TabBar.vue'
import HomePage from '../home/<USER>'
import DiscoverPage from '../discover/index.vue'
import PublishPage from '../publish/index.vue'
import MessagePage from '../message/index.vue'
import ProfilePage from '../profile/index.vue'

export default {
  name: 'SocialMain',
  components: {
    TabBar,
    HomePage,
    DiscoverPage,
    PublishPage,
    MessagePage,
    ProfilePage
  },
  data() {
    return {
      currentTab: 0 // 默认显示首页
    }
  },
  onLoad(options) {
    // 根据传入的参数设置当前选项卡
    if (options.tab) {
      this.currentTab = parseInt(options.tab)
    }
  },
  methods: {
    // 处理 TabBar 切换事件
    handleTabChange(data) {
      const { index } = data
      this.currentTab = index
      
      // 可以在这里添加切换时的额外逻辑
      console.log('切换到选项卡:', index)
    }
  }
}
</script>

<style lang="scss" scoped>
.social-main-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-area {
  flex: 1;
  overflow: hidden;
}
</style>
