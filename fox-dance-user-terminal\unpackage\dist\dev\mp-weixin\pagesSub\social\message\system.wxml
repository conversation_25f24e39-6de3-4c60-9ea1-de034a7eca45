<view class="system-message-container data-v-65989434"><view class="read-all-section data-v-65989434"><view data-event-opts="{{[['tap',[['markAllAsRead',['$event']]]]]}}" class="read-all-btn data-v-65989434" bindtap="__e"><u-icon vue-id="4d7a7352-1" name="checkmark-circle" size="20" color="#2979ff" class="data-v-65989434" bind:__l="__l"></u-icon><text class="read-all-text data-v-65989434">一键已读</text></view></view><scroll-view class="message-list data-v-65989434" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" data-event-opts="{{[['refresherrefresh',[['onRefresh',['$event']]]],['scrolltolower',[['loadMore',['$event']]]]]}}" bindrefresherrefresh="__e" bindscrolltolower="__e"><block wx:if="{{$root.g0}}"><view class="data-v-65989434"><block wx:for="{{$root.l0}}" wx:for-item="message" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['openMessageDetail',['$0'],[[['messageList','id',message.$orig.id]]]]]]]}}" class="message-card data-v-65989434" bindtap="__e"><view class="{{['message-icon','data-v-65989434','icon-'+message.$orig.type]}}"><u-icon vue-id="{{'4d7a7352-2-'+__i0__}}" name="{{message.m0}}" size="20" color="#fff" class="data-v-65989434" bind:__l="__l"></u-icon></view><view class="message-content data-v-65989434"><view class="message-header data-v-65989434"><text class="message-title data-v-65989434">{{message.$orig.title}}</text><text class="message-time data-v-65989434">{{message.m1}}</text></view><text class="message-desc data-v-65989434">{{message.$orig.content}}</text></view><block wx:if="{{!message.$orig.isRead}}"><view class="unread-dot data-v-65989434"></view></block></view></block></view></block><block wx:else><view class="empty-state data-v-65989434"><image class="empty-image data-v-65989434" src="/static/images/empty-message.png"></image><text class="empty-text data-v-65989434">暂无系统消息</text></view></block><block wx:if="{{$root.g1}}"><view class="load-more data-v-65989434"><u-loading vue-id="4d7a7352-3" mode="flower" size="24" class="data-v-65989434" bind:__l="__l"></u-loading><text class="load-text data-v-65989434">加载中...</text></view></block></scroll-view></view>