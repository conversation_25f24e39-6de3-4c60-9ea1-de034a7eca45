<template>
	<view class="myMemberCard" v-if="myCardInfo.id"  :style="{ '--qjbutton-color': qjbutton }">
		<view class="mymemxqCon">
			<view class="mymemxqCon_t">会员卡详情</view>
			<view class="mymemxqCon_b"><view>会员卡ID</view><text>{{myCardInfo.out_trade_no}}</text></view>
			<view class="mymemxqCon_b"><view>卡种类别</view><text>{{myCardInfo.type*1 == 0 ? '次卡' : '时长卡'}}</text></view>
			<view class="mymemxqCon_b" v-if="myCardInfo.type*1 == 0"><view>剩余次数</view><text>{{myCardInfo.surplus_frequency*1}}次</text></view>
			<view class="mymemxqCon_b" v-else><view>剩余时长</view><text>{{myCardInfo.day}}天</text></view>
			<view class="mymemxqCon_b"><view>购卡时间</view><text>{{myCardInfo.purchase_card_time}}</text></view>
			<view class="mymemxqCon_jh" v-if="myCardInfo.status == 0">
				<view class="mymemxqCon_jh_l">待激活</view>
				<!-- <view class="mymemxqCon_jh_r" @click="jhhyToggle = true">去激活<image src="/static/images/jt1.png"></image></view> -->
				<view class="mymemxqCon_jh_r" @click="xyToggle = true">去激活<image src="/static/images/jt1.png"></image></view>
			</view>
			<view class="mymemxqCon_b" v-else><view>激活时间</view><text>{{myCardInfo.activation_time}}</text></view>
			<!-- <view class="mymemxqCon_b"><view>到期时间</view><text>{{myCardInfo.become_time}}</text></view> -->
			<view class="mymemxqCon_b"><view>适用门店</view></view>
			<view class="mymemxqCon_c">
				<view v-for="(item,index) in myCardInfo.storeTable.name" :key="index">{{item}}</view>
			</view> 
			<view class="mymemxqCon_b" v-if="myCardInfo.status == 1"><view>默认会员卡</view><switch :checked="ismr" @change="switch1Change" style="transform:scale(0.7);position: relative;left:14rpx;" /></view>
		</view>
		
		<view class="peode_foo"  style="background: #f6f6f6;" v-if="myCardInfo.contract"><view  style="width:auto;margin:0 70rpx;" @click="ckhtTap(imgbaseUrl + myCardInfo.contract)">查看合同</view></view>
		
		<!-- 激活会员卡 go -->
		<view class="xjTanc hszt" v-if="jhhyToggle">
			<view class="xjTanc_n">
				<view class="xjTanc_a">温馨提示</view>
				<view class="xjTanc_b">是否激活该会员卡？</view>
				<view class="xjTanc_c">
					<view @click="jhhyToggle = false">取消</view>
					<view class="bak" @click="jhhySubTap">确认</view>
				</view>
			</view>
		</view>
		<!-- 激活会员卡 end -->
		
		<view class="xytc" v-if="xyToggle">
			<view class="xytcCon">
				<view class="xytcCon_a">激活该会员卡提示</view>
				<view class="xytcCon_b">欢迎使用Fox舞蹈小程序!为了更好的保您的个人权益，在使用本产品前，请先阅读并同意以下内容:</view>
				<!-- <view class="xytcCon_c"><view><rich-text :nodes="xyCont"></rich-text></view></view> -->
				<view class="xytcCon_c">
					<scroll-view
					    class="scroll-view"
					    :style="{ height: scrollViewHeight + 'px' }"
					    scroll-y
					    @scroll="onScroll"
						id="myScrollView"
					    ref="scrollViewRef"
					  >
					    <!-- 这里放置需要滚动的内容 -->
						<view><rich-text :nodes="xyCont"></rich-text></view>
					  </scroll-view>
				</view>
				<view class="xytcCon_b">如您同意以上内容，请点击同意并继续，开始使用我们的产品和服务!</view>
				<view class="xytcCon_f">
					<view class="ty" @click="tyTap" :style="isScrollToBottom ? '' : 'opacity:.7'">{{isScrollToBottom ? '同意并继续' : '请仔细阅读下滑查看完毕'}}</view>
					<view class="noty" @click="xyGbTap">不同意</view>
				</view>
			</view>
		</view>
		
	</view>
</template>


<script>
import {
	myCardxqApi,
	jhcardApi,
	setcardApi
} from '@/config/http.achieve.js'
import util from '@/utils/utils.js';
export default {
	data() {
		return {
			imgbaseUrl:'',
			isLogined:true,
			myCardInfo:{id:0},
			jhhyToggle:false,
			pageId:0,
			
			xyCont:'',
			xyToggle:false,
			scrollViewHeight: 300, // 可根据实际情况调整滚动视图的高度
			isScrollToBottom: false ,// 标记是否滚动到底部
			qjbutton:'#131315',
			ismr:false
		}
	},
	onShow() {
		this.qjbutton = uni.getStorageSync('storeInfo').button
	},
	onLoad(option) {
		this.imgbaseUrl = this.$baseUrl_ht;
		this.pageId = option.id
		this.myCardxqData();//门店详情
	},
	methods: {
		//查看合同
		ckhtTap(url){
			// console.log(this.myCardInfo.contract.substring(0,5),'sdsss')
			var urls = this.myCardInfo.contract.substring(0,5) == 'https' ? this.myCardInfo.contract : (this.imgbaseUrl + this.myCardInfo.contract);
			// console.log(urls)
			uni.showLoading({ title: '下载中...' });
			// 下载文件到临时路径
			uni.downloadFile({
			  url: urls,
			  success: (res) => {
			    if (res.statusCode === 200) {
			      const tempPath = res.tempFilePath;
			      uni.openDocument({
			        filePath: tempPath,
			        showMenu: true, // 显示右上角菜单
			        success: () => {},
			        fail: () => {
			          uni.showToast({ title: '无法打开文件', icon: 'none' });
			        }
			      });
			    }
			  },
			  fail: (err) => {
			    uni.showToast({ title: '下载失败', icon: 'none' });
			  },
			  complete: () => {
			    uni.hideLoading();
			  }
			});
		},
		switch1Change(e){
			console.log(e,'默认会员')
			this.ismr = e.detail.value;
			this.setcardData();
		},
		//设置默认会员卡
		setcardData(id){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			setcardApi({
				id:that.pageId
			}).then(res => {
				console.log('设置默认会员卡',res)
				if (res.code == 1) {
					uni.hideLoading();
					uni.showToast({
						title:'设置成功',
						duration: 2000
					});
				}
			})
		},
		onScroll(e) {
			var that = this;
		  this.$nextTick(() => {
				
			  const query = uni.createSelectorQuery().in(this);
			  query.select('.scroll-view').boundingClientRect();
			  query.select('.scroll-view').scrollOffset();
			  query.exec((res) => {
				  console.log(res,'res')
				if (res && res[0] && res[1]) {
				  const scrollViewHeight = res[0].height;
				  const scrollTop = res[1].scrollTop;
				  const scrollHeight = e.detail.scrollHeight;
				  if (scrollTop + scrollViewHeight >= scrollHeight-20) {
					console.log('已经滚动到底部');
					that.isScrollToBottom = true;
				  }
				}
			  });
			
		  });
		},
		//协议关闭
		xyGbTap(){
			this.xyToggle = false;
			this.isScrollToBottom = false;
		},
		//同意弹窗
		tyTap(){
			if(!this.isScrollToBottom){
				return false;
			}
			this.jhhySubTap();//激活会员
		},
		//门店详情
		myCardxqData(id){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			myCardxqApi({
				id:that.pageId
			}).then(res => {
				console.log('门店详情',res)
				if (res.code == 1) {
					that.ismr = res.data.default == 1 ? true : false
					that.myCardInfo = res.data;
					that.xyCont = res.data.agreement;
					uni.hideLoading();
				}
			})
		},
		//激活会员
		jhhySubTap(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			jhcardApi({
				id:that.pageId
			}).then(res => {
				console.log('激活会员',res)
				if (res.code == 1) {
					that.jhhyToggle = false;
					that.xyToggle = false;
					that.isScrollToBottom = false;
					that.myCardxqData();//门店详情
					uni.hideLoading();
					uni.showToast({
						title:'激活成功',
						duration: 2000
					});
					
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
	
</style>