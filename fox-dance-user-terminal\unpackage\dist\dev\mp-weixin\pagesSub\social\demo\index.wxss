@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.demo-container.data-v-ed92b0ba {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header.data-v-ed92b0ba {
  padding-top: 25px;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.header-content.data-v-ed92b0ba {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
}
.title.data-v-ed92b0ba {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}
.content.data-v-ed92b0ba {
  margin-top: calc(44px + 25px);
  padding: 20px 16px;
}
.intro-section.data-v-ed92b0ba {
  text-align: center;
  margin-bottom: 32px;
}
.intro-title.data-v-ed92b0ba {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  display: block;
  margin-bottom: 12px;
}
.intro-desc.data-v-ed92b0ba {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  display: block;
}
.feature-section.data-v-ed92b0ba, .tech-section.data-v-ed92b0ba, .design-section.data-v-ed92b0ba, .start-section.data-v-ed92b0ba {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.section-title.data-v-ed92b0ba {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16px;
}
.feature-grid.data-v-ed92b0ba {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
.feature-item.data-v-ed92b0ba {
  width: calc(50% - 8px);
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.feature-icon.data-v-ed92b0ba {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
}
.feature-icon.home.data-v-ed92b0ba {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.feature-icon.discover.data-v-ed92b0ba {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.feature-icon.publish.data-v-ed92b0ba {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.feature-icon.message.data-v-ed92b0ba {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.feature-icon.profile.data-v-ed92b0ba {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}
.feature-icon.detail.data-v-ed92b0ba {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}
.feature-name.data-v-ed92b0ba {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4px;
}
.feature-desc.data-v-ed92b0ba {
  font-size: 12px;
  color: #666;
  display: block;
}
.tech-list.data-v-ed92b0ba {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.tech-item.data-v-ed92b0ba {
  display: flex;
  align-items: center;
}
.tech-text.data-v-ed92b0ba {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}
.design-grid.data-v-ed92b0ba {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
.design-item.data-v-ed92b0ba {
  width: calc(50% - 8px);
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}
.design-title.data-v-ed92b0ba {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8px;
}
.design-desc.data-v-ed92b0ba {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  display: block;
}
.start-section.data-v-ed92b0ba {
  text-align: center;
}
.start-title.data-v-ed92b0ba {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8px;
}
.start-desc.data-v-ed92b0ba {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 20px;
}
.start-buttons.data-v-ed92b0ba {
  display: flex;
  gap: 12px;
  justify-content: center;
}

