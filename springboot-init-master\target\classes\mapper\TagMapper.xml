<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.TagMapper">

    <!-- 根据名称查询标签 -->
    <select id="selectByName" resultType="com.yupi.springbootinit.model.entity.Tag">
        SELECT * FROM tags 
        WHERE name = #{name} AND is_delete = 0
        LIMIT 1
    </select>

    <!-- 批量根据名称查询标签 -->
    <select id="selectByNames" resultType="com.yupi.springbootinit.model.entity.Tag">
        SELECT * FROM tags 
        WHERE name IN
        <foreach collection="names" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
        AND is_delete = 0
    </select>

    <!-- 查询热门标签（分页版本） -->
    <select id="selectHotTags" resultType="com.yupi.springbootinit.model.entity.Tag">
        SELECT * FROM tags
        WHERE is_delete = 0 AND is_hot = 1
        ORDER BY use_count DESC, create_time DESC
    </select>

    <!-- 查询热门标签（非分页版本） -->
    <select id="selectHotTagsList" resultType="com.yupi.springbootinit.model.entity.Tag">
        SELECT * FROM tags
        WHERE is_delete = 0 AND is_hot = 1
        ORDER BY use_count DESC, create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询使用次数最多的标签 -->
    <select id="selectTopUsedTags" resultType="com.yupi.springbootinit.model.entity.Tag">
        SELECT * FROM tags 
        WHERE is_delete = 0
        ORDER BY use_count DESC, create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 增加标签使用次数 -->
    <update id="incrementUseCount">
        UPDATE tags 
        SET use_count = use_count + #{increment}
        WHERE id = #{tagId}
    </update>

    <!-- 搜索标签（模糊匹配） -->
    <select id="searchTags" resultType="com.yupi.springbootinit.model.entity.Tag">
        SELECT * FROM tags 
        WHERE is_delete = 0
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') 
                 OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY use_count DESC, create_time DESC
    </select>

    <!-- 获取推荐标签 -->
    <select id="selectRecommendTags" resultType="com.yupi.springbootinit.model.entity.Tag">
        SELECT DISTINCT t.* FROM tags t
        INNER JOIN post_tags pt ON t.id = pt.tag_id
        INNER JOIN posts p ON pt.post_id = p.id
        WHERE t.is_delete = 0 AND p.user_id = #{userId}
        ORDER BY t.use_count DESC
        LIMIT #{limit}
    </select>

</mapper>
