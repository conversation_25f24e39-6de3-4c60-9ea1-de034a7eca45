{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?af97", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?033a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?f54b", "uni-app:///pagesSub/social/chat/detail.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?5937", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?7d44"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "chatId", "chatName", "otherUserAvatar", "isOnline", "messageList", "inputText", "scrollTop", "showExtensions", "showEmojis", "voiceMode", "isRecording", "currentUser", "avatar", "emojiList", "inputCustomStyle", "backgroundColor", "fontSize", "lineHeight", "minHeight", "maxHeight", "padding", "color", "placeholder<PERSON><PERSON><PERSON>", "onLoad", "methods", "loadMessages", "current", "size", "result", "id", "type", "content", "isMine", "timestamp", "showTime", "status", "console", "generateMockMessages", "duration", "isPlaying", "getCurrentUserId", "getMessageTypeString", "markAllMessagesAsRead", "userId", "conversationId", "formatMessageTime", "scrollToBottom", "sendMessage", "messageContent", "tempId", "message", "receiverId", "messageType", "setTimeout", "uni", "title", "icon", "chooseImage", "count", "sizeType", "sourceType", "success", "previewImage", "urls", "startVoiceRecord", "toggleVoiceMode", "startRecord", "stopRecord", "cancelRecord", "playVoice", "msg", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "toggleExtensions", "insert<PERSON><PERSON><PERSON>", "onInputFocus", "onInputBlur", "onInputChange", "goBack", "makeCall", "phoneNumber", "showMoreActions", "itemList", "viewUserProfile", "url", "clearChatHistory", "confirmText", "confirmColor", "clearChatHistoryFromServer", "reportUser", "confirmReport", "submitReport", "loadMoreMessages", "resendMessage", "chooseLocation", "chooseFile"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAAquB,CAAgB,qsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsRzvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;MACAC,YACA,gDACA,gDACA,gDACA,+CACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACA;kBACA;kBACA,+BACA;;kBAEA;oBACAC;oBACAC;oBACAC;oBACAC;oBACApB;oBACAqB;oBACAC;oBACAC;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;gBACA;cAAA;gBAGA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA,QACA;QACAR;QACAC;QACAC;QACAC;QACApB;QACAqB;QACAC;QACAC;MACA,GACA;QACAN;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAC;QACAC;QACAC;QACApB;QACAqB;QACAC;QACAC;MACA,GACA;QACAN;QACAC;QACAC;QACAO;QACAN;QACAC;QACAC;QACAC;QACAI;MACA,EACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAR;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAS;MACA;MACA;MACA;MAEA;QAAA;QACA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAC;gBACAC;gBAEAC;kBACArB;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAEA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAIA;kBACAgB;kBACAC;kBACArB;gBACA;cAAA;gBAJAH;gBAMA;kBACA;kBACAsB;kBACAA;;kBAEA;kBACAG;oBACAH;kBACA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAd;gBACAc;gBACAI;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MACAH;QACAI;QACAC;QACAC;QACAC;UACA;YACAhC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;UAEA;UACA;UACA;;UAEA;UACAkB;YACAH;UACA;QACA;MACA;IACA;IAEAY;MACAR;QACAS;QACArC;MACA;IACA;IAEAsC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MAEA;;MAEA;MACA;QACAtC;QACAC;QACAC;QACAO;QACAN;QACAC;QACAC;QACAC;QACAI;MACA;MAEA;MACA;MAEAc;QACAH;MACA;IACA;IAEAkB;MACA;IACA;IAEAC;MACA;MACA;QACA;UACAC;QACA;MACA;MAEApB;;MAEA;MACAG;QACAH;MACA;IACA;IAEAqB;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;IAAA,CACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACAvB;IACA;IAEAwB;MACAxB;QACAyB;MACA;IACA;IAEAC;MAAA;MACA1B;QACA2B;QACApB;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEA;IACAqB;MACA5B;QACA6B;MACA;IACA;IAEA;IACAC;MAAA;MACA9B;QACAC;QACAxB;QACAsD;QACAC;QACAzB;UACA;YACA;YACA;;YAEA;YACAP;cACAC;cACAC;cACAlB;YACA;;YAEA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAiD;MACA;MACA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAlBA,CAmBA;IAEA;IACAC;MAAA;MACA,qBACA,UACA,UACA,QACA,QACA,SACA;MAEAlC;QACA2B;QACApB;UACA;UACA;QACA;MACA;IACA;IAEA;IACA4B;MAAA;MACAnC;QACAC;QACAxB;QACAsD;QACAC;QACAzB;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACA6B;MACA;MACApC;QACAC;MACA;;MAEA;MACA;MACAF;QACAC;QAEAA;UACAC;UACAC;UACAlB;QACA;;QAEA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACA;IACA;IAEAqD;MACA;IAAA,CACA;IAEAC;MACA1C;MACAG;QACAH;MACA;IACA;IAEA2C;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACn0BA;AAAA;AAAA;AAAA;AAAg5C,CAAgB,gxCAAG,EAAC,C;;;;;;;;;;;ACAp6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/chat/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/chat/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=7a033bbb&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7a033bbb\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/chat/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=7a033bbb&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-input/u-input\" */ \"@/components/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.messageList, function (message, __i0__) {\n    var $orig = _vm.__get_orig(message)\n    var m0 = message.showTime ? _vm.formatMessageTime(message.timestamp) : null\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"chat-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <view class=\"header-left\" @click=\"goBack\">\n          <u-avatar\n            :src=\"otherUserAvatar\"\n            size=\"50\"\n            class=\"header-avatar\"\n          ></u-avatar>\n          <view class=\"chat-info\">\n            <view class=\"name-container\">\n              <text class=\"chat-name\">{{ chatName }}</text>\n              <view v-if=\"isOnline\" class=\"online-indicator\"></view>\n            </view>\n          </view>\n        </view>\n        <view class=\"header-actions\">\n          <u-icon name=\"phone\" size=\"24\" color=\"#333\" @click=\"makeCall\"></u-icon>\n          <u-icon name=\"more-dot-fill\" size=\"24\" color=\"#333\" @click=\"showMoreActions\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 消息列表 -->\n    <scroll-view \n      class=\"message-list\" \n      scroll-y \n      :scroll-top=\"scrollTop\"\n      @scrolltoupper=\"loadMoreMessages\"\n    >\n      <view class=\"message-item\" v-for=\"message in messageList\" :key=\"message.id\">\n        <!-- 时间分割线 -->\n        <view v-if=\"message.showTime\" class=\"time-divider\">\n          <text class=\"time-text\">{{ formatMessageTime(message.timestamp) }}</text>\n        </view>\n\n        <!-- 消息内容 -->\n        <view class=\"message-wrapper\" :class=\"{ 'is-mine': message.isMine }\">\n          <!-- 对方消息：左边头像，右边内容 -->\n          <template v-if=\"!message.isMine\">\n            <u-avatar\n              :src=\"message.avatar\"\n              size=\"64\"\n              class=\"message-avatar\"\n            ></u-avatar>\n\n            <view class=\"message-content\">\n              <!-- 文字消息 -->\n              <view\n                v-if=\"message.type === 'text'\"\n                class=\"message-bubble text-bubble\"\n              >\n                <text class=\"message-text\">{{ message.content }}</text>\n              </view>\n\n              <!-- 图片消息 -->\n              <view\n                v-else-if=\"message.type === 'image'\"\n                class=\"message-bubble image-bubble\"\n                @click=\"previewImage(message.content)\"\n              >\n                <image :src=\"message.content\" class=\"message-image\" mode=\"aspectFill\" />\n              </view>\n\n              <!-- 语音消息 -->\n              <view\n                v-else-if=\"message.type === 'voice'\"\n                class=\"message-bubble voice-bubble\"\n                @click=\"playVoice(message)\"\n              >\n                <u-icon name=\"volume-fill\" size=\"16\" color=\"#fff\"></u-icon>\n                <text class=\"voice-duration\">{{ message.duration }}''</text>\n                <view v-if=\"message.isPlaying\" class=\"voice-animation\">\n                  <view class=\"wave\"></view>\n                  <view class=\"wave\"></view>\n                  <view class=\"wave\"></view>\n                </view>\n              </view>\n            </view>\n          </template>\n\n          <!-- 我的消息：左边内容，右边头像 -->\n          <template v-else>\n            <view class=\"message-content\">\n              <!-- 文字消息 -->\n              <view\n                v-if=\"message.type === 'text'\"\n                class=\"message-bubble text-bubble mine\"\n              >\n                <text class=\"message-text\">{{ message.content }}</text>\n              </view>\n\n              <!-- 图片消息 -->\n              <view\n                v-else-if=\"message.type === 'image'\"\n                class=\"message-bubble image-bubble\"\n                @click=\"previewImage(message.content)\"\n              >\n                <image :src=\"message.content\" class=\"message-image\" mode=\"aspectFill\" />\n              </view>\n\n              <!-- 语音消息 -->\n              <view\n                v-else-if=\"message.type === 'voice'\"\n                class=\"message-bubble voice-bubble mine\"\n                @click=\"playVoice(message)\"\n              >\n                <u-icon name=\"volume-fill\" size=\"16\" color=\"#fff\"></u-icon>\n                <text class=\"voice-duration\">{{ message.duration }}''</text>\n                <view v-if=\"message.isPlaying\" class=\"voice-animation\">\n                  <view class=\"wave\"></view>\n                  <view class=\"wave\"></view>\n                  <view class=\"wave\"></view>\n                </view>\n              </view>\n\n              <!-- 消息状态 -->\n              <view class=\"message-status\">\n                <u-icon\n                  v-if=\"message.status === 'sending'\"\n                  name=\"loading\"\n                  size=\"12\"\n                  color=\"#999\"\n                ></u-icon>\n                <u-icon\n                  v-else-if=\"message.status === 'sent'\"\n                  name=\"checkmark\"\n                  size=\"12\"\n                  color=\"#999\"\n                ></u-icon>\n                <u-icon\n                  v-else-if=\"message.status === 'read'\"\n                  name=\"checkmark-done\"\n                  size=\"12\"\n                  color=\"#2979ff\"\n                ></u-icon>\n                <u-icon\n                  v-else-if=\"message.status === 'failed'\"\n                  name=\"close-circle\"\n                  size=\"12\"\n                  color=\"#ff4757\"\n                  @click=\"resendMessage(message)\"\n                ></u-icon>\n              </view>\n            </view>\n\n          </template>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 输入区域 -->\n    <view class=\"input-area\">\n      <!-- 扩展功能面板 -->\n      <view v-if=\"showExtensions\" class=\"extensions-panel\">\n        <view class=\"extension-grid\">\n          <view class=\"extension-item\" @click=\"chooseImage\">\n            <view class=\"extension-icon photo\">\n              <u-icon name=\"camera\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"extension-text\">照片</text>\n          </view>\n          \n          <view class=\"extension-item\" @click=\"startVoiceRecord\">\n            <view class=\"extension-icon voice\">\n              <u-icon name=\"mic\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"extension-text\">语音</text>\n          </view>\n          \n          <view class=\"extension-item\" @click=\"chooseLocation\">\n            <view class=\"extension-icon location\">\n              <u-icon name=\"map-pin\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"extension-text\">位置</text>\n          </view>\n          \n          <view class=\"extension-item\" @click=\"chooseFile\">\n            <view class=\"extension-icon file\">\n              <u-icon name=\"folder\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"extension-text\">文件</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 表情面板 -->\n      <view v-if=\"showEmojis\" class=\"emoji-panel\">\n        <scroll-view class=\"emoji-scroll\" scroll-y>\n          <view class=\"emoji-grid\">\n            <text \n              v-for=\"emoji in emojiList\" \n              :key=\"emoji\"\n              class=\"emoji-item\"\n              @click=\"insertEmoji(emoji)\"\n            >\n              {{ emoji }}\n            </text>\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 输入栏 -->\n      <view class=\"input-bar\">\n        <u-icon \n          name=\"mic\" \n          size=\"24\" \n          color=\"#666\" \n          @click=\"toggleVoiceMode\"\n          v-if=\"!inputText && !voiceMode\"\n        ></u-icon>\n        \n        <view class=\"input-wrapper\" v-if=\"!voiceMode\">\n          <u-input\n            v-model=\"inputText\"\n            type=\"textarea\"\n            placeholder=\"输入消息...\"\n            :auto-height=\"true\"\n            :show-confirm-bar=\"false\"\n            :clearable=\"false\"\n            :border=\"false\"\n            :custom-style=\"inputCustomStyle\"\n            :placeholder-style=\"placeholderStyle\"\n            :maxlength=\"500\"\n            :cursor-spacing=\"10\"\n            :adjust-position=\"true\"\n            @focus=\"onInputFocus\"\n            @blur=\"onInputBlur\"\n            @input=\"onInputChange\"\n          />\n        </view>\n\n        <!-- 语音录制按钮 -->\n        <view \n          v-else\n          class=\"voice-record-btn\"\n          :class=\"{ recording: isRecording }\"\n          @touchstart=\"startRecord\"\n          @touchend=\"stopRecord\"\n          @touchcancel=\"cancelRecord\"\n        >\n          <text class=\"record-text\">\n            {{ isRecording ? '松开发送' : '按住说话' }}\n          </text>\n        </view>\n\n        <view class=\"input-actions\">\n          <u-icon \n            name=\"emoji\" \n            size=\"24\" \n            :color=\"showEmojis ? '#2979ff' : '#666'\"\n            @click=\"toggleEmojis\"\n          ></u-icon>\n          \n          <u-icon \n            v-if=\"!inputText && !voiceMode\" \n            name=\"plus\" \n            size=\"24\" \n            :color=\"showExtensions ? '#2979ff' : '#666'\"\n            @click=\"toggleExtensions\"\n          ></u-icon>\n          \n          <view \n            v-if=\"inputText || voiceMode\"\n            class=\"send-btn\"\n            @click=\"sendMessage\"\n          >\n            <u-icon name=\"send\" color=\"#fff\" size=\"18\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getConversationMessages, sendMessage, markMessageAsRead } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'ChatDetail',\n  data() {\n    return {\n      chatId: '',\n      chatName: '',\n      otherUserAvatar: 'https://picsum.photos/100/100?random=800',\n      isOnline: true,\n      messageList: [],\n      inputText: '',\n      scrollTop: 0,\n      showExtensions: false,\n      showEmojis: false,\n      voiceMode: false,\n      isRecording: false,\n      currentUser: {\n        avatar: 'https://picsum.photos/100/100?random=999'\n      },\n      emojiList: [\n        '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',\n        '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',\n        '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',\n        '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏'\n      ],\n      // uview输入框自定义样式\n      inputCustomStyle: {\n        backgroundColor: 'transparent',\n        fontSize: '32rpx',\n        lineHeight: '1.4',\n        minHeight: '40rpx',\n        maxHeight: '200rpx',\n        padding: '0',\n        color: '#333'\n      },\n      // placeholder样式\n      placeholderStyle: 'color: #999; font-size: 32rpx;'\n    }\n  },\n  onLoad(options) {\n    this.chatId = options.id\n    this.chatName = options.name || '聊天'\n    this.loadMessages()\n  },\n  methods: {\n    async loadMessages() {\n      try {\n        const result = await getConversationMessages(this.chatId, {\n          current: 1,\n          size: 50\n        })\n\n        if (result && result.length > 0) {\n          this.messageList = result.map((message, index) => {\n            const prevMessage = result[index - 1]\n            const showTime = !prevMessage ||\n              (new Date(message.createTime) - new Date(prevMessage.createTime)) > 300000 // 5分钟\n\n            return {\n              id: message.id,\n              type: this.getMessageTypeString(message.messageType),\n              content: message.content,\n              isMine: message.senderId === this.getCurrentUserId(),\n              avatar: message.senderAvatar || this.otherUserAvatar,\n              timestamp: new Date(message.createTime),\n              showTime: showTime,\n              status: message.isRead === 1 ? 'read' : 'sent'\n            }\n          }).reverse() // 消息按时间正序显示\n\n          // 标记消息已读\n          await this.markAllMessagesAsRead()\n\n        } else {\n          // 使用模拟数据作为后备\n          this.messageList = this.generateMockMessages()\n        }\n      } catch (error) {\n        console.error('加载消息失败:', error)\n        // 使用模拟数据作为后备\n        this.messageList = this.generateMockMessages()\n      }\n\n      // 滚动到底部\n      this.$nextTick(() => {\n        this.scrollToBottom()\n      })\n    },\n\n    // 生成模拟消息数据\n    generateMockMessages() {\n      return [\n        {\n          id: 1,\n          type: 'text',\n          content: '你好！',\n          isMine: false,\n          avatar: 'https://picsum.photos/100/100?random=800',\n          timestamp: new Date(Date.now() - 3600000),\n          showTime: true,\n          status: 'read'\n        },\n        {\n          id: 2,\n          type: 'text',\n          content: '你好，很高兴认识你！',\n          isMine: true,\n          timestamp: new Date(Date.now() - 3500000),\n          showTime: false,\n          status: 'read'\n        },\n        {\n          id: 3,\n          type: 'image',\n          content: 'https://picsum.photos/200/200?random=801',\n          isMine: false,\n          avatar: 'https://picsum.photos/100/100?random=800',\n          timestamp: new Date(Date.now() - 1800000),\n          showTime: true,\n          status: 'read'\n        },\n        {\n          id: 4,\n          type: 'voice',\n          content: '',\n          duration: 3,\n          isMine: true,\n          timestamp: new Date(Date.now() - 300000),\n          showTime: false,\n          status: 'read',\n          isPlaying: false\n        }\n      ]\n      \n      this.scrollToBottom()\n    },\n\n    // 获取当前用户ID\n    getCurrentUserId() {\n      // TODO: 从用户状态或本地存储获取当前用户ID\n      return 1\n    },\n\n    // 将消息类型数字转换为字符串\n    getMessageTypeString(messageType) {\n      const typeMap = {\n        1: 'text',\n        2: 'image',\n        3: 'voice',\n        4: 'video'\n      }\n      return typeMap[messageType] || 'text'\n    },\n\n    // 标记所有消息已读\n    async markAllMessagesAsRead() {\n      try {\n        await markMessageAsRead({\n          userId: this.chatId,\n          conversationId: this.chatId\n        })\n      } catch (error) {\n        console.error('标记消息已读失败:', error)\n      }\n    },\n\n    formatMessageTime(timestamp) {\n      const date = new Date(timestamp)\n      const now = new Date()\n      const diff = now - date\n      \n      if (diff < 86400000) { // 今天\n        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`\n      } else {\n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`\n      }\n    },\n\n    scrollToBottom() {\n      this.$nextTick(() => {\n        this.scrollTop = 999999\n      })\n    },\n\n    async sendMessage() {\n      if (!this.inputText.trim()) return\n\n      const messageContent = this.inputText.trim()\n      const tempId = Date.now()\n\n      const message = {\n        id: tempId,\n        type: 'text',\n        content: messageContent,\n        isMine: true,\n        timestamp: new Date(),\n        showTime: false,\n        status: 'sending'\n      }\n\n      this.messageList.push(message)\n      this.inputText = ''\n      this.scrollToBottom()\n\n      try {\n        // 调用发送消息API\n        const result = await sendMessage({\n          receiverId: this.chatId,\n          messageType: 'text',\n          content: messageContent\n        })\n\n        if (result) {\n          // 更新消息状态和ID\n          message.id = result.id || tempId\n          message.status = 'sent'\n\n          // 模拟已读状态\n          setTimeout(() => {\n            message.status = 'read'\n          }, 1000)\n        } else {\n          message.status = 'failed'\n        }\n\n      } catch (error) {\n        console.error('发送消息失败:', error)\n        message.status = 'failed'\n        uni.showToast({\n          title: '发送失败',\n          icon: 'none'\n        })\n      }\n    },\n\n    chooseImage() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          const message = {\n            id: Date.now(),\n            type: 'image',\n            content: res.tempFilePaths[0],\n            isMine: true,\n            timestamp: new Date(),\n            showTime: false,\n            status: 'sending'\n          }\n          \n          this.messageList.push(message)\n          this.showExtensions = false\n          this.scrollToBottom()\n          \n          // 模拟上传成功\n          setTimeout(() => {\n            message.status = 'sent'\n          }, 1000)\n        }\n      })\n    },\n\n    previewImage(url) {\n      uni.previewImage({\n        urls: [url],\n        current: url\n      })\n    },\n\n    startVoiceRecord() {\n      this.voiceMode = true\n      this.showExtensions = false\n    },\n\n    toggleVoiceMode() {\n      this.voiceMode = !this.voiceMode\n    },\n\n    startRecord() {\n      this.isRecording = true\n      // 开始录音逻辑\n    },\n\n    stopRecord() {\n      if (!this.isRecording) return\n      \n      this.isRecording = false\n      \n      // 模拟语音消息\n      const message = {\n        id: Date.now(),\n        type: 'voice',\n        content: '',\n        duration: Math.floor(Math.random() * 10) + 1,\n        isMine: true,\n        timestamp: new Date(),\n        showTime: false,\n        status: 'sending',\n        isPlaying: false\n      }\n      \n      this.messageList.push(message)\n      this.scrollToBottom()\n      \n      setTimeout(() => {\n        message.status = 'sent'\n      }, 500)\n    },\n\n    cancelRecord() {\n      this.isRecording = false\n    },\n\n    playVoice(message) {\n      // 停止其他语音播放\n      this.messageList.forEach(msg => {\n        if (msg.type === 'voice') {\n          msg.isPlaying = false\n        }\n      })\n      \n      message.isPlaying = true\n      \n      // 模拟播放完成\n      setTimeout(() => {\n        message.isPlaying = false\n      }, message.duration * 1000)\n    },\n\n    toggleEmojis() {\n      this.showEmojis = !this.showEmojis\n      this.showExtensions = false\n    },\n\n    toggleExtensions() {\n      this.showExtensions = !this.showExtensions\n      this.showEmojis = false\n    },\n\n    insertEmoji(emoji) {\n      this.inputText += emoji\n    },\n\n    onInputFocus() {\n      this.showEmojis = false\n      this.showExtensions = false\n      // 滚动到底部，确保输入框可见\n      this.$nextTick(() => {\n        this.scrollToBottom()\n      })\n    },\n\n    onInputBlur() {\n      // 输入框失焦时的处理\n      // 可以在这里添加自动保存草稿等功能\n    },\n\n    onInputChange(value) {\n      // 处理输入内容变化\n      this.inputText = value\n      // 如果需要，可以在这里添加输入实时验证或字数统计\n    },\n\n    goBack() {\n      uni.navigateBack()\n    },\n\n    makeCall() {\n      uni.makePhoneCall({\n        phoneNumber: '10086'\n      })\n    },\n\n    showMoreActions() {\n      uni.showActionSheet({\n        itemList: ['查看资料', '清空聊天记录', '举报'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0:\n              this.viewUserProfile()\n              break\n            case 1:\n              this.clearChatHistory()\n              break\n            case 2:\n              this.reportUser()\n              break\n          }\n        }\n      })\n    },\n\n    // 查看用户资料\n    viewUserProfile() {\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?userId=${this.chatId}&name=${this.chatName}`\n      })\n    },\n\n    // 清空聊天记录\n    clearChatHistory() {\n      uni.showModal({\n        title: '清空聊天记录',\n        content: '确定要清空与该用户的所有聊天记录吗？此操作不可恢复。',\n        confirmText: '清空',\n        confirmColor: '#ff4757',\n        success: (res) => {\n          if (res.confirm) {\n            // 清空消息列表\n            this.messageList = []\n\n            // 显示成功提示\n            uni.showToast({\n              title: '聊天记录已清空',\n              icon: 'success',\n              duration: 2000\n            })\n\n            // 这里可以调用后端API清空服务器端的聊天记录\n            this.clearChatHistoryFromServer()\n          }\n        }\n      })\n    },\n\n    // 从服务器清空聊天记录\n    clearChatHistoryFromServer() {\n      // TODO: 调用后端API清空聊天记录\n      // 示例API调用\n      /*\n      uni.request({\n        url: '/api/chat/clear',\n        method: 'POST',\n        data: {\n          chatId: this.chatId\n        },\n        success: (res) => {\n          console.log('服务器聊天记录已清空')\n        },\n        fail: (err) => {\n          console.error('清空聊天记录失败:', err)\n          uni.showToast({\n            title: '清空失败，请重试',\n            icon: 'error'\n          })\n        }\n      })\n      */\n    },\n\n    // 举报用户\n    reportUser() {\n      const reportReasons = [\n        '发送垃圾信息',\n        '发送不当内容',\n        '骚扰他人',\n        '诈骗行为',\n        '其他违规行为'\n      ]\n\n      uni.showActionSheet({\n        itemList: reportReasons,\n        success: (res) => {\n          const reason = reportReasons[res.tapIndex]\n          this.confirmReport(reason)\n        }\n      })\n    },\n\n    // 确认举报\n    confirmReport(reason) {\n      uni.showModal({\n        title: '举报用户',\n        content: `确定要举报该用户\"${reason}\"吗？我们会认真处理您的举报。`,\n        confirmText: '举报',\n        confirmColor: '#ff4757',\n        success: (res) => {\n          if (res.confirm) {\n            // 提交举报\n            this.submitReport(reason)\n          }\n        }\n      })\n    },\n\n    // 提交举报到服务器\n    submitReport(reason) {\n      // 显示加载提示\n      uni.showLoading({\n        title: '提交中...'\n      })\n\n      // TODO: 调用后端API提交举报\n      // 模拟API调用\n      setTimeout(() => {\n        uni.hideLoading()\n\n        uni.showToast({\n          title: '举报已提交',\n          icon: 'success',\n          duration: 2000\n        })\n\n        // 实际API调用示例\n        /*\n        uni.request({\n          url: '/api/report/submit',\n          method: 'POST',\n          data: {\n            reportedUserId: this.chatId,\n            reportedUserName: this.chatName,\n            reason: reason,\n            reportType: 'chat'\n          },\n          success: (res) => {\n            uni.hideLoading()\n            uni.showToast({\n              title: '举报已提交',\n              icon: 'success'\n            })\n          },\n          fail: (err) => {\n            uni.hideLoading()\n            console.error('举报提交失败:', err)\n            uni.showToast({\n              title: '提交失败，请重试',\n              icon: 'error'\n            })\n          }\n        })\n        */\n      }, 1000)\n    },\n\n    loadMoreMessages() {\n      // 加载更多历史消息\n    },\n\n    resendMessage(message) {\n      message.status = 'sending'\n      setTimeout(() => {\n        message.status = 'sent'\n      }, 500)\n    },\n\n    chooseLocation() {\n      this.showExtensions = false\n      this.$u.toast('位置功能开发中')\n    },\n\n    chooseFile() {\n      this.showExtensions = false\n      this.$u.toast('文件功能开发中')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.chat-container {\n  height: 99vh;\n  display: flex;\n  flex-direction: column;\n  background: #f0f0f0;\n}\n\n.header {\n  background: #fff;\n  border-bottom: 2rpx solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 88rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 32rpx;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n}\n\n.chat-info {\n  margin-left: 24rpx;\n}\n\n.name-container {\n  position: relative;\n  display: inline-block;\n}\n\n.chat-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  padding-right: 20rpx;\n}\n\n.online-indicator {\n  position: absolute;\n  top: 25rpx;\n  right: 0;\n  width: 16rpx;\n  height: 16rpx;\n  background: #52c41a;\n  border-radius: 50%;\n  border: 2rpx solid #fff;\n  box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);\n  animation: online-pulse 2s infinite;\n}\n\n@keyframes online-pulse {\n  0% {\n    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);\n  }\n  50% {\n    box-shadow: 0 0 0 6rpx rgba(82, 196, 26, 0.1);\n  }\n  100% {\n    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);\n  }\n}\n\n.header-actions {\n  display: flex;\n  gap: 32rpx;\n}\n\n.message-list {\n  flex: 1;\n  padding: 0 32rpx 32rpx 32rpx;\n  width: 92%;\n}\n\n.message-item {\n  margin-bottom: 32rpx;\n  margin-top: 32rpx;\n}\n\n.time-divider {\n  text-align: center;\n  margin-bottom: 32rpx;\n}\n\n.time-text {\n  font-size: 24rpx;\n  color: #999;\n  background: rgba(0, 0, 0, 0.1);\n  padding: 8rpx 24rpx;\n  border-radius: 24rpx;\n}\n\n.message-wrapper {\n  display: flex;\n  align-items: flex-end;\n}\n\n.message-wrapper.is-mine {\n  justify-content: flex-end;\n}\n\n.message-avatar {\n  margin: 0 16rpx;\n}\n\n.message-content {\n  display: flex;\n  flex-direction: column;\n  max-width: 70%;\n}\n\n/* 对方消息：内容左对齐 */\n.message-wrapper:not(.is-mine) .message-content {\n  align-items: flex-start;\n}\n\n/* 我的消息：内容右对齐 */\n.message-wrapper.is-mine .message-content {\n  align-items: flex-end;\n}\n\n.message-bubble {\n  padding: 24rpx 32rpx;\n  border-radius: 36rpx;\n  margin-bottom: 8rpx;\n}\n\n.text-bubble {\n  background: #fff;\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n}\n\n.text-bubble.mine {\n  background: #2979ff;\n}\n\n.message-text {\n  font-size: 32rpx;\n  line-height: 1.4;\n  color: #333;\n}\n\n.mine .message-text {\n  color: #fff;\n}\n\n.image-bubble {\n  padding: 0;\n  overflow: hidden;\n  background: transparent;\n}\n\n.message-image {\n  width: 300rpx;\n  height: 300rpx;\n  border-radius: 24rpx;\n}\n\n.voice-bubble {\n  background: #2979ff;\n  display: flex;\n  align-items: center;\n  min-width: 160rpx;\n  position: relative;\n}\n\n.voice-duration {\n  color: #fff;\n  font-size: 28rpx;\n  margin-left: 16rpx;\n}\n\n.voice-animation {\n  display: flex;\n  gap: 4rpx;\n  margin-left: 16rpx;\n}\n\n.wave {\n  width: 4rpx;\n  height: 24rpx;\n  background: #fff;\n  animation: wave 1s infinite;\n}\n\n.wave:nth-child(2) {\n  animation-delay: 0.1s;\n}\n\n.wave:nth-child(3) {\n  animation-delay: 0.2s;\n}\n\n@keyframes wave {\n  0%, 100% { height: 8rpx; }\n  50% { height: 24rpx; }\n}\n\n.message-status {\n  margin-top: 8rpx;\n}\n\n.input-area {\n  background: #fff;\n  border-top: 2rpx solid #e4e7ed;\n}\n\n.extensions-panel, .emoji-panel {\n  height: 400rpx;\n  border-bottom: 2rpx solid #e4e7ed;\n}\n\n.extension-grid {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 40rpx;\n  gap: 40rpx;\n}\n\n.extension-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: calc(25% - 30rpx);\n}\n\n.extension-icon {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16rpx;\n}\n\n.extension-icon.photo {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.extension-icon.voice {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.extension-icon.location {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.extension-icon.file {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.extension-text {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.emoji-scroll {\n  height: 100%;\n  padding: 32rpx;\n}\n\n.emoji-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n}\n\n.emoji-item {\n  font-size: 48rpx;\n  padding: 16rpx;\n  text-align: center;\n  width: 80rpx;\n  height: 80rpx;\n  line-height: 48rpx;\n}\n\n.input-bar {\n  display: flex;\n  align-items: center;\n  padding: 16rpx 32rpx;\n  gap: 24rpx;\n  min-height: 104rpx;\n  /* 确保在键盘弹起时的兼容性 */\n  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));\n  box-sizing: border-box;\n}\n\n.input-wrapper {\n  flex: 1;\n  background: #f5f5f5;\n  border-radius: 40rpx;\n  padding: 16rpx 32rpx;\n  min-height: 72rpx;\n  display: flex;\n  align-items: center;\n  transition: all 0.2s ease;\n}\n\n.input-wrapper:focus-within {\n  background: #f0f0f0;\n  box-shadow: 0 0 0 2rpx rgba(41, 121, 255, 0.1);\n}\n\n/* 重置uview输入框样式 */\n.input-wrapper /deep/ .u-input {\n  flex: 1;\n  min-height: auto;\n}\n\n.input-wrapper /deep/ .u-input__input,\n.input-wrapper /deep/ .u-input__textarea {\n  font-size: 32rpx !important;\n  color: #333 !important;\n  line-height: 1.4 !important;\n  padding: 0 !important;\n  background: transparent !important;\n  min-height: 40rpx !important;\n}\n\n.input-wrapper /deep/ .u-input__textarea {\n  max-height: 200rpx !important;\n  overflow-y: auto;\n  resize: none;\n  /* 防止在某些设备上出现滚动条样式问题 */\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.input-wrapper /deep/ .u-input__textarea::-webkit-scrollbar {\n  display: none;\n}\n\n/* 确保placeholder样式一致 */\n.input-wrapper /deep/ .u-input__textarea::placeholder,\n.input-wrapper /deep/ .u-input__input::placeholder {\n  color: #999 !important;\n  font-size: 32rpx !important;\n  opacity: 1;\n}\n\n/* 微信小程序特殊样式适配 */\n/* #ifdef MP-WEIXIN */\n.input-wrapper /deep/ .u-input__textarea {\n  word-break: break-all;\n  white-space: pre-wrap;\n}\n/* #endif */\n\n/* 防止输入时的布局跳动 */\n.input-wrapper {\n  will-change: height;\n  contain: layout style;\n}\n\n.voice-record-btn {\n  flex: 1;\n  height: 72rpx;\n  background: #f5f5f5;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.voice-record-btn.recording {\n  background: #ff4757;\n}\n\n.record-text {\n  font-size: 32rpx;\n  color: #333;\n}\n\n.recording .record-text {\n  color: #fff;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n  gap: 24rpx;\n}\n\n.send-btn {\n  width: 72rpx;\n  height: 72rpx;\n  background: #2979ff;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752819904833\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}