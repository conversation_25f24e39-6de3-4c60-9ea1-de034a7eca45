package com.yupi.springbootinit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yupi.springbootinit.model.entity.Follow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 关注关系映射器
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface FollowMapper extends BaseMapper<Follow> {

    /**
     * 获取用户的关注数
     *
     * @param userId 用户ID
     * @return 关注数
     */
    @Select("SELECT COUNT(*) FROM follows WHERE follower_id = #{userId} AND status = 1 AND is_delete = 0")
    Integer getFollowingCount(@Param("userId") Long userId);

    /**
     * 获取用户的粉丝数
     *
     * @param userId 用户ID
     * @return 粉丝数
     */
    @Select("SELECT COUNT(*) FROM follows WHERE following_id = #{userId} AND status = 1 AND is_delete = 0")
    Integer getFollowerCount(@Param("userId") Long userId);

    /**
     * 检查是否已关注
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否已关注
     */
    @Select("SELECT COUNT(*) > 0 FROM follows WHERE follower_id = #{followerId} AND following_id = #{followingId} AND status = 1 AND is_delete = 0")
    Boolean isFollowing(@Param("followerId") Long followerId, @Param("followingId") Long followingId);

    /**
     * 获取互相关注的用户ID列表
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 用户ID列表
     */
    @Select("SELECT f1.following_id FROM follows f1 " +
            "INNER JOIN follows f2 ON f1.following_id = f2.follower_id AND f1.follower_id = f2.following_id " +
            "WHERE f1.follower_id = #{userId} AND f1.status = 1 AND f1.is_delete = 0 " +
            "AND f2.status = 1 AND f2.is_delete = 0 " +
            "ORDER BY f1.create_time DESC LIMIT #{limit}")
    List<Long> getMutualFollowUserIds(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 获取推荐关注的用户ID列表
     * 基于共同关注的用户推荐
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 用户ID列表
     */
    @Select("SELECT f2.following_id, COUNT(*) as common_count " +
            "FROM follows f1 " +
            "INNER JOIN follows f2 ON f1.following_id = f2.follower_id " +
            "WHERE f1.follower_id = #{userId} AND f1.status = 1 AND f1.is_delete = 0 " +
            "AND f2.status = 1 AND f2.is_delete = 0 " +
            "AND f2.following_id != #{userId} " +
            "AND f2.following_id NOT IN (" +
            "    SELECT following_id FROM follows " +
            "    WHERE follower_id = #{userId} AND status = 1 AND is_delete = 0" +
            ") " +
            "GROUP BY f2.following_id " +
            "ORDER BY common_count DESC, f2.create_time DESC " +
            "LIMIT #{limit}")
    List<Long> getRecommendUserIds(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 批量检查关注状态
     *
     * @param followerId 关注者ID
     * @param followingIds 被关注者ID列表
     * @return 已关注的用户ID列表
     */
    @Select("<script>" +
            "SELECT following_id FROM follows " +
            "WHERE follower_id = #{followerId} AND status = 1 AND is_delete = 0 " +
            "AND following_id IN " +
            "<foreach collection='followingIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<Long> batchCheckFollowStatus(@Param("followerId") Long followerId, 
                                     @Param("followingIds") List<Long> followingIds);
}
