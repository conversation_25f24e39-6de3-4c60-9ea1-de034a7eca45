@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.edit-profile-container.data-v-10e92e6d {
  min-height: 100vh;
  background: #f5f5f5;
  padding-top: 30rpx;
}
.content.data-v-10e92e6d {
  flex: 1;
}
.edit-section.data-v-10e92e6d {
  background: #fff;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.edit-item.data-v-10e92e6d {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.edit-item.data-v-10e92e6d:last-child {
  border-bottom: none;
}
.edit-label.data-v-10e92e6d {
  width: 160rpx;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}
.avatar-edit.data-v-10e92e6d {
  position: relative;
  display: flex;
  align-items: center;
}
.camera-icon.data-v-10e92e6d {
  position: absolute;
  bottom: 0;
  right: 0;
  background: #fff;
  border-radius: 50%;
  padding: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.edit-input.data-v-10e92e6d {
  flex: 1;
  margin-left: 24rpx;
}
.edit-textarea.data-v-10e92e6d {
  flex: 1;
  margin-left: 24rpx;
  min-height: 120rpx;
}
.button-section.data-v-10e92e6d {
  padding: 40rpx 32rpx 80rpx;
}
.save-button.data-v-10e92e6d {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

