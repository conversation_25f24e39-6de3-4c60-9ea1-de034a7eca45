<template>
	<view class="successpage">
		
		<view class="yycgCon" style="margin:68rpx 0 0 0;">
			<image src="/static/images/icon48.png" style="width: 636rpx;height: 636rpx;"></image>
			<view class="yycgCon_a" style="margin-top: 66rpx;">支付成功</view>
			<view class="yycgCon_b">可在个人中心“<text @click="kecGoTap">我的课包</text>”中查看</view>
		</view>
		
	</view>
</template>


<script>
export default {
	data() {
		return {
			isLogined:true,
		}
	},
	onShow() {
		
	},
	methods: {
		kecGoTap(url){
			uni.hideTabBar()
			uni.reLaunch({
				url:'/pages/mine/lessonPackage/lessonPackage'
			})
		}
	}
}
</script>

<style lang="scss">
.successpage{overflow: hidden;}
page{padding-bottom: 0;}
</style>