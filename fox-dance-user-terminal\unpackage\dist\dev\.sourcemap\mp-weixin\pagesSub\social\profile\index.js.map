{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?a76f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?fe51", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?fb44", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?c1c1", "uni-app:///pagesSub/social/profile/index.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?40a8", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?c29c", "uni-app:///main.js"], "names": ["name", "components", "PostCard", "data", "userInfo", "userId", "nickname", "avatar", "bio", "danceType", "postCount", "followingCount", "followersCount", "likeCount", "draftCount", "currentTab", "tabs", "id", "title", "coverImage", "username", "userAvatar", "content", "commentCount", "isLiked", "createTime", "onLoad", "onShow", "methods", "loadUserInfo", "loadTabData", "generateMockPostsForTab", "posts", "switchTab", "scanCode", "uni", "success", "console", "goSettings", "url", "editAvatar", "count", "sizeType", "sourceType", "editProfile", "goLikeList", "viewPost", "goPostDetail", "goUserProfile", "onPostLike", "post", "icon", "duration", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,aAAa,+OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eCyFxvB;EACAA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC,OACA;QACAhB;QACAG,OACA;UACAc;UACAC;UACAC;UACAC;UACAC;UACAC;UACAT;UACAU;UACAC;UACAC;QACA,GACA;UACAR;UACAC;UACAC;UACAC;UACAC;UACAC;UACAT;UACAU;UACAC;UACAC;QACA,GACA;UACAR;UACAC;UACAC;UACAC;UACAC;UACAC;UACAT;UACAU;UACAC;UACAC;QACA,GACA;UACAR;UACAC;UACAC;UACAC;UACAC;UACAC;UACAT;UACAU;UACAC;UACAC;QACA;MAEA,GACA;QAAAzB;QAAAG;MAAA,GACA;QAAAH;QAAAG;MAAA;IAEA;EACA;EACAuB;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IAAA,CACA;IAEAC;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QAAA;QACA;MACA;;MACA;QACA;QACA;MACA;MAEA;QACAC;UACAf;UACAC;UACAC;UACAC;UACAC;UACAC;UACAT;UACAU;UACAC;UACAC;QACA;MACA;MACA;IACA;IAEAQ;MACA;MACA;MACA;MACA;IACA;IAEAC;MACAC;QACAC;UACAC;QACA;MACA;IACA;IAEAC;MACAH;QACAI;MACA;IACA;IAEAC;MAAA;MACAL;QACAM;QACAC;QACAC;QACAP;UACA;UACA;QACA;MACA;IACA;IAEAQ;MACAT;QACAI;MACA;IACA;IAEAM;MACAV;QACAI;MACA;IACA;IAEAO;MACAX;QACAI;MACA;IACA;IAEA;IACAQ;MACAZ;QACAI;MACA;IACA;IAEAS;MACA;MACA;MAEAb;QACAI;MACA;IACA;IAEAU;MACA;MACAC;MACA;QACAA;QACAf;UACAjB;UACAiC;UACAC;QACA;MACA;QACAF;QACAf;UACAjB;UACAiC;UACAC;QACA;MACA;;MAEA;MACAf;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnTA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAgB,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pagesSub/social/profile/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4df458ff&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4df458ff\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4df458ff&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-empty/u-empty\" */ \"@/components/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.tabs[_vm.currentTab] && _vm.tabs[_vm.currentTab].data.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"profile-container\">\n    <!-- 蓝色渐变背景头部 -->\n    <view class=\"header-section\">\n      <view class=\"header-bg\"></view>\n\n      <!-- 顶部操作按钮 -->\n      <view class=\"header-actions\">\n        <u-icon name=\"scan\" color=\"#fff\" size=\"48rpx\" @click=\"scanCode\"></u-icon>\n        <u-icon name=\"setting\" color=\"#fff\" size=\"48rpx\" @click=\"goSettings\"></u-icon>\n      </view>\n\n      <!-- 用户信息 -->\n      <view class=\"user-info-section\">\n        <view class=\"user-avatar-container\">\n          <u-avatar :src=\"userInfo.avatar\" size=\"120\" @click=\"editAvatar\"></u-avatar>\n        </view>\n\n        <!-- 用户信息内容容器 -->\n        <view class=\"user-info-content\">\n          <view class=\"user-info-row\">\n            <!-- 左侧用户信息 -->\n            <view class=\"user-details\">\n              <text class=\"nickname\">{{ userInfo.nickname }}</text>\n              <text class=\"user-id\">ID: {{ userInfo.userId }}</text>\n              <text class=\"dance-type\">舞种：{{ userInfo.danceType || '街舞' }}</text>\n              <text class=\"bio\">{{ userInfo.bio || '美食爱好者 | 旅行达人 | 摄影 | 生活方式博主' }}</text>\n            </view>\n\n            <!-- 右侧编辑链接 -->\n            <view class=\"edit-section\">\n              <text class=\"edit-link\" @click=\"editProfile\">编辑资料</text>\n            </view>\n          </view>\n\n          <!-- 数据统计 -->\n          <view class=\"stats-row\">\n            <view class=\"stat-item\" @click=\"switchTab(0)\">\n              <text class=\"stat-number\">{{ userInfo.postCount }}</text>\n              <text class=\"stat-label\">帖子</text>\n            </view>\n            <view class=\"stat-item\" @click=\"goLikeList\">\n              <text class=\"stat-number\">{{ userInfo.likeCount }}</text>\n              <text class=\"stat-label\">获赞</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 操作按钮 -->\n      <view class=\"tabs-container\">\n        <u-tabs\n          :list=\"tabs\"\n          :current=\"currentTab\"\n          @change=\"switchTab\"\n          lineWidth=\"30\"\n          lineColor=\"#303133\"\n          :activeStyle=\"{ color: '#303133', fontWeight: 'bold' }\"\n          :inactiveStyle=\"{ color: '#606266' }\"\n        ></u-tabs>\n      </view>\n\n      <!-- 帖子列表 -->\n      <view class=\"posts-content\">\n        <view v-if=\"tabs[currentTab] && tabs[currentTab].data.length > 0\" class=\"post-grid\">\n          <PostCard\n            v-for=\"post in tabs[currentTab].data\"\n            :key=\"post.id\"\n            :post=\"post\"\n            class=\"post-card-item\"\n            @click=\"goPostDetail\"\n            @user-click=\"goUserProfile\"\n            @like=\"onPostLike\"\n          />\n        </view>\n        <view v-else class=\"empty-state\">\n          <u-empty mode=\"list\" text=\"暂无内容\"></u-empty>\n        </view>\n      </view>\n    </scroll-view>\n\n  </view>\n</template>\n\n<script>\nimport PostCard from '../components/PostCard.vue'\n\nexport default {\n  name: 'SocialProfile',\n  components: {\n    PostCard\n  },\n  data() {\n    return {\n      userInfo: {\n        userId: 'xiaoming_zhang',\n        nickname: '张小明',\n        avatar: '/static/images/toux.png',\n        bio: '美食爱好者 | 旅行达人 | 摄影师 | 生活方式博主',\n        danceType: '街舞',\n        postCount: 128,\n        followingCount: 256,\n        followersCount: 1024,\n        likeCount: 8547,\n        draftCount: 3\n      },\n      currentTab: 0,\n      tabs: [\n        {\n          name: '作品',\n          data: [\n            {\n              id: 1,\n              title: '你喜欢什么颜色的?',\n              coverImage: 'https://picsum.photos/400/400?random=100',\n              username: '张小明',\n              userAvatar: '/static/images/toux.png',\n              content: '分享一下今天的心情',\n              likeCount: 219,\n              commentCount: 15,\n              isLiked: false,\n              createTime: new Date(Date.now() - 3600000)\n            },\n            {\n              id: 2,\n              title: '这是在哪拍的?',\n              coverImage: 'https://picsum.photos/400/400?random=102',\n              username: '张小明',\n              userAvatar: '/static/images/toux.png',\n              content: '美丽的风景',\n              likeCount: 16,\n              commentCount: 3,\n              isLiked: true,\n              createTime: new Date(Date.now() - 7200000)\n            },\n            {\n              id: 3,\n              title: '特角色',\n              coverImage: 'https://picsum.photos/400/400?random=103',\n              username: '张小明',\n              userAvatar: '/static/images/toux.png',\n              content: '今天的造型',\n              likeCount: 12,\n              commentCount: 2,\n              isLiked: false,\n              createTime: new Date(Date.now() - 10800000)\n            },\n            {\n              id: 4,\n              title: '这才是自由',\n              coverImage: 'https://picsum.photos/400/400?random=104',\n              username: '张小明',\n              userAvatar: '/static/images/toux.png',\n              content: '享受自由的感觉',\n              likeCount: 3157,\n              commentCount: 89,\n              isLiked: true,\n              createTime: new Date(Date.now() - 14400000)\n            }\n          ]\n        },\n        { name: '喜欢', data: [] },\n        { name: '收藏', data: [] }\n      ]\n    }\n  },\n  onLoad() {\n    this.loadUserInfo()\n    this.loadTabData(this.currentTab)\n  },\n  onShow() {\n    // 页面显示时刷新数据\n    this.loadUserInfo()\n  },\n  methods: {\n    loadUserInfo() {\n      // 加载用户信息\n      // 这里应该调用API获取用户数据\n    },\n\n    loadTabData(tabIndex) {\n      if (tabIndex > 0 && this.tabs[tabIndex].data.length === 0) {\n        const mockData = this.generateMockPostsForTab(tabIndex)\n        this.$set(this.tabs[tabIndex], 'data', mockData)\n      }\n    },\n\n    generateMockPostsForTab(tabIndex) {\n      const posts = []\n      const titles = {\n        1: ['超爱的瞬间', '百看不厌', '为它点赞', '今日最佳'], // 喜欢\n        2: ['我的珍藏', '稍后再看', '深度好文', '灵感来源'] // 收藏\n      }\n      const contents = {\n        1: ['这个瞬间太美了', '真的很棒', '必须点赞', '今天最棒的内容'],\n        2: ['值得收藏', '有空再看', '写得很好', '很有启发']\n      }\n\n      for (let i = 0; i < 6; i++) {\n        posts.push({\n          id: tabIndex * 10 + i,\n          title: titles[tabIndex][i % 4],\n          coverImage: `https://picsum.photos/400/400?random=${tabIndex * 10 + i}`,\n          username: this.userInfo.nickname,\n          userAvatar: this.userInfo.avatar,\n          content: contents[tabIndex][i % 4],\n          likeCount: Math.floor(Math.random() * 1000),\n          commentCount: Math.floor(Math.random() * 50),\n          isLiked: Math.random() > 0.5,\n          createTime: new Date(Date.now() - Math.random() * 86400000 * 30)\n        })\n      }\n      return posts\n    },\n\n    switchTab(item) {\n      const index = typeof item === 'object' ? item.index : item;\n      if (this.currentTab === index) return\n      this.currentTab = index\n      this.loadTabData(index)\n    },\n\n    scanCode() {\n      uni.scanCode({\n        success: (res) => {\n          console.log('扫码结果:', res)\n        }\n      })\n    },\n\n    goSettings() {\n      uni.navigateTo({\n        url: '/pagesSub/social/settings/index'\n      })\n    },\n\n    editAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          // 上传头像\n          this.userInfo.avatar = res.tempFilePaths[0]\n        }\n      })\n    },\n\n    editProfile() {\n      uni.navigateTo({\n        url: '/pagesSub/social/profile/edit'\n      })\n    },\n\n    goLikeList() {\n      uni.navigateTo({\n        url: '/pagesSub/social/like/list'\n      })\n    },\n\n    viewPost(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    },\n\n    // PostCard组件需要的方法\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    },\n\n    goUserProfile(post) {\n      // 如果是自己的帖子，不需要跳转\n      if (post.username === this.userInfo.nickname) return\n\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${post.userId || post.id}`\n      })\n    },\n\n    onPostLike(post) {\n      // 切换点赞状态\n      post.isLiked = !post.isLiked\n      if (post.isLiked) {\n        post.likeCount++\n        uni.showToast({\n          title: '点赞成功',\n          icon: 'success',\n          duration: 1000\n        })\n      } else {\n        post.likeCount--\n        uni.showToast({\n          title: '取消点赞',\n          icon: 'none',\n          duration: 1000\n        })\n      }\n\n      // 这里可以调用API同步到服务器\n      console.log('点赞操作:', post.id, post.isLiked)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-container {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding-bottom: 100rpx;\n}\n\n.header-section {\n  position: relative;\n  background: #fff;\n}\n\n.header-bg {\n  height: 400rpx;\n  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);\n}\n\n.header-actions {\n  position: absolute;\n  top: 60rpx;\n  right: 32rpx;\n  display: flex;\n  gap: 32rpx;\n  z-index: 10;\n}\n\n.user-info-section {\n  padding: 40rpx 50rpx;\n  background: #f8f9fa;\n}\n\n.user-info-content {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-top: 50rpx;\n  border: 1rpx solid #e9ecef;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n.user-avatar-container {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 24rpx;\n  position: absolute;\n  top: 340rpx;\n  left: 9%;\n}\n\n.user-info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 24rpx;\n}\n\n.user-details {\n  flex: 1;\n  text-align: left;\n}\n\n.edit-section {\n  flex-shrink: 0;\n  margin-left: 20rpx;\n  display: flex;\n  align-items: flex-start;\n}\n\n.edit-link {\n  font-size: 28rpx;\n  font-weight: 500;\n  border: 1rpx solid #2979ff;\n  border-radius: 10rpx;\n  padding: 10rpx 20rpx;\n  margin: 10rpx;\n  color: #2979ff;\n}\n\n.nickname {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n.user-id {\n  font-size: 24rpx;\n  color: #999;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n.dance-type {\n  font-size: 26rpx;\n  color: #999;\n  display: block;\n  margin-bottom: 16rpx;\n  font-weight: 500;\n}\n\n.bio {\n  font-size: 28rpx;\n  color: #999;\n  line-height: 1.4;\n  display: block;\n}\n\n.stats-row {\n  display: flex;\n  justify-content: center;\n  gap: 80rpx;\n  margin-bottom: 0;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-number {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.tabs-container {\n  background: #fff;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.content {\n  background: #fff;\n  min-height: 60vh;\n}\n\n.posts-content {\n  padding: 32rpx;\n}\n\n.post-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n}\n\n.post-card-item {\n  width: calc(50% - 8rpx);\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752736818272\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/profile/index.vue'\ncreatePage(Page)"], "sourceRoot": ""}