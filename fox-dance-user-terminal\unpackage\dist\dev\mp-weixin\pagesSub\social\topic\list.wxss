@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.topic-list-container.data-v-2c87410a {
  min-height: 100vh;
  background: #f8f9fa;
}
.header.data-v-2c87410a {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  padding: 25px 0 0;
  border-bottom: 1rpx solid #e4e7ed;
}
.header-content.data-v-2c87410a {
  padding: 0 32rpx;
}
.nav-bar.data-v-2c87410a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
}
.nav-title.data-v-2c87410a {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.nav-right.data-v-2c87410a {
  width: 40rpx;
}
.search-section.data-v-2c87410a {
  padding: 24rpx 0;
}
.search-bar.data-v-2c87410a {
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
}
.search-input.data-v-2c87410a {
  flex: 1;
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
}
.content.data-v-2c87410a {
  margin-top: calc(140rpx + 25px);
  padding: 32rpx;
  width: auto;
}
.topic-grid.data-v-2c87410a {
  display: flex;
  flex-wrap: wrap;
  gap: 23rpx;
}
.topic-card.data-v-2c87410a {
  width: calc(50% - 12rpx);
  height: 240rpx;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.topic-cover.data-v-2c87410a {
  width: 100%;
  height: 100%;
}
.topic-overlay.data-v-2c87410a {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 24rpx 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}
.topic-info.data-v-2c87410a {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.topic-name.data-v-2c87410a {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
  display: block;
}
.topic-stats.data-v-2c87410a {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}
.topic-actions.data-v-2c87410a {
  margin-top: 16rpx;
  display: flex;
  justify-content: flex-end;
}
.load-more.data-v-2c87410a {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  gap: 16rpx;
}
.load-text.data-v-2c87410a {
  font-size: 28rpx;
  color: #999;
}
.no-more.data-v-2c87410a {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}
.no-more-text.data-v-2c87410a {
  font-size: 28rpx;
  color: #999;
}
.empty-state.data-v-2c87410a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}
.empty-text.data-v-2c87410a {
  font-size: 32rpx;
  color: #999;
  margin: 32rpx 0 16rpx;
}
.empty-desc.data-v-2c87410a {
  font-size: 28rpx;
  color: #ccc;
}

