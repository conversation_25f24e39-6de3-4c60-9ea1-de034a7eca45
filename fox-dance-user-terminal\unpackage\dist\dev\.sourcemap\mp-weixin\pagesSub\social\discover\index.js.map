{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?ac56", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?f6d4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?752b", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?ebdf", "uni-app:///pagesSub/social/discover/index.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?aac2", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?659d", "uni-app:///main.js"], "names": ["name", "components", "FollowButton", "data", "hotTopics", "recommendUsers", "hotPosts", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "onShow", "console", "activated", "methods", "loadDiscoverData", "loadHotTopics", "id", "cover", "postCount", "loadRecommendUsers", "nickname", "avatar", "description", "isFollowed", "loadHotPosts", "username", "userAvatar", "content", "coverImage", "likeCount", "commentCount", "loadFeaturedContent", "title", "subtitle", "onUserFollow", "onFollowChange", "user", "goSearch", "uni", "url", "goTopic", "goTopicList", "forceRefresh", "goUserProfile", "goPostDetail", "goFeaturedDetail", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC2HxvB;EACAA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EAEAC;IACA;IACA;MACAC;MACA;IACA;EACA;EAEA;EACAC;IACA;MACAD;MACA;IACA;EACA;EACAE;IACAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA,kBACA;QACAC;QACAf;QACAgB;QACAC;MACA,GACA;QACAF;QACAf;QACAgB;QACAC;MACA,GACA;QACAF;QACAf;QACAgB;QACAC;MACA,GACA;QACAF;QACAf;QACAgB;QACAC;MACA,EACA;IACA;IAEAC;MACA;MACA,uBACA;QACAH;QACAI;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAI;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAI;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAI;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAI;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAI;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAI;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAI;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAI;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAI;QACAC;QACAC;QACAC;MACA,EACA;IACA;IAEAC;MACA;MACA,iBACA;QACAR;QACAS;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAd;QACAS;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;IACA;IAEAC;MACA;MACA,wBACA;QACAf;QACAgB;QACAC;QACAhB;MACA,GACA;QACAD;QACAgB;QACAC;QACAhB;MACA,EACA;IACA;IAEAiB;MACAvB;MACA;IACA;IAEAwB;MACA;MACA;QAAA;MAAA;MACA;QACAC;MACA;MACAzB;IACA;IAEA0B;MACAC;QACAC;MACA;IACA;IAEAC;MACAF;QACAC;MACA;IACA;IAEAE;MACAH;QACAC;MACA;IACA;IAEA;IACAG;MACA/B;MACA;MACA;MACA;MACA;MACA;IACA;IAEAgC;MACAL;QACAC;MACA;IACA;IAIAK;MACAN;QACAC;MACA;IACA;IAEAM;MACAP;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrXA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAO,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pagesSub/social/discover/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1fcbd0ae&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1fcbd0ae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/discover/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1fcbd0ae&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"discover-container\">\n    <!-- 顶部搜索 -->\n    <view class=\"header\">\n      <view class=\"search-bar\" @click=\"goSearch\">\n        <u-icon name=\"search\" size=\"18\" color=\"#999\"></u-icon>\n        <text class=\"search-placeholder\">搜索话题、用户...</text>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 热门话题 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">热门话题</text>\n          <text class=\"more-btn\" @click=\"goTopicList\">更多</text>\n        </view>\n        <view class=\"topic-grid\">\n          <view \n            v-for=\"topic in hotTopics\" \n            :key=\"topic.id\"\n            class=\"topic-card\"\n            @click=\"goTopic(topic)\"\n          >\n            <image :src=\"topic.cover\" class=\"topic-cover\" mode=\"aspectFill\" />\n            <view class=\"topic-info\">\n              <text class=\"topic-name\">#{{ topic.name }}</text>\n              <text class=\"topic-count\">{{ topic.postCount }}条帖子</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 推荐用户 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">推荐关注</text>\n        </view>\n        <scroll-view class=\"user-scroll\" scroll-x>\n          <view class=\"user-list\">\n            <view \n              v-for=\"user in recommendUsers\" \n              :key=\"user.id\"\n              class=\"user-card\"\n              @click=\"goUserProfile(user)\"\n            >\n              <u-avatar :src=\"user.avatar\" size=\"60\"></u-avatar>\n              <text class=\"user-name\">{{ user.nickname }}</text>\n              <text class=\"user-desc\">{{ user.description }}</text>\n              <FollowButton\n                :user=\"user\"\n                :followed=\"user.isFollowed\"\n                size=\"mini\"\n                @follow=\"onUserFollow\"\n                @change=\"onFollowChange\"\n                @click.stop\n              />\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 热门帖子 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">热门帖子</text>\n        </view>\n        <view class=\"hot-posts\">\n          <view \n            v-for=\"post in hotPosts\" \n            :key=\"post.id\"\n            class=\"hot-post-item\"\n            @click=\"goPostDetail(post)\"\n          >\n            <view class=\"post-content\">\n              <view class=\"user-info\">\n                <u-avatar :src=\"post.userAvatar\" size=\"32\"></u-avatar>\n                <text class=\"username\">{{ post.username }}</text>\n              </view>\n              <text class=\"post-text\">{{ post.content }}</text>\n              <view class=\"post-stats\">\n                <text class=\"stat-item\">{{ post.likeCount }}赞</text>\n                <text class=\"stat-item\">{{ post.commentCount }}评论</text>\n              </view>\n            </view>\n            <image \n              v-if=\"post.coverImage\" \n              :src=\"post.coverImage\" \n              class=\"post-cover\"\n              mode=\"aspectFill\"\n            />\n          </view>\n        </view>\n      </view>\n\n      <!-- 精选内容 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">精选内容</text>\n        </view>\n        <view class=\"featured-grid\">\n          <view \n            v-for=\"item in featuredContent\" \n            :key=\"item.id\"\n            class=\"featured-item\"\n            @click=\"goFeaturedDetail(item)\"\n          >\n            <image :src=\"item.cover\" class=\"featured-cover\" mode=\"aspectFill\" />\n            <view class=\"featured-overlay\">\n              <text class=\"featured-title\">{{ item.title }}</text>\n              <text class=\"featured-subtitle\">{{ item.subtitle }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n\n  </view>\n</template>\n\n<script>\nimport FollowButton from '../components/FollowButton.vue'\n\nexport default {\n  name: 'SocialDiscover',\n  components: {\n    FollowButton\n  },\n  data() {\n    return {\n      hotTopics: [],\n      recommendUsers: [],\n      hotPosts: [],\n      featuredContent: []\n    }\n  },\n  onLoad() {\n    this.loadDiscoverData()\n  },\n\n  onShow() {\n    // 页面显示时检查是否需要加载数据\n    if (!this.hotTopics || this.hotTopics.length === 0) {\n      console.log('发现页显示时重新加载数据')\n      this.loadDiscoverData()\n    }\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    if (!this.hotTopics || this.hotTopics.length === 0) {\n      console.log('发现页激活时重新加载数据')\n      this.loadDiscoverData()\n    }\n  },\n  methods: {\n    loadDiscoverData() {\n      this.loadHotTopics()\n      this.loadRecommendUsers()\n      this.loadHotPosts()\n      this.loadFeaturedContent()\n    },\n\n    loadHotTopics() {\n      // 模拟热门话题数据\n      this.hotTopics = [\n        {\n          id: 1,\n          name: '今日穿搭',\n          cover: 'https://picsum.photos/200/120?random=1',\n          postCount: 1234\n        },\n        {\n          id: 2,\n          name: '美食分享',\n          cover: 'https://picsum.photos/200/120?random=2',\n          postCount: 856\n        },\n        {\n          id: 3,\n          name: '旅行日记',\n          cover: 'https://picsum.photos/200/120?random=3',\n          postCount: 642\n        },\n        {\n          id: 4,\n          name: '生活记录',\n          cover: 'https://picsum.photos/200/120?random=4',\n          postCount: 789\n        }\n      ]\n    },\n\n    loadRecommendUsers() {\n      // 模拟推荐用户数据\n      this.recommendUsers = [\n        {\n          id: 1,\n          nickname: '小美',\n          avatar: 'https://picsum.photos/100/100?random=10',\n          description: '分享生活美好',\n          isFollowed: false\n        },\n        {\n          id: 2,\n          nickname: '旅行达人',\n          avatar: 'https://picsum.photos/100/100?random=11',\n          description: '世界那么大',\n          isFollowed: false\n        },\n        {\n          id: 3,\n          nickname: '美食家',\n          avatar: 'https://picsum.photos/100/100?random=12',\n          description: '发现美味',\n          isFollowed: true\n        },\n        {\n          id: 4,\n          nickname: '摄影师',\n          avatar: 'https://picsum.photos/100/100?random=13',\n          description: '记录美好瞬间',\n          isFollowed: false\n        },\n        {\n          id: 5,\n          nickname: '健身达人',\n          avatar: 'https://picsum.photos/100/100?random=14',\n          description: '健康生活方式',\n          isFollowed: false\n        },\n        {\n          id: 6,\n          nickname: '音乐人',\n          avatar: 'https://picsum.photos/100/100?random=15',\n          description: '用音乐表达情感',\n          isFollowed: true\n        },\n        {\n          id: 7,\n          nickname: '读书爱好者',\n          avatar: 'https://picsum.photos/100/100?random=16',\n          description: '书中自有黄金屋',\n          isFollowed: false\n        },\n        {\n          id: 8,\n          nickname: '手工达人',\n          avatar: 'https://picsum.photos/100/100?random=17',\n          description: '手作温暖生活',\n          isFollowed: false\n        },\n        {\n          id: 9,\n          nickname: '宠物博主',\n          avatar: 'https://picsum.photos/100/100?random=18',\n          description: '萌宠日常分享',\n          isFollowed: true\n        },\n        {\n          id: 10,\n          nickname: '科技极客',\n          avatar: 'https://picsum.photos/100/100?random=19',\n          description: '探索科技前沿',\n          isFollowed: false\n        }\n      ]\n    },\n\n    loadHotPosts() {\n      // 模拟热门帖子数据\n      this.hotPosts = [\n        {\n          id: 1,\n          username: '时尚博主',\n          userAvatar: 'https://picsum.photos/100/100?random=20',\n          content: '今天的穿搭分享，简约而不简单的搭配技巧...',\n          coverImage: 'https://picsum.photos/120/120?random=30',\n          likeCount: 234,\n          commentCount: 45\n        },\n        {\n          id: 2,\n          username: '美食探店',\n          userAvatar: 'https://picsum.photos/100/100?random=21',\n          content: '发现了一家超棒的咖啡店，环境和咖啡都很赞！',\n          coverImage: 'https://picsum.photos/120/120?random=31',\n          likeCount: 189,\n          commentCount: 32\n        }\n      ]\n    },\n\n    loadFeaturedContent() {\n      // 模拟精选内容数据\n      this.featuredContent = [\n        {\n          id: 1,\n          title: '春日穿搭指南',\n          subtitle: '时尚达人教你搭配',\n          cover: 'https://picsum.photos/300/200?random=40'\n        },\n        {\n          id: 2,\n          title: '周末好去处',\n          subtitle: '城市探索攻略',\n          cover: 'https://picsum.photos/300/200?random=41'\n        }\n      ]\n    },\n\n    onUserFollow(data) {\n      console.log('关注操作:', data)\n      // 这里可以调用API进行关注/取消关注操作\n    },\n\n    onFollowChange(data) {\n      // 更新本地数据\n      const user = this.recommendUsers.find(u => u.id === data.user.id)\n      if (user) {\n        user.isFollowed = data.isFollowed\n      }\n      console.log('关注状态变化:', data)\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: '/pagesSub/social/search/index'\n      })\n    },\n\n    goTopic(topic) {\n      uni.navigateTo({\n        url: `/pagesSub/social/topic/detail?id=${topic.id}`\n      })\n    },\n\n    goTopicList() {\n      uni.navigateTo({\n        url: '/pagesSub/social/topic/list'\n      })\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log('强制刷新发现页数据...')\n      this.hotTopics = []\n      this.recommendUsers = []\n      this.hotPosts = []\n      this.featuredContent = []\n      this.loadDiscoverData()\n    },\n\n    goUserProfile(user) {\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${user.id}`\n      })\n    },\n\n\n\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    },\n\n    goFeaturedDetail(item) {\n      uni.navigateTo({\n        url: `/pagesSub/social/featured/detail?id=${item.id}`\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.discover-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 200rpx;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  padding: var(--status-bar-height) 32rpx 24rpx;\n  border-bottom: 2rpx solid #e4e7ed;\n}\n\n.search-bar {\n  height: 72rpx;\n  background: #f5f5f5;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  padding: 0 32rpx;\n}\n\n.search-placeholder {\n  margin-left: 16rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.content {\n  margin-top: calc(125rpx + var(--status-bar-height));\n  padding: 0 32rpx;\n  width: auto;\n}\n\n.section {\n  margin-bottom: 48rpx;\n}\n\n.section-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 24rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.more-btn {\n  font-size: 28rpx;\n  color: #2979ff;\n}\n\n.topic-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 22rpx;\n}\n\n.topic-card {\n  width: calc(50% - 12rpx);\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.topic-cover {\n  width: 100%;\n  height: 160rpx;\n}\n\n.topic-info {\n  padding: 24rpx;\n}\n\n.topic-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  display: block;\n}\n\n.topic-count {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 8rpx;\n  display: block;\n}\n\n.user-scroll {\n  white-space: nowrap;\n}\n\n.user-list {\n  display: flex;\n  gap: 32rpx;\n  padding-bottom: 16rpx;\n}\n\n.user-card {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 32rpx 24rpx;\n  min-width: 200rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.user-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin: 16rpx 0 8rpx;\n  text-align: center;\n}\n\n.user-desc {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 24rpx;\n  text-align: center;\n}\n\n.hot-posts {\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.hot-post-item {\n  display: flex;\n  padding: 32rpx;\n  border-bottom: 2rpx solid #f0f0f0;\n}\n\n.hot-post-item:last-child {\n  border-bottom: none;\n}\n\n.post-content {\n  flex: 1;\n  margin-right: 24rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.username {\n  margin-left: 16rpx;\n  font-size: 26rpx;\n  color: #666;\n}\n\n.post-text {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.4;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n.post-stats {\n  display: flex;\n  gap: 32rpx;\n}\n\n.stat-item {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.post-cover {\n  width: 160rpx;\n  height: 160rpx;\n  border-radius: 16rpx;\n}\n\n.featured-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.featured-item {\n  position: relative;\n  height: 240rpx;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.featured-cover {\n  width: 100%;\n  height: 100%;\n}\n\n.featured-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));\n  padding: 40rpx 32rpx 32rpx;\n}\n\n.featured-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #fff;\n  display: block;\n}\n\n.featured-subtitle {\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 8rpx;\n  display: block;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752827279629\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/discover/index.vue'\ncreatePage(Page)"], "sourceRoot": ""}