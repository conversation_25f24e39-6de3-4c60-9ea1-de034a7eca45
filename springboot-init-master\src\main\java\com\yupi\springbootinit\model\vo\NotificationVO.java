package com.yupi.springbootinit.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息通知视图对象
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@ApiModel(description = "消息通知视图对象")
public class NotificationVO implements Serializable {

    /**
     * 通知ID
     */
    @ApiModelProperty(value = "通知ID", example = "1")
    private Long id;

    /**
     * 接收通知的用户ID
     */
    @ApiModelProperty(value = "接收通知的用户ID", example = "1")
    private Long userId;

    /**
     * 发送通知的用户ID
     */
    @ApiModelProperty(value = "发送通知的用户ID", example = "2")
    private Long senderId;

    /**
     * 发送者昵称
     */
    @ApiModelProperty(value = "发送者昵称", example = "张小明")
    private String senderNickname;

    /**
     * 发送者头像
     */
    @ApiModelProperty(value = "发送者头像", example = "https://example.com/avatar.jpg")
    private String senderAvatar;

    /**
     * 通知类型：1-点赞，2-评论，3-关注，4-系统通知
     */
    private Integer type;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 相关ID（帖子ID、评论ID等）
     */
    private Long relatedId;

    /**
     * 相关类型（post、comment等）
     */
    private String relatedType;

    /**
     * 是否已读：0-未读，1-已读
     */
    private Integer isRead;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 阅读时间
     */
    private Date readTime;

    /**
     * 时间显示文本
     */
    private String timeText;

    private static final long serialVersionUID = 1L;

    // 手动添加getter/setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public String getSenderNickname() {
        return senderNickname;
    }

    public void setSenderNickname(String senderNickname) {
        this.senderNickname = senderNickname;
    }

    public String getSenderAvatar() {
        return senderAvatar;
    }

    public void setSenderAvatar(String senderAvatar) {
        this.senderAvatar = senderAvatar;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(Long relatedId) {
        this.relatedId = relatedId;
    }

    public String getRelatedType() {
        return relatedType;
    }

    public void setRelatedType(String relatedType) {
        this.relatedType = relatedType;
    }

    public Integer getIsRead() {
        return isRead;
    }

    public void setIsRead(Integer isRead) {
        this.isRead = isRead;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getReadTime() {
        return readTime;
    }

    public void setReadTime(Date readTime) {
        this.readTime = readTime;
    }

    public String getTimeText() {
        return timeText;
    }

    public void setTimeText(String timeText) {
        this.timeText = timeText;
    }
}
