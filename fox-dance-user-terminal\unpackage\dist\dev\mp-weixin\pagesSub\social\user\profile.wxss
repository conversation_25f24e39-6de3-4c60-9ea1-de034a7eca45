@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.user-profile-container.data-v-9e24a6bc {
  min-height: 100vh;
  background: #f8f9fa;
}
.header.data-v-9e24a6bc {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding-top: 25px;
}
.header-content.data-v-9e24a6bc {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}
.title.data-v-9e24a6bc {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.content.data-v-9e24a6bc {
  margin-top: calc(44px + 25px);
  padding: 16px;
  width: auto;
}
.user-section.data-v-9e24a6bc {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.user-info.data-v-9e24a6bc {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}
.user-details.data-v-9e24a6bc {
  flex: 1;
  margin-left: 16px;
  margin-right: 12px;
}
.nickname.data-v-9e24a6bc {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}
.user-id.data-v-9e24a6bc {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 8px;
}
.bio.data-v-9e24a6bc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  display: block;
}
.stats-section.data-v-9e24a6bc {
  display: flex;
  justify-content: space-around;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
.stat-item.data-v-9e24a6bc {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-number.data-v-9e24a6bc {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}
.stat-label.data-v-9e24a6bc {
  font-size: 12px;
  color: #999;
}
.posts-section.data-v-9e24a6bc {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.section-header.data-v-9e24a6bc {
  margin-bottom: 16px;
}
.section-title.data-v-9e24a6bc {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.post-grid.data-v-9e24a6bc {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.post-card-item.data-v-9e24a6bc {
  width: calc(50% - 4px);
}
.empty-state.data-v-9e24a6bc {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}
.empty-text.data-v-9e24a6bc {
  font-size: 14px;
  color: #999;
  margin-top: 12px;
}

