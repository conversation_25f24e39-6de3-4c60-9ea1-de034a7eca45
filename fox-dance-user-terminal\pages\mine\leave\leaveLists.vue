<template>
	<view class="leaveLists" :style="{ '--qjbutton-color': qjbutton }">
		
		<view class="lea_nav">
			<view class="lea_nav_l">
				<view class="lea_nav_l_li" :class="type == 0 ? 'lea_nav_l_li_ac' : ''" @click="navTap(0)"><view><text>全部</text><text></text></view></view>
				<view class="lea_nav_l_li" :class="type == 1 ? 'lea_nav_l_li_ac' : ''" @click="navTap(1)"><view><text>进行中</text><text></text></view></view>
				<view class="lea_nav_l_li" :class="type == 2 ? 'lea_nav_l_li_ac' : ''" @click="navTap(2)"><view><text>已过期</text><text></text></view></view>
			</view>
			<view class="lea_nav_r">
				<text>筛选时间</text>
				<image src="/static/images/icon27.png"></image>
				<view class="lea_nav_r_sj"><uni-datetime-picker v-model="range" type="daterange" @change="maskClick" /></view>
			</view>
		</view>
		
		<view class="lea_con">
			<view class="lea_con_li" v-for="(item,index) in leaveLists" :key="index">
				<view class="lea_con_li_a">
					<view class="lea_con_li_a_l">请假<text></text>{{item.card.type*1 == 0 ? '次卡' : '时长卡'}}</view>
					<view class="lea_con_li_a_r" :style="item.status == 1 ? 'color:#F48477' : ''">{{item.status == 0 ? '进行中' : item.status == 1 ? '已过期' : item.status == 2 ? '已销假' : ''}}</view>
					<!-- "status": 2 //状态:0=请假中,1=已过期,2=已销假 -->
				</view>
				<view class="lea_con_li_b">{{item.start_time}} - {{item.end_time}}</view>
				<view class="lea_con_li_c" v-if="item.status == 0"><view @click="xjTap(item.id)">提前销假</view></view>
			</view>
		</view>
		
		<view class="gg_loding" v-if="!zanwsj">
			<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
				<view></view>
				<text>加载中</text>
			</view>
			<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
		</view>
		<view class="gg_zwsj" v-if="zanwsj">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无数据</text>
			</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<!-- 销假弹窗 go -->
		<view class="xjTanc" v-if="xjToggle">
			<view class="xjTanc_n">
				<view class="xjTanc_a">温馨提示</view>
				<view class="xjTanc_b">是否提前销假？</view>
				<view class="xjTanc_c">
					<view @click="xjToggle = false">取消</view>
					<view class="bak" @click="xjSubTap">确认</view>
				</view>
			</view>
		</view>
		<!-- 销假弹窗 end -->
		
	</view>
</template>


<script>
import {
	askForLeaveRecordApi,
	cancelAskForLeaveApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			navLists:['全部','等位中','待开课','授课中','已完成'],
			type:0,
			range: [],
			
			leaveLists:[],
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			isLogined:false,
			imgbaseUrl:'',//图片地址
			xjToggle:false,
			qjbutton:'#131315',
		}
	},
	onLoad() {
		this.imgbaseUrl = this.$baseUrl;
		this.page = 1;
		this.leaveLists = [];
		this.leaveData();//请假列表
		this.qjbutton = uni.getStorageSync('storeInfo').button
	},
	onShow() {
		
	},
	methods: {
		//销假
		xjTap(id){
			this.xjId = id;
			this.xjToggle = true;
		},
		//销假
		xjSubTap(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			cancelAskForLeaveApi({
				id:that.xjId
			}).then(res => {
				console.log('销假',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.xjToggle = false;
					uni.showToast({
						icon:'success',
						title: '销假成功',
						duration: 2000
					});
					setTimeout(()=>{
						uni.navigateBack();
					},1500)
				}
			})
		},
		//请假列表
		leaveData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			askForLeaveRecordApi({
				page:that.page,
				size:10,
				type:that.type,
				start_dete:that.range[0],
				end_dete:that.range[1],
			}).then(res => {
				console.log('请假列表',res)
				if (res.code == 1) {
					var obj = res.data.data;
					that.leaveLists = that.leaveLists.concat(obj);
					that.zanwsj = that.leaveLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.leaveLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottom() {
			// console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.leaveData();
				}
			}
		},
		onPullDownRefresh: function() {
		    // console.log('我被下拉了');
		    this.page = 1;
			this.leaveLists = [];
			this.leaveData();//请假列表
		},
		maskClick(e){
			console.log('maskClick事件:', e);
			this.page = 1;
			this.leaveLists = [];
			this.leaveData();//请假列表
		},
		navTap(index){
			this.type = index;
			this.page = 1;
			this.leaveLists = [];
			this.leaveData();//请假列表
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.leaveLists{
	overflow:hidden;
}
page{padding-bottom: 0;}
</style>