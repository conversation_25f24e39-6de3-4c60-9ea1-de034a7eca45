package com.yupi.springbootinit.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yupi.springbootinit.config.ListJsonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 帖子实体
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@TableName(value = "posts")
@Data
public class Post implements Serializable {

    /**
     * 帖子ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 发布用户ID，关联ba_user表
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 帖子内容
     */
    private String content;

    /**
     * 帖子图片数组，存储图片URL列表
     */
    @TableField(value = "images", typeHandler = ListJsonTypeHandler.class)
    private List<String> images;

    /**
     * 封面图片URL
     */
    @TableField("cover_image")
    private String coverImage;

    /**
     * 位置名称
     */
    @TableField("location_name")
    private String locationName;

    /**
     * 纬度
     */
    @TableField("location_latitude")
    private BigDecimal locationLatitude;

    /**
     * 经度
     */
    @TableField("location_longitude")
    private BigDecimal locationLongitude;

    /**
     * 详细地址
     */
    @TableField("location_address")
    private String locationAddress;

    /**
     * 点赞数
     */
    @TableField("like_count")
    private Integer likeCount;

    /**
     * 评论数
     */
    @TableField("comment_count")
    private Integer commentCount;

    /**
     * 分享数
     */
    @TableField("share_count")
    private Integer shareCount;

    /**
     * 浏览数
     */
    @TableField("view_count")
    private Integer viewCount;

    /**
     * 是否公开：0-私密，1-公开
     */
    @TableField("is_public")
    private Integer isPublic;

    /**
     * 状态：0-草稿，1-已发布，2-已删除
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
