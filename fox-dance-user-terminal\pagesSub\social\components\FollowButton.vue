<template>
  <u-button
    :type="isFollowed ? 'default' : 'primary'"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    :customStyle="buttonStyle"
    @click="handleFollow"
  >
    {{ isFollowed ? '已关注' : '关注' }}
  </u-button>
</template>

<script>
export default {
  name: 'FollowButton',
  props: {
    // 用户信息
    user: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 关注状态
    followed: {
      type: Boolean,
      default: false
    },
    // 按钮尺寸
    size: {
      type: String,
      default: 'mini',
      validator: (value) => ['large', 'normal', 'mini'].includes(value)
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 自定义样式
    customStyle: {
      type: [String, Object],
      default: ''
    },
    // 是否显示加载状态
    showLoading: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isFollowed: false,
      loading: false
    }
  },
  computed: {
    buttonStyle() {
      const baseStyle = {
        fontSize: this.getFontSize(),
        height: this.getHeight(),
        minWidth: this.getMinWidth(),
        borderRadius: this.getBorderRadius()
      }
      
      if (typeof this.customStyle === 'string') {
        return `${this.customStyle}; ${this.objectToStyle(baseStyle)}`
      } else if (typeof this.customStyle === 'object') {
        return { ...baseStyle, ...this.customStyle }
      }
      
      return baseStyle
    }
  },
  watch: {
    followed: {
      immediate: true,
      handler(newVal) {
        this.isFollowed = newVal
      }
    }
  },
  methods: {
    async handleFollow() {
      if (this.disabled || this.loading) return
      
      if (this.showLoading) {
        this.loading = true
      }
      
      try {
        // 触发关注/取消关注事件
        this.$emit('follow', {
          user: this.user,
          isFollowed: !this.isFollowed,
          originalStatus: this.isFollowed
        })
        
        // 切换状态
        this.isFollowed = !this.isFollowed
        
        // 显示提示
        const message = this.isFollowed ? '关注成功' : '取消关注'
        if (this.$u && this.$u.toast) {
          this.$u.toast(message)
        } else {
          uni.showToast({
            title: message,
            icon: this.isFollowed ? 'success' : 'none'
          })
        }
        
        // 触发状态变化事件
        this.$emit('change', {
          user: this.user,
          isFollowed: this.isFollowed
        })
        
      } catch (error) {
        console.error('关注操作失败:', error)
        
        // 显示错误提示
        const errorMessage = '操作失败，请重试'
        if (this.$u && this.$u.toast) {
          this.$u.toast(errorMessage)
        } else {
          uni.showToast({
            title: errorMessage,
            icon: 'none'
          })
        }
        
        // 触发错误事件
        this.$emit('error', {
          user: this.user,
          error: error
        })
      } finally {
        if (this.showLoading) {
          setTimeout(() => {
            this.loading = false
          }, 500) // 延迟500ms隐藏加载状态，提供更好的用户体验
        }
      }
    },
    
    getFontSize() {
      const sizeMap = {
        large: '32rpx',
        normal: '28rpx',
        mini: '24rpx'
      }
      return sizeMap[this.size] || sizeMap.mini
    },
    
    getHeight() {
      const heightMap = {
        large: '80rpx',
        normal: '64rpx',
        mini: '56rpx'
      }
      return heightMap[this.size] || heightMap.mini
    },
    
    getMinWidth() {
      const widthMap = {
        large: '120rpx',
        normal: '100rpx',
        mini: '80rpx'
      }
      return widthMap[this.size] || widthMap.mini
    },
    
    getBorderRadius() {
      const radiusMap = {
        large: '40rpx',
        normal: '32rpx',
        mini: '28rpx'
      }
      return radiusMap[this.size] || radiusMap.mini
    },
    
    objectToStyle(obj) {
      return Object.keys(obj).map(key => {
        const kebabKey = key.replace(/([A-Z])/g, '-$1').toLowerCase()
        return `${kebabKey}: ${obj[key]}`
      }).join('; ')
    },
    
    // 外部调用方法：手动设置关注状态
    setFollowStatus(status) {
      this.isFollowed = status
    },
    
    // 外部调用方法：获取当前关注状态
    getFollowStatus() {
      return this.isFollowed
    }
  }
}
</script>

<style lang="scss" scoped>
// 组件内部不需要额外样式，所有样式通过uview组件和customStyle处理
</style>
