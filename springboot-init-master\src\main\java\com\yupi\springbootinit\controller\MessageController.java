package com.yupi.springbootinit.controller;

import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.model.dto.message.MessageSendRequest;
import com.yupi.springbootinit.model.vo.ConversationVO;
import com.yupi.springbootinit.model.vo.MessageVO;
import com.yupi.springbootinit.service.MessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 私信系统接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/messages")
@Slf4j
@Api(tags = "私信系统接口")
public class MessageController {

    @Resource
    private MessageService messageService;

    /**
     * 发送消息
     */
    @PostMapping("/send")
    @ApiOperation(value = "发送消息")
    public BaseResponse<MessageVO> sendMessage(@RequestBody MessageSendRequest sendRequest,
                                              HttpServletRequest request) {
        if (sendRequest == null) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "请求参数不能为空");
        }

        if (sendRequest.getReceiverId() == null || sendRequest.getReceiverId() <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "接收者ID无效");
        }

        if (sendRequest.getContent() == null || sendRequest.getContent().trim().isEmpty()) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "消息内容不能为空");
        }

        if (sendRequest.getContent().length() > 1000) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "消息内容不能超过1000个字符");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            if (currentUserId.equals(sendRequest.getReceiverId())) {
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "不能给自己发送消息");
            }

            sendRequest.setSenderId(currentUserId);
            MessageVO message = messageService.sendMessage(sendRequest);

            if (message != null) {
                log.info("发送消息成功 - senderId: {}, receiverId: {}, messageId: {}", 
                        currentUserId, sendRequest.getReceiverId(), message.getId());
                return ResultUtils.success(message);
            } else {
                log.warn("发送消息失败 - senderId: {}, receiverId: {}", 
                        currentUserId, sendRequest.getReceiverId());
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "发送消息失败");
            }

        } catch (Exception e) {
            log.error("发送消息异常 - senderId: {}, receiverId: {}, error: {}", 
                    1L, sendRequest.getReceiverId(), e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "发送消息失败");
        }
    }

    /**
     * 获取会话列表
     */
    @GetMapping("/conversations")
    @ApiOperation(value = "获取会话列表")
    public BaseResponse<List<ConversationVO>> getConversations(@RequestParam(defaultValue = "1") Integer current,
                                                              @RequestParam(defaultValue = "20") Integer size,
                                                              HttpServletRequest request) {
        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            List<ConversationVO> conversations = messageService.getConversations(currentUserId, current, size);
            log.info("获取会话列表成功 - userId: {}, count: {}", currentUserId, conversations.size());
            return ResultUtils.success(conversations);

        } catch (Exception e) {
            log.error("获取会话列表异常 - userId: {}, error: {}", 1L, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取会话列表失败");
        }
    }

    /**
     * 获取会话消息
     */
    @GetMapping("/conversation/{userId}")
    @ApiOperation(value = "获取会话消息")
    public BaseResponse<List<MessageVO>> getConversationMessages(@PathVariable Long userId,
                                                                @RequestParam(defaultValue = "1") Integer current,
                                                                @RequestParam(defaultValue = "20") Integer size,
                                                                HttpServletRequest request) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID无效");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            List<MessageVO> messages = messageService.getConversationMessages(currentUserId, userId, current, size);
            log.info("获取会话消息成功 - currentUserId: {}, targetUserId: {}, count: {}", 
                    currentUserId, userId, messages.size());
            return ResultUtils.success(messages);

        } catch (Exception e) {
            log.error("获取会话消息异常 - currentUserId: {}, targetUserId: {}, error: {}", 
                    1L, userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取会话消息失败");
        }
    }

    /**
     * 标记消息已读
     */
    @PutMapping("/read")
    @ApiOperation(value = "标记消息已读")
    public BaseResponse<Boolean> markMessagesAsRead(@RequestParam(required = false) Long messageId,
                                                   @RequestParam(required = false) Long conversationUserId,
                                                   HttpServletRequest request) {
        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            boolean result;
            if (messageId != null) {
                // 标记单个消息已读
                result = messageService.markMessageAsRead(messageId, currentUserId);
                log.info("标记单个消息已读 - messageId: {}, userId: {}, result: {}", 
                        messageId, currentUserId, result);
            } else if (conversationUserId != null) {
                // 标记会话所有消息已读
                result = messageService.markConversationAsRead(currentUserId, conversationUserId);
                log.info("标记会话消息已读 - currentUserId: {}, conversationUserId: {}, result: {}", 
                        currentUserId, conversationUserId, result);
            } else {
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "请提供消息ID或会话用户ID");
            }

            if (result) {
                return ResultUtils.success(true);
            } else {
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "标记消息已读失败");
            }

        } catch (Exception e) {
            log.error("标记消息已读异常 - messageId: {}, conversationUserId: {}, error: {}", 
                    messageId, conversationUserId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "标记消息已读失败");
        }
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{messageId}")
    @ApiOperation(value = "删除消息")
    public BaseResponse<Boolean> deleteMessage(@PathVariable Long messageId, HttpServletRequest request) {
        if (messageId == null || messageId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "消息ID无效");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            boolean result = messageService.deleteMessage(messageId, currentUserId);
            if (result) {
                log.info("删除消息成功 - messageId: {}, userId: {}", messageId, currentUserId);
                return ResultUtils.success(true);
            } else {
                log.warn("删除消息失败 - messageId: {}, userId: {}", messageId, currentUserId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "删除消息失败");
            }

        } catch (Exception e) {
            log.error("删除消息异常 - messageId: {}, error: {}", messageId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "删除消息失败");
        }
    }

    /**
     * 删除会话
     */
    @DeleteMapping("/conversation/{userId}")
    @ApiOperation(value = "删除会话")
    public BaseResponse<Boolean> deleteConversation(@PathVariable Long userId, HttpServletRequest request) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID无效");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            boolean result = messageService.deleteConversation(currentUserId, userId);
            if (result) {
                log.info("删除会话成功 - currentUserId: {}, targetUserId: {}", currentUserId, userId);
                return ResultUtils.success(true);
            } else {
                log.warn("删除会话失败 - currentUserId: {}, targetUserId: {}", currentUserId, userId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "删除会话失败");
            }

        } catch (Exception e) {
            log.error("删除会话异常 - currentUserId: {}, targetUserId: {}, error: {}", 
                    1L, userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "删除会话失败");
        }
    }

    /**
     * 获取未读消息数
     */
    @GetMapping("/unread-count")
    @ApiOperation(value = "获取未读消息数")
    public BaseResponse<Map<String, Integer>> getUnreadCount(HttpServletRequest request) {
        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            Map<String, Integer> unreadCount = messageService.getUnreadCount(currentUserId);
            log.info("获取未读消息数成功 - userId: {}, unreadCount: {}", currentUserId, unreadCount);
            return ResultUtils.success(unreadCount);

        } catch (Exception e) {
            log.error("获取未读消息数异常 - userId: {}, error: {}", 1L, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取未读消息数失败");
        }
    }

    /**
     * 搜索消息
     */
    @GetMapping("/search")
    @ApiOperation(value = "搜索消息")
    public BaseResponse<List<MessageVO>> searchMessages(@RequestParam String keyword,
                                                       @RequestParam(defaultValue = "1") Integer current,
                                                       @RequestParam(defaultValue = "20") Integer size,
                                                       HttpServletRequest request) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "搜索关键词不能为空");
        }

        if (keyword.length() > 50) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "搜索关键词不能超过50个字符");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            List<MessageVO> messages = messageService.searchMessages(currentUserId, keyword.trim(), current, size);
            log.info("搜索消息成功 - userId: {}, keyword: {}, count: {}", 
                    currentUserId, keyword, messages.size());
            return ResultUtils.success(messages);

        } catch (Exception e) {
            log.error("搜索消息异常 - userId: {}, keyword: {}, error: {}", 
                    1L, keyword, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "搜索消息失败");
        }
    }
}
