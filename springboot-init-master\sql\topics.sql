-- 话题表
CREATE TABLE IF NOT EXISTS topics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '话题ID',
    title VARCHAR(100) NOT NULL COMMENT '话题标题',
    description TEXT COMMENT '话题描述',
    user_id BIGINT NOT NULL COMMENT '创建用户ID',
    comment_user_count INT NOT NULL DEFAULT 0 COMMENT '评论人数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除，0-未删除，1-已删除',
    INDEX idx_user(user_id),
    INDEX idx_create_time(create_time)
) COMMENT='话题表' COLLATE=utf8mb4_unicode_ci;
-- topics表添加话题封面图
    ALTER TABLE topics ADD cover_image VARCHAR(255) DEFAULT NULL COMMENT '话题封面图URL';
-- topics表添加话题图片数组
    ALTER TABLE topics ADD topic_images JSON DEFAULT NULL COMMENT '话题图片数组';
