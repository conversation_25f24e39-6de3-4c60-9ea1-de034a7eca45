package com.yupi.springbootinit.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yupi.springbootinit.annotation.AuthCheck;
import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.constant.UserConstant;
import com.yupi.springbootinit.exception.ThrowUtils;
import com.yupi.springbootinit.model.dto.post.PostCreateDTO;
import com.yupi.springbootinit.model.dto.post.PostQueryDTO;
import com.yupi.springbootinit.model.dto.post.PostUpdateDTO;
import com.yupi.springbootinit.model.entity.User;
import com.yupi.springbootinit.model.vo.PostVO;
import com.yupi.springbootinit.service.PostService;
import com.yupi.springbootinit.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 帖子接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/post")
@Slf4j
@Api(tags = "帖子管理")
public class PostController {

    @Resource
    private PostService postService;

    @Resource
    private UserService userService;

    /**
     * 创建帖子
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建帖子")
    public BaseResponse<Long> createPost(@Valid @RequestBody PostCreateDTO postCreateDTO, HttpServletRequest request) {
        User loginUser = userService.getLoginUser(request);
        Long postId = postService.createPost(postCreateDTO, loginUser.getId());
        return ResultUtils.success(postId);
    }

    /**
     * 更新帖子
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新帖子")
    public BaseResponse<Boolean> updatePost(@Valid @RequestBody PostUpdateDTO postUpdateDTO, HttpServletRequest request) {
        User loginUser = userService.getLoginUser(request);
        Boolean result = postService.updatePost(postUpdateDTO, loginUser.getId());
        return ResultUtils.success(result);
    }

    /**
     * 删除帖子
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除帖子")
    public BaseResponse<Boolean> deletePost(@RequestParam Long postId, HttpServletRequest request) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        User loginUser = userService.getLoginUser(request);
        Boolean result = postService.deletePost(postId, loginUser.getId());
        return ResultUtils.success(result);
    }

    /**
     * 根据ID获取帖子详情
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取帖子详情")
    public BaseResponse<PostVO> getPostDetail(@RequestParam Long postId, HttpServletRequest request) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        User loginUser = userService.getLoginUserPermitNull(request);
        Long currentUserId = loginUser != null ? loginUser.getId() : null;
        PostVO postVO = postService.getPostDetail(postId, currentUserId);
        
        // 增加浏览数
        if (currentUserId != null) {
            postService.incrementViewCount(postId, currentUserId);
        }
        
        return ResultUtils.success(postVO);
    }

    /**
     * 分页查询帖子列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询帖子列表")
    public BaseResponse<Page<PostVO>> getPostPage(@RequestBody PostQueryDTO postQueryDTO, HttpServletRequest request) {
        log.info("request:{}", request.getHeaderNames());
        User loginUser = userService.getLoginUserPermitNull(request);
        log.info("当前用户loginUser:{}", loginUser);
        Long currentUserId = loginUser != null ? loginUser.getId() : null;
        log.info("当前用户currentUserId:{}", currentUserId);
        Page<PostVO> postPage = postService.getPostPage(postQueryDTO, currentUserId);
        return ResultUtils.success(postPage);
    }

    /**
     * 获取用户的帖子列表
     */
    @GetMapping("/user")
    @ApiOperation(value = "获取用户的帖子列表")
    public BaseResponse<Page<PostVO>> getUserPosts(@RequestParam Long userId,
                                                  @RequestParam(defaultValue = "1") Integer current,
                                                  @RequestParam(defaultValue = "10") Integer pageSize,
                                                  @RequestParam(required = false) Integer status,
                                                  HttpServletRequest request) {
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR);
        User loginUser = userService.getLoginUserPermitNull(request);
        Long currentUserId = loginUser != null ? loginUser.getId() : null;
        Page<PostVO> postPage = postService.getUserPosts(userId, currentUserId, current, pageSize, status);
        return ResultUtils.success(postPage);
    }

    /**
     * 获取附近的帖子
     */
    @GetMapping("/nearby")
    @ApiOperation(value = "获取附近的帖子")
    public BaseResponse<Page<PostVO>> getNearbyPosts(@RequestParam BigDecimal latitude,
                                                    @RequestParam BigDecimal longitude,
                                                    @RequestParam(defaultValue = "10.0") Double radius,
                                                    @RequestParam(defaultValue = "1") Integer current,
                                                    @RequestParam(defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest request) {
        ThrowUtils.throwIf(latitude == null || longitude == null, ErrorCode.PARAMS_ERROR);
        User loginUser = userService.getLoginUserPermitNull(request);
        Long currentUserId = loginUser != null ? loginUser.getId() : null;
        Page<PostVO> postPage = postService.getNearbyPosts(latitude, longitude, radius, currentUserId, current, pageSize);
        return ResultUtils.success(postPage);
    }

    /**
     * 获取热门帖子
     */
    @GetMapping("/hot")
    @ApiOperation(value = "获取热门帖子")
    public BaseResponse<Page<PostVO>> getHotPosts(@RequestParam(defaultValue = "1") Integer current,
                                                 @RequestParam(defaultValue = "10") Integer pageSize,
                                                 @RequestParam(defaultValue = "7") Integer days,
                                                 HttpServletRequest request) {
        User loginUser = userService.getLoginUserPermitNull(request);
        Long currentUserId = loginUser != null ? loginUser.getId() : null;
        Page<PostVO> postPage = postService.getHotPosts(currentUserId, current, pageSize, days);
        return ResultUtils.success(postPage);
    }

    /**
     * 获取关注用户的帖子
     */
    @GetMapping("/following")
    @ApiOperation(value = "获取关注用户的帖子")
    public BaseResponse<Page<PostVO>> getFollowingPosts(@RequestParam(defaultValue = "1") Integer current,
                                                       @RequestParam(defaultValue = "10") Integer pageSize,
                                                       HttpServletRequest request) {
        User loginUser = userService.getLoginUser(request);
        Page<PostVO> postPage = postService.getFollowingPosts(loginUser.getId(), current, pageSize);
        return ResultUtils.success(postPage);
    }

    /**
     * 根据标签获取帖子
     */
    @PostMapping("/tags")
    @ApiOperation(value = "根据标签获取帖子")
    public BaseResponse<Page<PostVO>> getPostsByTags(@RequestBody List<String> tagNames,
                                                    @RequestParam(defaultValue = "1") Integer current,
                                                    @RequestParam(defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest request) {
        ThrowUtils.throwIf(tagNames == null || tagNames.isEmpty(), ErrorCode.PARAMS_ERROR);
        User loginUser = userService.getLoginUserPermitNull(request);
        Long currentUserId = loginUser != null ? loginUser.getId() : null;
        Page<PostVO> postPage = postService.getPostsByTags(tagNames, currentUserId, current, pageSize);
        return ResultUtils.success(postPage);
    }

    /**
     * 点赞帖子
     */
    @PostMapping("/like")
    @ApiOperation(value = "点赞帖子")
    public BaseResponse<Boolean> likePost(@RequestParam Long postId, HttpServletRequest request) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        User loginUser = userService.getLoginUser(request);
        Boolean result = postService.likePost(postId, loginUser.getId());
        return ResultUtils.success(result);
    }

    /**
     * 取消点赞帖子
     */
    @PostMapping("/unlike")
    @ApiOperation(value = "取消点赞帖子")
    public BaseResponse<Boolean> unlikePost(@RequestParam Long postId, HttpServletRequest request) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        User loginUser = userService.getLoginUser(request);
        Boolean result = postService.unlikePost(postId, loginUser.getId());
        return ResultUtils.success(result);
    }

    /**
     * 收藏帖子
     */
    @PostMapping("/favorite")
    @ApiOperation(value = "收藏帖子")
    public BaseResponse<Boolean> favoritePost(@RequestParam Long postId, HttpServletRequest request) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        User loginUser = userService.getLoginUser(request);
        Boolean result = postService.favoritePost(postId, loginUser.getId());
        return ResultUtils.success(result);
    }

    /**
     * 取消收藏帖子
     */
    @PostMapping("/unfavorite")
    @ApiOperation(value = "取消收藏帖子")
    public BaseResponse<Boolean> unfavoritePost(@RequestParam Long postId, HttpServletRequest request) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        User loginUser = userService.getLoginUser(request);
        Boolean result = postService.unfavoritePost(postId, loginUser.getId());
        return ResultUtils.success(result);
    }

    /**
     * 分享帖子
     */
    @PostMapping("/share")
    @ApiOperation(value = "分享帖子")
    public BaseResponse<Boolean> sharePost(@RequestParam Long postId,
                                          @RequestParam Integer shareType,
                                          HttpServletRequest request) {
        ThrowUtils.throwIf(postId == null || postId <= 0 || shareType == null, ErrorCode.PARAMS_ERROR);
        User loginUser = userService.getLoginUser(request);
        Boolean result = postService.sharePost(postId, shareType, loginUser.getId());
        return ResultUtils.success(result);
    }

    /**
     * 获取用户收藏的帖子
     */
    @GetMapping("/favorites")
    @ApiOperation(value = "获取用户收藏的帖子")
    public BaseResponse<Page<PostVO>> getFavoritePosts(@RequestParam(defaultValue = "1") Integer current,
                                                      @RequestParam(defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest request) {
        User loginUser = userService.getLoginUser(request);
        Page<PostVO> postPage = postService.getFavoritePosts(loginUser.getId(), current, pageSize);
        return ResultUtils.success(postPage);
    }

    /**
     * 管理员删除帖子
     */
    @PostMapping("/admin/delete")
    @AuthCheck(mustRole = UserConstant.ADMIN_ROLE)
    @ApiOperation(value = "管理员删除帖子")
    public BaseResponse<Boolean> adminDeletePost(@RequestParam Long postId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        Boolean result = postService.removeById(postId);
        return ResultUtils.success(result);
    }
}
