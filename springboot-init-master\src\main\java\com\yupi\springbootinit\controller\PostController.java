package com.yupi.springbootinit.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yupi.springbootinit.annotation.AuthCheck;
import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.constant.UserConstant;
import com.yupi.springbootinit.exception.BusinessException;
import com.yupi.springbootinit.exception.ThrowUtils;
import com.yupi.springbootinit.model.dto.post.PostCreateDTO;
import com.yupi.springbootinit.model.dto.post.PostQueryDTO;
import com.yupi.springbootinit.model.dto.post.PostUpdateDTO;

import com.yupi.springbootinit.model.vo.PostVO;
import com.yupi.springbootinit.service.PostService;
import com.yupi.springbootinit.service.UserService;
import com.yupi.springbootinit.service.BaUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 帖子接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/post")
@Slf4j
@Api(tags = "帖子管理")
public class PostController {

    @Resource
    private PostService postService;

    @Resource
    private UserService userService;

    @Resource
    private BaUserService baUserService;

    /**
     * 创建帖子
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建帖子")
    public BaseResponse<Long> createPost(@Valid @RequestBody PostCreateDTO postCreateDTO) {
        // 验证用户ID
        ThrowUtils.throwIf(postCreateDTO.getUserId() == null || postCreateDTO.getUserId() <= 0,
                          ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(postCreateDTO.getUserId()) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Long postId = postService.createPost(postCreateDTO, postCreateDTO.getUserId());
        return ResultUtils.success(postId);
    }

    /**
     * 更新帖子
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新帖子")
    public BaseResponse<Boolean> updatePost(@Valid @RequestBody PostUpdateDTO postUpdateDTO) {
        // 验证用户ID
        ThrowUtils.throwIf(postUpdateDTO.getUserId() == null || postUpdateDTO.getUserId() <= 0,
                          ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(postUpdateDTO.getUserId()) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postService.updatePost(postUpdateDTO, postUpdateDTO.getUserId());
        return ResultUtils.success(result);
    }

    /**
     * 删除帖子
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除帖子")
    public BaseResponse<Boolean> deletePost(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postService.deletePost(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 根据ID获取帖子详情
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取帖子详情")
    public BaseResponse<PostVO> getPostDetail(@RequestParam Long postId,
                                             @RequestParam(required = false) Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");

        // 如果传入了userId，验证用户是否存在
        if (userId != null && userId > 0) {
            if (baUserService.getById(userId) == null) {
                throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
            }
        }

        PostVO postVO = postService.getPostDetail(postId, userId);

        // 增加浏览数（仅登录用户）
        if (userId != null && userId > 0) {
            postService.incrementViewCount(postId, userId);
        }

        return ResultUtils.success(postVO);
    }

    /**
     * 分页查询帖子列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询帖子列表")
    public BaseResponse<Page<PostVO>> getPostPage(@RequestBody PostQueryDTO postQueryDTO) {
        // userId在PostQueryDTO中是可选的，允许未登录用户查看帖子列表
        Long currentUserId = postQueryDTO.getUserId();

        // 如果传入了userId，验证用户是否存在
        if (currentUserId != null && currentUserId > 0) {
            if (baUserService.getById(currentUserId) == null) {
                throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
            }
        }

        log.info("当前用户currentUserId:{}", currentUserId);
        Page<PostVO> postPage = postService.getPostPage(postQueryDTO, currentUserId);
        return ResultUtils.success(postPage);
    }

    /**
     * 获取用户的帖子列表
     */
    @GetMapping("/user")
    @ApiOperation(value = "获取用户的帖子列表")
    public BaseResponse<Page<PostVO>> getUserPosts(@RequestParam Long userId,
                                                  @RequestParam(defaultValue = "1") Integer current,
                                                  @RequestParam(defaultValue = "10") Integer pageSize,
                                                  @RequestParam(required = false) Integer status,
                                                  @RequestParam(required = false) Long currentUserId) {
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 如果传入了currentUserId，验证用户是否存在
        if (currentUserId != null && currentUserId > 0) {
            if (baUserService.getById(currentUserId) == null) {
                throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "当前用户不存在");
            }
        }

        Page<PostVO> postPage = postService.getUserPosts(userId, currentUserId, current, pageSize, status);
        return ResultUtils.success(postPage);
    }

    /**
     * 获取附近的帖子
     */
    @GetMapping("/nearby")
    @ApiOperation(value = "获取附近的帖子")
    public BaseResponse<Page<PostVO>> getNearbyPosts(@RequestParam BigDecimal latitude,
                                                    @RequestParam BigDecimal longitude,
                                                    @RequestParam(defaultValue = "10.0") Double radius,
                                                    @RequestParam(defaultValue = "1") Integer current,
                                                    @RequestParam(defaultValue = "10") Integer pageSize,
                                                    @RequestParam(required = false) Long userId) {
        ThrowUtils.throwIf(latitude == null || longitude == null, ErrorCode.PARAMS_ERROR, "经纬度不能为空");

        // 如果传入了userId，验证用户是否存在
        if (userId != null && userId > 0) {
            if (baUserService.getById(userId) == null) {
                throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
            }
        }

        Page<PostVO> postPage = postService.getNearbyPosts(latitude, longitude, radius, userId, current, pageSize);
        return ResultUtils.success(postPage);
    }

    /**
     * 获取热门帖子
     */
    @GetMapping("/hot")
    @ApiOperation(value = "获取热门帖子")
    public BaseResponse<Page<PostVO>> getHotPosts(@RequestParam(defaultValue = "1") Integer current,
                                                 @RequestParam(defaultValue = "10") Integer pageSize,
                                                 @RequestParam(defaultValue = "7") Integer days,
                                                 @RequestParam(required = false) Long userId) {
        // 如果传入了userId，验证用户是否存在
        if (userId != null && userId > 0) {
            if (baUserService.getById(userId) == null) {
                throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
            }
        }

        Page<PostVO> postPage = postService.getHotPosts(userId, current, pageSize, days);
        return ResultUtils.success(postPage);
    }

    /**
     * 获取关注用户的帖子
     */
    @GetMapping("/following")
    @ApiOperation(value = "获取关注用户的帖子")
    public BaseResponse<Page<PostVO>> getFollowingPosts(@RequestParam(defaultValue = "1") Integer current,
                                                       @RequestParam(defaultValue = "10") Integer pageSize,
                                                       @RequestParam Long userId) {
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Page<PostVO> postPage = postService.getFollowingPosts(userId, current, pageSize);
        return ResultUtils.success(postPage);
    }

    /**
     * 根据标签获取帖子
     */
    @PostMapping("/tags")
    @ApiOperation(value = "根据标签获取帖子")
    public BaseResponse<Page<PostVO>> getPostsByTags(@RequestBody List<String> tagNames,
                                                    @RequestParam(defaultValue = "1") Integer current,
                                                    @RequestParam(defaultValue = "10") Integer pageSize,
                                                    @RequestParam(required = false) Long userId) {
        ThrowUtils.throwIf(tagNames == null || tagNames.isEmpty(), ErrorCode.PARAMS_ERROR, "标签列表不能为空");

        // 如果传入了userId，验证用户是否存在
        if (userId != null && userId > 0) {
            if (baUserService.getById(userId) == null) {
                throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
            }
        }

        Page<PostVO> postPage = postService.getPostsByTags(tagNames, userId, current, pageSize);
        return ResultUtils.success(postPage);
    }

    /**
     * 点赞帖子
     */
    @PostMapping("/like")
    @ApiOperation(value = "点赞帖子")
    public BaseResponse<Boolean> likePost(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postService.likePost(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 取消点赞帖子
     */
    @PostMapping("/unlike")
    @ApiOperation(value = "取消点赞帖子")
    public BaseResponse<Boolean> unlikePost(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postService.unlikePost(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 收藏帖子
     */
    @PostMapping("/favorite")
    @ApiOperation(value = "收藏帖子")
    public BaseResponse<Boolean> favoritePost(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postService.favoritePost(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 取消收藏帖子
     */
    @PostMapping("/unfavorite")
    @ApiOperation(value = "取消收藏帖子")
    public BaseResponse<Boolean> unfavoritePost(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postService.unfavoritePost(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 分享帖子
     */
    @PostMapping("/share")
    @ApiOperation(value = "分享帖子")
    public BaseResponse<Boolean> sharePost(@RequestParam Long postId,
                                          @RequestParam Integer shareType,
                                          @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(shareType == null, ErrorCode.PARAMS_ERROR, "分享类型无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postService.sharePost(postId, shareType, userId);
        return ResultUtils.success(result);
    }

    /**
     * 获取用户收藏的帖子
     */
    @GetMapping("/favorites")
    @ApiOperation(value = "获取用户收藏的帖子")
    public BaseResponse<Page<PostVO>> getFavoritePosts(@RequestParam(defaultValue = "1") Integer current,
                                                      @RequestParam(defaultValue = "10") Integer pageSize,
                                                      @RequestParam Long userId) {
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Page<PostVO> postPage = postService.getFavoritePosts(userId, current, pageSize);
        return ResultUtils.success(postPage);
    }

    /**
     * 管理员删除帖子
     */
    @PostMapping("/admin/delete")
    @AuthCheck(mustRole = UserConstant.ADMIN_ROLE)
    @ApiOperation(value = "管理员删除帖子")
    public BaseResponse<Boolean> adminDeletePost(@RequestParam Long postId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        Boolean result = postService.removeById(postId);
        return ResultUtils.success(result);
    }
}
