package com.yupi.springbootinit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yupi.springbootinit.mapper.MessageMapper;
import com.yupi.springbootinit.model.dto.message.MessageSendRequest;
import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.model.entity.Message;
import com.yupi.springbootinit.model.vo.ConversationVO;
import com.yupi.springbootinit.model.vo.MessageVO;
import com.yupi.springbootinit.service.BaUserService;
import com.yupi.springbootinit.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 私信消息服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
@Slf4j
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements MessageService {

    @Resource
    private BaUserService baUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MessageVO sendMessage(MessageSendRequest sendRequest) {
        try {
            // 检查发送权限
            if (!checkSendPermission(sendRequest.getSenderId(), sendRequest.getReceiverId())) {
                log.warn("无权限发送消息 - senderId: {}, receiverId: {}", 
                        sendRequest.getSenderId(), sendRequest.getReceiverId());
                return null;
            }

            // 获取或创建会话ID
            Long conversationId = getOrCreateConversationId(sendRequest.getSenderId(), sendRequest.getReceiverId());

            // 创建消息
            Message message = new Message();
            message.setConversationId(conversationId);
            message.setSenderId(sendRequest.getSenderId());
            message.setReceiverId(sendRequest.getReceiverId());
            message.setMessageType(sendRequest.getMessageType() != null ? sendRequest.getMessageType() : 1);
            message.setContent(sendRequest.getContent());
            message.setMediaUrl(sendRequest.getMediaUrl());
            message.setMediaSize(sendRequest.getMediaSize());
            message.setMediaDuration(sendRequest.getMediaDuration());
            message.setReplyToMessageId(sendRequest.getReplyToMessageId());
            message.setIsRead(0);
            message.setStatus(2); // 已发送
            message.setCreateTime(new Date());
            message.setUpdateTime(new Date());
            message.setIsDelete(0);

            boolean result = this.save(message);
            if (result) {
                log.info("发送消息成功 - messageId: {}, senderId: {}, receiverId: {}", 
                        message.getId(), sendRequest.getSenderId(), sendRequest.getReceiverId());
                return convertToMessageVO(message, sendRequest.getSenderId());
            } else {
                log.error("发送消息失败 - senderId: {}, receiverId: {}", 
                        sendRequest.getSenderId(), sendRequest.getReceiverId());
                return null;
            }

        } catch (Exception e) {
            log.error("发送消息异常 - senderId: {}, receiverId: {}, error: {}", 
                    sendRequest.getSenderId(), sendRequest.getReceiverId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<ConversationVO> getConversations(Long userId, Integer current, Integer size) {
        try {
            // TODO: 实际应该查询conversations表
            // 这里返回模拟数据
            List<ConversationVO> conversations = new ArrayList<>();
            
            // 模拟会话数据
            for (int i = 1; i <= Math.min(size, 5); i++) {
                ConversationVO conversation = new ConversationVO();
                conversation.setId((long) i);
                conversation.setOtherUserId((long) (i + 1));
                conversation.setOtherUserNickname("用户" + (i + 1));
                conversation.setOtherUserAvatar("https://picsum.photos/100/100?random=" + (i + 1));
                conversation.setLastMessageId((long) (i * 10));
                conversation.setLastMessageContent("这是最后一条消息内容 " + i);
                conversation.setLastMessageTime(new Date());
                conversation.setUnreadCount(i);
                conversation.setCreateTime(new Date());
                conversation.setTimeText(formatTimeText(new Date()));
                conversations.add(conversation);
            }
            
            return conversations;

        } catch (Exception e) {
            log.error("获取会话列表异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MessageVO> getConversationMessages(Long currentUserId, Long otherUserId, Integer current, Integer size) {
        try {
            // 查询两个用户之间的消息
            QueryWrapper<Message> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(wrapper -> 
                wrapper.and(w -> w.eq("sender_id", currentUserId).eq("receiver_id", otherUserId))
                       .or(w -> w.eq("sender_id", otherUserId).eq("receiver_id", currentUserId))
            );
            queryWrapper.orderByDesc("create_time");

            Page<Message> page = new Page<>(current, size);
            Page<Message> messagePage = this.page(page, queryWrapper);

            return messagePage.getRecords().stream()
                    .map(message -> convertToMessageVO(message, currentUserId))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取会话消息异常 - currentUserId: {}, otherUserId: {}, error: {}", 
                    currentUserId, otherUserId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markMessageAsRead(Long messageId, Long userId) {
        try {
            UpdateWrapper<Message> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", messageId);
            updateWrapper.eq("receiver_id", userId);
            updateWrapper.eq("is_read", 0);
            updateWrapper.set("is_read", 1);
            updateWrapper.set("read_time", new Date());
            updateWrapper.set("status", 4); // 已读

            boolean result = this.update(updateWrapper);
            log.info("标记消息已读 - messageId: {}, userId: {}, result: {}", messageId, userId, result);
            return result;

        } catch (Exception e) {
            log.error("标记消息已读异常 - messageId: {}, userId: {}, error: {}", 
                    messageId, userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markConversationAsRead(Long currentUserId, Long otherUserId) {
        try {
            UpdateWrapper<Message> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("sender_id", otherUserId);
            updateWrapper.eq("receiver_id", currentUserId);
            updateWrapper.eq("is_read", 0);
            updateWrapper.set("is_read", 1);
            updateWrapper.set("read_time", new Date());
            updateWrapper.set("status", 4); // 已读

            boolean result = this.update(updateWrapper);
            log.info("标记会话消息已读 - currentUserId: {}, otherUserId: {}, result: {}", 
                    currentUserId, otherUserId, result);
            return result;

        } catch (Exception e) {
            log.error("标记会话消息已读异常 - currentUserId: {}, otherUserId: {}, error: {}", 
                    currentUserId, otherUserId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMessage(Long messageId, Long userId) {
        try {
            // 软删除：只有发送者可以删除消息
            UpdateWrapper<Message> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", messageId);
            updateWrapper.eq("sender_id", userId);
            updateWrapper.set("is_delete", 1);

            boolean result = this.update(updateWrapper);
            log.info("删除消息 - messageId: {}, userId: {}, result: {}", messageId, userId, result);
            return result;

        } catch (Exception e) {
            log.error("删除消息异常 - messageId: {}, userId: {}, error: {}", 
                    messageId, userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteConversation(Long currentUserId, Long otherUserId) {
        try {
            // 软删除：删除当前用户参与的所有消息
            UpdateWrapper<Message> updateWrapper = new UpdateWrapper<>();
            updateWrapper.and(wrapper -> 
                wrapper.and(w -> w.eq("sender_id", currentUserId).eq("receiver_id", otherUserId))
                       .or(w -> w.eq("sender_id", otherUserId).eq("receiver_id", currentUserId))
            );
            updateWrapper.set("is_delete", 1);

            boolean result = this.update(updateWrapper);
            log.info("删除会话 - currentUserId: {}, otherUserId: {}, result: {}", 
                    currentUserId, otherUserId, result);
            return result;

        } catch (Exception e) {
            log.error("删除会话异常 - currentUserId: {}, otherUserId: {}, error: {}", 
                    currentUserId, otherUserId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Integer> getUnreadCount(Long userId) {
        Map<String, Integer> unreadCount = new HashMap<>();
        
        try {
            QueryWrapper<Message> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("receiver_id", userId);
            queryWrapper.eq("is_read", 0);
            
            int totalUnread = Math.toIntExact(this.count(queryWrapper));
            unreadCount.put("total", totalUnread);
            unreadCount.put("messages", totalUnread);

        } catch (Exception e) {
            log.error("获取未读消息数异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            unreadCount.put("total", 0);
            unreadCount.put("messages", 0);
        }
        
        return unreadCount;
    }

    @Override
    public List<MessageVO> searchMessages(Long userId, String keyword, Integer current, Integer size) {
        try {
            QueryWrapper<Message> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(wrapper -> 
                wrapper.eq("sender_id", userId).or().eq("receiver_id", userId)
            );
            queryWrapper.like("content", keyword);
            queryWrapper.orderByDesc("create_time");

            Page<Message> page = new Page<>(current, size);
            Page<Message> messagePage = this.page(page, queryWrapper);

            return messagePage.getRecords().stream()
                    .map(message -> convertToMessageVO(message, userId))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("搜索消息异常 - userId: {}, keyword: {}, error: {}", 
                    userId, keyword, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Long getOrCreateConversationId(Long userId1, Long userId2) {
        try {
            // TODO: 实际应该查询或创建conversations表记录
            // 这里返回模拟的会话ID
            return Math.min(userId1, userId2) * 1000 + Math.max(userId1, userId2);

        } catch (Exception e) {
            log.error("获取或创建会话ID异常 - userId1: {}, userId2: {}, error: {}", 
                    userId1, userId2, e.getMessage(), e);
            return 1L;
        }
    }

    @Override
    public Integer getConversationUnreadCount(Long conversationId, Long userId) {
        try {
            QueryWrapper<Message> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("conversation_id", conversationId);
            queryWrapper.eq("receiver_id", userId);
            queryWrapper.eq("is_read", 0);
            
            return Math.toIntExact(this.count(queryWrapper));

        } catch (Exception e) {
            log.error("获取会话未读消息数异常 - conversationId: {}, userId: {}, error: {}", 
                    conversationId, userId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchMarkAsRead(List<Long> messageIds, Long userId) {
        try {
            UpdateWrapper<Message> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", messageIds);
            updateWrapper.eq("receiver_id", userId);
            updateWrapper.eq("is_read", 0);
            updateWrapper.set("is_read", 1);
            updateWrapper.set("read_time", new Date());
            updateWrapper.set("status", 4);

            boolean result = this.update(updateWrapper);
            log.info("批量标记消息已读 - messageIds: {}, userId: {}, result: {}", 
                    messageIds.size(), userId, result);
            return result;

        } catch (Exception e) {
            log.error("批量标记消息已读异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<ConversationVO> getRecentContacts(Long userId, Integer limit) {
        try {
            return getConversations(userId, 1, limit);

        } catch (Exception e) {
            log.error("获取最近联系人异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean checkSendPermission(Long senderId, Long receiverId) {
        try {
            // 检查接收者是否存在
            BaUser receiver = baUserService.getById(receiverId);
            if (receiver == null) {
                log.warn("接收者不存在 - receiverId: {}", receiverId);
                return false;
            }

            // TODO: 可以添加更多权限检查逻辑
            // 例如：黑名单检查、关注关系检查等

            return true;

        } catch (Exception e) {
            log.error("检查发送权限异常 - senderId: {}, receiverId: {}, error: {}", 
                    senderId, receiverId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Integer> getMessageStats(Long userId) {
        Map<String, Integer> stats = new HashMap<>();
        
        try {
            // 发送消息数
            QueryWrapper<Message> sentWrapper = new QueryWrapper<>();
            sentWrapper.eq("sender_id", userId);
            int sentCount = Math.toIntExact(this.count(sentWrapper));
            stats.put("sentCount", sentCount);

            // 接收消息数
            QueryWrapper<Message> receivedWrapper = new QueryWrapper<>();
            receivedWrapper.eq("receiver_id", userId);
            int receivedCount = Math.toIntExact(this.count(receivedWrapper));
            stats.put("receivedCount", receivedCount);

            // 未读消息数
            QueryWrapper<Message> unreadWrapper = new QueryWrapper<>();
            unreadWrapper.eq("receiver_id", userId);
            unreadWrapper.eq("is_read", 0);
            int unreadCount = Math.toIntExact(this.count(unreadWrapper));
            stats.put("unreadCount", unreadCount);

        } catch (Exception e) {
            log.error("获取消息统计异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            stats.put("sentCount", 0);
            stats.put("receivedCount", 0);
            stats.put("unreadCount", 0);
        }
        
        return stats;
    }

    /**
     * 转换为MessageVO对象
     */
    private MessageVO convertToMessageVO(Message message, Long currentUserId) {
        try {
            MessageVO vo = new MessageVO();
            vo.setId(message.getId());
            vo.setConversationId(message.getConversationId());
            vo.setSenderId(message.getSenderId());
            vo.setReceiverId(message.getReceiverId());
            vo.setMessageType(message.getMessageType());
            vo.setContent(message.getContent());
            vo.setMediaUrl(message.getMediaUrl());
            vo.setIsRead(message.getIsRead());
            vo.setReadTime(message.getReadTime());
            vo.setCreateTime(message.getCreateTime());

            // 设置发送者信息
            BaUser sender = baUserService.getById(message.getSenderId());
            if (sender != null) {
                vo.setSenderNickname(sender.getNickname());
                vo.setSenderAvatar(sender.getAvatar());
            }

            // 设置时间显示文本
            vo.setTimeText(formatTimeText(message.getCreateTime()));

            // 判断是否为当前用户发送的消息
            vo.setIsSent(currentUserId.equals(message.getSenderId()));

            return vo;

        } catch (Exception e) {
            log.error("转换MessageVO异常 - messageId: {}, error: {}", message.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 格式化时间显示文本
     */
    private String formatTimeText(Date createTime) {
        if (createTime == null) {
            return "";
        }

        long diff = System.currentTimeMillis() - createTime.getTime();
        long seconds = diff / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (seconds < 60) {
            return "刚刚";
        } else if (minutes < 60) {
            return minutes + "分钟前";
        } else if (hours < 24) {
            return hours + "小时前";
        } else if (days < 7) {
            return days + "天前";
        } else {
            SimpleDateFormat sdf = new SimpleDateFormat("MM-dd HH:mm");
            return sdf.format(createTime);
        }
    }
}
