<template>
	<view class="userReport" v-if="loding">
		
		<u-navbar back-icon-color="#fff" back-icon-size="42" title="用户报告" background="none" :border-bottom="false" title-color="#fff" title-size="32">
		</u-navbar>
		
		<view class="yueb years_yueb">
			<swiper class="swiper" :current="swiperIndex" :vertical="true" @change="swiperChange" @animationfinish="swiperEnd">
				<swiper-item>
					<view class="swiper-item years_one">
						<view :style="'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'"></view>
						<image :src="imgbaseUrlOss + '/userreport/icon8.png'" mode="aspectFill" class="years_one_bj"></image>
						<view class="years_one_a animate__animated animate__bounceInDown" v-if="configDate.oneAni1">{{currentYear}}*</view>
						<view class="years_one_b animate__animated animate__bounceInDown" v-if="configDate.oneAni2">年度报告</view>
						<view class="years_one_c animate__animated animate__bounceInDown" @click="swiperIndex = 1" v-if="configDate.oneAni3">回顾心动时刻 →</view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item years_two">
						<view :style="'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'"></view>
						<image :src="imgbaseUrlOss + '/userreport/icon9.png'" mode="aspectFill" class="years_one_bj"></image>
						<view class="years_two" v-if="userReport.appointment_num*1 > 0">
							<view class="years_two_a animate__animated animate__lightSpeedInLeft" v-if="configDate.twoAni0">今年你一共约课</view>
							<view class="years_two_b animate__animated animate__lightSpeedInLeft" v-if="configDate.twoAni1" style="margin:10rpx 0;"><text>{{userReport.appointment_num*1}}</text>次</view>
							<view class="years_two_b animate__animated animate__lightSpeedInLeft" v-if="configDate.twoAni2"><text>{{userReport.total_time*1}}</text>分钟</view>
							<view class="years_two_d animate__animated animate__lightSpeedInLeft" v-if="configDate.twoAni3">最近一次你上的课程是</view>
							<view class="years_two_e animate__animated animate__lightSpeedInLeft" v-if="configDate.twoAni4">{{userReport.last_course.teacher.name}}的《{{userReport.last_course.name}}》</view>
						</view>
						<view class="years_two" v-else>
							<view class="years_two_f animate__animated animate__lightSpeedInLeft" v-if="configDate.twoAni0">今年你约课的次数为<text>0</text></view>
							<view class="years_two_f animate__animated animate__lightSpeedInLeft" v-if="configDate.twoAni1">每个小任务都是通往目标的阶梯，别忘了拾级而上，别让它孤单地留在原地。</view>
						</view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item years_thr">
						<view :style="'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'"></view>
						<image :src="imgbaseUrlOss + '/userreport/icon10.png'" mode="aspectFill" class="years_one_bj"></image>
						<view class="years_thr_a">
							<text class="animate__animated animate__rotateInDownLeft" v-if="configDate.thrAni1">一堂</text>
							<text class="animate__animated animate__rotateInDownLeft" v-if="configDate.thrAni1">被遗忘的课程</text>
						</view>
						<view class="years_thr_b" v-if="userReport.appointment_num*1 > 0">
							<view class="years_thr_b_a animate__animated animate__rotateInDownLeft" v-if="configDate.thrAni2">{{userReport.forget_teacher}}的</view>
							<view class="years_thr_b_b animate__animated animate__rotateInDownLeft" v-if="configDate.thrAni3">《{{userReport.course_name}}》</view>
							<view class="years_thr_b_c animate__animated animate__rotateInDownLeft" v-if="configDate.thrAni4">曾经是你的挚爱</view>
							<view class="years_thr_b_d animate__animated animate__rotateInDownLeft" v-if="configDate.thrAni5">但是已经<text>{{userReport.days}}</text>天没有练习它了</view>
						</view>
						<view class="years_thr_f" v-else>
							<view class="years_thr_b_e animate__animated animate__rotateInDownLeft" v-if="configDate.thrAni2"><text>{{currentYear}}</text>年，你还没有上过课。 打开用户报告的你，想必也怀揣着进取的心期待明年你的表现</view>
						</view>
						
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item years_fou">
						<view :style="'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'"></view>
						<image :src="imgbaseUrlOss + '/userreport/icon11.png'" mode="aspectFill" class="years_one_bj"></image>
						<template v-if="userReport.sign_num*1 > 0">
							<view class="years_fou_a animate__animated animate__rotateInDownLeft" style="margin-top: 44rpx;" v-if="configDate.fouAni1">这是你坚持最久的两个习惯......</view>
							<view class="years_fou_a animate__animated animate__rotateInDownLeft" v-if="configDate.fouAni2">本年度打卡<text>{{userReport.sign_num*1}}</text>次</view>
							<view class="years_fou_a animate__animated animate__rotateInDownLeft" v-if="configDate.fouAni3">本年度练习<text>{{userReport.practice_time*1}}</text>小时</view>
							
							<view class="years_fou_a animate__animated animate__rotateInDownLeft" style="margin-top: 92rpx;" v-if="configDate.fouAni4">在此过程中，你发现了自己很多不足</view>
							<view class="years_fou_a animate__animated animate__rotateInDownLeft" v-if="configDate.fouAni5">也让你明白</view>
							<view class="years_fou_a animate__animated animate__rotateInDownLeft" v-if="configDate.fouAni6">放弃很容易，但坚持更酷！</view>
						</template>
						<template v-else>
							<view class="years_fou_a animate__animated animate__rotateInDownLeft" style="margin-top: 44rpx;" v-if="configDate.fouAni1">本年度你并未坚持打卡......</view>
							<view class="years_fou_a animate__animated animate__rotateInDownLeft" v-if="configDate.fouAni2">本年度打卡<text>0</text>次</view>
							<view class="years_fou_a animate__animated animate__rotateInDownLeft" v-if="configDate.fouAni3">本年度练习<text>0</text>次</view>
						</template>
					</view>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- <view class="userd1 animate__animated animate__bounce">搜索</view>
		
		<view class="userd1 animate__animated animate__fadeInLeftBig">搜索</view> -->

	</view>
</template>


<script>
import {
	yearReportApi,
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			imgbaseUrl:'',//图片地址
			imgbaseUrlOss:'',
			safeAreaTop:wx.getWindowInfo().safeArea.top,
			menuButtonInfoHeight:uni.getMenuButtonBoundingClientRect().height,
			loding:false,
			isLogined:true,
			currentYear:'',
			swiperIndex:0,
			configDate:{
				oneAni1:false,
				oneAni2:false,
				oneAni3:false,
				
				twoAni0:false,
				twoAni1:false,
				twoAni2:false,
				twoAni3:false,
				twoAni4:false,
				
				thrAni1:false,
				thrAni2:false,
				thrAni3:false,
				thrAni4:false,
				thrAni5:false,
				
				fouAni1:false,
				fouAni2:false,
				fouAni3:false,
				fouAni4:false,
				fouAni5:false,
				fouAni6:false,
				
			},
			userReport:{}
		}
	},
	onShow: function(){
	   this.imgbaseUrl = this.$baseUrl;
	   this.imgbaseUrlOss = this.$baseUrlOss;
	   
	  
	},
	onLoad(option) {
		let currentDate = new Date();
		this.currentYear = currentDate.getFullYear();
		this.yearUserReportData(option.id);//年报
		
	},
	methods: {
		//年报
		yearUserReportData(id){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			yearReportApi({id:id}).then(res => {
				console.log('年报',res)
				if (res.code == 1) {
					uni.hideLoading();
					/*res.data.appointment_num = 2
					
					
					res.data.last_course = {
						"name": "JAZZ/KPOP常规课堂——Sunny", //课程名称
						"teacher": {
							"name": "Sunny" //老师名称
						} //老师
					}
					
					res.data.sign_num = 2;*/
					
					
					that.userReport = res.data;
					that.loding = true;
					that.cshData()
				}
			})
		},
		//初始化
		cshData(){
			var that = this;
			if(this.swiperIndex == 0){
				that.configDate.oneAni1 = true;
				setTimeout(()=>{
					that.configDate.oneAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.oneAni3 = true;
				},2000);
			}
		},
		//监听swiper
		swiperChange(e){
			var that = this;
			// console.log(e,'监听swiper')
			this.swiperIndex = e.detail.current
			
			if(this.swiperIndex == 1){
				that.configDate.twoAni0 = true;
				setTimeout(()=>{
					that.configDate.twoAni1 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.twoAni2 = true;
				},2000);
				setTimeout(()=>{
					that.configDate.twoAni3 = true;
				},3000);
				setTimeout(()=>{
					that.configDate.twoAni4 = true;
				},4000);
			}
			
			if(this.swiperIndex == 2){
				that.configDate.thrAni1 = true;
				setTimeout(()=>{
					that.configDate.thrAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.thrAni3 = true;
				},2000);
				setTimeout(()=>{
					that.configDate.thrAni4 = true;
				},3000);
				setTimeout(()=>{
					that.configDate.thrAni5 = true;
				},4000);
			}
			
			if(this.swiperIndex == 3){
				that.configDate.fouAni1 = true;
				setTimeout(()=>{
					that.configDate.fouAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.fouAni3 = true;
				},2000);
				setTimeout(()=>{
					that.configDate.fouAni4 = true;
				},3000);
				setTimeout(()=>{
					that.configDate.fouAni5 = true;
				},4000);
				setTimeout(()=>{
					that.configDate.fouAni6 = true;
				},5000);
			}
			
		},
		//动画结束时会触发
		swiperEnd(e){
			// console.log(e,'动画结束时会触发')
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">

.userReport{-overflow: hidden;}
page{padding-bottom: 0;background:#fff;}
.userd1{
	width: 200rpx;
	height: 200rpx;
	background:red;
	margin: auto;
}
</style>