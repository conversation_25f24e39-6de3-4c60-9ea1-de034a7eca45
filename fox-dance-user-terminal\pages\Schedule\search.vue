<template>
	<view class="search">
		
		<view class="les_search">
			<view class="les_search_l"><image src="/static/images/search.png"></image><input type="text" placeholder-style="color:#999999" placeholder="请搜索你想要的课程" v-model="keywords" confirm-type="search" @confirm="searchTap(keywords)" /></view>
			<view class="les_search_r" @click="searchTap(keywords)">搜索</view>
		</view>
		
		<view class="sear_one" v-if="keywordsLists_kc.length > 0">
			<view class="sear_one_t">历史搜索<image src="/static/images/icon32.png" @click="clearTap"></image></view>
			<view class="sear_one_b"><text v-for="(item,index) in keywordsLists_kc" :key="index" @click="searchTap(item.name)">{{item.name}}</text></view>
		</view>
		
		<view class="sear_one" v-if="keywordsLists_kc.length == 0">
			<view class="sear_one_t">历史搜索</view>
			<view class="sear_one_b" style="text-align:center;font-size:26rpx;color:#999;">暂无历史记录</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
export default {
	data() {
		return {
			isLogined:true,
			keywords:'',
			keywordsLists_kc:[],
			mdId:0,//门店id
		}
	},
	onLoad(option) {
		this.mdId = option.id;
		this.keywordsLists_kc = uni.getStorageSync("keywordsLists_kc") == '' ? [] : JSON.parse(uni.getStorageSync("keywordsLists_kc"))
	},
	onShow() {
		
	},
	methods: {
		//清空历史记录
		clearTap(){
			var that = this;
			uni.showModal({
				title: '提示',
				content: '确认要清空历史记录吗？',
				success: function(res) {
					if (res.confirm) {
						uni.removeStorageSync("keywordsLists_kc")
						that.keywordsLists_kc = [];
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
			
			
		},
		//搜索
		searchTap(keywords){
			// var keywordsLists_kc = this.keywordsLists_kc;
			var isyou = []
			for(var i=0;i<this.keywordsLists_kc.length;i++){
				if(this.keywordsLists_kc[i].name == keywords){
					isyou.push(i)
				}
			}
			if(keywords.split(' ').join('').length == 0){
				keywords = ''
			}
			this.keywordsLists_kc = (isyou.length == 0 && keywords.length != 0)  ? this.keywordsLists_kc.concat({name:keywords}) : this.keywordsLists_kc;
			uni.setStorageSync("keywordsLists_kc",JSON.stringify(this.keywordsLists_kc))
			
			console.log(keywords.length,'keywords',this.keywordsLists_kc)
			uni.navigateTo({
				url:'/pages/Schedule/searchResults?keywords=' + keywords + '&id=' + this.mdId
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.search{overflow: hidden;}
page{padding-bottom: 0;}
</style>