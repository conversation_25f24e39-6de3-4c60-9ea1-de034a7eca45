<template>
	<view class="confirmOrder" :style="{ '--qjbutton-color': qjbutton }" v-if="productxq.id">
		
		<view class="qrdd_a" @click="goToAddr('diancan')" v-if="shouAddr.area == ''"><image src="/static/images/icon33.png"></image>添加收货地址</view>
		<view class="qrdd_b" @click="goToAddr('diancan')" v-else>
			<view class="qrdd_b_a">{{shouAddr.name}} {{shouAddr.phone}}</view>
			<view class="qrdd_b_b">{{shouAddr.area+shouAddr.detail}}</view>
		</view>
		
		<view class="qrdd_c" v-if="false">
			<view class="qrdd_c_li">
				<image :src="imgbaseUrl + productxq.image" mode="aspectFill" class="qrdd_c_li_l"></image>
				<view class="qrdd_c_li_r">
					<view class="qrdd_c_li_r_a">{{productxq.name}}</view>
					<!-- <view class="qrdd_c_li_r_b">已选：420g</view> -->
					<view class="qrdd_c_li_r_c"><view>￥{{productxq.redeem_points*1}}</view><text>x1</text></view>
				</view>
			</view>
		</view>
		
		<view class="qrdd_c">
			<view class="spe_n_a">
				<view class="spe_n_a_l"><image :src="imgbaseUrl + productxq.image" mode="aspectFill"></image></view>
				<view class="spe_n_a_r">
					<view class="spe_n_a_r_a">
						<view>{{productxq.name}}</view>
					</view>
					<view class="spe_n_a_r_b">已选：{{productxq.xuanztext}}</view>
					<view class="spe_n_a_r_c"><view>￥{{productxq.redeem_points*1}}</view><text>x{{productxq.num}}</text></view>
				</view>
			</view>
		</view>
		
		<view class="qrdd_d">
			<view>运费</view>
			<input type="text" :disabled="true" value="包邮"  placeholder-style="color:#999999;" />
		</view>
		<view class="qrdd_d qrdd_bz">
			<view>备注</view>
			<input type="text" placeholder="如有特殊要求，请填写" v-model="remark" placeholder-style="color: #999999;" />
		</view>
		
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<view class="peodex_foo">
			<view class="peodex_foo_l">总计：<text>￥{{(productxq.redeem_points*1)*(productxq.num)}}</text></view>
			<view class="peodex_foo_r" @click="dhSubTap">立即购买</view>
		</view>
		
		
	</view>
</template>


<script>
import {
	exchangeSubApi,
	addrList
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			productxq:{id:0},
			imgbaseUrl:'',
			remark:'',
			shouAddr:{area:''},
			qjbutton:'#131315',
			jinzLd:true
		}
	},
	onShow() {
		if (uni.getStorageSync('diancan')) {
			this.shouAddr = uni.getStorageSync('diancan')
		}else{
			this.shouAddr = {area:''}
		}
	},
	onLoad(option) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.imgbaseUrl = this.$baseUrl;
		this.productxq = JSON.parse(option.productxq);
		this.addressData();
		console.log(this.productxq)
	},
	methods: {
		//提交兑换
		dhSubTap(){
			if(this.shouAddr.area == ''){
				uni.showToast({
					icon:'none',
					title:'请选择收货地址~',
					duration: 2000
				});
				return false;
			}
			var that = this;
			if(!that.jinzLd){
			  uni.showToast({
				icon:'none',
				title:'您点击的太快了~',
				duration: 2000
			  });
			  return false;
			}
			that.jinzLd = false;
			uni.showLoading({
			  title:'支付中...',
			});
			exchangeSubApi({
				goods_id:that.productxq.id,
				addr_id:that.shouAddr.id,
				sku_id:that.productxq.skuid,
				num:that.productxq.num,
				remark:that.remark
			}).then(res => {
				console.log('提交兑换',res)
				
				
				
				
				
				if (res.code == 1) {
					/*uni.hideLoading();
					that.jinzLd = true
					uni.redirectTo({
						url:'/pages/buy/coursePackage/success'
					})
					return false;*/
					//调取微信支付
					uni.requestPayment({
						timeStamp:res.data.timeStamp,
						nonceStr:res.data.nonceStr,
						package:res.data.package,
						signType:res.data.signType,
						paySign:res.data.paySign,
						success: function (res) {
							//支付成功
							uni.hideLoading();
							/*uni.showToast({
							  icon:'success',
							  title: '支付成功',
							  duration: 2000
							});*/
							that.jinzLd = true
							uni.redirectTo({
								url:'/pages/buy/pointsMall/success'
							})
						},
						fail: function (err) {
							console.log('fail:' + JSON.stringify(err),'不回话接口');
							if (err.errMsg == "requestPayment:fail cancel") {
								that.jinzLd = true
								  uni.hideLoading();
								  uni.showToast({
									  icon:'none',
									  title: '支付取消',
									  duration: 2000
								  });
								  // setTimeout(function(){
								  // 	uni.switchTab({
								  // 		url:'/pages/order/order'
								  // 	})
								  // },1500)
							}
						}
					});
					
				}else{
					that.jinzLd = true
					uni.hideLoading();
					uni.showToast({
						icon:'none',
						title: res.msg,
						duration: 2000
					});
				}
			})
		},
		//收货地址
		goToAddr(type) {
			uni.navigateTo({
				url: `/pages/mine/address?type=${type}`
			})
		},
		//收货地址
		addressData(){
			addrList({page: 1,size: 999,}).then(res => {
				if (res.code == 1) {
					console.log(res,'地址列表')
					var arrdArr = [];
					for(var i=0;i<res.data.length;i++){
						if(res.data[i].is_default == 1){
							arrdArr.push(res.data[i])
						}
					}
					if(arrdArr.length == 0){
						this.shouAddr = {area:''}
					}else{
						this.shouAddr = arrdArr[0]
						uni.setStorageSync('diancan',arrdArr[0])
					}
				} else {
					this.mescroll.endErr()
					// this.mescroll.endSuccess();
				}
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.confirmOrder{overflow: hidden;}
page{padding-bottom: 0;}
</style>