<template>
	<view class="login" :style="{backgroundSize:bgs}">
		<u-navbar title="FOX舞蹈" back-icon-color="#333" back-icon-size="42"
			:background="{background:'transparent'}" :border-bottom="false" title-color="#333" title-size="32">

		</u-navbar>
		
		<view class="logt_bj" :style="'top:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;'"><image src="/static/images/loginbj.png"></image></view>
		
		<view class="acount-login">
			<!-- <view class=" login_text ">
				<view class="">
					欢迎来到
				</view>
				<view class="">
					FOX空间
				</view>
			</view> -->
			<view class="acount-login_a">
				<text>手机号</text>
				<text>登陆 / 注册</text>
			</view>
			<view class="acount-login_b">未注册的手机将会自动注册</view>
			<!-- btn样式在 App.vue 文件中 -->
			<view class="btn">
				手机号一键登陆
				<button v-if="isAgreement" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" size="lg"></text>
				</button>
				<button v-else size="lg" @click="tisTap">
				</button>
			</view>
			<view class="m-b-30 sm flex row-center" @click="IsAgree" style="height:56rpx;">
				<image v-if="!isAgreement" src="/static/images/Mr.png" mode="scaleToFill"></image>
				<image v-else src="/static/images/Mr_x.png" mode="scaleToFill"></image>
				<view class="flex">
					我已阅读并同意授权
					<view @click.stop="navTo('/pages/login/xieYi?type=1')" style="color: #945048;">《用户注册购卡协议》</view>
					<!-- 和
					<view @click.stop="navTo('/pages/login/xieYi?type=2')" style="color: #945048;">《隐私政策》</view> -->
				</view>
			</view>
		</view>
		
		<view class="logo"><image src="/static/images/logo.png"></image></view>
		
		<view class="logsetTc" v-if="editzlToggle">
			<view class="logsetTc_n">
				<view class="logsetTc_t">
					<view>请先设置头像和昵称</view>
					<text>注册、登录小程序</text>
				</view>
				<view class="logsetTc_c">
					<view class="qyrz_con_li tx">
						<button open-type="chooseAvatar" @chooseavatar="chooseAvatarsc">获取头像</button>
						<view class="qyrz_con_li_l">头像</view>
						<view class="qyrz_con_li_r">
							<view class="uni-input" style="height:auto;"><image :src="avatar == '' ? '/static/images/toux.png' : imgbaseUrl + avatar" mode="aspectFill" class="edma_one_a"></image><image src="/static/images/right_more.png"></image></view>
						</view>
					</view>
					<view class="qyrz_con_li">
						<view class="qyrz_con_li_l">昵称</view>
						<view class="qyrz_con_li_r">
							<input type="nickname" placeholder="请输入昵称" v-model="nickname"  maxlength="8" />
							<!-- <view class="uni-input">{{nickname}}<image src="/static/images/icon3.png"></image></view> -->
						</view>
					</view>
				</view>
				<view class="logsetTc_f">
					<view @click="qxTap">跳过</view>
					<view @click="subTap">确定</view>
				</view>
			</view>
		</view>
		
	</view>
</template>

<script>
	import {
		login,
		upImg,
		userInfoApi,
		toeditUserApi,
		getContractApi
	} from '@/config/http.achieve.js'
	export default {
		data() {
			return {
				bgs: '',
				safeAreaTop:wx.getWindowInfo().safeArea.top,
				menuButtonInfoHeight:uni.getMenuButtonBoundingClientRect().height,
				isAgreement: false,
				avatar:'',//头像
				nickname:'',//昵称
				imgbaseUrl:'',
				editzlToggle:false,//编辑资料弹窗
			};
		},
		onLoad(option) {
			this.imgbaseUrl = this.$baseUrl;
			let sys = uni.getSystemInfoSync()
			this.bgs = '100%' + ' ' + (Number(sys.statusBarHeight) + 456) + 'px'
			
			this.getCode()
			// this.getLoginCode()
		},
		methods: {
			//取消弹窗
			qxTap(){
				this.editzlToggle = false;
				if(uni.getStorageSync('tokenwx')){
					uni.removeStorageSync('tokenwx')
					uni.switchTab({
						url:'/pages/index/index'
					})
				}else{
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				}
			},
			subTap(){
				if (this.avatar == '/static/images/avatar.png') {
					uni.showToast({
						icon:'none',
						title: '请上传头像',
						duration: 2000
					});
					return false;
				}
				if (this.nickname.split(" ").join("").length == 0) {
					uni.showToast({
						icon:'none',
						title: '请输入昵称',
						duration: 2000
					});
					return false;
				}
				uni.showLoading({
					title: '加载中'
				});
				toeditUserApi({
					// userId:uni.getStorageSync('user').userId,
					avatar:this.avatar,
					nickname:this.nickname
				}).then(res => {
					if (res.code == 1) {
						uni.hideLoading();
						uni.showToast({
							title:'设置成功',
							duration: 2000
						});
						if(uni.getStorageSync('tokenwx')){
							uni.removeStorageSync('tokenwx')
							setTimeout(() => {
								uni.switchTab({
									url:'/pages/index/index'
								})
							}, 1500)
						}else{
							setTimeout(() => {
								uni.navigateBack()
							}, 1500)
						}
					}else{
						
					}
				})
			},
			//获取头像
			chooseAvatarsc(event){
			  // console.log(event,'event')
			  var that = this;
			  const tempFilePaths = event.detail.avatarUrl
			  uni.showLoading({
			  	title:'加载中'
			  })
			  upImg(tempFilePaths, 'file',{driver:'cos'}).then(ress => {
			  	console.log('上传图片',ress)
			  	if (ress.code == 1) {
			  		uni.hideLoading();
			  		that.avatar = ress.data.file.url
			  	}
			  })
			  
			},
			//个人信息
			userData(){
				uni.showLoading({
					title: '加载中'
				});
				let that = this;
				userInfoApi({}).then(res => {
					if (res.code == 1) {
						console.log('个人信息',res);
						that.avatar = res.data.avatar;
						that.nickname = res.data.nickname == '' ? '微信昵称' : res.data.nickname;
						uni.hideLoading();
					}
				})
			},
			tisTap(){
				uni.showToast({
					title: '请先阅读并同意授权《用户注册购卡协议》',
					icon: 'none',
					duration: 1000
				})
			},
			IsAgree() {
				this.isAgreement = !this.isAgreement
			},
			getCode() {
				let that = this
				uni.login({
					provider: 'weixin', //使用微信登录
					success: function(res) {
						console.log(res);
						that.minicode = res.code
						// return
					},
				})
			},
			getPhoneNumber(phone) {
				console.log(phone, '45646546546');
				// return false;
				let that = this
				
				if (this.isAgreement == true) {
					if (!phone.detail.errMsg.includes('getPhoneNumber:ok')) {
						//this.getLoginCode()
						return
					}
					uni.showLoading({
						title: '登录中...',
						icon: 'loading'
					})
					const parentId = uni.getStorageSync('parentId')
					const silverGrantId = uni.getStorageSync('silverGrantId')
					let params = {
						code: this.minicode,
						phone_code:phone.detail.code,
						pid:uni.getStorageSync("pid") ? uni.getStorageSync("pid") : '',
						// iv: phone.detail.iv,
						// encryptedData: phone.detail.encryptedData,
						// superior:uni.getStorageSync("pid") ? uni.getStorageSync("pid") : '',
					}
					if (parentId) {
						params = {
							...params,
							parentId: parentId
						}
					}
					if (silverGrantId) {
						params = {
							...params,
							silverGrantId: silverGrantId
						}
					}
					// return console.log(params,'54654654');
					login({
						...params,
					}).then(res => {
						console.log(res,'登录')
						if (res.code == 1) {
							uni.hideLoading();
							uni.showToast({
								title: '登录成功',
								icon: 'success',
								duration: 2000
							})
							uni.setStorageSync('token', res.data.userinfo.token)
							// uni.setStorageSync('token', '49e2e458-2c96-489d-a19a-9aacf70b3c13')
							uni.setStorageSync('userid', res.data.userinfo.id);
							that.getContractData(res.data.userinfo.avatar);//获取未签署的合同
							/*
							if(res.data.userinfo.avatar == '/static/images/avatar.png'){
								//需要设置头像昵称
								that.editzlToggle = true;
							}else{
								if(uni.getStorageSync('tokenwx')){
									uni.removeStorageSync('tokenwx')
									uni.switchTab({
										url:'/pages/index/index'
									})
								}else{
									setTimeout(() => {
										uni.navigateBack()
									}, 1500)
								}
							}*/
						} else {
							that.getCode()
							uni.hideLoading()
							uni.showToast({
								title: '登录失败',
								icon: 'error',
								duration: 1000
							})
							
							// this.getLoginCode()
						}
			
					})
				} else {
					uni.showToast({
						title: '请先同意协议',
						icon: 'none',
						duration: 1000,
					})
				}
			},
			//获取未签署的合同
			getContractData(avatar){
				var that = this;
				getContractApi({
				}).then(res => {
					console.log('获取未签署的合同',res)
					if (res.code == 1) {
						// res.data = 22;
						if(res.data){
							//需要签署合同
							setTimeout(function(){
								uni.reLaunch({
									url:'/pages/index/signing?id=' + res.data
								})
							},1200)
						}else{
							//不需要签署
							if(avatar == '/static/images/avatar.png'){
								//需要设置头像昵称
								that.editzlToggle = true;
							}else{
								if(uni.getStorageSync('tokenwx')){
									uni.removeStorageSync('tokenwx')
									uni.switchTab({
										url:'/pages/index/index'
									})
								}else{
									setTimeout(() => {
										uni.navigateBack()
									}, 1500)
								}
							}
						}
					}
				})
			},
			// 是否显示密码
			showPass() {
				this.isType = !this.isType
				// if (this.type == 'text') {
				// 	this.type = 'password'
				// } else {
				// 	this.type = 'text'
				// }
			},

			codeChange(tip) {
				this.codeTips = tip
			},
			navTo(url) {
				uni.navigateTo({
					url: url
				})
			},
			checkId(type) {
				confirmId({
					identity: type
				}).then(res => {
					if (res.code == 1) {
						if (type == 1) {
							uni.setStorageSync('identity', 'user')
						} else if (type == 2) {
							uni.setStorageSync('identity', 'master')
						}

						uni.switchTab({
							url: '/pages/index/index'
						})
					}
				})
			},

			
		},

	}
</script>
<style lang="scss">
	.logo{
		width:233rpx;
		height:47rpx;
		position: fixed;
		bottom:10%;
		left:50%;
		margin-left:-116rpx;
	}
	.logo image{display:block;width: 100%;height: 100%;}
	.logt_bj{
		width:620rpx;
		height:600rpx;
		position: fixed;
		top:0;
		right:0;
		z-index: 1;
	}
	.logt_bj image{display:block;width: 100%;height: 100%;}
	.color {
		color: #bfbfbf;
	}

	page {
		background-color: #fff !important;
		padding: 0;

		.login {
			//背景图片根据需求修改
			// background-image: url(@/static/login/login_top_bgi.png);
			// background-image: url(https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/userreport/login_top_bgi.png);
			background-size: 100% 912rpx;
			background-repeat: no-repeat;
			min-height: 100vh;
			display: flex;
			flex-direction: column;

			.acount-login {
				width: 750rpx;
				height: auto;
				display: flex;
				flex-direction: column;
				box-sizing: border-box;
				min-height: 0;
				flex: 1;
				padding-top: 220rpx;
				position:relative;
				z-index: 2;
				.login_text {
					width: 100%;
					view{
						&:nth-child(1){
							padding-left: 128rpx;
							font-size: 50rpx;
							font-family: Maoken Glitch Sans;
							font-size: 50rpx;
							color: #131315;
							line-height: 58rpx;
							
						}
						&:nth-child(2){
							font-family: Maoken Glitch Sans;
							padding-left: 210rpx;
							margin-top: 15rpx;
							font-size: 50rpx;
							color: #131315;
							font-weight: Regular;
							line-height: 59rpx;
						}
					}
				}

				.sm {
					margin-top: 46rpx;

					image {
						width: 22rpx;
						height: 22rpx;
						margin-right: 8rpx;
					}

					view {
						font-size: 22rpx;
						color: #131315!important;
						line-height: 26rpx;
					}
				}

			}
		}
	}

	.btn {
		margin:270rpx 70rpx 0 70rpx;
		height: 90rpx;
		border-radius: 45rpx;
		position: relative;
		overflow:hidden;
		font-size:38rpx;
	}
	.btn button{
		display: block;
		width: 100%;
		height: 90rpx;
		position: absolute;
		top: 0;left: 0;
		opacity: 0;
	}
	.acount-login_a{
		margin:0 70rpx;
		height:auto;
		overflow:hidden;
	}
	.acount-login_a text{
		display: block;
		font-size: 70rpx;
		color:#000;
		font-weight: bold;
	}
	.acount-login_b{color:#a19494;font-size: 24rpx;margin:30rpx 70rpx 0 70rpx;}
</style>