<template>
	<view class="login">
		<view class="mpwx-login">
			
			<view style="padding-top: 128rpx;text-align: center;">
				<image src="../../static/logo.png" mode="scaleToFill" class="pay_icon"></image>
				<view class="title">友榕企服</view>
			</view>
			<view class="name" style="margin-top: 135rpx;">申请获取以下权限</view>
			<view class="huoqu">获取你的手机号快速注册</view>

			<button v-if="isAgreement" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" size="lg"
				class="btn    row-center "
				style="margin-top: 87rpx;ont-size: 32rpx;border-radius: 44rpx;font-weight: bold;">
				<text>手机号快捷登录</text>
			</button>
			<button v-else size="lg" @click="isAgree" class="btn row-center "
				style="margin-top: 87rpx;font-size: 32rpx;border-radius: 44rpx;font-weight: bold;">
				<text>手机号快捷登录</text>
			</button>
			
		</view>

		<view  class="m-b-56 flex row-center" @click="IsAgree">
			<image v-if="!isAgreement" src="/static/images/Mr.png" mode="scaleToFill"
				style="width: 32rpx;height: 32rpx;margin-right: 14rpx;"></image>
			<image v-else src="/static/images/Mr_x.png" mode="scaleToFill"
				style="width: 32rpx;height: 32rpx;margin-right: 14rpx;"></image>
			<view class="flex" style="font-size: 24rpx;color:#999999">
				已阅读并同意
				<view style="color: #F2861A;" @click.stop="navTo('/pages/login/xieyi?type=user')">《用户协议》</view>
				和
				<view style="color: #F2861A;" @click.stop="navTo('/pages/login/xieyi?type=privacy')">《隐私政策》</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		login,
		wlogin,
	} from '@/config/http.achieve.js'
	export default {
		data() {
			return {
				isAgreement: false,
				code: '',
				isWeixin: '',
				text: '',
				minicode: '',
			};
		},

		async onLoad(option) {
			this.getLoginCode()
		},
		onUnload() {

		},
		methods: {
			
			IsAgree() {
				this.isAgreement = !this.isAgreement
			},
			isAgree() {
				if (this.isAgreement == false) {
					uni.showToast({
						title: '请先同意协议',
						icon: 'none',
						duration: 1000,
					})
				} else {
					return
				}
			},
			navTo(url) {
				uni.navigateTo({
					url: url
				})
			},
			// 手机号授权登录
			getLoginCode() {
				uni.login({
					provider: 'weixin',
					success: (res) => {
						this.minicode = res.code
					}
				})
			},
			getPhoneNumber(phone) {

				uni.showLoading({
					title: '登录中...',
					icon: 'loading'
				})
				if (this.isAgreement == true) {
					if (!phone.detail?.errMsg?.includes('getPhoneNumber:ok')) {
						this.getLoginCode()
						return
					}

					let params = {
						code: this.minicode,
						iv: encodeURIComponent(phone.detail.iv),
						// iv: phone.detail.iv,
						encryptedData: encodeURIComponent(phone.detail.encryptedData)
						// encryptedData: phone.detail.encryptedData
					}
					if(uni.getStorageSync('type')){
						params.source = uni.getStorageSync('type')
					}
					if(uni.getStorageSync('isGround')){
						params.is_crowd = uni.getStorageSync('isGround')
					}
					login({
						...params,
					}).then(res => {
						if (res.code == 1) {
							uni.showToast({
								title: '登陆成功',
								icon: 'success',
							})
							// 处理自己的逻辑
							uni.setStorageSync('user', res.data)
							uni.navigateBack()
						} else {
							uni.showToast({
								title: '登陆失败',
								icon: 'error',
								duration: 1000
							})
							uni.hideLoading()
						}

					})
				} else {
					uni.showToast({
						title: '请先勾选\"已阅读并同意《服务协议》和《隐私协议》\"',
						icon: 'none',
					})
				}

			},

		},
		computed: {

		}
	};
</script>
<style lang="scss">
	page {
		background-color: #fff;
		padding: 0;
		font-family: PingFang SC;

		.pay_icon {
			margin: 0 auto;
			width: 180rpx;
			height: 180rpx;
			border-radius: 50rpx;
		}

		.title {
			font-size: 40rpx;
			font-weight: bold;
			color: #333333;
			margin-top: 40rpx;
		}

		.name {
			font-size: 24rpx;
			font-weight: bold;
			color: #333333;
			margin-left: 59rpx;

		}

		.huoqu {
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
			margin-left: 59rpx;
			margin-top: 36rpx;
		}

		.login {
			min-height: 100vh;
			display: flex;
			flex-direction: column;

			.mpwx-login {
				min-height: 0;
				flex: 1;

				.avatar {
					display: inline-block;
					width: 120rpx;
					height: 120rpx;
					border-radius: 50%;
					border: 1px solid #eee;
					overflow: hidden;
				}

				.user-name {
					height: 40rpx;
				}

				.image {
					width: 50rpx;
					height: 50rpx;
				}

				.btn {
					width: 638rpx;
					height: 88rpx;
					margin: 80rpx auto 0;
				}
			}


			.acount-login {
				padding-top: 100rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				box-sizing: border-box;
				min-height: 0;
				flex: 1;

				.logo {

					height: 80rpx;
					margin-bottom: 50rpx;
				}

				.input {
					width: 670rpx;
					border-bottom: $-solid-border;
					margin-top: 30rpx;
				}

				.sms-btn {
					border: 1px solid $-color-primary;
					width: 176rpx;
					height: 60rpx;
					box-sizing: border-box;
				}

				.wx-login {
					margin-top: 60rpx;

					.image {
						margin-top: 40rpx;
						width: 80rpx;
						height: 80rpx;
					}
				}
			}
		}
	}
</style>