package com.yupi.springbootinit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yupi.springbootinit.mapper.FollowMapper;
import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.model.entity.Follow;
import com.yupi.springbootinit.model.vo.FollowVO;
import com.yupi.springbootinit.service.BaUserService;
import com.yupi.springbootinit.service.FollowService;
import com.yupi.springbootinit.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 关注服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
@Slf4j
public class FollowServiceImpl extends ServiceImpl<FollowMapper, Follow> implements FollowService {

    @Resource
    private BaUserService baUserService;

    @Resource
    private NotificationService notificationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean followUser(Long followerId, Long followingId) {
        try {
            // 检查是否已经关注
            if (isFollowing(followerId, followingId)) {
                log.warn("用户已关注 - followerId: {}, followingId: {}", followerId, followingId);
                return false;
            }

            // 检查目标用户是否存在
            BaUser targetUser = baUserService.getById(followingId);
            if (targetUser == null) {
                log.warn("目标用户不存在 - followingId: {}", followingId);
                return false;
            }

            // 查找是否存在历史关注记录
            QueryWrapper<Follow> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("follower_id", followerId);
            queryWrapper.eq("following_id", followingId);
            Follow existingFollow = this.getOne(queryWrapper);

            boolean result;
            if (existingFollow != null) {
                // 更新现有记录
                existingFollow.setStatus(1);
                existingFollow.setUpdateTime(new Date());
                result = this.updateById(existingFollow);
            } else {
                // 创建新的关注记录
                Follow follow = new Follow();
                follow.setFollowerId(followerId);
                follow.setFollowingId(followingId);
                follow.setStatus(1);
                follow.setCreateTime(new Date());
                follow.setUpdateTime(new Date());
                follow.setIsDelete(0);
                result = this.save(follow);
            }

            if (result) {
                // 发送关注通知
                notificationService.sendFollowNotification(followingId, followerId);
                
                // TODO: 更新用户关注统计
                // updateFollowStats(followerId, 1, 0);
                // updateFollowStats(followingId, 0, 1);
                
                log.info("关注用户成功 - followerId: {}, followingId: {}", followerId, followingId);
            }

            return result;

        } catch (Exception e) {
            log.error("关注用户异常 - followerId: {}, followingId: {}, error: {}", 
                    followerId, followingId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unfollowUser(Long followerId, Long followingId) {
        try {
            // 检查是否已经关注
            if (!isFollowing(followerId, followingId)) {
                log.warn("用户未关注 - followerId: {}, followingId: {}", followerId, followingId);
                return false;
            }

            // 更新关注状态为取消
            UpdateWrapper<Follow> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("follower_id", followerId);
            updateWrapper.eq("following_id", followingId);
            updateWrapper.eq("status", 1);
            updateWrapper.set("status", 0);
            updateWrapper.set("update_time", new Date());

            boolean result = this.update(updateWrapper);

            if (result) {
                // TODO: 更新用户关注统计
                // updateFollowStats(followerId, -1, 0);
                // updateFollowStats(followingId, 0, -1);
                
                log.info("取消关注用户成功 - followerId: {}, followingId: {}", followerId, followingId);
            }

            return result;

        } catch (Exception e) {
            log.error("取消关注用户异常 - followerId: {}, followingId: {}, error: {}", 
                    followerId, followingId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean isFollowing(Long followerId, Long followingId) {
        try {
            QueryWrapper<Follow> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("follower_id", followerId);
            queryWrapper.eq("following_id", followingId);
            queryWrapper.eq("status", 1);

            return this.count(queryWrapper) > 0;

        } catch (Exception e) {
            log.error("检查关注状态异常 - followerId: {}, followingId: {}, error: {}", 
                    followerId, followingId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getFollowStatus(Long currentUserId, Long targetUserId) {
        Map<String, Object> status = new HashMap<>();
        
        try {
            // 检查当前用户是否关注目标用户
            boolean isFollowing = isFollowing(currentUserId, targetUserId);
            status.put("isFollowing", isFollowing);
            
            // 检查目标用户是否关注当前用户
            boolean isFollowedBy = isFollowing(targetUserId, currentUserId);
            status.put("isFollowedBy", isFollowedBy);
            
            // 是否互相关注
            boolean isMutualFollow = isFollowing && isFollowedBy;
            status.put("isMutualFollow", isMutualFollow);
            
            // 获取关注统计
            Map<String, Integer> stats = getFollowStats(targetUserId);
            status.put("followerCount", stats.get("followerCount"));
            status.put("followingCount", stats.get("followingCount"));
            
        } catch (Exception e) {
            log.error("获取关注状态异常 - currentUserId: {}, targetUserId: {}, error: {}", 
                    currentUserId, targetUserId, e.getMessage(), e);
            status.put("isFollowing", false);
            status.put("isFollowedBy", false);
            status.put("isMutualFollow", false);
            status.put("followerCount", 0);
            status.put("followingCount", 0);
        }
        
        return status;
    }

    @Override
    public List<FollowVO> getFollowingList(Long userId, Integer current, Integer size, Long currentUserId) {
        try {
            // 查询关注列表
            QueryWrapper<Follow> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("follower_id", userId);
            queryWrapper.eq("status", 1);
            queryWrapper.orderByDesc("create_time");

            Page<Follow> page = new Page<>(current, size);
            Page<Follow> followPage = this.page(page, queryWrapper);

            // 转换为VO
            return followPage.getRecords().stream()
                    .map(follow -> convertToFollowVO(follow.getFollowingId(), currentUserId, follow.getCreateTime()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取关注列表异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<FollowVO> getFollowersList(Long userId, Integer current, Integer size, Long currentUserId) {
        try {
            // 查询粉丝列表
            QueryWrapper<Follow> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("following_id", userId);
            queryWrapper.eq("status", 1);
            queryWrapper.orderByDesc("create_time");

            Page<Follow> page = new Page<>(current, size);
            Page<Follow> followPage = this.page(page, queryWrapper);

            // 转换为VO
            return followPage.getRecords().stream()
                    .map(follow -> convertToFollowVO(follow.getFollowerId(), currentUserId, follow.getCreateTime()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取粉丝列表异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Integer> getFollowStats(Long userId) {
        Map<String, Integer> stats = new HashMap<>();
        
        try {
            // 获取关注数
            Integer followingCount = getFollowingCount(userId);
            stats.put("followingCount", followingCount);
            
            // 获取粉丝数
            Integer followerCount = getFollowerCount(userId);
            stats.put("followerCount", followerCount);
            
        } catch (Exception e) {
            log.error("获取关注统计异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            stats.put("followingCount", 0);
            stats.put("followerCount", 0);
        }
        
        return stats;
    }

    @Override
    public Map<Long, Boolean> batchCheckFollowStatus(Long currentUserId, List<Long> userIds) {
        Map<Long, Boolean> statusMap = new HashMap<>();
        
        try {
            // 批量查询关注状态
            QueryWrapper<Follow> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("follower_id", currentUserId);
            queryWrapper.in("following_id", userIds);
            queryWrapper.eq("status", 1);

            List<Follow> follows = this.list(queryWrapper);
            Set<Long> followingIds = follows.stream()
                    .map(Follow::getFollowingId)
                    .collect(Collectors.toSet());

            // 构建结果映射
            for (Long userId : userIds) {
                statusMap.put(userId, followingIds.contains(userId));
            }

        } catch (Exception e) {
            log.error("批量检查关注状态异常 - currentUserId: {}, error: {}", currentUserId, e.getMessage(), e);
            // 返回默认值
            for (Long userId : userIds) {
                statusMap.put(userId, false);
            }
        }
        
        return statusMap;
    }

    @Override
    public List<FollowVO> getMutualFollows(Long userId, Integer current, Integer size, Long currentUserId) {
        try {
            // TODO: 实现互相关注查询逻辑
            // 这里返回模拟数据
            return new ArrayList<>();

        } catch (Exception e) {
            log.error("获取互相关注列表异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Integer getFollowingCount(Long userId) {
        try {
            QueryWrapper<Follow> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("follower_id", userId);
            queryWrapper.eq("status", 1);
            
            return Math.toIntExact(this.count(queryWrapper));

        } catch (Exception e) {
            log.error("获取关注数异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Integer getFollowerCount(Long userId) {
        try {
            QueryWrapper<Follow> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("following_id", userId);
            queryWrapper.eq("status", 1);
            
            return Math.toIntExact(this.count(queryWrapper));

        } catch (Exception e) {
            log.error("获取粉丝数异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public boolean updateFollowStats(Long userId, Integer followingIncrement, Integer followerIncrement) {
        try {
            // TODO: 实现用户关注统计更新
            log.debug("更新关注统计 - userId: {}, followingIncrement: {}, followerIncrement: {}", 
                    userId, followingIncrement, followerIncrement);
            return true;

        } catch (Exception e) {
            log.error("更新关注统计异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<FollowVO> getRecommendUsers(Long userId, Integer limit) {
        try {
            // TODO: 实现推荐用户逻辑
            return new ArrayList<>();

        } catch (Exception e) {
            log.error("获取推荐用户异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<FollowVO> getRecentFollowing(Long userId, Integer limit) {
        try {
            return getFollowingList(userId, 1, limit, userId);

        } catch (Exception e) {
            log.error("获取最近关注异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<FollowVO> getRecentFollowers(Long userId, Integer limit) {
        try {
            return getFollowersList(userId, 1, limit, userId);

        } catch (Exception e) {
            log.error("获取最近粉丝异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换为FollowVO对象
     */
    private FollowVO convertToFollowVO(Long userId, Long currentUserId, Date followTime) {
        try {
            BaUser user = baUserService.getById(userId);
            if (user == null) {
                return null;
            }

            FollowVO vo = new FollowVO();
            vo.setUserId(user.getId() != null ? user.getId().longValue() : null);
            vo.setNickname(user.getNickname());
            vo.setAvatar(user.getAvatar());
            vo.setBio(user.getBio());
            vo.setDanceType(user.getDance_type());

            // 获取关注统计
            Map<String, Integer> stats = getFollowStats(userId);
            vo.setFollowerCount(stats.get("followerCount"));
            vo.setFollowingCount(stats.get("followingCount"));

            // 检查当前用户是否关注了这个用户
            if (currentUserId != null && !currentUserId.equals(userId)) {
                vo.setIsFollowed(isFollowing(currentUserId, userId));
                vo.setIsMutualFollow(isFollowing(currentUserId, userId) && isFollowing(userId, currentUserId));
            } else {
                vo.setIsFollowed(false);
                vo.setIsMutualFollow(false);
            }

            // 格式化关注时间
            if (followTime != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                vo.setFollowTime(sdf.format(followTime));
            }

            // TODO: 设置其他字段
            vo.setLevel(1);
            vo.setIsOnline(false);
            vo.setLastActiveTime("");

            return vo;

        } catch (Exception e) {
            log.error("转换FollowVO异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return null;
        }
    }
}
