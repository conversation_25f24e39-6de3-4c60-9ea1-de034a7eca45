<template>
  <view class="demo-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <text class="title">帖子分享App演示</text>
      </view>
    </view>

    <scroll-view class="content" scroll-y>
      <!-- 功能介绍 -->
      <view class="intro-section">
        <text class="intro-title">🎉 帖子分享社交App</text>
        <text class="intro-desc">
          一个现代化的社交分享平台，支持帖子发布、图片分享、实时聊天等功能
        </text>
      </view>

      <!-- 功能列表 -->
      <view class="feature-section">
        <text class="section-title">✨ 核心功能</text>
        
        <view class="feature-grid">
          <view class="feature-item" @click="goPage('/pagesSub/social/home/<USER>')">
            <view class="feature-icon home">
              <u-icon name="home" color="#fff" size="24"></u-icon>
            </view>
            <text class="feature-name">帖子卡片流</text>
            <text class="feature-desc">网格布局，瀑布流展示</text>
          </view>

          <view class="feature-item" @click="goPage('/pagesSub/social/discover/index')">
            <view class="feature-icon discover">
              <u-icon name="search" color="#fff" size="24"></u-icon>
            </view>
            <text class="feature-name">发现页面</text>
            <text class="feature-desc">热门话题和推荐</text>
          </view>

          <view class="feature-item" @click="goPage('/pagesSub/social/publish/index')">
            <view class="feature-icon publish">
              <u-icon name="plus" color="#fff" size="24"></u-icon>
            </view>
            <text class="feature-name">发布帖子</text>
            <text class="feature-desc">分享生活精彩瞬间</text>
          </view>

          <view class="feature-item" @click="goPage('/pagesSub/social/message/index')">
            <view class="feature-icon message">
              <u-icon name="chat" color="#fff" size="24"></u-icon>
            </view>
            <text class="feature-name">消息聊天</text>
            <text class="feature-desc">与朋友实时交流</text>
          </view>

          <view class="feature-item" @click="goPage('/pagesSub/social/profile/index')">
            <view class="feature-icon profile">
              <u-icon name="account" color="#fff" size="24"></u-icon>
            </view>
            <text class="feature-name">个人中心</text>
            <text class="feature-desc">管理个人资料</text>
          </view>

          <view class="feature-item" @click="goPage('/pagesSub/social/post/detail?id=1')">
            <view class="feature-icon detail">
              <u-icon name="file-text" color="#fff" size="24"></u-icon>
            </view>
            <text class="feature-name">帖子详情</text>
            <text class="feature-desc">查看帖子和评论</text>
          </view>
        </view>
      </view>

      <!-- 技术特性 -->
      <view class="tech-section">
        <text class="section-title">🛠 技术特性</text>
        
        <view class="tech-list">
          <view class="tech-item">
            <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
            <text class="tech-text">基于uni-app + Vue2开发</text>
          </view>
          <view class="tech-item">
            <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
            <text class="tech-text">使用uview-ui组件库</text>
          </view>
          <view class="tech-item">
            <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
            <text class="tech-text">支持微信小程序平台</text>
          </view>
          <view class="tech-item">
            <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
            <text class="tech-text">现代化卡片式设计</text>
          </view>
          <view class="tech-item">
            <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
            <text class="tech-text">组件化开发模式</text>
          </view>
          <view class="tech-item">
            <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
            <text class="tech-text">响应式布局适配</text>
          </view>
        </view>
      </view>

      <!-- 设计亮点 -->
      <view class="design-section">
        <text class="section-title">🎨 设计亮点</text>
        
        <view class="design-grid">
          <view class="design-item">
            <text class="design-title">简约现代</text>
            <text class="design-desc">采用简洁的设计语言，注重用户体验</text>
          </view>
          <view class="design-item">
            <text class="design-title">色彩统一</text>
            <text class="design-desc">统一的色彩系统，保持视觉一致性</text>
          </view>
          <view class="design-item">
            <text class="design-title">交互流畅</text>
            <text class="design-desc">流畅的动画效果，提升操作体验</text>
          </view>
          <view class="design-item">
            <text class="design-title">适配完善</text>
            <text class="design-desc">完美适配不同尺寸的移动设备</text>
          </view>
        </view>
      </view>

      <!-- 开始体验 -->
      <view class="start-section">
        <text class="start-title">🚀 开始体验</text>
        <text class="start-desc">点击上方功能卡片，体验完整的社交分享功能</text>
        
        <view class="start-buttons">
          <u-button
            type="primary"
            size="large"
            text="进入首页"
            @click="goPage('/pagesSub/social/home/<USER>')"
          ></u-button>
          <u-button
            type="default"
            size="large"
            text="发布帖子"
            @click="goPage('/pagesSub/social/publish/index')"
          ></u-button>
          <u-button
            type="warning"
            size="large"
            text="组件测试"
            @click="goPage('/pagesSub/social/test/index')"
          ></u-button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'SocialDemo',
  methods: {
    goPage(url) {
      uni.navigateTo({
        url: url,
        fail: (err) => {
          console.error('页面跳转失败:', err)
          this.$u.toast('页面跳转失败')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  padding-top: var(--status-bar-height);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.content {
  margin-top: calc(44px + var(--status-bar-height));
  padding: 20px 16px;
}

.intro-section {
  text-align: center;
  margin-bottom: 32px;
}

.intro-title {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  display: block;
  margin-bottom: 12px;
}

.intro-desc {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  display: block;
}

.feature-section, .tech-section, .design-section, .start-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16px;
}

.feature-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.feature-item {
  width: calc(50% - 8px);
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
}

.feature-icon.home {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-icon.discover {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.feature-icon.publish {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.feature-icon.message {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.feature-icon.profile {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.feature-icon.detail {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.feature-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.feature-desc {
  font-size: 12px;
  color: #666;
  display: block;
}

.tech-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tech-item {
  display: flex;
  align-items: center;
}

.tech-text {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}

.design-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.design-item {
  width: calc(50% - 8px);
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.design-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.design-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  display: block;
}

.start-section {
  text-align: center;
}

.start-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.start-desc {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 20px;
}

.start-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
