<template>
	<view class="messageCenter" :style="{ '--qjbutton-color': qjbutton }">
		
		<view class="mes_one">
			<view :class="type == 0 ? 'mes_one_ac' : ''" @click="tabTap(0)">全部</view>
			<view :class="type == 1 ? 'mes_one_ac' : ''" @click="tabTap(1)">消息</view>
			<view :class="type == 2 ? 'mes_one_ac' : ''" @click="tabTap(2)">通知</view>
		</view>
		
		<view class="mes_two">
			<view class="mes_two_li" v-for="(item,index) in messageLists" :key="index" @click="yhbgTap(item)">
				<image src="/static/images/icon26.png" class="mes_two_li_tz" v-if="item.type == 1 || item.type == 7"></image>
				<image :src="item.profile == '' ? '/static/images/icon26-1.png' : imgbaseUrl + item.profile" class="mes_two_li_xx" v-else></image>
				<view class="mes_two_li_c">
					<div class="mes_two_li_c_t"><view>{{item.type == 1 ? '系统消息' : '通知'}}</view><text>{{item.create_time}}</text></div>
					<div class="mes_two_li_c_b"><view>{{item.content}}</view><text style="display:none;">2</text></div>
				</view>
			</view>
		</view>
		<view class="gg_loding" v-if="!zanwsj">
			<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
				<view></view>
				<text>加载中</text>
			</view>
			<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
		</view>
		<view class="gg_zwsj" v-if="zanwsj">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无数据</text>
			</view>
		</view>
		
		<view class="tzxf" @click="navTo('/pages/mine/tzgl')"><text>通知</text><text>管理</text></view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	messageApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			type:0,
			isLogined:true,
			messageLists:[],//消息列表
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			imgbaseUrl:'',
			qjbutton:'#131315',
		}
	},
	onShow() {
		this.imgbaseUrl = this.$baseUrl;
		this.page = 1;
		this.messageLists = [];
		this.messageData()//消息列表
	},
	onLoad() {
		this.qjbutton = uni.getStorageSync('storeInfo').button
	},
	methods: {
		//用户报告跳转
		yhbgTap(item){
			
			var type = item.type
			if(type == 7){
				uni.navigateTo({
					url:'/pages/prizedraw/dengji'
				})
			}
			if(type == 3 || type == 4 || type == 5){
				// 3=周报,4=月报,5=年报
				uni.navigateTo({
					url:type == 3 ? '/pages/mine/userReport/weeksUserReport?id=' + item.id : type == 4 ? '/pages/mine/userReport/monthUserReport?id=' + item.id : type == 5 ? '/pages/mine/userReport/yearsUserReport?id=' + item.id : ''
				})
			}
			
		},
		tabTap(index){
			this.type = index;
			this.page = 1;
			this.messageLists = [];
			this.messageData();//消息列表
		},
		//消息列表
		messageData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			messageApi({
				page:that.page,
				size:10,
				type:that.type,
			}).then(res => {
				console.log('消息列表',res)
				if (res.code == 1) {
					var obj = res.data.data;
					that.messageLists = that.messageLists.concat(obj);
					that.zanwsj = that.messageLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.messageLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottom() {
			//console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.messageData();
				}
			}
		},
		onPullDownRefresh: function() {
		    console.log('我被下拉了');
		    this.page = 1;
			this.messageLists = [];
			this.messageData();//消息列表
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
page{padding-bottom: 0;background: #FFFFFF;}
</style>