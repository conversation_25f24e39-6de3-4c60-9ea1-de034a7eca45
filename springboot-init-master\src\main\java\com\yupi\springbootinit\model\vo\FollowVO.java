package com.yupi.springbootinit.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 关注信息视图对象
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@ApiModel(description = "关注信息视图对象")
public class FollowVO implements Serializable {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称", example = "张小明")
    private String nickname;

    /**
     * 用户头像
     */
    @ApiModelProperty(value = "用户头像", example = "https://example.com/avatar.jpg")
    private String avatar;

    /**
     * 个人简介
     */
    @ApiModelProperty(value = "个人简介", example = "热爱街舞的舞者")
    private String bio;

    /**
     * 舞种
     */
    @ApiModelProperty(value = "舞种", example = "街舞")
    private String danceType;

    /**
     * 粉丝数
     */
    @ApiModelProperty(value = "粉丝数", example = "1000")
    private Integer followerCount;

    /**
     * 关注数
     */
    @ApiModelProperty(value = "关注数", example = "500")
    private Integer followingCount;

    /**
     * 是否已关注（当前用户是否关注了这个用户）
     */
    @ApiModelProperty(value = "是否已关注", example = "true")
    private Boolean isFollowed;

    /**
     * 是否互相关注
     */
    @ApiModelProperty(value = "是否互相关注", example = "false")
    private Boolean isMutualFollow;

    /**
     * 关注时间
     */
    @ApiModelProperty(value = "关注时间", example = "2025-07-17 10:00:00")
    private String followTime;

    /**
     * 用户等级
     */
    @ApiModelProperty(value = "用户等级", example = "5")
    private Integer level;

    /**
     * 是否在线
     */
    @ApiModelProperty(value = "是否在线", example = "true")
    private Boolean isOnline;

    /**
     * 最后活跃时间
     */
    @ApiModelProperty(value = "最后活跃时间", example = "2025-07-17 15:30:00")
    private String lastActiveTime;

    private static final long serialVersionUID = 1L;

    // 手动添加getter/setter方法
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getDanceType() {
        return danceType;
    }

    public void setDanceType(String danceType) {
        this.danceType = danceType;
    }

    public Integer getFollowerCount() {
        return followerCount;
    }

    public void setFollowerCount(Integer followerCount) {
        this.followerCount = followerCount;
    }

    public Integer getFollowingCount() {
        return followingCount;
    }

    public void setFollowingCount(Integer followingCount) {
        this.followingCount = followingCount;
    }

    public Boolean getIsFollowed() {
        return isFollowed;
    }

    public void setIsFollowed(Boolean isFollowed) {
        this.isFollowed = isFollowed;
    }

    public Boolean getIsMutualFollow() {
        return isMutualFollow;
    }

    public void setIsMutualFollow(Boolean isMutualFollow) {
        this.isMutualFollow = isMutualFollow;
    }

    public String getFollowTime() {
        return followTime;
    }

    public void setFollowTime(String followTime) {
        this.followTime = followTime;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Boolean getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(Boolean isOnline) {
        this.isOnline = isOnline;
    }

    public String getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(String lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }
}
