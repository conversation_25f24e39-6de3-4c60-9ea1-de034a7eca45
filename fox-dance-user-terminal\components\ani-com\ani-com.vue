<template>
	<view class="box">
		<view class="asd">
			
		</view>
		<button type="default" style="position: fixed;top: 0;left: 50%;z-index: 9999;" @click="run">开始</button>
		<view style="height: 100%;width: 100%;" :style="`transform: translateX(-${late}%)`">
			<view class="bb" :style="'transition: all '+time+'s linear;transform: translateX(-'+((currentIndex - 3 + curLate)* 20)+'%)'">
				<!-- 前置防止抽到1-20数值 -->
				<view class="ff" v-for="(item,index) in 20">
					{{item}}
				</view>
				<view class="ff" v-for="(item,index) in 100">
					{{item}}
				</view>
				<!-- 后置，防止抽到99和100 -->
				<view class="ff" v-for="(item,index) in 3">
					{{item}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentIndex: 0,
				stoped: true,
				late: 0,
				curLate:0,//小于0的偏移量
				mixCount: 0,
				time:0.2,//过度时间
				value:0,//目标值
				defaultCount: 20 //默认的动画个数
			}
		},
		onLoad() {

		},
		methods: {
			run() {
				if (!this.stoped) {
					return
				}
				this.stoped = false
				let num = Math.floor(Math.random() * 100 + 1)
				// let num =99
				if (num < this.defaultCount) {
					this.late = (this.defaultCount - Math.abs(this.defaultCount - num)) * 20
					this.curLate = Math.abs(this.defaultCount - num)
				} else {
					this.late = this.defaultCount * 20
					this.curLate = 0
				}
				console.log(num)
				this.currentIndex = num - this.defaultCount
				this.mixCount = 0
				this.runTask(num - this.defaultCount)
			},
			/**
			 * num是固定的初始值
			 * */
			runTask(num) {
				console.log(this.currentIndex, num + this.defaultCount)
				//前20%是慢速
				//20%到80%快速
				//80%到100%是慢速
				if (this.currentIndex < num + this.defaultCount) {
					if (this.mixCount <= this.defaultCount * 0.2) {
						this.time = 0.2
						setTimeout(() => {
							this.mixCount += 1
							this.currentIndex += 1
							this.runTask(num)
						}, 50)
					} else if (this.mixCount <= this.defaultCount * 0.8 && this.mixCount > this.defaultCount * 0.2) {
						this.time = 0.2
						setTimeout(() => {
							this.mixCount += 1
							this.currentIndex += 1
							this.runTask(num)
						}, 50)
					} else if (this.mixCount <= this.defaultCount && this.mixCount > this.defaultCount * 0.8) {
						this.time =1
						setTimeout(() => {
							this.mixCount += 1
							this.currentIndex += 1
							this.runTask(num)
						}, 500)
					}
				} else if (this.currentIndex >= num + this.defaultCount) {
					console.log('jies')
					this.stoped = true
					wx.showToast({
						title:num + this.defaultCount
					})
					// this.currentIndex = 0
					// this.late = 0
					// this.currentIndex = 0
				}

			}
		}
	}
</script>

<style lang="less">
	view {
		box-sizing: border-box;
	}

	.box {
		width: 100vw;
		height: 100vh;
		background: linear-gradient(to right, rgba(0, 0, 0, 0.1), #fff 80%, rgba(0, 0, 0, 0.1));
	}

	.bb {
		display: flex;
		align-items: center;
		flex-shrink: 0;
		height: 100%;
		width: 100%;
	}

	.ff {
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid #000;
		width: 20%;
		height: 100rpx;
		flex-shrink: 0;
	}
	.asd{
		position: fixed;
		width: 1px;
		height: 100vh;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		background: #000;
		margin: auto;
	}
</style>
