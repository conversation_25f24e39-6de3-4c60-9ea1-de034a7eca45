<template>
	<view class="integral" :style="{ '--qjbutton-color': qjbutton }">
		
		<view class="int_one">
			<view class="int_one_a">我的积分</view>
			<view class="int_one_b">{{score}}</view>
			<view class="int_one_c" @click="navTo('/pages/mine/order/order')">积分订单</view>
		</view>
		
		<view class="int_two">
			<view class="int_two_l">
				<view :class="type == 0 ? 'int_two_l_ac' : ''" @click="tabTap(0)">全部</view>
				<view :class="type == 1 ? 'int_two_l_ac' : ''" @click="tabTap(1)">获得</view>
				<view :class="type == 2 ? 'int_two_l_ac' : ''" @click="tabTap(2)">消耗</view>
			</view>
			<view class="int_two_r">
				<picker mode="date" :value="date_sj" fields="month" @change="bindDateChange_sj">
					<view class="uni-input">{{date_sj}}<text></text></view>
				</picker>
			</view>
		</view>
		
		<view class="int_thr" v-if="!zanwsj">
			<view class="int_thr_li" v-for="(item,index) in scoreLists" :key="index">
				<view class="int_thr_li_a">{{item.type == 1 ? '兑换扣除' : item.type == 2 ? '邀请增加' : item.type == 3 ? '系统增加' : item.type == 4 ? '系统扣除' : ''}}</view>
				<view class="int_thr_li_b">{{item.create_time}}</view>
				<view class="int_thr_li_c">{{item.type == 1 ? '-' : item.type == 2 ? '+' : item.type == 3 ? '+' : item.type == 4 ? '-' : '+'}}{{item.score*1}}</view>
			</view>
			<view class="gg_loding">
				<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
					<view></view>
					<text>加载中</text>
				</view>
				<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
			</view>
		</view>
		
		<view class="gg_zwsj" v-if="zanwsj">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无数据</text>
			</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>

<script>
import {
	scoreListApi
} from '@/config/http.achieve.js'

export default {
	data() {
		return {
			isLogined:false,//是否登录
			isH5:false,//是否是h5
			score:0,
			type:0,
			date_sj: '请选择',
			scoreLists:[],//积分明细
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			imgbaseUrl:'',//图片地址
			qjbutton:'#131315',
		}
	},
	created(){
		
	},
	onLoad(options) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		// this.userData();//个人信息
		this.scoreData();//积分明细/提现记录
	},
	methods: {
		tabTap(index){
			this.type = index;
			this.page = 1;
			this.scoreLists = [];
			this.scoreData();
		},
		bindDateChange_sj: function(e) {
			this.date_sj = e.detail.value
			//积分明细/提现记录
			this.page = 1;
			this.scoreLists = [];
			this.scoreData();
		},
		//积分明细/提现记录
		scoreData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			scoreListApi({
				page:that.page,
				size:10,
				date:that.date_sj == '请选择' ? '' : that.date_sj,
				type:that.type,//0：全部 1：获得 2：消耗
			}).then(res => {
				console.log('积分明细',res)
				if (res.code == 1) {
					var obj = res.data.details.data;
					that.score = res.data.score
					that.scoreLists = that.scoreLists.concat(obj);
					that.zanwsj = that.scoreLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.details.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.scoreLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottom() {
			//console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.scoreData();
				}
			}
		},
		onPullDownRefresh: function() {
		    console.log('我被下拉了');
		    this.page = 1;
			this.scoreLists = [];
			this.scoreData();//积分明细/提现记录
		},
		//个人信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userDetailApi().then(res => {
				if (res.code == 1) {
					console.log('个人信息',res);
					uni.hideLoading();
				}
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
	},
}
</script>

<style lang="less">
page{padding-bottom:0;}
.integral{
	overflow:hidden;
}
</style>
