<template>
	<view class="ranking" :style="{ '--qjbutton-color': qjbutton }">
		
		<view class="ran_one">
			<view :class="type == 0 ? 'ran_one_ac' : ''" @click="tabTap(0)">本月上课次数榜</view>
			<view :class="type == 1 ? 'ran_one_ac' : ''" @click="tabTap(1)">上月刷课王</view>
		</view>
			
		<view class="ran_two" v-if="rankingA.length != 0">
			<view class="ran_two_li">
				<view class="ran_two_li_a" v-if="rankingA.length >= 2">
					<image src="/static/images/icon24.png"></image>
					<image :src="imgbaseUrl + rankingA[1].user.avatar" mode="aspectFit"></image>
					<view>{{rankingA[1].user.nickname}}</view>
					<text>上课{{rankingA[1].count}}次</text>
				</view>
				<view class="ran_two_li_b">TOP2</view>
			</view>
			<view class="ran_two_li">
				<view class="ran_two_li_a" v-if="rankingA.length >= 1">
					<image src="/static/images/icon23.png"></image>
					<image :src="imgbaseUrl + rankingA[0].user.avatar" mode="aspectFit" style="border-color:#FEC41F;"></image>
					<view>{{rankingA[0].user.nickname}}</view>
					<text>上课{{rankingA[0].count}}次</text>
				</view>
				<view class="ran_two_li_b">TOP1</view>
			</view>
			<view class="ran_two_li">
				<view class="ran_two_li_a" v-if="rankingA.length >= 3">
					<image src="/static/images/icon25.png"></image>
					<image :src="imgbaseUrl + rankingA[2].user.avatar" mode="aspectFit" style="border-color:#E4B98F;"></image>
					<view>{{rankingA[2].user.nickname}}</view>
					<text>上课{{rankingA[2].count}}次</text>
				</view>
				<view class="ran_two_li_b">TOP3</view>
			</view>
		</view>
		
		<view class="ran_thr">
			<view class="ran_thr_t" v-if="rankingA.length != 0"><view>排名</view><view>用户名</view><view>上课次数</view></view>
			<view class="ran_thr_b" v-if="rankingA.length != 0">
				<view class="ran_thr_b_li" v-for="(item,index) in rankingB" :key="index">
					<view class="ran_thr_b_li_a">{{index+4}}</view>
					<view class="ran_thr_b_li_b"><image :src="imgbaseUrl + item.user.avatar" mode="aspectFit"></image><text>{{item.user.nickname}}</text></view>
					<view class="ran_thr_b_li_c">已上课{{item.count}}次</view>
				</view>
			</view>
			<view style="font-size: 26rpx;color: #333;text-align:center;margin:40rpx 0;" v-if="rankingB.length == 0 && rankingA.length != 0">暂无更多排名</view>
			<view class="ran_thr_c"  v-if="rankingA.length != 0">仅展示前100名</view>
		</view>
		
		<view class="gg_zwsj" v-if="rankingA.length == 0" style="margin-bottom:60rpx;">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text style="color:#fff;">暂无排名</text>
			</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	myRankApi,
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			type:0,
			rankingA:[],
			rankingB:[],
			imgbaseUrl:'',
			qjbutton:'#131315',
		}
	},
	onShow() {
		
	},
	onLoad() {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.imgbaseUrl = this.$baseUrl;
		this.myRankData();//排行榜
	},
	methods: {
		tabTap(type){
			this.type = type;
			this.myRankData();//排行榜
		},
		//排行榜
		myRankData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			myRankApi({
				type:that.type
			}).then(res => {
				console.log('排行榜',res)
				if (res.code == 1) {
					/*res.data = [{
						"count": 2,
							"user": {
								"nickname": "昵称昵称1",
								"avatar": "/static/images/avatar.png"
							}
						},
						{
							"count": 2,
								"user": {
									"nickname": "昵称昵称2",
									"avatar": "/static/images/avatar.png"
								}
							},
							{
								"count": 2,
									"user": {
										"nickname": "昵称昵称",
										"avatar": "/static/images/avatar.png"
									}
								}
					]*/
					// that.myRankData = res.data;
					that.rankingA = res.data.slice(0,3)
					that.rankingB = res.data.slice(3,99)
					// console.log(that.rankingA,'that.rankingA')
					uni.hideLoading();
				}
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.ranking{overflow:hidden;}
page{padding-bottom: 0;background: #000;}
</style>