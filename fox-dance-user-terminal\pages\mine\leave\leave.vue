<template>
	<view class="leave" :style="{ '--qjbutton-color': qjbutton }">
		
		<view class="lea_one"><text @click="navTo('/pages/mine/leave/leaveLists')">请假记录</text></view>
		
		<view class="lea_two">
			<view class="lea_two_a">
				
				<!-- <view class="lea_two_a_li" @click="navTo('/pages/mine/leave/leaveCard')"> -->
				<view class="lea_two_a_li" @click="navTo('/pages/mine/memberCard/myMemberCard?xzhyk=1')">
					<view class="lea_two_a_li_l"><text>*</text>请假卡片</view>
					<view class="lea_two_a_li_r">
						<view class="uni-input"><text>{{selectCards.out_trade_no == '' ? '请选择' : '会员ID：' + selectCards.out_trade_no}}</text><image src="/static/images/right_more.png"></image></view>
					</view>
				</view>
			</view>
			
			<view class="lea_two_a">
				<view class="lea_two_a_li">
					<view class="lea_two_a_li_l"><text>*</text>开始时间</view>
					<view class="lea_two_a_li_r">
						<view class="uni-input"><text>{{date_start == '' ? '请选择' : date_start}}</text><image src="/static/images/right_more.png"></image></view>
						<view class="lea_nav_r_sj"><uni-datetime-picker v-model="date_start" type="date" /></view>
					</view>
				</view>
				<view class="lea_two_a_li">
					<view class="lea_two_a_li_l"><text>*</text>结束时间</view>
					<view class="lea_two_a_li_r">
						<view class="uni-input"><text>{{date_end == '' ? '请选择' : date_end}}</text><image src="/static/images/right_more.png"></image></view>
						<view class="lea_nav_r_sj"><uni-datetime-picker v-model="date_end" type="date" /></view>
					</view>
				</view>
			</view>
			
			<view class="lea_two_b">
				<view class="lea_two_b_a"><view class="lea_two_a_li_l"><text>*</text>备注</view></view>
				<textarea placeholder="请输入请假理由" placeholder-style="color: #999999;" v-model="notes"></textarea>
			</view>
		</view>
		
		<view class="lea_two_sub" @click="qjsubTap">确认请假</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>

<script>
import {
	confirmAskForLeaveApi
} from '@/config/http.achieve.js'

export default {
	data() {
		return {
			isLogined:false,//是否登录
			isH5:false,//是否是h5
			type:0,
			date_sj: '请选择',
			date_start: '',
			date_end: '',
			selectCards:{out_trade_no:''},
			notes:'',//备注
			qjbutton:'#131315',
		}
	},
	onShow() {
		this.selectCards = uni.getStorageSync('selectCards') ? uni.getStorageSync('selectCards') : {out_trade_no:''}
	},
	created(){
		
	},
	onLoad(options) {
		
		this.qjbutton = uni.getStorageSync('storeInfo').button
		var that = this;
		uni.setNavigationBarColor({
			frontColor: '#ffffff', // 文字颜色
			backgroundColor:uni.getStorageSync('storeInfo').button
		})
		// this.userData();//个人信息
	},
	methods: {
		maskClick(e){
			console.log('maskClick事件:', e);
		},
		bindDateChange_start: function(e) {
			this.date_start = e.detail.value
		},
		bindDateChange_end: function(e) {
			this.date_end = e.detail.value
		},
		tabTap(index){
			this.type = index;
		},
		bindDateChange_sj: function(e) {
			this.date_sj = e.detail.value
		},
		//确认请假
		qjsubTap(){
			var that = this;
			if(that.selectCards.out_trade_no == ''){
				uni.showToast({
					icon:'none',
					title: '请选择请假卡片',
					duration: 2000
				});
				return false;
			}
			if(that.date_start == ''){
				uni.showToast({
					icon:'none',
					title: '请选择请假开始时间',
					duration: 2000
				});
				return false;
			}
			if(that.date_end == ''){
				uni.showToast({
					icon:'none',
					title: '请选择请假结束时间',
					duration: 2000
				});
				return false;
			}
			
			var startDate = new Date(that.date_start);
			var endDate = new Date(that.date_end);
			if (startDate > endDate) {
				uni.showToast({
					icon:'none',
					title: '开始时间不能大于结束时间',
					duration: 2000
				});
				return false;
			}

			if(that.notes.split(' ').join('').length == 0){
				uni.showToast({
					icon:'none',
					title: '请输入请假理由',
					duration: 2000
				});
				return false;
			}
			
			uni.showLoading({
				title: '加载中'
			});
			confirmAskForLeaveApi({
				card_id:that.selectCards.id,
				start_time:that.date_start,
				end_time:that.date_end,
				notes:that.notes,
			}).then(res => {
				if (res.code == 1) {
					console.log('确认请假',res);
					uni.hideLoading();
					uni.showToast({
						icon:'success',
						title: '请假成功',
						duration: 2000
					});
					setTimeout(()=>{
						uni.navigateBack();
					},1500)
				}
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
	},
}
</script>

<style lang="less">
page{padding-bottom:0;}
.leave{
	overflow:hidden;
}
</style>
