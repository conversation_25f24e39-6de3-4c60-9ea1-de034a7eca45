package com.yupi.springbootinit.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户资料视图对象
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@ApiModel(description = "用户资料视图对象")
public class UserProfileVO implements Serializable {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", example = "xiaoming")
    private String username;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称", example = "张小明")
    private String nickname;

    /**
     * 头像URL
     */
    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    /**
     * 个人简介
     */
    @ApiModelProperty(value = "个人简介", example = "热爱生活，喜欢分享")
    private String bio;

    /**
     * 舞种
     */
    @ApiModelProperty(value = "舞种", example = "街舞")
    private String danceType;

    /**
     * 用户等级
     */
    @ApiModelProperty(value = "用户等级", example = "5")
    private Integer level;

    /**
     * 关注数
     */
    @ApiModelProperty(value = "关注数", example = "100")
    private Integer followingCount;

    /**
     * 粉丝数
     */
    @ApiModelProperty(value = "粉丝数", example = "200")
    private Integer followerCount;

    /**
     * 帖子数
     */
    @ApiModelProperty(value = "帖子数", example = "50")
    private Integer postCount;

    /**
     * 收到的点赞数
     */
    @ApiModelProperty(value = "收到的点赞数", example = "1000")
    private Integer likeReceivedCount;

    /**
     * 是否已关注（仅在查看其他用户时有效）
     */
    @ApiModelProperty(value = "是否已关注", example = "false")
    private Boolean isFollowed;

    private static final long serialVersionUID = 1L;
}
