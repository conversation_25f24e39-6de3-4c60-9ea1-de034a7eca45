.xytc{
	width: 100%;
	height: 100%;
	position: fixed;
	top:0;left:0;
	background:rgba(0, 0, 0, .7);
	z-index: 9999999;
	display: flex;
	align-items:center;
	justify-content:center;
}
.xytcCon{
	width:562rpx;
	height:auto;
	overflow:hidden;
	border-radius:20rpx;
	background:#fff;
}
.xytcCon_a{
	margin:46rpx 20rpx 0 20rpx;
	height:auto;
	overflow:hidden;
	font-size: 36rpx;
	color: #3f3f3f;
	text-align:center;
	font-weight: bold;
}
.xytcCon_b{
	margin:20rpx 40rpx 0 40rpx;
	height:auto;
	overflow:hidden;
	font-size: 24rpx;
	color: #505050;
}
.xytcCon_c{
	margin:20rpx 40rpx 0 40rpx;
	height:auto;
	overflow:hidden;
	background:#f4f5fa;
	border-radius:10rpx;
}
.xytcCon_c view{
	margin:14rpx;
	/* height:300rpx; */
	/* overflow:auto; */
}
.xytcCon_c scroll-view{
	width: 100%;
}
.xytcCon_f{
	margin:30rpx 40rpx 20rpx 40rpx;
	height:auto;
	overflow:hidden;
}
.xytcCon_f .ty{
	width: 100%;
	height: 100rpx;
	text-align:center;
	line-height: 100rpx;
	font-size:30rpx;
	color:#fff;
	background:#0ece92;
	border-radius: 10rpx;
}
.xytcCon_f .noty{
	margin-top: 10rpx;
	text-align:center;
	width: 100%;
	height:100rpx;
	font-size:28rpx;
	color:#8d8d8d;
	line-height: 100rpx;
}