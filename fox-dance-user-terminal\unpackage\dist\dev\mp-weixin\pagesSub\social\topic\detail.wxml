<view class="topic-detail-container data-v-5790f86e"><view class="header data-v-5790f86e"><view class="nav-bar data-v-5790f86e"><u-icon vue-id="cae2d350-1" name="arrow-left" size="20" color="#fff" data-event-opts="{{[['^click',[['goBack']]]]}}" bind:click="__e" class="data-v-5790f86e" bind:__l="__l"></u-icon><text class="nav-title data-v-5790f86e">话题详情</text><u-icon vue-id="cae2d350-2" name="more-dot-fill" size="20" color="#fff" data-event-opts="{{[['^click',[['showMore']]]]}}" bind:click="__e" class="data-v-5790f86e" bind:__l="__l"></u-icon></view></view><view class="topic-header data-v-5790f86e" style="{{'background-image:'+('url('+topicInfo.cover+')')+';'}}"><view class="topic-overlay data-v-5790f86e"><view class="topic-content data-v-5790f86e"><text class="topic-name data-v-5790f86e">{{"#"+topicInfo.name}}</text><text class="topic-desc data-v-5790f86e">{{topicInfo.description}}</text><view class="topic-stats data-v-5790f86e"><text class="stat-item data-v-5790f86e">{{topicInfo.postCount+"条帖子"}}</text><text class="stat-item data-v-5790f86e">{{topicInfo.followCount+"人关注"}}</text></view><view class="topic-actions data-v-5790f86e"><u-button vue-id="cae2d350-3" type="{{topicInfo.isFollowed?'default':'primary'}}" size="default" text="{{topicInfo.isFollowed?'已关注':'关注话题'}}" data-event-opts="{{[['^click',[['toggleFollow']]]]}}" bind:click="__e" class="data-v-5790f86e" bind:__l="__l" vue-slots="{{['default']}}">{{topicInfo.isFollowed?'已关注':'关注话题'}}</u-button><u-button vue-id="cae2d350-4" type="primary" plain="{{true}}" size="default" text="发布帖子" data-event-opts="{{[['^click',[['publishPost']]]]}}" bind:click="__e" class="data-v-5790f86e" bind:__l="__l" vue-slots="{{['default']}}">发布帖子</u-button></view></view></view></view><scroll-view class="posts-container data-v-5790f86e" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="filter-bar data-v-5790f86e"><u-tabs vue-id="cae2d350-5" list="{{filterTabs}}" current="{{selectedFilterIndex}}" is-scroll="{{false}}" active-color="#2979ff" inactive-color="#666" bar-width="{{40}}" bar-height="{{4}}" data-event-opts="{{[['^change',[['selectFilter']]]]}}" bind:change="__e" class="data-v-5790f86e" bind:__l="__l"></u-tabs></view><view class="posts-list data-v-5790f86e"><block wx:for="{{posts}}" wx:for-item="post" wx:for-index="__i0__" wx:key="id"><post-card vue-id="{{'cae2d350-6-'+__i0__}}" post="{{post}}" data-event-opts="{{[['^like',[['onPostLike']]],['^comment',[['onPostComment']]],['^share',[['onPostShare']]],['^click',[['goPostDetail']]]]}}" bind:like="__e" bind:comment="__e" bind:share="__e" bind:click="__e" class="data-v-5790f86e" bind:__l="__l"></post-card></block></view><block wx:if="{{hasMore}}"><view class="load-more data-v-5790f86e"><block wx:if="{{loading}}"><u-icon vue-id="cae2d350-7" name="loading" size="16" color="#999" class="data-v-5790f86e" bind:__l="__l"></u-icon></block><text class="load-text data-v-5790f86e">{{loading?'加载中...':'上拉加载更多'}}</text></view></block><block wx:if="{{$root.g0}}"><view class="no-more data-v-5790f86e"><text class="no-more-text data-v-5790f86e">没有更多帖子了</text></view></block><block wx:if="{{$root.g1}}"><view class="empty-state data-v-5790f86e"><u-icon vue-id="cae2d350-8" name="file-text" size="60" color="#ccc" class="data-v-5790f86e" bind:__l="__l"></u-icon><text class="empty-text data-v-5790f86e">暂无帖子</text><text class="empty-desc data-v-5790f86e">成为第一个发布帖子的人吧</text><u-button style="margin-top:20rpx;" vue-id="cae2d350-9" type="primary" size="default" text="发布帖子" data-event-opts="{{[['^click',[['publishPost']]]]}}" bind:click="__e" class="data-v-5790f86e" bind:__l="__l"></u-button></view></block></scroll-view></view>