<template>
  <view class="discover-container">
    <!-- 顶部搜索 -->
    <view class="header">
      <view class="search-bar" @click="goSearch">
        <u-icon name="search" size="18" color="#999"></u-icon>
        <text class="search-placeholder">搜索话题、用户...</text>
      </view>
    </view>

    <scroll-view class="content" scroll-y>
      <!-- 热门话题 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">热门话题</text>
          <text class="more-btn" @click="goTopicList">更多</text>
        </view>
        <view class="topic-grid">
          <view 
            v-for="topic in hotTopics" 
            :key="topic.id"
            class="topic-card"
            @click="goTopic(topic)"
          >
            <image :src="topic.cover" class="topic-cover" mode="aspectFill" />
            <view class="topic-info">
              <text class="topic-name">#{{ topic.name }}</text>
              <text class="topic-count">{{ topic.postCount }}条帖子</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 推荐用户 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">推荐关注</text>
        </view>
        <scroll-view class="user-scroll" scroll-x>
          <view class="user-list">
            <view 
              v-for="user in recommendUsers" 
              :key="user.id"
              class="user-card"
              @click="goUserProfile(user)"
            >
              <u-avatar :src="user.avatar" size="60"></u-avatar>
              <text class="user-name">{{ user.nickname }}</text>
              <text class="user-desc">{{ user.description }}</text>
              <FollowButton
                :user="user"
                :followed="user.isFollowed"
                size="mini"
                @follow="onUserFollow"
                @change="onFollowChange"
                @click.stop
              />
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 热门帖子 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">热门帖子</text>
        </view>
        <view class="hot-posts">
          <view 
            v-for="post in hotPosts" 
            :key="post.id"
            class="hot-post-item"
            @click="goPostDetail(post)"
          >
            <view class="post-content">
              <view class="user-info">
                <u-avatar :src="post.userAvatar" size="32"></u-avatar>
                <text class="username">{{ post.username }}</text>
              </view>
              <text class="post-text">{{ post.content }}</text>
              <view class="post-stats">
                <text class="stat-item">{{ post.likeCount }}赞</text>
                <text class="stat-item">{{ post.commentCount }}评论</text>
              </view>
            </view>
            <image 
              v-if="post.coverImage" 
              :src="post.coverImage" 
              class="post-cover"
              mode="aspectFill"
            />
          </view>
        </view>
      </view>

      <!-- 精选内容 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">精选内容</text>
        </view>
        <view class="featured-grid">
          <view 
            v-for="item in featuredContent" 
            :key="item.id"
            class="featured-item"
            @click="goFeaturedDetail(item)"
          >
            <image :src="item.cover" class="featured-cover" mode="aspectFill" />
            <view class="featured-overlay">
              <text class="featured-title">{{ item.title }}</text>
              <text class="featured-subtitle">{{ item.subtitle }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

  </view>
</template>

<script>
import FollowButton from '../components/FollowButton.vue'

export default {
  name: 'SocialDiscover',
  components: {
    FollowButton
  },
  data() {
    return {
      hotTopics: [],
      recommendUsers: [],
      hotPosts: [],
      featuredContent: []
    }
  },
  onLoad() {
    this.loadDiscoverData()
  },
  methods: {
    loadDiscoverData() {
      this.loadHotTopics()
      this.loadRecommendUsers()
      this.loadHotPosts()
      this.loadFeaturedContent()
    },

    loadHotTopics() {
      // 模拟热门话题数据
      this.hotTopics = [
        {
          id: 1,
          name: '今日穿搭',
          cover: 'https://picsum.photos/200/120?random=1',
          postCount: 1234
        },
        {
          id: 2,
          name: '美食分享',
          cover: 'https://picsum.photos/200/120?random=2',
          postCount: 856
        },
        {
          id: 3,
          name: '旅行日记',
          cover: 'https://picsum.photos/200/120?random=3',
          postCount: 642
        },
        {
          id: 4,
          name: '生活记录',
          cover: 'https://picsum.photos/200/120?random=4',
          postCount: 789
        }
      ]
    },

    loadRecommendUsers() {
      // 模拟推荐用户数据
      this.recommendUsers = [
        {
          id: 1,
          nickname: '小美',
          avatar: 'https://picsum.photos/100/100?random=10',
          description: '分享生活美好',
          isFollowed: false
        },
        {
          id: 2,
          nickname: '旅行达人',
          avatar: 'https://picsum.photos/100/100?random=11',
          description: '世界那么大',
          isFollowed: false
        },
        {
          id: 3,
          nickname: '美食家',
          avatar: 'https://picsum.photos/100/100?random=12',
          description: '发现美味',
          isFollowed: true
        },
        {
          id: 4,
          nickname: '摄影师',
          avatar: 'https://picsum.photos/100/100?random=13',
          description: '记录美好瞬间',
          isFollowed: false
        },
        {
          id: 5,
          nickname: '健身达人',
          avatar: 'https://picsum.photos/100/100?random=14',
          description: '健康生活方式',
          isFollowed: false
        },
        {
          id: 6,
          nickname: '音乐人',
          avatar: 'https://picsum.photos/100/100?random=15',
          description: '用音乐表达情感',
          isFollowed: true
        },
        {
          id: 7,
          nickname: '读书爱好者',
          avatar: 'https://picsum.photos/100/100?random=16',
          description: '书中自有黄金屋',
          isFollowed: false
        },
        {
          id: 8,
          nickname: '手工达人',
          avatar: 'https://picsum.photos/100/100?random=17',
          description: '手作温暖生活',
          isFollowed: false
        },
        {
          id: 9,
          nickname: '宠物博主',
          avatar: 'https://picsum.photos/100/100?random=18',
          description: '萌宠日常分享',
          isFollowed: true
        },
        {
          id: 10,
          nickname: '科技极客',
          avatar: 'https://picsum.photos/100/100?random=19',
          description: '探索科技前沿',
          isFollowed: false
        }
      ]
    },

    loadHotPosts() {
      // 模拟热门帖子数据
      this.hotPosts = [
        {
          id: 1,
          username: '时尚博主',
          userAvatar: 'https://picsum.photos/100/100?random=20',
          content: '今天的穿搭分享，简约而不简单的搭配技巧...',
          coverImage: 'https://picsum.photos/120/120?random=30',
          likeCount: 234,
          commentCount: 45
        },
        {
          id: 2,
          username: '美食探店',
          userAvatar: 'https://picsum.photos/100/100?random=21',
          content: '发现了一家超棒的咖啡店，环境和咖啡都很赞！',
          coverImage: 'https://picsum.photos/120/120?random=31',
          likeCount: 189,
          commentCount: 32
        }
      ]
    },

    loadFeaturedContent() {
      // 模拟精选内容数据
      this.featuredContent = [
        {
          id: 1,
          title: '春日穿搭指南',
          subtitle: '时尚达人教你搭配',
          cover: 'https://picsum.photos/300/200?random=40'
        },
        {
          id: 2,
          title: '周末好去处',
          subtitle: '城市探索攻略',
          cover: 'https://picsum.photos/300/200?random=41'
        }
      ]
    },

    onUserFollow(data) {
      console.log('关注操作:', data)
      // 这里可以调用API进行关注/取消关注操作
    },

    onFollowChange(data) {
      // 更新本地数据
      const user = this.recommendUsers.find(u => u.id === data.user.id)
      if (user) {
        user.isFollowed = data.isFollowed
      }
      console.log('关注状态变化:', data)
    },

    goSearch() {
      uni.navigateTo({
        url: '/pagesSub/social/search/index'
      })
    },

    goTopic(topic) {
      uni.navigateTo({
        url: `/pagesSub/social/topic/detail?id=${topic.id}`
      })
    },

    goTopicList() {
      uni.navigateTo({
        url: '/pagesSub/social/topic/list'
      })
    },

    goUserProfile(user) {
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${user.id}`
      })
    },



    goPostDetail(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      })
    },

    goFeaturedDetail(item) {
      uni.navigateTo({
        url: `/pagesSub/social/featured/detail?id=${item.id}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.discover-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 200rpx;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  padding: var(--status-bar-height) 32rpx 24rpx;
  border-bottom: 2rpx solid #e4e7ed;
}

.search-bar {
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
}

.search-placeholder {
  margin-left: 16rpx;
  color: #999;
  font-size: 28rpx;
}

.content {
  margin-top: calc(125rpx + var(--status-bar-height));
  padding: 0 32rpx;
  width: auto;
}

.section {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.more-btn {
  font-size: 28rpx;
  color: #2979ff;
}

.topic-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 23rpx;
}

.topic-card {
  width: calc(50% - 12rpx);
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.topic-cover {
  width: 100%;
  height: 160rpx;
}

.topic-info {
  padding: 24rpx;
}

.topic-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
}

.topic-count {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

.user-scroll {
  white-space: nowrap;
}

.user-list {
  display: flex;
  gap: 32rpx;
  padding-bottom: 16rpx;
}

.user-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  min-width: 200rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.user-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin: 16rpx 0 8rpx;
  text-align: center;
}

.user-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
  text-align: center;
}

.hot-posts {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.hot-post-item {
  display: flex;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.hot-post-item:last-child {
  border-bottom: none;
}

.post-content {
  flex: 1;
  margin-right: 24rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.username {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #666;
}

.post-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  display: block;
  margin-bottom: 16rpx;
}

.post-stats {
  display: flex;
  gap: 32rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999;
}

.post-cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
}

.featured-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.featured-item {
  position: relative;
  height: 240rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.featured-cover {
  width: 100%;
  height: 100%;
}

.featured-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 32rpx 32rpx;
}

.featured-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  display: block;
}

.featured-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8rpx;
  display: block;
}
</style>
