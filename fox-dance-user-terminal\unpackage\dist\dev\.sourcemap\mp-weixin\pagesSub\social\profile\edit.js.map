{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/edit.vue?11b6", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/edit.vue?3793", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/edit.vue?58a7", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/edit.vue?7de6", "uni-app:///pagesSub/social/profile/edit.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/edit.vue?8ff7", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/profile/edit.vue?f8de"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "userInfo", "userId", "nickname", "avatar", "bio", "danceType", "onLoad", "methods", "loadUserInfo", "goBack", "uni", "editAvatar", "count", "sizeType", "sourceType", "success", "saveProfile", "title", "icon", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAmuB,CAAgB,msBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoEvvB;EACAC;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IAAA,CACA;IAEAC;MACAC;IACA;IAEAC;MAAA;MACAD;QACAE;QACAC;QACAC;QACAC;UACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MACAN;QACAO;QACAC;MACA;MAEAC;QACAT;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtHA;AAAA;AAAA;AAAA;AAA84C,CAAgB,8wCAAG,EAAC,C;;;;;;;;;;;ACAl6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/profile/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/profile/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=10e92e6d&scoped=true&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&id=10e92e6d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"10e92e6d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/profile/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=10e92e6d&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-input/u-input\" */ \"@/components/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"edit-profile-container\">\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 头像编辑 -->\n      <view class=\"edit-section\">\n        <view class=\"edit-item\">\n          <text class=\"edit-label\">头像</text>\n          <view class=\"avatar-edit\" @click=\"editAvatar\">\n            <u-avatar :src=\"userInfo.avatar\" size=\"80\"></u-avatar>\n            <u-icon name=\"camera\" color=\"#999\" size=\"32rpx\" class=\"camera-icon\"></u-icon>\n          </view>\n        </view>\n      </view>\n\n      <!-- 基本信息编辑 -->\n      <view class=\"edit-section\">\n        <view class=\"edit-item\">\n          <text class=\"edit-label\">昵称</text>\n          <u-input \n            v-model=\"userInfo.nickname\" \n            placeholder=\"请输入昵称\"\n            border=\"none\"\n            class=\"edit-input\"\n          ></u-input>\n        </view>\n\n        <view class=\"edit-item\">\n          <text class=\"edit-label\">用户ID</text>\n          <u-input \n            v-model=\"userInfo.userId\" \n            placeholder=\"请输入用户ID\"\n            border=\"none\"\n            class=\"edit-input\"\n          ></u-input>\n        </view>\n\n        <view class=\"edit-item\">\n          <text class=\"edit-label\">舞种</text>\n          <u-input \n            v-model=\"userInfo.danceType\" \n            placeholder=\"请输入舞种\"\n            border=\"none\"\n            class=\"edit-input\"\n          ></u-input>\n        </view>\n\n        <view class=\"edit-item\">\n          <text class=\"edit-label\">个人简介</text>\n          <u-input \n            v-model=\"userInfo.bio\" \n            type=\"textarea\"\n            placeholder=\"请输入个人简介\"\n            border=\"none\"\n            class=\"edit-textarea\"\n            :autoHeight=\"true\"\n          ></u-input>\n        </view>\n      </view>\n\n      <!-- 保存按钮 -->\n      <view class=\"button-section\">\n        <u-button type=\"primary\" @click=\"saveProfile\" class=\"save-button\">保存</u-button>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'EditProfile',\n  data() {\n    return {\n      userInfo: {\n        userId: 'xiaoming_zhang',\n        nickname: '张小明',\n        avatar: '/static/images/toux.png',\n        bio: '美食爱好者 | 旅行达人 | 摄影师 | 生活方式博主',\n        danceType: '街舞'\n      }\n    }\n  },\n  onLoad() {\n    this.loadUserInfo()\n  },\n  methods: {\n    loadUserInfo() {\n      // 加载用户信息\n      // 这里应该调用API获取用户数据\n    },\n\n    goBack() {\n      uni.navigateBack()\n    },\n\n    editAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          // 上传头像\n          this.userInfo.avatar = res.tempFilePaths[0]\n        }\n      })\n    },\n\n    saveProfile() {\n      // 保存用户资料\n      uni.showToast({\n        title: '保存成功',\n        icon: 'success'\n      })\n      \n      setTimeout(() => {\n        uni.navigateBack()\n      }, 1500)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.edit-profile-container {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding-top: 30rpx;\n}\n\n.content {\n  flex: 1;\n}\n\n.edit-section {\n  background: #fff;\n  margin: 24rpx 32rpx;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.edit-item {\n  display: flex;\n  align-items: center;\n  padding: 32rpx 24rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.edit-item:last-child {\n  border-bottom: none;\n}\n\n.edit-label {\n  width: 160rpx;\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.avatar-edit {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.camera-icon {\n  position: absolute;\n  bottom: 0;\n  right: 0;\n  background: #fff;\n  border-radius: 50%;\n  padding: 8rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.edit-input {\n  flex: 1;\n  margin-left: 24rpx;\n}\n\n.edit-textarea {\n  flex: 1;\n  margin-left: 24rpx;\n  min-height: 120rpx;\n}\n\n.button-section {\n  padding: 40rpx 32rpx 80rpx;\n}\n\n.save-button {\n  width: 100%;\n  height: 88rpx;\n  border-radius: 44rpx;\n  font-size: 32rpx;\n  font-weight: 500;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=10e92e6d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=10e92e6d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752818685350\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}