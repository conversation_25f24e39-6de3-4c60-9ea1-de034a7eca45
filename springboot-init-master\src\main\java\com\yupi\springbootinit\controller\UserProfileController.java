package com.yupi.springbootinit.controller;

import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.model.dto.user.UserProfileUpdateRequest;
import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.model.entity.UserStats;
import com.yupi.springbootinit.model.vo.UserProfileVO;
import com.yupi.springbootinit.service.BaUserService;
import com.yupi.springbootinit.service.UserStatsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 用户资料接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/user")
@Slf4j
@Api(tags = "用户资料接口")
public class UserProfileController {

    @Resource
    private BaUserService baUserService;

    @Resource
    private UserStatsService userStatsService;

    /**
     * 获取用户详情
     */
    @GetMapping("/profile/{userId}")
    @ApiOperation(value = "获取用户详情")
    public BaseResponse<UserProfileVO> getUserProfile(@PathVariable Long userId) {
        if (userId == null || userId <= 0) {
            log.warn("获取用户详情请求参数错误 - userId无效: {}", userId);
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "无效的用户ID");
        }

        try {
            // 查询用户基本信息
            BaUser user = baUserService.getById(userId);
            if (user == null) {
                log.warn("用户不存在 - userId: {}", userId);
                return ResultUtils.error(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
            }

            // 查询用户统计信息
            UserStats userStats = userStatsService.getById(userId);
            if (userStats == null) {
                // 如果统计信息不存在，创建默认的
                userStats = new UserStats();
                userStats.setUserId(userId);
                userStats.setFollowingCount(0);
                userStats.setFollowerCount(0);
                userStats.setPostCount(0);
                userStats.setLikeReceivedCount(0);
                userStatsService.save(userStats);
            }

            // 构建返回对象
            UserProfileVO userProfileVO = new UserProfileVO();
            userProfileVO.setUserId(userId);
            userProfileVO.setUsername(user.getUsername());
            userProfileVO.setNickname(user.getNickname());
            userProfileVO.setAvatar(user.getAvatar());
            userProfileVO.setBio(user.getBio());
            userProfileVO.setDanceType(user.getDance_type());
            userProfileVO.setLevel(user.getLevel());
            userProfileVO.setFollowingCount(userStats.getFollowingCount());
            userProfileVO.setFollowerCount(userStats.getFollowerCount());
            userProfileVO.setPostCount(userStats.getPostCount());
            userProfileVO.setLikeReceivedCount(userStats.getLikeReceivedCount());

            log.info("获取用户详情成功 - userId: {}", userId);
            return ResultUtils.success(userProfileVO);

        } catch (Exception e) {
            log.error("获取用户详情失败 - userId: {}, error: {}", userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取用户详情失败");
        }
    }

    /**
     * 获取当前用户资料
     */
    @GetMapping("/profile")
    @ApiOperation(value = "获取当前用户资料")
    public BaseResponse<UserProfileVO> getCurrentUserProfile(HttpServletRequest request) {
        // TODO: 从session或token中获取当前用户ID
        // 这里暂时使用固定值，实际应该从认证信息中获取
        Long currentUserId = 1L; // 临时固定值
        
        return getUserProfile(currentUserId);
    }

    /**
     * 更新用户资料
     */
    @PutMapping("/profile")
    @ApiOperation(value = "更新用户资料")
    public BaseResponse<Boolean> updateUserProfile(@RequestBody UserProfileUpdateRequest updateRequest, 
                                                  HttpServletRequest request) {
        if (updateRequest == null) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "请求参数不能为空");
        }

        try {
            // TODO: 从session或token中获取当前用户ID
            Long currentUserId = 1L; // 临时固定值

            // 查询用户是否存在
            BaUser user = baUserService.getById(currentUserId);
            if (user == null) {
                return ResultUtils.error(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
            }

            // 更新用户信息
            if (updateRequest.getNickname() != null) {
                user.setNickname(updateRequest.getNickname());
            }
            if (updateRequest.getBio() != null) {
                user.setBio(updateRequest.getBio());
            }
            if (updateRequest.getDanceType() != null) {
                user.setDance_type(updateRequest.getDanceType());
            }
            if (updateRequest.getAvatar() != null) {
                user.setAvatar(updateRequest.getAvatar());
            }

            boolean result = baUserService.updateById(user);
            if (result) {
                log.info("更新用户资料成功 - userId: {}", currentUserId);
                return ResultUtils.success(true);
            } else {
                log.warn("更新用户资料失败 - userId: {}", currentUserId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "更新用户资料失败");
            }

        } catch (Exception e) {
            log.error("更新用户资料异常 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "更新用户资料失败");
        }
    }

    /**
     * 获取用户统计数据
     */
    @GetMapping("/stats/{userId}")
    @ApiOperation(value = "获取用户统计数据")
    public BaseResponse<UserStats> getUserStats(@PathVariable Long userId) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "无效的用户ID");
        }

        try {
            UserStats userStats = userStatsService.getById(userId);
            if (userStats == null) {
                // 如果统计信息不存在，返回默认值
                userStats = new UserStats();
                userStats.setUserId(userId);
                userStats.setFollowingCount(0);
                userStats.setFollowerCount(0);
                userStats.setPostCount(0);
                userStats.setLikeReceivedCount(0);
            }

            return ResultUtils.success(userStats);

        } catch (Exception e) {
            log.error("获取用户统计数据失败 - userId: {}, error: {}", userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取用户统计数据失败");
        }
    }
}
