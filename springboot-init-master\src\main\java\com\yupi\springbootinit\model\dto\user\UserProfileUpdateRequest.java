package com.yupi.springbootinit.model.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户资料更新请求
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@ApiModel(description = "用户资料更新请求")
public class UserProfileUpdateRequest implements Serializable {

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称", example = "张小明")
    private String nickname;

    /**
     * 个人简介
     */
    @ApiModelProperty(value = "个人简介", example = "热爱生活，喜欢分享")
    private String bio;

    /**
     * 舞种
     */
    @ApiModelProperty(value = "舞种", example = "街舞")
    private String danceType;

    /**
     * 头像URL
     */
    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    private static final long serialVersionUID = 1L;
}
