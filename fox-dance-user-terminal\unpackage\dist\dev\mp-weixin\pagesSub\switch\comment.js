(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesSub/switch/comment"],{

/***/ 650:
/*!*************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/main.js?{"page":"pagesSub%2Fswitch%2Fcomment"} ***!
  \*************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _comment = _interopRequireDefault(__webpack_require__(/*! ./pagesSub/switch/comment.vue */ 651));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_comment.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 651:
/*!******************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue ***!
  \******************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _comment_vue_vue_type_template_id_7270b30e_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./comment.vue?vue&type=template&id=7270b30e&scoped=true& */ 652);
/* harmony import */ var _comment_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./comment.vue?vue&type=script&lang=js& */ 654);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _comment_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _comment_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _comment_vue_vue_type_style_index_0_id_7270b30e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./comment.vue?vue&type=style&index=0&id=7270b30e&lang=scss&scoped=true& */ 664);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 73);

var renderjs





/* normalize component */

var component = Object(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _comment_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _comment_vue_vue_type_template_id_7270b30e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _comment_vue_vue_type_template_id_7270b30e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "7270b30e",
  null,
  false,
  _comment_vue_vue_type_template_id_7270b30e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesSub/switch/comment.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 652:
/*!*************************************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?vue&type=template&id=7270b30e&scoped=true& ***!
  \*************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_template_id_7270b30e_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=template&id=7270b30e&scoped=true& */ 653);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_template_id_7270b30e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_template_id_7270b30e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_template_id_7270b30e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_template_id_7270b30e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 653:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?vue&type=template&id=7270b30e&scoped=true& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uLoading: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-loading/u-loading */ "components/uview-ui/components/u-loading/u-loading").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-loading/u-loading.vue */ 973))
    },
    uIcon: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-icon/u-icon */ "components/uview-ui/components/u-icon/u-icon").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-icon/u-icon.vue */ 870))
    },
    uPopup: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-popup/u-popup */ "components/uview-ui/components/u-popup/u-popup").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-popup/u-popup.vue */ 884))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 =
    _vm.topicInfo &&
    _vm.topicInfo.topicImages &&
    _vm.topicInfo.topicImages.length > 0
  var g1 = g0 ? _vm.topicInfo.topicImages.length : null
  var l0 = g0
    ? _vm.__map(_vm.topicInfo.topicImages, function (image, index) {
        var $orig = _vm.__get_orig(image)
        var m0 = _vm.processImageUrl(image)
        return {
          $orig: $orig,
          m0: m0,
        }
      })
    : null
  var m1 =
    _vm.topicInfo && _vm.topicInfo.createTime
      ? _vm.formatTime(_vm.topicInfo.createTime)
      : null
  var m2 = _vm.getCurrentFilterTotal()
  var g2 =
    _vm.activeFilter === "hot" && !(_vm.loading && _vm.activeFilter === "hot")
      ? _vm.commentListHot.length
      : null
  var l2 =
    _vm.activeFilter === "hot" &&
    !(_vm.loading && _vm.activeFilter === "hot") &&
    !(g2 == 0)
      ? _vm.__map(_vm.commentListHot, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m3 = _vm.processImageUrl(item.user.avatar)
          var m4 =
            item.user.level >= 0 ? _vm.getLevelColor(item.user.level) : null
          var m5 = _vm.formatTime(item.created_at)
          var g3 = !item.showFullContent ? item.content.length : null
          var g4 =
            !item.showFullContent && g3 > 100
              ? item.content.slice(0, 100)
              : null
          var g5 = item.content.length
          var g6 = item.replies && item.replies.length > 0
          var l1 = g6
            ? _vm.__map(item.replies.slice(0, 2), function (reply, rIndex) {
                var $orig = _vm.__get_orig(reply)
                var g7 = reply.content.length
                var g8 = g7 > 50 ? reply.content.slice(0, 50) : null
                return {
                  $orig: $orig,
                  g7: g7,
                  g8: g8,
                }
              })
            : null
          return {
            $orig: $orig,
            m3: m3,
            m4: m4,
            m5: m5,
            g3: g3,
            g4: g4,
            g5: g5,
            g6: g6,
            l1: l1,
          }
        })
      : null
  var g9 =
    _vm.activeFilter === "hot" &&
    !(_vm.loading && _vm.activeFilter === "hot") &&
    !(g2 == 0) &&
    !_vm.pagination.hot.loading
      ? !_vm.pagination.hot.hasMore && _vm.commentListHot.length > 0
      : null
  var g10 =
    _vm.activeFilter === "new" && !(_vm.loading && _vm.activeFilter === "new")
      ? _vm.commentListNew.length
      : null
  var l4 =
    _vm.activeFilter === "new" &&
    !(_vm.loading && _vm.activeFilter === "new") &&
    !(g10 == 0)
      ? _vm.__map(_vm.commentListNew, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m6 = _vm.processImageUrl(item.user.avatar)
          var m7 =
            item.user.level >= 0 ? _vm.getLevelColor(item.user.level) : null
          var m8 = _vm.formatTime(item.created_at)
          var g11 = !item.showFullContent ? item.content.length : null
          var g12 =
            !item.showFullContent && g11 > 100
              ? item.content.slice(0, 100)
              : null
          var g13 = item.content.length
          var g14 = item.replies && item.replies.length > 0
          var l3 = g14
            ? _vm.__map(item.replies.slice(0, 2), function (reply, rIndex) {
                var $orig = _vm.__get_orig(reply)
                var g15 = reply.content.length
                var g16 = g15 > 50 ? reply.content.slice(0, 50) : null
                return {
                  $orig: $orig,
                  g15: g15,
                  g16: g16,
                }
              })
            : null
          return {
            $orig: $orig,
            m6: m6,
            m7: m7,
            m8: m8,
            g11: g11,
            g12: g12,
            g13: g13,
            g14: g14,
            l3: l3,
          }
        })
      : null
  var g17 =
    _vm.activeFilter === "new" &&
    !(_vm.loading && _vm.activeFilter === "new") &&
    !(g10 == 0) &&
    !_vm.pagination.new.loading
      ? !_vm.pagination.new.hasMore && _vm.commentListNew.length > 0
      : null
  var g18 =
    _vm.activeFilter === "my" && !(_vm.loading && _vm.activeFilter === "my")
      ? _vm.commentListMy.length == 0 || _vm.commentListMy === null
      : null
  var l6 =
    _vm.activeFilter === "my" &&
    !(_vm.loading && _vm.activeFilter === "my") &&
    !g18
      ? _vm.__map(_vm.commentListMy, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m9 = _vm.processImageUrl(item.user.avatar)
          var m10 =
            item.user.level >= 0 ? _vm.getLevelColor(item.user.level) : null
          var m11 = _vm.formatTime(item.created_at)
          var g19 = !item.showFullContent ? item.content.length : null
          var g20 =
            !item.showFullContent && g19 > 100
              ? item.content.slice(0, 100)
              : null
          var g21 = item.content.length
          var g22 = item.replies && item.replies.length > 0
          var l5 = g22
            ? _vm.__map(item.replies.slice(0, 2), function (reply, rIndex) {
                var $orig = _vm.__get_orig(reply)
                var g23 = reply.content.length
                var g24 = g23 > 50 ? reply.content.slice(0, 50) : null
                return {
                  $orig: $orig,
                  g23: g23,
                  g24: g24,
                }
              })
            : null
          return {
            $orig: $orig,
            m9: m9,
            m10: m10,
            m11: m11,
            g19: g19,
            g20: g20,
            g21: g21,
            g22: g22,
            l5: l5,
          }
        })
      : null
  var g25 =
    _vm.activeFilter === "my" &&
    !(_vm.loading && _vm.activeFilter === "my") &&
    !g18 &&
    !_vm.pagination.my.loading
      ? !_vm.pagination.my.hasMore && _vm.commentListMy.length > 0
      : null
  var m12 = _vm.isCommentOwner(_vm.currentMoreComment)
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        l0: l0,
        m1: m1,
        m2: m2,
        g2: g2,
        l2: l2,
        g9: g9,
        g10: g10,
        l4: l4,
        g17: g17,
        g18: g18,
        l6: l6,
        g25: g25,
        m12: m12,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 654:
/*!*******************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=script&lang=js& */ 655);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 655:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 83));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 85));
var _commentApi = _interopRequireDefault(__webpack_require__(/*! @/config/comment.api.js */ 656));
var _topicApi = _interopRequireDefault(__webpack_require__(/*! @/config/topic.api.js */ 658));
var _store = __webpack_require__(/*! @/config/store */ 660);
var _dayjs = _interopRequireDefault(__webpack_require__(/*! dayjs */ 661));
var _relativeTime = _interopRequireDefault(__webpack_require__(/*! dayjs/plugin/relativeTime */ 662));
__webpack_require__(/*! dayjs/locale/zh-cn */ 663);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var CommentInput = function CommentInput() {
  __webpack_require__.e(/*! require.ensure | pagesSub/switch/components/CommentInput */ "pagesSub/switch/components/CommentInput").then((function () {
    return resolve(__webpack_require__(/*! ../../../../用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue */ 980));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var CommentSkeleton = function CommentSkeleton() {
  __webpack_require__.e(/*! require.ensure | pagesSub/switch/components/CommentSkeleton */ "pagesSub/switch/components/CommentSkeleton").then((function () {
    return resolve(__webpack_require__(/*! ./components/CommentSkeleton.vue */ 987));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
_dayjs.default.extend(_relativeTime.default);
_dayjs.default.locale('zh-cn');
var _default = {
  components: {
    CommentInput: CommentInput,
    CommentSkeleton: CommentSkeleton
  },
  data: function data() {
    return {
      activeFilter: 'hot',
      commentList: [],
      commentText: '',
      loading: true,
      loadingMore: false,
      // 加载更多状态
      isRefreshing: false,
      page: 1,
      limit: 10,
      hasMore: true,
      showMorePopup: false,
      // 更多操作弹窗
      currentMoreComment: null,
      // 当前操作的评论
      // 回复状态管理
      isReplyMode: false,
      // 是否处于回复模式
      currentReply: null,
      // 当前被回复的评论
      inputPlaceholder: '说点什么...',
      // 输入框提示文字
      pageHeight: 'calc(100vh - 120rpx)',
      // 页面滚动容器高度
      contentId: '',
      // 内容ID
      contentType: '',
      // 内容类型
      topicId: '',
      // 话题ID
      storeId: '',
      // 店铺ID
      storeName: '',
      // 店铺名称
      storeImage: '',
      userId: '',
      // 用户ID
      topicInfo: null,
      // 话题信息（包含图片）
      storeInfo: null,
      // 店铺信息
      keyboardHeight: 0,
      // 键盘高度
      inputContainerBottom: 0,
      // 输入框容器底部距离
      isKeyboardShow: false,
      // 键盘是否显示
      totalComments: 0,
      // 评论总数
      // 各筛选条件的真实总数
      commentStats: {
        hotTotal: 0,
        // 热门评论总数
        newTotal: 0,
        // 最新评论总数
        myTotal: 0 // 我的评论总数
      },

      commentListHot: [],
      commentListNew: [],
      commentListMy: [],
      // 分页相关数据
      pagination: {
        hot: {
          page: 1,
          pageSize: 10,
          hasMore: true,
          loading: false
        },
        new: {
          page: 1,
          pageSize: 10,
          hasMore: true,
          loading: false
        },
        my: {
          page: 1,
          pageSize: 10,
          hasMore: true,
          loading: false
        }
      },
      isLoadingMore: false,
      // 是否正在加载更多
      loadingText: '加载中...',
      // 加载提示文本
      scrollTop: 0,
      // scroll-view的滚动位置
      scrollIntoView: '' // scroll-view的滚动到指定元素
    };
  },
  onLoad: function onLoad(options) {
    // 获取页面参数
    this.contentId = options.content_id || '';
    this.contentType = options.content_type || '';

    // 修复：将topicId转换为Long类型（使用Number确保兼容性）
    this.topicId = options.topicId ? Number(options.topicId) : null;

    // 新增：店铺相关参数
    this.storeId = options.storeId ? Number(options.storeId) : null;
    this.storeName = options.storeName ? decodeURIComponent(options.storeName) : null;
    this.storeImage = options.storeImage || null;

    // 如果没有传入内容ID或类型，使用默认值
    if (!this.contentId) this.contentId = 'default_content';
    if (!this.contentType) this.contentType = 'video';

    // 修复：将userId转换为Long类型（使用Number确保兼容性）
    var userIdStr = uni.getStorageSync('userid') || '222';
    this.userId = Number(userIdStr);
    console.log('📱 评论页面参数:', {
      contentId: this.contentId,
      contentType: this.contentType,
      topicId: this.topicId,
      storeId: this.storeId,
      storeName: this.storeName,
      userId: this.userId
    });

    // 如果是话题页面，获取话题信息
    if (this.topicId) {
      this.fetchTopicInfo();
    }

    // 如果是店铺页面，设置店铺信息
    if (this.storeId && this.storeName) {
      this.setupStoreInfo();
    }

    // 获取评论统计信息
    this.fetchCommentStats();

    // 获取各标签的评论列表
    this.fetchComments();
    this.setPageHeight();

    // 设置键盘监听
    this.setupKeyboardListener();

    // 将调试方法挂载到全局，方便控制台调用
  },
  // 在页面显示时启用键盘监听
  onShow: function onShow() {
    // 确保只有一个有效的键盘监听
    uni.offKeyboardHeightChange(); // 先移除可能存在的监听
    this.setupKeyboardListener();
  },
  // 在页面隐藏时取消键盘监听
  onHide: function onHide() {
    // 取消监听键盘高度变化
    uni.offKeyboardHeightChange();
    console.log('页面隐藏，取消键盘高度监听');
  },
  // 在页面卸载时取消键盘监听
  onUnload: function onUnload() {
    // 取消监听键盘高度变化
    uni.offKeyboardHeightChange();
    console.log('页面卸载，取消键盘高度监听');
  },
  methods: {
    // 兼容性时间戳函数 - 替代performance.now()
    getTimestamp: function getTimestamp() {
      // 微信小程序环境使用Date.now()
      if (typeof performance !== 'undefined' && performance.now) {
        return performance.now();
      }
      return Date.now();
    },
    /**
     * 设置店铺信息
     */
    setupStoreInfo: function setupStoreInfo() {
      console.log('🏪 设置店铺信息 - storeId:', this.storeId, 'storeName:', this.storeName, 'storeImage:', this.storeImage);

      // 构建店铺信息对象
      this.storeInfo = {
        id: this.storeId,
        name: this.storeName,
        title: "".concat(this.storeName, "\u627E\u642D\u5B50"),
        description: '快来寻找你的搭子吧！',
        storeImage: this.storeImage,
        commentUserCount: 0,
        // 初始值，后续会通过API更新
        createTime: new Date().toISOString()
      };
      console.log('🏪 店铺信息设置完成:', this.storeInfo);

      // 设置页面标题
      uni.setNavigationBarTitle({
        title: this.storeInfo.title
      });

      // 加载店铺评论
      this.fetchComments();
    },
    /**
     * 获取店铺评论列表
     */
    fetchStoreComments: function fetchStoreComments(storeId, userId, filter, current, pageSize) {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var _this$$config, _this$$config$apis, baseUrl, url, params, response;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                console.log('🏪 调用店铺评论API - storeId:', storeId, 'filter:', filter);

                // 构建请求URL和参数
                baseUrl = ((_this$$config = _this.$config) === null || _this$$config === void 0 ? void 0 : (_this$$config$apis = _this$$config.apis) === null || _this$$config$apis === void 0 ? void 0 : _this$$config$apis.vote_baseUrl) || 'https://vote.foxdance.com.cn';
                url = "".concat(baseUrl, "/api/comments/store/").concat(storeId);
                params = {
                  userId: userId,
                  filter: filter,
                  current: current,
                  pageSize: pageSize
                };
                console.log('🏪 店铺评论请求URL:', url);
                console.log('🏪 店铺评论请求参数:', params);

                // 发送请求
                _context.next = 9;
                return new Promise(function (resolve, reject) {
                  uni.request({
                    url: url,
                    method: 'GET',
                    data: params,
                    header: {
                      'Content-Type': 'application/json',
                      'bausertoken': uni.getStorageSync('bausertoken') || uni.getStorageSync('token')
                    },
                    success: function success(res) {
                      console.log('🏪 店铺评论API响应:', res);
                      if (res.statusCode === 200) {
                        resolve(res.data);
                      } else {
                        var _res$data;
                        reject(new Error("HTTP ".concat(res.statusCode, ": ").concat(((_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.message) || '请求失败')));
                      }
                    },
                    fail: function fail(err) {
                      console.error('🏪 店铺评论API请求失败:', err);
                      reject(err);
                    }
                  });
                });
              case 9:
                response = _context.sent;
                return _context.abrupt("return", response);
              case 13:
                _context.prev = 13;
                _context.t0 = _context["catch"](0);
                console.error('🏪 获取店铺评论失败:', _context.t0);
                throw _context.t0;
              case 17:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 13]]);
      }))();
    },
    // 获取当前筛选类型下的评论数量（已加载的数量）
    getCurrentCommentCount: function getCurrentCommentCount() {
      switch (this.activeFilter) {
        case 'hot':
          return this.commentListHot.length;
        case 'new':
          return this.commentListNew.length;
        case 'my':
          return this.commentListMy.length;
        default:
          return 0;
      }
    },
    // 获取当前筛选条件对应的真实总数
    getCurrentFilterTotal: function getCurrentFilterTotal() {
      switch (this.activeFilter) {
        case 'hot':
          return this.commentStats.hotTotal || 0;
        case 'new':
          return this.commentStats.newTotal || 0;
        case 'my':
          return this.commentStats.myTotal || 0;
        default:
          return 0;
      }
    },
    // 设置键盘高度监听器
    setupKeyboardListener: function setupKeyboardListener() {
      var _this2 = this;
      uni.onKeyboardHeightChange(function (res) {
        console.log('🎹 键盘高度变化:', res.height);
        console.log('📱 回复弹窗状态:', _this2.showReplyPopup);
        console.log('🎯 回复输入框焦点状态:', _this2.isReplyInputFocused);
        _this2.keyboardHeight = res.height;
        _this2.isKeyboardShow = res.height > 0;
        if (res.height > 0) {
          // 键盘弹出，调整主输入框位置
          _this2.inputContainerBottom = res.height;
          console.log('🔧 调整主输入框位置:', _this2.inputContainerBottom);
          if (_this2.isReplyMode) {
            console.log('� 当前处于回复模式');
          }
        } else {
          // 键盘收起，恢复主输入框位置
          _this2.inputContainerBottom = 0;
          console.log('📥 键盘收起，重置主输入框位置');
        }
      });
      console.log('键盘高度监听器已设置');
    },
    // 判断当前用户是否是评论所有者
    isCommentOwner: function isCommentOwner(comment) {
      if (!comment || !comment.user) return false;
      return String(comment.user.id) == String(this.userId);
    },
    onInputFocus: function onInputFocus(e) {
      var _this3 = this;
      // 主输入框获取焦点，键盘弹出

      this.isKeyboardShow = true;

      // 微信小程序中，键盘弹出时的额外处理

      // 延时获取键盘高度，因为键盘弹出需要时间
      setTimeout(function () {
        if (_this3.keyboardHeight === 0) {
          // 如果监听器没有获取到键盘高度，使用默认值
          _this3.keyboardHeight = 280; // 微信小程序默认键盘高度
          _this3.inputContainerBottom = _this3.keyboardHeight;
        }
      }, 300);
    },
    onInputBlur: function onInputBlur() {
      var _this4 = this;
      // 输入框失去焦点，键盘收起
      this.isKeyboardShow = false;

      // 延时重置，确保键盘完全收起
      setTimeout(function () {
        if (!_this4.isKeyboardShow) {
          _this4.keyboardHeight = 0;
          _this4.inputContainerBottom = 0;
        }
      }, 100);
    },
    // 聚焦输入框
    focusInput: function focusInput() {
      if (this.$refs.mainCommentInput) {
        this.$refs.mainCommentInput.focus();
      }
    },
    // 隐藏蒙版层并收起键盘
    hideMaskAndKeyboard: function hideMaskAndKeyboard() {
      console.log('点击蒙版层，收起键盘');

      // 检查是否处于回复模式，如果是则取消回复
      if (this.isReplyMode) {
        console.log('🚫 检测到回复模式，取消回复');
        this.cancelReplyMode();
      }

      // 让输入框失去焦点
      if (this.$refs.mainCommentInput) {
        this.$refs.mainCommentInput.blur();
      }

      // 强制隐藏键盘
      uni.hideKeyboard();

      // 重置键盘状态
      this.isKeyboardShow = false;
      this.keyboardHeight = 0;
      this.inputContainerBottom = 0;
    },
    // 回复弹窗相关方法已移除，改为使用底部主输入框进行回复
    setPageHeight: function setPageHeight() {
      var inputBoxHeight = 120; // 底部输入框高度（rpx）

      // 计算页面滚动容器高度，减去输入框高度
      var pageHeight = "calc(100vh - ".concat(inputBoxHeight, "rpx)");
      this.pageHeight = pageHeight;
    },
    goBack: function goBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    changeFilter: function changeFilter(type) {
      if (this.activeFilter === type) return;
      this.activeFilter = type;

      // 如果该类型的评论列表为空，则加载数据
      if (type === 'hot' && this.commentListHot.length === 0) {
        this.fetchCommentsByType('hot');
      } else if (type === 'new' && this.commentListNew.length === 0) {
        this.fetchCommentsByType('new');
      } else if (type === 'my' && this.commentListMy.length === 0) {
        this.fetchCommentsByType('my');
      }
    },
    // 获取所有类型的评论
    fetchComments: function fetchComments() {
      // 初始化加载热门评论
      this.fetchCommentsByType('hot');
    },
    // 获取评论统计信息
    fetchCommentStats: function fetchCommentStats() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var res, _res;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                if (!(!_this5.topicId && !_this5.storeId || !_this5.userId)) {
                  _context2.next = 3;
                  break;
                }
                console.warn('⚠️ 缺少必要参数，无法获取评论统计');
                return _context2.abrupt("return");
              case 3:
                _context2.prev = 3;
                console.log('🔢 开始获取评论统计信息...');
                if (!_this5.topicId) {
                  _context2.next = 11;
                  break;
                }
                _context2.next = 8;
                return _topicApi.default.getTopicCommentStats(_this5.topicId, _this5.userId);
              case 8:
                res = _context2.sent;
                _context2.next = 15;
                break;
              case 11:
                if (!_this5.storeId) {
                  _context2.next = 15;
                  break;
                }
                _context2.next = 14;
                return _this5.fetchStoreCommentStats(_this5.storeId, _this5.userId);
              case 14:
                res = _context2.sent;
              case 15:
                if (res && res.code === 0) {
                  _this5.commentStats = res.data;
                  console.log('🔢 评论统计获取成功:', _this5.commentStats);
                } else {
                  console.error('❌ 获取评论统计失败:', (_res = res) === null || _res === void 0 ? void 0 : _res.message);
                  // 使用默认值
                  _this5.commentStats = {
                    hotTotal: 0,
                    newTotal: 0,
                    myTotal: 0
                  };
                }
                _context2.next = 22;
                break;
              case 18:
                _context2.prev = 18;
                _context2.t0 = _context2["catch"](3);
                console.error('❌ 获取评论统计异常:', _context2.t0);
                // 使用默认值
                _this5.commentStats = {
                  hotTotal: 0,
                  newTotal: 0,
                  myTotal: 0
                };
              case 22:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[3, 18]]);
      }))();
    },
    /**
     * 获取店铺评论统计信息
     */
    fetchStoreCommentStats: function fetchStoreCommentStats(storeId, userId) {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var _this6$$config, _this6$$config$apis, baseUrl, url, params, response;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                console.log('🏪 调用店铺评论统计API - storeId:', storeId);
                baseUrl = ((_this6$$config = _this6.$config) === null || _this6$$config === void 0 ? void 0 : (_this6$$config$apis = _this6$$config.apis) === null || _this6$$config$apis === void 0 ? void 0 : _this6$$config$apis.vote_baseUrl) || 'https://vote.foxdance.com.cn';
                url = "".concat(baseUrl, "/api/comments/store/").concat(storeId, "/stats");
                params = {
                  userId: userId
                };
                console.log('🏪 店铺评论统计请求URL:', url);
                console.log('🏪 店铺评论统计请求参数:', params);
                _context3.next = 9;
                return new Promise(function (resolve, reject) {
                  uni.request({
                    url: url,
                    method: 'GET',
                    data: params,
                    header: {
                      'Content-Type': 'application/json',
                      'bausertoken': uni.getStorageSync('bausertoken') || uni.getStorageSync('token')
                    },
                    success: function success(res) {
                      console.log('🏪 店铺评论统计API响应:', res);
                      if (res.statusCode === 200) {
                        resolve(res.data);
                      } else {
                        var _res$data2;
                        reject(new Error("HTTP ".concat(res.statusCode, ": ").concat(((_res$data2 = res.data) === null || _res$data2 === void 0 ? void 0 : _res$data2.message) || '请求失败')));
                      }
                    },
                    fail: function fail(err) {
                      console.error('🏪 店铺评论统计API请求失败:', err);
                      reject(err);
                    }
                  });
                });
              case 9:
                response = _context3.sent;
                return _context3.abrupt("return", response);
              case 13:
                _context3.prev = 13;
                _context3.t0 = _context3["catch"](0);
                console.error('🏪 获取店铺评论统计失败:', _context3.t0);
                throw _context3.t0;
              case 17:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 13]]);
      }))();
    },
    // 根据类型获取评论
    fetchCommentsByType: function fetchCommentsByType(type) {
      var _this7 = this;
      console.log("\u8BF7\u6C42".concat(type, "\u8BC4\u8BBA\u5217\u8868"));
      this.loading = true;

      // 重置分页状态
      this.pagination[type] = {
        page: 1,
        pageSize: 10,
        hasMore: true,
        loading: false
      };

      // 准备分页参数 - 修复参数名和数据类型
      var startTime = this.getTimestamp();
      var params = {
        userId: Number(this.userId),
        // 使用Number确保Long兼容性
        contentId: this.contentId,
        contentType: this.contentType,
        filter: type,
        current: 1,
        // 修复：使用current而不是page
        pageSize: this.pagination[type].pageSize
      };
      console.log("\uD83D\uDE80 \u5F00\u59CB\u8BF7\u6C42".concat(type, "\u8BC4\u8BBA\u5217\u8868\uFF0C\u53C2\u6570:"), params);

      // 判断是否为话题类型的评论
      if (this.topicId) {
        console.log("\uD83C\uDFAF \u68C0\u6D4B\u5230topicId: ".concat(this.topicId, "\uFF0C\u4F7F\u7528\u8BDD\u9898API\u83B7\u53D6\u8BC4\u8BBA"));
        console.log("\uD83D\uDCCB \u8BDD\u9898API\u8C03\u7528\u53C2\u6570:", {
          topicId: Number(this.topicId),
          userId: Number(this.userId),
          filter: type,
          current: params.current,
          pageSize: params.pageSize
        });

        // 使用话题API获取评论 - 修复数据类型为Long兼容
        _topicApi.default.getTopicComments(Number(this.topicId), Number(this.userId), type, params.current, params.pageSize).then(function (res) {
          var endTime = _this7.getTimestamp();
          var loadTime = endTime - startTime;
          console.log("\u2705 \u8BDD\u9898".concat(type, "\u8BC4\u8BBA\u5217\u8868API\u8FD4\u56DE\uFF0C\u8017\u65F6: ").concat(loadTime.toFixed(2), "ms"));
          if (res.code === 0) {
            var data = res.data;

            // 性能优化：减少JSON序列化
            console.log("\uD83D\uDCCA \u8BDD\u9898".concat(type, "\u8BC4\u8BBA\u5217\u8868\u6570\u636E\u6982\u89C8:"), {
              total: data.total,
              commentsCount: data.comments ? data.comments.length : 0,
              hasMore: data.hasMore,
              current: data.current,
              pages: data.pages
            });

            // 处理评论数据（优化版）
            var processedData = _this7.processCommentDataOptimized(data);

            // 根据类型更新对应的评论列表
            switch (type) {
              case 'hot':
                _this7.commentListHot = processedData;
                break;
              case 'new':
                _this7.commentListNew = processedData;
                break;
              case 'my':
                _this7.commentListMy = processedData;
                break;
            }

            // 更新总评论数和分页信息
            _this7.totalComments = data && data.total || 0;
            _this7.pagination[type].hasMore = data.hasMore !== false;
            console.log("\uD83C\uDFAF \u8BDD\u9898".concat(type, "\u8BC4\u8BBA\u52A0\u8F7D\u5B8C\u6210\uFF0C\u603B\u6570: ").concat(_this7.totalComments, ", \u5F53\u524D\u663E\u793A: ").concat(processedData.length));
          } else {
            // 请求失败，显示错误信息
            _this7.handleApiError(type, res.message || '获取评论失败');
          }
        }).catch(function (err) {
          console.error("\u83B7\u53D6".concat(type, "\u8BC4\u8BBA\u5217\u8868\u5931\u8D25:"), err);
          _this7.handleApiError(type, '网络请求错误');
        }).finally(function () {
          _this7.loading = false;
          _this7.isRefreshing = false;
        });
      } else if (this.storeId) {
        console.log("\uD83C\uDFEA \u68C0\u6D4B\u5230storeId: ".concat(this.storeId, "\uFF0C\u4F7F\u7528\u5E97\u94FAAPI\u83B7\u53D6\u8BC4\u8BBA"));
        console.log("\uD83D\uDCCB \u5E97\u94FAAPI\u8C03\u7528\u53C2\u6570:", {
          storeId: Number(this.storeId),
          userId: Number(this.userId),
          filter: type,
          current: params.current,
          pageSize: params.pageSize
        });

        // 使用店铺API获取评论
        this.fetchStoreComments(Number(this.storeId), Number(this.userId), type, params.current, params.pageSize).then(function (res) {
          var endTime = _this7.getTimestamp();
          var loadTime = endTime - startTime;
          console.log("\u2705 \u5E97\u94FA".concat(type, "\u8BC4\u8BBA\u5217\u8868API\u8FD4\u56DE\uFF0C\u8017\u65F6: ").concat(loadTime.toFixed(2), "ms"));
          if (res.code === 0) {
            var data = res.data;
            console.log("\uD83D\uDCCA \u5E97\u94FA".concat(type, "\u8BC4\u8BBA\u5217\u8868\u6570\u636E\u6982\u89C8:"), {
              total: data.total,
              commentsCount: data.comments ? data.comments.length : 0,
              hasMore: data.hasMore,
              current: data.current,
              pages: data.pages
            });

            // 处理评论数据
            var processedData = _this7.processCommentDataOptimized(data);

            // 根据类型更新对应的评论列表
            switch (type) {
              case 'hot':
                _this7.commentListHot = processedData;
                break;
              case 'new':
                _this7.commentListNew = processedData;
                break;
              case 'my':
                _this7.commentListMy = processedData;
                break;
            }

            // 更新总评论数和分页信息
            _this7.totalComments = data && data.total || 0;
            _this7.pagination[type].hasMore = data.hasMore !== false;
            console.log("\uD83C\uDFAF \u5E97\u94FA".concat(type, "\u8BC4\u8BBA\u52A0\u8F7D\u5B8C\u6210\uFF0C\u603B\u6570: ").concat(_this7.totalComments, ", \u5F53\u524D\u663E\u793A: ").concat(processedData.length));
          } else {
            _this7.handleApiError(type, res.message || '获取店铺评论失败');
          }
        }).catch(function (err) {
          console.error("\u83B7\u53D6\u5E97\u94FA".concat(type, "\u8BC4\u8BBA\u5217\u8868\u5931\u8D25:"), err);
          _this7.handleApiError(type, '网络请求错误');
        }).finally(function () {
          _this7.loading = false;
          _this7.isRefreshing = false;
        });
      } else {
        // 使用普通评论API获取评论
        console.log('请求参数:', JSON.stringify(params));
        _commentApi.default.getCommentList(params).then(function (res) {
          var endTime = _this7.getTimestamp();
          var loadTime = endTime - startTime;
          console.log("\u2705 ".concat(type, "\u8BC4\u8BBA\u5217\u8868API\u8FD4\u56DE\uFF0C\u8017\u65F6: ").concat(loadTime.toFixed(2), "ms"));
          if (res.code === 0) {
            var data = res.data;

            // 性能优化：减少JSON序列化
            console.log("\uD83D\uDCCA ".concat(type, "\u8BC4\u8BBA\u5217\u8868\u6570\u636E\u6982\u89C8:"), {
              total: data.total,
              commentsCount: data.comments ? data.comments.length : 0,
              hasMore: data.hasMore
            });

            // 处理评论数据（优化版）
            var processedData = _this7.processCommentDataOptimized(data);

            // 根据类型更新对应的评论列表
            switch (type) {
              case 'hot':
                _this7.commentListHot = processedData;
                break;
              case 'new':
                _this7.commentListNew = processedData;
                break;
              case 'my':
                _this7.commentListMy = processedData;
                break;
            }

            // 更新总评论数和分页信息
            _this7.totalComments = data && data.total || 0;
            _this7.pagination[type].hasMore = data.hasMore !== false;
            console.log("\uD83C\uDFAF ".concat(type, "\u8BC4\u8BBA\u52A0\u8F7D\u5B8C\u6210\uFF0C\u603B\u6570: ").concat(_this7.totalComments, ", \u5F53\u524D\u663E\u793A: ").concat(processedData.length));
          } else {
            // 请求失败，显示错误信息
            _this7.handleApiError(type, res.message || '获取评论失败');
          }
        }).catch(function (err) {
          console.error("\u83B7\u53D6".concat(type, "\u8BC4\u8BBA\u5217\u8868\u5931\u8D25:"), err);
          _this7.handleApiError(type, '网络请求错误');
        }).finally(function () {
          _this7.loading = false;
          _this7.isRefreshing = false;
        });
      }
    },
    // 处理API错误
    handleApiError: function handleApiError(type, message) {
      var _this8 = this;
      // 显示错误提示
      uni.showToast({
        title: message,
        icon: 'none'
      });

      // 确保评论列表不为undefined
      switch (type) {
        case 'hot':
          if (!this.commentListHot) this.commentListHot = [];
          break;
        case 'new':
          if (!this.commentListNew) this.commentListNew = [];
          break;
        case 'my':
          if (!this.commentListMy) this.commentListMy = [];
          break;
      }

      // 如果是刷新操作，提供重试选项
      if (this.isRefreshing) {
        setTimeout(function () {
          uni.showModal({
            title: '提示',
            content: '获取评论失败，是否重试？',
            confirmText: '重试',
            success: function success(res) {
              if (res.confirm) {
                _this8.fetchCommentsByType(type);
              }
            }
          });
        }, 500);
      }
    },
    // 懒加载更多评论（优化版）
    loadMoreComments: function loadMoreComments() {
      var _this9 = this;
      var type = this.activeFilter; // 使用当前激活的筛选类型
      console.log("\uD83D\uDD04 \u89E6\u53D1".concat(type, "\u8BC4\u8BBA\u61D2\u52A0\u8F7D"));

      // 防抖处理，避免重复请求
      if (this.pagination[type].loading || !this.pagination[type].hasMore) {
        console.log("\u26A0\uFE0F ".concat(type, "\u8BC4\u8BBA\u6B63\u5728\u52A0\u8F7D\u6216\u5DF2\u65E0\u66F4\u591A\u6570\u636E\uFF0C\u8DF3\u8FC7\u8BF7\u6C42"));
        return;
      }

      // 性能优化：增强防抖处理，减少低端设备的请求频率
      var now = Date.now();
      var lastRequestTime = this.lastRequestTime || 0;
      if (now - lastRequestTime < 800) {
        // 增加到800ms，减少低端设备的负担
        console.log("\u26A0\uFE0F \u8BF7\u6C42\u8FC7\u4E8E\u9891\u7E41\uFF0C\u8DF3\u8FC7".concat(type, "\u8BC4\u8BBA\u61D2\u52A0\u8F7D"));
        return;
      }
      this.lastRequestTime = now;

      // 设置加载状态
      this.pagination[type].loading = true;
      this.loadingText = '加载更多评论...';

      // 计算下一页页码
      var nextPage = this.pagination[type].page + 1;
      var startTime = this.getTimestamp();
      console.log("\uD83D\uDCC4 ".concat(type, "\u8BC4\u8BBA\u5F53\u524D\u9875\u7801: ").concat(this.pagination[type].page, ", \u8BF7\u6C42\u9875\u7801: ").concat(nextPage));

      // 准备分页参数 - 修复参数传递问题
      var apiCall;
      if (this.topicId) {
        // 话题评论API调用 - 修复数据类型为Long兼容
        console.log("\uD83C\uDFAF \u8C03\u7528\u8BDD\u9898\u8BC4\u8BBAAPI: topicId=".concat(this.topicId, ", type=").concat(type, ", page=").concat(nextPage));
        apiCall = _topicApi.default.getTopicComments(Number(this.topicId), Number(this.userId), type, nextPage, this.pagination[type].pageSize);
      } else {
        // 普通评论API调用 - 修复分页
        // 参数和数据类型为Long兼容
        var params = {
          userId: Number(this.userId),
          // 使用Number确保Long兼容性
          contentId: this.contentId,
          contentType: this.contentType,
          filter: type,
          current: nextPage,
          // 修复：使用current而不是page
          pageSize: this.pagination[type].pageSize
        };
        console.log("\uD83D\uDCCB \u8C03\u7528\u666E\u901A\u8BC4\u8BBAAPI\uFF0C\u53C2\u6570:", JSON.stringify(params));
        apiCall = _commentApi.default.getCommentList(params);
      }
      apiCall.then(function (res) {
        var endTime = _this9.getTimestamp();
        var loadTime = endTime - startTime;
        console.log("\u2705 ".concat(type, "\u8BC4\u8BBA\u5206\u9875API\u8FD4\u56DE\uFF0C\u8017\u65F6: ").concat(loadTime.toFixed(2), "ms"));
        if (res.code === 0) {
          var data = res.data;

          // 性能优化：减少日志输出
          console.log("\uD83D\uDCCA ".concat(type, "\u8BC4\u8BBA\u5206\u9875\u6570\u636E\u6982\u89C8:"), {
            commentsCount: data.comments ? data.comments.length : 0,
            total: data.total,
            hasMore: data.hasMore
          });

          // 处理不同的数据结构（优化版）
          var rawComments = [];
          if (data.comments && Array.isArray(data.comments)) {
            rawComments = data.comments;
          } else if (data.items && Array.isArray(data.items)) {
            rawComments = data.items;
          } else if (Array.isArray(data)) {
            rawComments = data;
          }
          var newComments = _this9.processCommentDataOptimized({
            comments: rawComments
          });
          if (newComments && newComments.length > 0) {
            // 检查是否有重复数据（优化版）
            var existingIds = new Set(_this9.getExistingCommentIds(type));
            var filteredComments = newComments.filter(function (comment) {
              return !existingIds.has(comment.id);
            });
            console.log("\uFFFD ".concat(type, "\u8BC4\u8BBA\u53BB\u91CD: \u539F\u59CB").concat(newComments.length, "\u6761\uFF0C\u53BB\u91CD\u540E").concat(filteredComments.length, "\u6761"));
            if (filteredComments.length > 0) {
              // 追加新评论到对应列表（优化：使用concat而不是展开运算符）
              switch (type) {
                case 'hot':
                  _this9.commentListHot = _this9.commentListHot.concat(filteredComments);
                  break;
                case 'new':
                  _this9.commentListNew = _this9.commentListNew.concat(filteredComments);
                  break;
                case 'my':
                  _this9.commentListMy = _this9.commentListMy.concat(filteredComments);
                  break;
              }

              // 更新分页信息
              _this9.pagination[type].page = nextPage;
              console.log("\u2705 ".concat(type, "\u8BC4\u8BBA\u52A0\u8F7D\u6210\u529F\uFF0C\u9875\u7801: ").concat(nextPage, "\uFF0C\u65B0\u589E: ").concat(filteredComments.length, "\u6761"));
            }

            // 检查是否还有更多数据
            if (data.hasMore === false || newComments.length < _this9.pagination[type].pageSize) {
              _this9.pagination[type].hasMore = false;
              console.log("\uD83D\uDD1A ".concat(type, "\u8BC4\u8BBA\u5DF2\u52A0\u8F7D\u5B8C\u6BD5"));
            }
          } else {
            // 没有更多数据
            _this9.pagination[type].hasMore = false;
            console.log("\uD83D\uDD1A ".concat(type, "\u8BC4\u8BBA\u65E0\u66F4\u591A\u6570\u636E"));
          }
        } else {
          // API返回错误
          console.error("\u274C ".concat(type, "\u8BC4\u8BBAAPI\u8FD4\u56DE\u9519\u8BEF:"), res.message);
          uni.showToast({
            title: res.message || '加载失败',
            icon: 'none'
          });
        }
      }).catch(function (err) {
        console.error("\u274C ".concat(type, "\u8BC4\u8BBA\u61D2\u52A0\u8F7D\u5931\u8D25:"), err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      }).finally(function () {
        // 重置加载状态
        _this9.pagination[type].loading = false;
        _this9.loadingText = '加载中...';
        console.log("\uD83D\uDD04 ".concat(type, "\u8BC4\u8BBA\u52A0\u8F7D\u72B6\u6001\u91CD\u7F6E"));
      });
    },
    // 获取已存在的评论ID列表，用于去重
    getExistingCommentIds: function getExistingCommentIds(type) {
      var existingComments = [];
      switch (type) {
        case 'hot':
          existingComments = this.commentListHot;
          break;
        case 'new':
          existingComments = this.commentListNew;
          break;
        case 'my':
          existingComments = this.commentListMy;
          break;
      }
      return existingComments.map(function (comment) {
        return comment.id;
      });
    },
    // 优化的评论数据处理方法 - 增强渲染性能
    processCommentDataOptimized: function processCommentDataOptimized(data) {
      var startTime = this.getTimestamp();
      if (!data || !data.comments) {
        console.warn('⚠️ 评论数据为空或格式错误');
        return [];
      }
      var comments = Array.isArray(data.comments) ? data.comments : [];
      console.log("\uFFFD \u5F00\u59CB\u5904\u7406\u8BC4\u8BBA\u6570\u636E\uFF0C\u6570\u91CF: ".concat(comments.length));
      var processedComments = comments.map(function (comment) {
        if (!comment) return null;

        // 优化：减少对象创建和属性复制
        var processedComment = Object.assign({}, comment, {
          created_at: comment.createdAt || comment.created_at || new Date().toISOString(),
          is_liked: comment.isLiked || comment.is_liked || false,
          showFullContent: false
        });

        // 确保用户对象存在
        if (!processedComment.user) {
          processedComment.user = {
            id: 0,
            nickname: '未知用户',
            avatar: '/static/images/toux.png',
            level: 0
          };
        } else {
          // 处理用户头像为空的情况
          if (!processedComment.user.avatar) {
            processedComment.user.avatar = '/static/images/toux.png';
          }
          processedComment.user.nickname = processedComment.user.nickname || '未知用户';
          processedComment.user.level = processedComment.user.level || 0;
        }
        return processedComment;
      }).filter(function (comment) {
        return comment !== null;
      });
      var endTime = this.getTimestamp();
      console.log("\u2705 \u8BC4\u8BBA\u6570\u636E\u5904\u7406\u5B8C\u6210\uFF0C\u8017\u65F6: ".concat((endTime - startTime).toFixed(2), "ms\uFF0C\u5904\u7406\u6570\u91CF: ").concat(processedComments.length));
      return processedComments;
    },
    onRefresh: function onRefresh() {
      this.isRefreshing = true;

      // 重置当前激活标签的分页状态
      this.pagination[this.activeFilter] = {
        page: 1,
        pageSize: 10,
        hasMore: true,
        loading: false
      };

      // 刷新评论统计信息
      this.fetchCommentStats();

      // 刷新当前激活的评论列表
      this.fetchCommentsByType(this.activeFilter);
    },
    likeComment: function likeComment(item, index, type) {
      var _this10 = this;
      // 调用点赞/取消点赞API
      var action = item.is_liked ? 'unlike' : 'like';
      _commentApi.default.likeComment(Number(item.id), {
        userId: Number(this.userId),
        // 使用Number确保Long兼容性
        action: action
      }).then(function (res) {
        console.log('点赞评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          // 更新评论点赞状态和数量
          var updatedIsLiked = res.data.isLiked || res.data.is_liked;
          var updatedLikes = res.data.likes;

          // 根据类型更新对应的评论列表
          switch (type) {
            case 'hot':
              _this10.commentListHot[index].is_liked = updatedIsLiked;
              _this10.commentListHot[index].likes = updatedLikes;
              break;
            case 'new':
              _this10.commentListNew[index].is_liked = updatedIsLiked;
              _this10.commentListNew[index].likes = updatedLikes;
              break;
            case 'my':
              _this10.commentListMy[index].is_liked = updatedIsLiked;
              _this10.commentListMy[index].likes = updatedLikes;
              break;
          }
        } else {
          uni.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      }).catch(function () {
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      });
    },
    goToDetail: function goToDetail(item) {
      // 跳转到评论详情页面
      uni.navigateTo({
        url: "/pagesSub/switch/comment-detail?id=".concat(item.id, "&userId=").concat(this.userId)
      });
    },
    sendComment: function sendComment() {
      if (this.commentText.length > 1000) {
        uni.showToast({
          title: '评论字数不能超过1000字',
          icon: 'none'
        });
        return;
      }
      if (!this.commentText.trim()) return;

      // 根据当前状态决定是发送评论还是回复
      if (this.isReplyMode && this.currentReply) {
        this.sendReply();
      } else {
        this.sendNormalComment();
      }
    },
    // 发送普通评论
    sendNormalComment: function sendNormalComment() {
      // 判断是话题评论还是店铺评论
      if (this.topicId) {
        this.sendTopicComment();
      } else if (this.storeId) {
        this.sendStoreComment();
      } else {
        // 普通评论
        this.sendRegularComment();
      }
    },
    // 发送话题评论
    sendTopicComment: function sendTopicComment() {
      var _this11 = this;
      var data = {
        userId: Number(this.userId),
        contentId: this.contentId,
        topicId: Number(this.topicId),
        content: this.commentText.trim()
      };
      console.log('🎯 发送话题评论请求数据:', JSON.stringify(data));
      _commentApi.default.postComment(data).then(function (res) {
        console.log('发送话题评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          _this11.handleCommentSuccess();
        } else {
          _this11.handleCommentError(res.message || '话题评论失败');
        }
      }).catch(function (err) {
        console.error('发送话题评论失败:', err);
        _this11.handleCommentError('网络错误，请重试');
      });
    },
    // 发送店铺评论
    sendStoreComment: function sendStoreComment() {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var data, res;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                data = {
                  userId: Number(_this12.userId),
                  storeId: Number(_this12.storeId),
                  content: _this12.commentText.trim()
                };
                console.log('🏪 发送店铺评论请求数据:', JSON.stringify(data));
                _context4.prev = 2;
                _context4.next = 5;
                return _this12.postStoreComment(data);
              case 5:
                res = _context4.sent;
                console.log('发送店铺评论API返回数据:', JSON.stringify(res));
                if (res.code === 0) {
                  _this12.handleCommentSuccess();
                } else {
                  _this12.handleCommentError(res.message || '店铺评论失败');
                }
                _context4.next = 14;
                break;
              case 10:
                _context4.prev = 10;
                _context4.t0 = _context4["catch"](2);
                console.error('发送店铺评论失败:', _context4.t0);
                _this12.handleCommentError('网络错误，请重试');
              case 14:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[2, 10]]);
      }))();
    },
    // 发送普通评论
    sendRegularComment: function sendRegularComment() {
      var _this13 = this;
      var data = {
        userId: Number(this.userId),
        contentId: this.contentId,
        content: this.commentText.trim()
      };
      console.log('📝 发送普通评论请求数据:', JSON.stringify(data));
      _commentApi.default.postComment(data).then(function (res) {
        console.log('发送普通评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          _this13.handleCommentSuccess();
        } else {
          _this13.handleCommentError(res.message || '评论失败');
        }
      }).catch(function (err) {
        console.error('发送普通评论失败:', err);
        _this13.handleCommentError('网络错误，请重试');
      });
    },
    // 处理评论成功
    handleCommentSuccess: function handleCommentSuccess() {
      // 清空输入框并重置状态
      this.clearInputAndResetState();

      // 重新加载评论列表 - 发布后切换到"最新"标签
      this.fetchCommentsByType('new');
      this.activeFilter = 'new';
      this.currentTabIndex = 1;

      // 刷新评论统计
      this.fetchCommentStats();
      uni.showToast({
        title: '评论成功',
        icon: 'success'
      });
    },
    // 处理评论错误
    handleCommentError: function handleCommentError(message) {
      uni.showToast({
        title: message,
        icon: 'none'
      });
    },
    // 发送店铺评论API调用
    postStoreComment: function postStoreComment(data) {
      var _this14 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var _this14$$config, _this14$$config$apis;
        var baseUrl, url;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                baseUrl = ((_this14$$config = _this14.$config) === null || _this14$$config === void 0 ? void 0 : (_this14$$config$apis = _this14$$config.apis) === null || _this14$$config$apis === void 0 ? void 0 : _this14$$config$apis.vote_baseUrl) || 'https://vote.foxdance.com.cn';
                url = "".concat(baseUrl, "/api/comments/store");
                return _context5.abrupt("return", new Promise(function (resolve, reject) {
                  uni.request({
                    url: url,
                    method: 'POST',
                    data: data,
                    header: {
                      'Content-Type': 'application/json',
                      'bausertoken': uni.getStorageSync('bausertoken') || uni.getStorageSync('token')
                    },
                    success: function success(res) {
                      console.log('🏪 店铺评论API响应:', res);
                      if (res.statusCode === 200) {
                        resolve(res.data);
                      } else {
                        var _res$data3;
                        reject(new Error("HTTP ".concat(res.statusCode, ": ").concat(((_res$data3 = res.data) === null || _res$data3 === void 0 ? void 0 : _res$data3.message) || '请求失败')));
                      }
                    },
                    fail: function fail(err) {
                      console.error('🏪 店铺评论API请求失败:', err);
                      reject(err);
                    }
                  });
                }));
              case 3:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    replyComment: function replyComment(item) {
      var _this15 = this;
      // 设置回复状态
      this.isReplyMode = true;
      this.currentReply = item;
      this.inputPlaceholder = "@".concat(item.user.nickname);

      // 聚焦主输入框
      this.$nextTick(function () {
        if (_this15.$refs.mainCommentInput) {
          _this15.$refs.mainCommentInput.focus();
        }
      });
    },
    // 显示更多操作弹窗
    showMoreOptions: function showMoreOptions(item) {
      this.currentMoreComment = item;
      this.showMorePopup = true;
    },
    // 从更多操作弹窗中点击回复
    replyFromMore: function replyFromMore() {
      var _this16 = this;
      if (this.currentMoreComment) {
        this.showMorePopup = false;
        // 等待更多操作弹窗关闭后再打开回复弹窗
        setTimeout(function () {
          _this16.replyComment(_this16.currentMoreComment);
        }, 300);
      }
    },
    // 复制评论内容
    copyComment: function copyComment() {
      var _this17 = this;
      if (!this.currentMoreComment) return;
      uni.setClipboardData({
        data: this.currentMoreComment.content,
        success: function success() {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
          _this17.showMorePopup = false;
        }
      });
    },
    // 删除评论
    deleteComment: function deleteComment() {
      var _this18 = this;
      if (!this.currentMoreComment) return;
      uni.showModal({
        title: '删除评论',
        content: '确认要删除这条评论吗？删除后无法恢复',
        confirmText: '删除',
        confirmColor: '#f56c6c',
        success: function success(res) {
          if (res.confirm) {
            // 调用API删除评论 - 修复数据类型为Long兼容
            _commentApi.default.deleteComment(Number(_this18.currentMoreComment.id), {
              userId: Number(_this18.userId) // 使用Number确保Long兼容性
            }).then(function (res) {
              console.log('删除评论API返回数据:', JSON.stringify(res));
              if (res.code === 0) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });

                // 关闭弹窗
                _this18.showMorePopup = false;

                // 从列表中移除已删除的评论
                var removeFromList = function removeFromList(list, commentId) {
                  var index = list.findIndex(function (item) {
                    return item.id == commentId;
                  });
                  if (index > -1) {
                    list.splice(index, 1);
                  }
                };

                // 根据当前标签页删除对应列表中的评论
                removeFromList(_this18.commentListHot, _this18.currentMoreComment.id);
                removeFromList(_this18.commentListNew, _this18.currentMoreComment.id);
                removeFromList(_this18.commentListMy, _this18.currentMoreComment.id);

                // 更新评论总数
                _this18.totalComments--;
              } else {
                uni.showToast({
                  title: res.message || '删除失败',
                  icon: 'none'
                });
              }
            }).catch(function (err) {
              console.error('删除评论失败:', err);
              uni.showToast({
                title: '网络请求错误',
                icon: 'none'
              });
            });
          }
        }
      });
    },
    sendReply: function sendReply() {
      var _this19 = this;
      if (!this.commentText.trim() || !this.currentReply) return;

      // 调用回复评论API - 修复数据类型为Long兼容
      _commentApi.default.replyComment(Number(this.currentReply.id), {
        userId: Number(this.userId),
        // 使用Number确保Long兼容性
        content: this.commentText.trim(),
        replyToId: null // 直接回复评论，不是回复其他回复
      }).then(function (res) {
        console.log('回复评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          // 回复成功
          // 清空输入框并重置回复状态
          _this19.clearInputAndResetState();

          // 重新加载当前标签页的评论列表
          _this19.fetchCommentsByType(_this19.activeFilter);
          uni.showToast({
            title: '回复成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: res.message || '回复失败',
            icon: 'none'
          });
        }
      }).catch(function (err) {
        console.error('回复评论失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      });
    },
    // 清空输入框并重置回复状态
    clearInputAndResetState: function clearInputAndResetState() {
      var clearInput = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
      // 根据参数决定是否清空输入框
      if (clearInput) {
        if (this.$refs.mainCommentInput) {
          this.$refs.mainCommentInput.clear();
        } else {
          this.commentText = '';
        }
        console.log('🔄 输入框已清空');
      } else {
        console.log('🔄 保留输入内容');
      }

      // 重置回复状态
      this.isReplyMode = false;
      this.currentReply = null;
      this.inputPlaceholder = '说点什么...';
      console.log('🔄 回复状态已重置');
    },
    // 取消回复模式
    cancelReplyMode: function cancelReplyMode() {
      var clearInput = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      var wasInReplyMode = this.isReplyMode;
      var replyTargetName = this.currentReply && this.currentReply.user ? this.currentReply.user.nickname : '未知用户';
      var hasInputContent = this.commentText && this.commentText.trim();

      // 重置回复状态，但可以选择保留输入内容
      this.isReplyMode = false;
      this.currentReply = null;
      this.inputPlaceholder = '说点什么...';

      // 根据参数决定是否清空输入框
      if (clearInput) {
        if (this.$refs.mainCommentInput) {
          this.$refs.mainCommentInput.clear();
        } else {
          this.commentText = '';
        }
      }
      if (wasInReplyMode) {
        console.log("\u274C \u5DF2\u53D6\u6D88\u56DE\u590D\u6A21\u5F0F\uFF0C\u539F\u56DE\u590D\u5BF9\u8C61: ".concat(replyTargetName));

        // 根据是否有输入内容给出不同的提示
        var toastTitle = '已取消回复';
        if (hasInputContent && !clearInput) {
          toastTitle = '已取消回复，内容转为评论';
        }

        // 给用户视觉反馈
        uni.showToast({
          title: toastTitle,
          icon: 'none',
          duration: 1500
        });
      }
    },
    formatTime: function formatTime(timeString) {
      if (!timeString) return '';
      return (0, _dayjs.default)(timeString).fromNow();
    },
    // 获取话题信息
    fetchTopicInfo: function fetchTopicInfo() {
      var _this20 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var res, processedImages;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                if (_this20.topicId) {
                  _context6.next = 2;
                  break;
                }
                return _context6.abrupt("return");
              case 2:
                _context6.prev = 2;
                console.log('🎯 获取话题信息，topicId:', _this20.topicId, 'userId:', _this20.userId);
                _context6.next = 6;
                return _topicApi.default.getTopicById(_this20.topicId, _this20.userId);
              case 6:
                res = _context6.sent;
                console.log('🔍 API响应完整数据:', JSON.stringify(res, null, 2));
                if (res.code === 0 && res.data) {
                  console.log('🔍 API响应data字段:', JSON.stringify(res.data, null, 2));
                  console.log('🔍 topicImages字段类型:', (0, _typeof2.default)(res.data.topicImages));
                  console.log('🔍 topicImages字段值:', res.data.topicImages);
                  console.log('🔍 topicImages是否为数组:', Array.isArray(res.data.topicImages));
                  _this20.topicInfo = res.data;
                  console.log('✅ 话题信息获取成功，赋值后的topicInfo:', _this20.topicInfo);
                  console.log('✅ 赋值后的topicImages:', _this20.topicInfo.topicImages);

                  // 如果话题有图片，记录详细日志并验证URL
                  if (_this20.topicInfo.topicImages && _this20.topicInfo.topicImages.length > 0) {
                    console.log('🖼️ 话题包含图片，数量:', _this20.topicInfo.topicImages.length);
                    console.log('🖼️ 原始图片URL列表:', _this20.topicInfo.topicImages);

                    // 验证和处理图片URL
                    processedImages = _this20.topicInfo.topicImages.map(function (url, index) {
                      var processedUrl = _this20.processImageUrl(url);
                      console.log("\uD83D\uDDBC\uFE0F \u56FE\u7247".concat(index + 1, ": ").concat(url, " -> ").concat(processedUrl));
                      return processedUrl;
                    }); // 更新处理后的图片URL（可选，保持原始数据不变）
                    console.log('🖼️ 处理后图片URL列表:', processedImages);
                  } else {
                    console.warn('📷 话题不包含图片或topicImages为null/empty');
                    console.warn('📷 topicImages详细信息:', {
                      value: _this20.topicInfo.topicImages,
                      type: (0, _typeof2.default)(_this20.topicInfo.topicImages),
                      isArray: Array.isArray(_this20.topicInfo.topicImages),
                      length: _this20.topicInfo.topicImages ? _this20.topicInfo.topicImages.length : 'N/A'
                    });
                  }
                } else {
                  console.warn('⚠️ 获取话题信息失败:', res.message);
                  console.warn('⚠️ 完整响应:', res);
                  uni.showToast({
                    title: res.message || '获取话题信息失败',
                    icon: 'none'
                  });
                }
                _context6.next = 15;
                break;
              case 11:
                _context6.prev = 11;
                _context6.t0 = _context6["catch"](2);
                console.error('❌ 获取话题信息异常:', _context6.t0);
                uni.showToast({
                  title: '网络请求错误',
                  icon: 'none'
                });
              case 15:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[2, 11]]);
      }))();
    },
    // 处理话题图片加载错误
    handleTopicImageError: function handleTopicImageError(index) {
      console.error('❌ 话题图片加载失败，索引:', index);
      if (this.topicInfo && this.topicInfo.topicImages && this.topicInfo.topicImages[index]) {
        var originalUrl = this.topicInfo.topicImages[index];
        var processedUrl = this.processImageUrl(originalUrl);
        console.error('❌ 原始URL:', originalUrl);
        console.error('❌ 处理后URL:', processedUrl);

        // 尝试使用备用图片或提示用户
        this.handleImageLoadError(processedUrl, '话题图片');
      }
    },
    // 预览话题图片
    previewTopicImage: function previewTopicImage(index) {
      var _this21 = this;
      console.log('🔥 开始预览话题图片，索引:', index);
      if (!this.topicInfo || !this.topicInfo.topicImages || this.topicInfo.topicImages.length === 0) {
        uni.showToast({
          title: '没有可预览的图片',
          icon: 'none'
        });
        return;
      }
      if (index < 0 || index >= this.topicInfo.topicImages.length) {
        uni.showToast({
          title: '图片索引错误',
          icon: 'none'
        });
        return;
      }

      // 处理图片URL，确保可访问性
      var processedUrls = this.topicInfo.topicImages.map(function (url) {
        return _this21.processImageUrl(url);
      });
      var processedCurrentUrl = processedUrls[index];
      console.log('🔥 处理后的话题图片URL数组:', processedUrls);
      console.log('🔥 当前预览URL:', processedCurrentUrl);
      uni.previewImage({
        current: processedCurrentUrl,
        // 修复：使用图片URL而不是索引
        urls: processedUrls,
        // 修复：使用处理后的URL数组
        success: function success() {
          console.log('✅ 话题图片预览成功');
        },
        fail: function fail(error) {
          console.error('❌ 话题图片预览失败:', error);
          uni.showToast({
            title: '图片预览失败: ' + (error.errMsg || '未知错误'),
            icon: 'none'
          });
        }
      });
    },
    getLevelColor: function getLevelColor(level) {
      var colors = {
        0: '#cccbc8',
        // 灰色
        1: '#c6ffe6',
        2: '#61bc84',
        // 绿色
        3: '#4d648d',
        4: '#1F3A5F',
        5: '#9c27b0',
        6: '#6c35de',
        7: '#ffd299',
        // 橙色
        8: '#FF7F50',
        // 红色
        9: '#f35d74',
        // 紫色
        10: '#bb2649' // 粉色
      };

      return colors[level] || '#8dc63f';
    },
    // 处理图片URL，确保可访问性
    processImageUrl: function processImageUrl(url) {
      if (!url) return '';

      // 如果URL是相对路径，转换为绝对路径
      if (url.startsWith('/')) {
        return 'https://file.foxdance.com.cn' + url;
      }

      // 如果URL不包含协议，添加https
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        return 'https://' + url;
      }
      return url;
    },
    // 处理图片加载错误
    handleImageLoadError: function handleImageLoadError(_) {
      var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '图片';
      uni.showToast({
        title: context + '加载失败',
        icon: 'none'
      });
    },
    // 切换评论内容的展开/收起状态
    toggleContent: function toggleContent(item, index, type) {
      var wasExpanded = item.showFullContent;

      // 根据类型更新对应的评论列表
      switch (type) {
        case 'hot':
          this.$set(this.commentListHot[index], 'showFullContent', !item.showFullContent);
          break;
        case 'new':
          this.$set(this.commentListNew[index], 'showFullContent', !item.showFullContent);
          break;
        case 'my':
          this.$set(this.commentListMy[index], 'showFullContent', !item.showFullContent);
          break;
      }

      // 如果是从展开状态收起，则滚动到评论顶部
      if (wasExpanded) {
        this.scrollToComment(index, type);
      }
    },
    // 滚动到指定评论的顶部位置
    scrollToComment: function scrollToComment(index, type) {
      var _this22 = this;
      var commentId = "comment-".concat(type, "-").concat(index);
      console.log("\uD83C\uDFAF \u5F00\u59CB\u6EDA\u52A8\u5230\u8BC4\u8BBA - ".concat(commentId, ", \u7D22\u5F15: ").concat(index, ", \u7C7B\u578B: ").concat(type));

      // 优先使用scroll-top方法，因为它更可控
      setTimeout(function () {
        _this22.scrollToCommentByScrollTop(commentId);
      }, 200);

      // 如果scroll-top失败，再尝试scrollIntoView
      setTimeout(function () {
        // 检查scroll-top是否成功，如果scrollTop仍然很小，说明可能失败了
        if (_this22.scrollTop < 50) {
          console.log("\uD83D\uDD04 scroll-top\u53EF\u80FD\u5931\u8D25\uFF0C\u5C1D\u8BD5scrollIntoView - ".concat(commentId));
          _this22.scrollToCommentByScrollIntoView(commentId);
        }
      }, 600);
    },
    // 方法1: 使用scrollIntoView滚动到评论
    scrollToCommentByScrollIntoView: function scrollToCommentByScrollIntoView(commentId) {
      var _this23 = this;
      console.log("\uD83D\uDCCD \u4F7F\u7528scrollIntoView\u6EDA\u52A8\u5230 - ".concat(commentId));

      // 先验证目标元素是否存在
      this.$nextTick(function () {
        var query = uni.createSelectorQuery().in(_this23);
        query.select("#".concat(commentId)).boundingClientRect(function (rect) {
          if (rect) {
            console.log("\uD83D\uDCCD \u627E\u5230\u76EE\u6807\u5143\u7D20 - ".concat(commentId, ":"), rect);

            // 设置scroll-into-view属性
            _this23.scrollIntoView = commentId;

            // 清除scrollIntoView，避免影响后续滚动
            setTimeout(function () {
              _this23.scrollIntoView = '';
              console.log("\u2705 scrollIntoView\u8BBE\u7F6E\u6210\u529F - ".concat(commentId));
            }, 800); // 增加清除延时
          } else {
            console.warn("\u26A0\uFE0F \u672A\u627E\u5230\u76EE\u6807\u5143\u7D20 - ".concat(commentId));
            // 如果scrollIntoView失败，尝试使用scroll-top方法
            setTimeout(function () {
              _this23.scrollToCommentByScrollTop(commentId);
            }, 100);
          }
        }).exec();
      });
    },
    // 方法2: 使用scroll-top属性滚动到评论
    scrollToCommentByScrollTop: function scrollToCommentByScrollTop(commentId) {
      var _this24 = this;
      console.log("\uD83D\uDCCD \u4F7F\u7528scroll-top\u6EDA\u52A8\u5230 - ".concat(commentId));
      this.$nextTick(function () {
        var query = uni.createSelectorQuery().in(_this24);

        // 获取scroll-view容器和目标元素的位置信息
        query.select('.page-scroll-view').boundingClientRect();
        query.select("#".concat(commentId)).boundingClientRect();
        // 同时获取话题信息区域的高度，用于更精确的计算
        query.select('.topic-info-section').boundingClientRect();
        // 获取当前scroll-view的滚动信息
        query.select('.page-scroll-view').scrollOffset();
        query.exec(function (res) {
          console.log("\uD83D\uDCCA scroll-top\u67E5\u8BE2\u7ED3\u679C - ".concat(commentId, ":"), res);
          if (res && res.length >= 3) {
            var scrollViewRect = res[0]; // scroll-view容器
            var commentRect = res[1]; // 目标评论元素
            var topicInfoRect = res[2]; // 话题信息区域
            var scrollInfo = res[3]; // 当前滚动信息

            if (scrollViewRect && commentRect) {
              // 获取当前真实的滚动位置
              var currentScrollTop = scrollInfo ? scrollInfo.scrollTop : _this24.scrollTop || 0;

              // 计算评论元素在整个内容中的绝对位置
              var commentAbsoluteTop = currentScrollTop + (commentRect.top - scrollViewRect.top);

              // 设置合理的顶部偏移量
              var topOffset = 120;
              var targetScrollTop = Math.max(0, commentAbsoluteTop - topOffset);

              // 如果话题信息区域存在，记录其高度用于调试
              var topicInfoHeight = 0;
              if (topicInfoRect) {
                topicInfoHeight = topicInfoRect.height || 0;
                console.log("\uD83D\uDCCF \u8BDD\u9898\u4FE1\u606F\u533A\u57DF\u9AD8\u5EA6: ".concat(topicInfoHeight));
              }
              console.log("\uD83D\uDCD0 scroll-top\u8BE6\u7EC6\u8BA1\u7B97 - ".concat(commentId, ":"), {
                scrollViewTop: scrollViewRect.top,
                commentTop: commentRect.top,
                currentScrollTop: currentScrollTop,
                commentAbsoluteTop: commentAbsoluteTop,
                topicInfoHeight: topicInfoRect ? topicInfoRect.height : 0,
                topOffset: topOffset,
                targetScrollTop: targetScrollTop
              });

              // 使用更可靠的滚动方式
              if (targetScrollTop > 0) {
                // 先重置到一个不同的值，强制触发更新
                _this24.scrollTop = targetScrollTop + 1;
                _this24.$nextTick(function () {
                  _this24.scrollTop = targetScrollTop;
                  console.log("\u2705 scroll-top\u8BBE\u7F6E\u6210\u529F - ".concat(commentId, ", \u4F4D\u7F6E: ").concat(targetScrollTop));
                });
              } else {
                console.warn("\u26A0\uFE0F \u8BA1\u7B97\u7684\u6EDA\u52A8\u4F4D\u7F6E\u4E3A0\u6216\u8D1F\u6570 - ".concat(commentId, ", \u4F4D\u7F6E: ").concat(targetScrollTop));
                // 如果计算结果为0，尝试一个最小的滚动位置
                _this24.scrollTop = 50;
              }
            } else {
              console.warn("\u26A0\uFE0F \u83B7\u53D6\u5143\u7D20\u4F4D\u7F6E\u5931\u8D25 - scrollView: ".concat(!!scrollViewRect, ", comment: ").concat(!!commentRect));
            }
          } else {
            console.warn("\u26A0\uFE0F \u67E5\u8BE2\u7ED3\u679C\u4E0D\u5B8C\u6574 - ".concat(commentId, ":"), res);
          }
        });
      });
    },
    // 调试方法：检查DOM元素是否存在
    debugScrollElements: function debugScrollElements(index, type) {
      var commentId = "comment-".concat(type, "-").concat(index);
      console.log("\uD83D\uDD0D \u8C03\u8BD5\u6EDA\u52A8\u5143\u7D20 - ".concat(commentId));
      var query = uni.createSelectorQuery().in(this);
      query.select("#".concat(commentId)).boundingClientRect();
      query.select('.page-scroll-view').boundingClientRect();
      query.exec(function (res) {
        console.log('🔍 调试结果:', {
          commentId: commentId,
          commentElement: res[0],
          scrollViewElement: res[1],
          hasComment: !!res[0],
          hasScrollView: !!res[1]
        });
      });
    },
    // 测试方法：手动触发滚动
    testScroll: function testScroll() {
      var _this25 = this;
      var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
      var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'hot';
      console.log("\uD83E\uDDEA \u6D4B\u8BD5\u6EDA\u52A8\u529F\u80FD - ".concat(type, "-").concat(index));

      // 先检查元素是否存在
      this.debugScrollElements(index, type);

      // 测试scrollIntoView
      var commentId = "comment-".concat(type, "-").concat(index);
      setTimeout(function () {
        console.log("\uD83E\uDDEA \u8BBE\u7F6EscrollIntoView - ".concat(commentId));
        _this25.scrollIntoView = commentId;
        setTimeout(function () {
          _this25.scrollIntoView = '';
          console.log("\uD83E\uDDEA \u6D4B\u8BD5\u5B8C\u6210 - ".concat(commentId));
        }, 1000);
      }, 500);
    },
    // 强制滚动方法：用于测试
    forceScrollToComment: function forceScrollToComment(index, type) {
      var _this26 = this;
      var commentId = "comment-".concat(type, "-").concat(index);
      console.log("\uD83D\uDE80 \u5F3A\u5236\u6EDA\u52A8\u5230\u8BC4\u8BBA - ".concat(commentId));

      // 直接设置scrollIntoView，不使用延时
      this.scrollIntoView = commentId;

      // 同时尝试scroll-top方式
      this.$nextTick(function () {
        var query = uni.createSelectorQuery().in(_this26);
        query.select("#".concat(commentId)).boundingClientRect(function (rect) {
          if (rect) {
            console.log("\uD83D\uDE80 \u5F3A\u5236\u6EDA\u52A8 - \u627E\u5230\u5143\u7D20:", rect);
            // 计算一个简单的滚动位置
            var targetScrollTop = Math.max(0, rect.top - 100);
            _this26.scrollTop = targetScrollTop;
            console.log("\uD83D\uDE80 \u5F3A\u5236\u6EDA\u52A8 - \u8BBE\u7F6EscrollTop: ".concat(targetScrollTop));
          } else {
            console.warn("\uD83D\uDE80 \u5F3A\u5236\u6EDA\u52A8 - \u672A\u627E\u5230\u5143\u7D20: ".concat(commentId));
          }
        }).exec();
      });
    },
    // 简单滚动测试：直接滚动到指定位置
    testScrollToPosition: function testScrollToPosition() {
      var position = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 300;
      console.log("\uD83E\uDDEA \u6D4B\u8BD5\u6EDA\u52A8\u5230\u4F4D\u7F6E: ".concat(position));
      this.scrollTop = position;
    },
    // 获取当前滚动状态
    getCurrentScrollStatus: function getCurrentScrollStatus() {
      console.log("\uD83D\uDCCA \u5F53\u524D\u6EDA\u52A8\u72B6\u6001:", {
        scrollTop: this.scrollTop,
        scrollIntoView: this.scrollIntoView
      });

      // 获取实际的滚动位置
      var query = uni.createSelectorQuery().in(this);
      query.select('.page-scroll-view').scrollOffset(function (scrollInfo) {
        console.log("\uD83D\uDCCA \u5B9E\u9645\u6EDA\u52A8\u4F4D\u7F6E:", scrollInfo);
      }).exec();
    },
    // 处理评论数据的方法
    processCommentData: function processCommentData(data) {
      // 处理数据，转换字段名称以适配前端展示
      if (!data) {
        console.warn('评论数据为空');
        return [];
      }
      var commentsList = data.comments || [];
      console.log("\u5904\u7406\u8BC4\u8BBA\u6570\u636E\uFF0C\u8BC4\u8BBA\u6570\u91CF:", commentsList.length);
      if (commentsList.length > 0) {
        commentsList.forEach(function (item) {
          if (!item) return;

          // 转换字段名
          item.created_at = item.createdAt || new Date().toISOString();
          item.is_liked = item.isLiked || false;
          item.reply_count = item.replyCount || 0;
          item.likes = item.likes || 0;

          // 确保user对象存在
          if (!item.user) {
            item.user = {
              id: 0,
              nickname: '未知用户',
              avatar: '/static/images/toux.png',
              level: 0
            };
          } else {
            // 处理用户头像为空的情况
            if (!item.user.avatar) {
              item.user.avatar = '/static/images/toux.png';
            }

            // 确保其他用户字段存在
            item.user.nickname = item.user.nickname || '未知用户';
            item.user.level = item.user.level || 0;
          }

          // 确保replies字段存在
          if (!item.replies) {
            item.replies = [];
          } else if (item.replies.length > 0) {
            // 转换回复中的字段名
            item.replies.forEach(function (reply) {
              if (!reply) return;
              reply.created_at = reply.createdAt || new Date().toISOString();
              reply.is_liked = reply.isLiked || false;

              // 确保reply_to存在
              if (reply.replyTo) {
                reply.reply_to = reply.replyTo;
              }

              // 确保user对象存在
              if (!reply.user) {
                reply.user = {
                  id: 0,
                  nickname: '未知用户',
                  avatar: '/static/images/toux.png'
                };
              } else {
                // 处理用户头像为空的情况
                if (!reply.user.avatar) {
                  reply.user.avatar = '/static/images/toux.png';
                }

                // 确保其他用户字段存在
                reply.user.nickname = reply.user.nickname || '未知用户';
              }
            });
          }
        });
      }
      return commentsList;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 664:
/*!****************************************************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?vue&type=style&index=0&id=7270b30e&lang=scss&scoped=true& ***!
  \****************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_style_index_0_id_7270b30e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=style&index=0&id=7270b30e&lang=scss&scoped=true& */ 665);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_style_index_0_id_7270b30e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_style_index_0_id_7270b30e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_style_index_0_id_7270b30e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_style_index_0_id_7270b30e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_vue_vue_type_style_index_0_id_7270b30e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 665:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?vue&type=style&index=0&id=7270b30e&lang=scss&scoped=true& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[650,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesSub/switch/comment.js.map