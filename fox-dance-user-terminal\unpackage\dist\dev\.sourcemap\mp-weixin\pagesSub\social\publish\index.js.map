{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?06f6", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?ec02", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?97c8", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?f53f", "uni-app:///pagesSub/social/publish/index.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?a837", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?a170", "uni-app:///main.js"], "names": ["name", "data", "userInfo", "avatar", "nickname", "postContent", "selectedImages", "selectedTopics", "selectedLocation", "visibility", "showTopicModal", "showLocationModal", "topicKeyword", "allTopics", "id", "postCount", "nearbyLocations", "address", "computed", "canPublish", "visibilityText", "public", "friends", "private", "filteredTopics", "topic", "onLoad", "methods", "loadHotTopics", "hotTags", "console", "goBack", "uni", "title", "content", "success", "chooseImage", "count", "sizeType", "sourceType", "removeImage", "selectTopic", "toggleTopic", "searchTopics", "selectLocation", "selectLocationItem", "setVisibility", "itemList", "publishPost", "postData", "images", "coverImage", "tags", "locationName", "isPublic", "result", "setTimeout", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyKxvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEAC;MACAC;MAEAC;MACAC,YACA;QAAAC;QAAAd;QAAAe;MAAA,GACA;QAAAD;QAAAd;QAAAe;MAAA,GACA;QAAAD;QAAAd;QAAAe;MAAA,GACA;QAAAD;QAAAd;QAAAe;MAAA,GACA;QAAAD;QAAAd;QAAAe;MAAA,GACA;QAAAD;QAAAd;QAAAe;MAAA,GACA;QAAAD;QAAAd;QAAAe;MAAA,GACA;QAAAD;QAAAd;QAAAe;MAAA,GACA;QAAAD;QAAAd;QAAAe;MAAA,GACA;QAAAD;QAAAd;QAAAe;MAAA,EACA;MAEAC,kBACA;QACAF;QACAd;QACAiB;MACA,GACA;QACAH;QACAd;QACAiB;MACA,GACA;QACAH;QACAd;QACAiB;MACA;IAEA;EACA;EACAC;IACAC;MACA;IACA;IAEAC;MACA;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QAAA,OACAC;MAAA,EACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;oBAAA;sBACAf;sBACAd;sBACAe;oBACA;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MACA;QACAC;UACAC;UACAC;UACAC;YACA;cACAH;YACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEAI;MAAA;MACA;MACAJ;QACAK;QACAC;QACAC;QACAJ;UAAA;UACA;QACA;MACA;IACA;IAEAK;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MAAA;MACAd;QACAe;QACAZ;UACA;UACA;QACA;MACA;IACA;IAEAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAhB;kBAAAC;gBAAA;gBAAA;gBAGA;gBACAgB;kBACAf;kBACAgB;kBACAC;kBACAC;oBAAA;kBAAA;kBACAC;kBACAC;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAEAvB;gBAEA;kBACA;kBAEAwB;oBACAxB;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;gBACAE;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpXA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAyB,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pagesSub/social/publish/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=bb7c3636&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bb7c3636\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/publish/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=bb7c3636&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-input/u-input\" */ \"@/components/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.postContent.length\n  var g1 = _vm.selectedImages.length\n  var g2 = _vm.selectedTopics.length\n  var g3 = g2\n    ? _vm.selectedTopics\n        .map(function (t) {\n          return \"#\" + t\n        })\n        .join(\" \")\n    : null\n  var l0 = _vm.__map(_vm.filteredTopics, function (topic, __i0__) {\n    var $orig = _vm.__get_orig(topic)\n    var g4 = _vm.selectedTopics.includes(topic.name)\n    return {\n      $orig: $orig,\n      g4: g4,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showTopicModal = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showLocationModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"publish-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"cancel-btn\" @click=\"goBack\">取消</text>\n        <text class=\"title\">发布帖子</text>\n        <text \n          class=\"publish-btn\" \n          :class=\"{ disabled: !canPublish }\"\n          @click=\"publishPost\"\n        >\n          发布\n        </text>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 用户信息 -->\n      <view class=\"user-section\">\n        <u-avatar :src=\"userInfo.avatar\" size=\"40\"></u-avatar>\n        <text class=\"username\">{{ userInfo.nickname }}</text>\n      </view>\n\n      <!-- 文字输入 -->\n      <view class=\"text-section\">\n        <textarea\n          v-model=\"postContent\"\n          class=\"content-input\"\n          placeholder=\"分享你的生活...\"\n          :maxlength=\"500\"\n          auto-height\n          :show-confirm-bar=\"false\"\n        />\n        <view class=\"char-count\">{{ postContent.length }}/500</view>\n      </view>\n\n      <!-- 图片上传 -->\n      <view class=\"image-section\">\n        <view class=\"image-grid\">\n          <view \n            v-for=\"(image, index) in selectedImages\" \n            :key=\"index\"\n            class=\"image-item\"\n          >\n            <image :src=\"image\" class=\"uploaded-image\" mode=\"aspectFill\" />\n            <view class=\"delete-btn\" @click=\"removeImage(index)\">\n              <u-icon name=\"close\" color=\"#fff\" size=\"16\"></u-icon>\n            </view>\n          </view>\n          <view \n            v-if=\"selectedImages.length < 9\"\n            class=\"add-image-btn\"\n            @click=\"chooseImage\"\n          >\n            <u-icon name=\"camera\" color=\"#999\" size=\"32\"></u-icon>\n            <text class=\"add-text\">添加图片</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 功能选项 -->\n      <view class=\"options-section\">\n        <!-- 话题选择 -->\n        <view class=\"option-item\" @click=\"selectTopic\">\n          <view class=\"option-left\">\n            <u-icon name=\"tags\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">添加话题</text>\n          </view>\n          <view class=\"option-right\">\n            <text v-if=\"selectedTopics.length\" class=\"selected-topics\">\n              {{ selectedTopics.map(t => '#' + t).join(' ') }}\n            </text>\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n\n        <!-- 位置定位 -->\n        <view class=\"option-item\" @click=\"selectLocation\">\n          <view class=\"option-left\">\n            <u-icon name=\"map\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">添加位置</text>\n          </view>\n          <view class=\"option-right\">\n            <text v-if=\"selectedLocation\" class=\"selected-location\">\n              {{ selectedLocation.name }}\n            </text>\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n\n        <!-- 可见性设置 -->\n        <view class=\"option-item\" @click=\"setVisibility\">\n          <view class=\"option-left\">\n            <u-icon name=\"eye\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">可见性</text>\n          </view>\n          <view class=\"option-right\">\n            <text class=\"visibility-text\">{{ visibilityText }}</text>\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n      </view>\n\n      <!-- 提醒文字 -->\n      <view class=\"tips-section\">\n        <text class=\"tips-text\">\n          发布即表示同意《社区公约》，请文明发言，共建和谐社区\n        </text>\n      </view>\n    </scroll-view>\n\n    <!-- 话题选择弹窗 -->\n    <u-popup v-model=\"showTopicModal\" mode=\"bottom\" border-radius=\"20\">\n      <view class=\"topic-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">选择话题</text>\n          <u-icon name=\"close\" @click=\"showTopicModal = false\"></u-icon>\n        </view>\n        <view class=\"topic-search\">\n          <u-input \n            v-model=\"topicKeyword\" \n            placeholder=\"搜索话题\"\n            prefix-icon=\"search\"\n            @input=\"searchTopics\"\n          />\n        </view>\n        <view class=\"topic-list\">\n          <view \n            v-for=\"topic in filteredTopics\" \n            :key=\"topic.id\"\n            class=\"topic-option\"\n            :class=\"{ selected: selectedTopics.includes(topic.name) }\"\n            @click=\"toggleTopic(topic)\"\n          >\n            <text class=\"topic-name\">#{{ topic.name }}</text>\n            <text class=\"topic-count\">{{ topic.postCount }}条帖子</text>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n\n    <!-- 位置选择弹窗 -->\n    <u-popup v-model=\"showLocationModal\" mode=\"bottom\" border-radius=\"20\">\n      <view class=\"location-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">选择位置</text>\n          <u-icon name=\"close\" @click=\"showLocationModal = false\"></u-icon>\n        </view>\n        <view class=\"location-list\">\n          <view \n            v-for=\"location in nearbyLocations\" \n            :key=\"location.id\"\n            class=\"location-option\"\n            @click=\"selectLocationItem(location)\"\n          >\n            <u-icon name=\"map-pin\" color=\"#2979ff\" size=\"16\"></u-icon>\n            <view class=\"location-info\">\n              <text class=\"location-name\">{{ location.name }}</text>\n              <text class=\"location-address\">{{ location.address }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport { createPost, getHotTags } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'SocialPublish',\n  data() {\n    return {\n      userInfo: {\n        avatar: 'https://picsum.photos/100/100?random=999',\n        nickname: '我的昵称'\n      },\n      postContent: '',\n      selectedImages: [],\n      selectedTopics: [],\n      selectedLocation: null,\n      visibility: 'public', // public, friends, private\n      \n      showTopicModal: false,\n      showLocationModal: false,\n      \n      topicKeyword: '',\n      allTopics: [\n        { id: 1, name: '街舞', postCount: 1234 },\n        { id: 2, name: '现代舞', postCount: 856 },\n        { id: 3, name: '芭蕾', postCount: 642 },\n        { id: 4, name: '拉丁舞', postCount: 789 },\n        { id: 5, name: '爵士舞', postCount: 456 },\n        { id: 6, name: '民族舞', postCount: 321 },\n        { id: 7, name: '古典舞', postCount: 298 },\n        { id: 8, name: '舞蹈教学', postCount: 567 },\n        { id: 9, name: '舞蹈比赛', postCount: 234 },\n        { id: 10, name: '舞蹈培训', postCount: 189 }\n      ],\n      \n      nearbyLocations: [\n        {\n          id: 1,\n          name: '星巴克咖啡',\n          address: '北京市朝阳区三里屯太古里'\n        },\n        {\n          id: 2,\n          name: '三里屯太古里',\n          address: '北京市朝阳区三里屯路19号'\n        },\n        {\n          id: 3,\n          name: '朝阳公园',\n          address: '北京市朝阳区朝阳公园南路1号'\n        }\n      ]\n    }\n  },\n  computed: {\n    canPublish() {\n      return this.postContent.trim().length > 0 || this.selectedImages.length > 0\n    },\n    \n    visibilityText() {\n      const map = {\n        public: '公开',\n        friends: '仅朋友可见',\n        private: '仅自己可见'\n      }\n      return map[this.visibility]\n    },\n    \n    filteredTopics() {\n      if (!this.topicKeyword) return this.allTopics\n      return this.allTopics.filter(topic => \n        topic.name.includes(this.topicKeyword)\n      )\n    }\n  },\n  onLoad() {\n    this.loadHotTopics()\n  },\n  methods: {\n    // 加载热门话题\n    async loadHotTopics() {\n      try {\n        const hotTags = await getHotTags(20)\n        if (hotTags && hotTags.length > 0) {\n          this.allTopics = hotTags.map(tag => ({\n            id: tag.tagId || tag.id,\n            name: tag.tagName || tag.name,\n            postCount: tag.postCount || 0\n          }))\n        }\n      } catch (error) {\n        console.error('加载热门话题失败:', error)\n        // 使用默认话题列表\n      }\n    },\n    goBack() {\n      if (this.postContent || this.selectedImages.length) {\n        uni.showModal({\n          title: '提示',\n          content: '确定要放弃编辑吗？',\n          success: (res) => {\n            if (res.confirm) {\n              uni.navigateBack()\n            }\n          }\n        })\n      } else {\n        uni.navigateBack()\n      }\n    },\n\n    chooseImage() {\n      const maxCount = 9 - this.selectedImages.length\n      uni.chooseImage({\n        count: maxCount,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          this.selectedImages.push(...res.tempFilePaths)\n        }\n      })\n    },\n\n    removeImage(index) {\n      this.selectedImages.splice(index, 1)\n    },\n\n    selectTopic() {\n      this.showTopicModal = true\n    },\n\n    toggleTopic(topic) {\n      const index = this.selectedTopics.indexOf(topic.name)\n      if (index > -1) {\n        this.selectedTopics.splice(index, 1)\n      } else {\n        if (this.selectedTopics.length < 3) {\n          this.selectedTopics.push(topic.name)\n        } else {\n          this.$u.toast('最多选择3个话题')\n        }\n      }\n    },\n\n    searchTopics() {\n      // 搜索话题逻辑\n    },\n\n    selectLocation() {\n      this.showLocationModal = true\n    },\n\n    selectLocationItem(location) {\n      this.selectedLocation = location\n      this.showLocationModal = false\n    },\n\n    setVisibility() {\n      uni.showActionSheet({\n        itemList: ['公开', '仅朋友可见', '仅自己可见'],\n        success: (res) => {\n          const visibilityMap = ['public', 'friends', 'private']\n          this.visibility = visibilityMap[res.tapIndex]\n        }\n      })\n    },\n\n    async publishPost() {\n      if (!this.canPublish) return\n\n      uni.showLoading({ title: '发布中...' })\n\n      try {\n        // 构建发布数据\n        const postData = {\n          content: this.postContent.trim(),\n          images: this.selectedImages,\n          coverImage: this.selectedImages.length > 0 ? this.selectedImages[0] : null,\n          tags: this.selectedTopics.map(topic => topic.name),\n          locationName: this.location,\n          isPublic: this.visibility === 'public' ? 1 : 0\n        }\n\n        // 调用发布API\n        const result = await createPost(postData)\n\n        uni.hideLoading()\n\n        if (result) {\n          this.$u.toast('发布成功')\n\n          setTimeout(() => {\n            uni.navigateBack()\n          }, 1500)\n        } else {\n          this.$u.toast('发布失败，请重试')\n        }\n\n      } catch (error) {\n        console.error('发布帖子失败:', error)\n        uni.hideLoading()\n        this.$u.toast('发布失败，请重试')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.publish-container {\n  min-height: 100vh;\n\n  background: #f8f9fa;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 1px solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n}\n\n.cancel-btn, .publish-btn {\n  font-size: 16px;\n  color: #2979ff;\n}\n\n.publish-btn.disabled {\n  color: #ccc;\n}\n\n.title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.content {\n  margin-top: calc(44px + var(--status-bar-height));\n  padding: 16px;\n  width: auto;\n}\n\n.user-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.username {\n  margin-left: 12px;\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.text-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  position: relative;\n}\n\n.content-input {\n  width: 100%;\n  min-height: 120px;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #333;\n}\n\n.char-count {\n  position: absolute;\n  bottom: 12px;\n  right: 16px;\n  font-size: 12px;\n  color: #999;\n}\n\n.image-section {\n  margin-bottom: 16px;\n}\n\n.image-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.image-item {\n  position: relative;\n  width: calc(33.33% - 6px);\n  height: 100px;\n}\n\n.uploaded-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n}\n\n.delete-btn {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  width: 24px;\n  height: 24px;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-image-btn {\n  width: calc(33.33% - 6px);\n  height: 100px;\n  background: #f5f5f5;\n  border: 2px dashed #ddd;\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-text {\n  font-size: 12px;\n  color: #999;\n  margin-top: 4px;\n}\n\n.options-section {\n  background: #fff;\n  border-radius: 12px;\n  margin-bottom: 16px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.option-item:last-child {\n  border-bottom: none;\n}\n\n.option-left {\n  display: flex;\n  align-items: center;\n}\n\n.option-text {\n  margin-left: 12px;\n  font-size: 15px;\n  color: #333;\n}\n\n.option-right {\n  display: flex;\n  align-items: center;\n}\n\n.selected-topics, .selected-location, .visibility-text {\n  font-size: 14px;\n  color: #666;\n  margin-right: 8px;\n}\n\n.tips-section {\n  padding: 16px;\n}\n\n.tips-text {\n  font-size: 12px;\n  color: #999;\n  line-height: 1.4;\n  text-align: center;\n}\n\n.topic-modal, .location-modal {\n  background: #fff;\n  border-radius: 20px 20px 0 0;\n  max-height: 60vh;\n}\n\n.modal-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20px 20px 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.modal-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.topic-search {\n  padding: 16px 20px;\n}\n\n.topic-list, .location-list {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.topic-option {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.topic-option.selected {\n  background: #f0f8ff;\n}\n\n.topic-name {\n  font-size: 15px;\n  color: #333;\n}\n\n.topic-count {\n  font-size: 12px;\n  color: #999;\n}\n\n.location-option {\n  display: flex;\n  align-items: center;\n  padding: 12px 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.location-info {\n  margin-left: 12px;\n  flex: 1;\n}\n\n.location-name {\n  font-size: 15px;\n  color: #333;\n  display: block;\n}\n\n.location-address {\n  font-size: 12px;\n  color: #999;\n  margin-top: 2px;\n  display: block;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752811212502\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/publish/index.vue'\ncreatePage(Page)"], "sourceRoot": ""}