<template>
	<view class="switchStores" v-if="loding">
		
		<view class="mdqh_head">
			<view class="mdqh_head_t">
				<view class="mdqh_head_t_l"><image src="/static/images/icon18-1.png"></image>{{storeInfo.name}}</view>
				<view class="mdqh_head_t_r"><image src="/static/images/search.png"></image><input type="text" placeholder="搜索门店名称" placeholder-style="color: #999999;" v-model="keywords"  confirm-type="search" @confirm="searchTap(keywords)" /></view>
			</view>
			<view class="mdqh_head_b">
				<view class="mdqh_head_b_li" :class="tabIndex == 0 ? 'mdqh_head_b_li_ac' : ''" @click="tabTap(0)">全部</view>
				<view class="mdqh_head_b_li" :class="tabIndex == 1 ? 'mdqh_head_b_li_ac' : ''" @click="tabTap(1)">入驻
					<image src="/static/images/icon46.png" v-if="rzIndex == 0"></image>
					<image src="/static/images/icon46-1.png" v-if="rzIndex == 1"></image>
					<image src="/static/images/icon46-2.png" v-if="rzIndex == 2"></image>
				</view>
				<view class="mdqh_head_b_li" :class="tabIndex == 2 ? 'mdqh_head_b_li_ac' : ''" @click="tabTap(2)">最近距离
					<image src="/static/images/icon46.png" v-if="jlIndex == 0"></image>
					<image src="/static/images/icon46-1.png" v-if="jlIndex == 1"></image>
					<image src="/static/images/icon46-2.png" v-if="jlIndex == 2"></image>
				</view>
			</view>
		</view>
		
		<view class="mdqh_con">
			<view class="mdqh_con_li" :style="'background:' + item.background" v-for="(item,index) in storesLists" :key="index" @click="navTo('/pages/index/storesDetail?id=' + item.id)">
				<image :src="imgbaseUrl + item.image" class="mdqh_con_li_l"></image>
				<view class="mdqh_con_li_r">
					<view class="mdqh_con_li_r_a" :style="'color:' + item.written_words">{{item.name}}</view>
					<view class="mdqh_con_li_r_b">{{item.introduce}}</view>
					<view class="mdqh_con_li_r_c" @click="dhTap(item)"><image src="/static/images/icon35.png"></image>{{item.address}}</view>
					<view class="mdqh_con_li_r_d"><text>距离你{{item.distance}}km</text><view  :style="'background:' + item.button" @click.stop="qhmdTap(item)" v-if="!mdlist">切换门店</view></view>
				</view>
			</view>
		</view>
		
		<view class="gg_zwsj" style="margin-top:92rpx;" v-if="storesLists.length == 0">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无门店</text>
			</view>
		</view>
		
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	storeListsApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			loding:false,
			storesLists:[],
			name:'',
			keywords:'',
			tabIndex:0,
			rzIndex:0,
			jlIndex:0,
			imgbaseUrl:'',
			storeInfo:{address:''},
			mdlist:false,
		}
	},
	onLoad(option) {
		this.mdlist = option.mdlist ? true : false
		uni.setNavigationBarTitle({
			title:option.mdlist ? '门店列表' : '切换门店'
		})
	},
	onShow() {
		this.imgbaseUrl = this.$baseUrl;
		this.storeData();//门店列表
		this.storeInfo = uni.getStorageSync('storeInfo')
	},
	methods: {
		//切换门店
		qhmdTap(item){
			console.log(item)
			if(item.id == uni.getStorageSync('storeInfo').id){
				uni.showToast({
					title: '当前门店，无需切换',
					icon: 'none',
					duration: 1000
				})
				return false;
			}
			/*item.address = '事实上事实上'
			item.id = 2
			item.name = '反反复复反反复复凤飞飞'*/
			uni.setStorageSync('qhwc',1);
			uni.setStorageSync('storeInfo',{
				address: item.address,
				id: item.id,
				name: item.name,
			})
			uni.showToast({
				title: '切换成功',
				icon: 'success',
				duration: 1000
			})
			setTimeout(function(){
				uni.navigateBack()
			},1000)
		},
		//搜索
		searchTap(){
			this.storeData();//门店列表
		},
		tabTap(index){
			this.tabIndex = index;
			if(index == 1){
				var rzIndex = this.rzIndex;
				rzIndex++
				this.rzIndex = rzIndex++
				if(this.rzIndex == 3){
					this.rzIndex = 1
				}
				this.jlIndex = 0;
			}
			if(index == 2){
				var jlIndex = this.jlIndex;
				jlIndex++
				this.jlIndex = jlIndex++
				if(this.jlIndex == 3){
					this.jlIndex = 1
				}
				this.rzIndex = 0;
			}
			this.storeData();//门店列表
		},
		//门店列表
		storeData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			
			if(this.tabIndex == 0){
				var type = 1;
			}else if(this.tabIndex == 1){
				var type = this.rzIndex == 0 ? 1 : this.rzIndex == 1 ? 2 : this.rzIndex == 2 ? 3 : 1;
			}else if(this.tabIndex == 2){
				var type = this.jlIndex == 0 ? 1 : this.jlIndex == 1 ? 4 : this.jlIndex == 2 ? 5 : 1;
			}
			// 1：全部 2：入驻：升序 3：入驻：倒序 4：距离：升序 5：距离：倒序
			
			storeListsApi({
				name:that.keywords,
				type:type,
				longitude:uni.getStorageSync('postion').longitude,
				latitude:uni.getStorageSync('postion').latitude,
				limit:9999,
			}).then(res => {
				console.log('门店列表',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.loding = true;
					that.storesLists = res.data.data;
				}
			})
			
		},
		//导航
		dhTap(item){
			var that = this;
			uni.openLocation({
				name:item.address,
				latitude: item.latitude*1,
				longitude: item.longitude*1,
				success: function () {
					console.log('success');
				}
			});
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.switchStores{overflow: hidden;}
page{padding-bottom: 0;}
</style>