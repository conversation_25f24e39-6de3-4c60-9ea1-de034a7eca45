-- ========================================
-- 阶段三：社交功能接口开发 - 数据库更新脚本
-- 执行时间：2025-07-17
-- 说明：为支持关注系统和私信系统功能添加必要的数据库表和字段
-- ========================================

-- 1. 创建关注关系表
CREATE TABLE IF NOT EXISTS follows (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关注ID',
    follower_id BIGINT NOT NULL COMMENT '关注者用户ID',
    following_id BIGINT NOT NULL COMMENT '被关注者用户ID',
    status TINYINT(1) DEFAULT 1 COMMENT '关注状态：0-已取消，1-已关注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    INDEX idx_follower_id (follower_id),
    INDEX idx_following_id (following_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    UNIQUE KEY uk_follower_following (follower_id, following_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注关系表';

-- 2. 创建会话表
CREATE TABLE IF NOT EXISTS conversations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '会话ID',
    user1_id BIGINT NOT NULL COMMENT '用户1 ID（较小的用户ID）',
    user2_id BIGINT NOT NULL COMMENT '用户2 ID（较大的用户ID）',
    last_message_id BIGINT COMMENT '最后一条消息ID',
    last_message_time DATETIME COMMENT '最后消息时间',
    user1_unread_count INT DEFAULT 0 COMMENT '用户1未读消息数',
    user2_unread_count INT DEFAULT 0 COMMENT '用户2未读消息数',
    user1_deleted TINYINT(1) DEFAULT 0 COMMENT '用户1是否删除会话',
    user2_deleted TINYINT(1) DEFAULT 0 COMMENT '用户2是否删除会话',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    INDEX idx_user1_id (user1_id),
    INDEX idx_user2_id (user2_id),
    INDEX idx_last_message_time (last_message_time),
    UNIQUE KEY uk_user1_user2 (user1_id, user2_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='私信会话表';

-- 3. 创建私信消息表
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '消息ID',
    conversation_id BIGINT NOT NULL COMMENT '会话ID',
    sender_id BIGINT NOT NULL COMMENT '发送者用户ID',
    receiver_id BIGINT NOT NULL COMMENT '接收者用户ID',
    message_type TINYINT(1) DEFAULT 1 COMMENT '消息类型：1-文字，2-图片，3-语音，4-视频，5-文件',
    content TEXT COMMENT '消息内容',
    media_url VARCHAR(500) COMMENT '媒体文件URL',
    media_size BIGINT COMMENT '媒体文件大小（字节）',
    media_duration INT COMMENT '媒体文件时长（秒）',
    reply_to_message_id BIGINT COMMENT '引用消息ID（回复消息时使用）',
    is_read TINYINT(1) DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
    status TINYINT(1) DEFAULT 2 COMMENT '消息状态：1-发送中，2-已发送，3-已送达，4-已读，5-发送失败',
    read_time DATETIME COMMENT '阅读时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_message_type (message_type),
    INDEX idx_is_read (is_read),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    INDEX idx_reply_to_message_id (reply_to_message_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='私信消息表';

-- 4. 为ba_user表添加关注统计字段（如果不存在）
ALTER TABLE ba_user ADD COLUMN following_count INT DEFAULT 0 COMMENT '关注数' AFTER dance_type;
ALTER TABLE ba_user ADD COLUMN follower_count INT DEFAULT 0 COMMENT '粉丝数' AFTER following_count;

-- 5. 创建用户在线状态表（可选）
CREATE TABLE IF NOT EXISTS user_online_status (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    is_online TINYINT(1) DEFAULT 0 COMMENT '是否在线：0-离线，1-在线',
    last_active_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    device_type VARCHAR(50) COMMENT '设备类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_is_online (is_online),
    INDEX idx_last_active_time (last_active_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户在线状态表';

-- 6. 创建消息类型枚举说明表（可选，用于前端显示）
CREATE TABLE IF NOT EXISTS message_types (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '类型ID',
    type_code INT NOT NULL COMMENT '类型代码',
    type_name VARCHAR(50) NOT NULL COMMENT '类型名称',
    type_desc VARCHAR(200) COMMENT '类型描述',
    icon_url VARCHAR(200) COMMENT '图标URL',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    UNIQUE KEY uk_type_code (type_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息类型表';

-- 7. 插入消息类型数据
INSERT IGNORE INTO message_types (type_code, type_name, type_desc, sort_order) VALUES
(1, '文字', '文字消息', 1),
(2, '图片', '图片消息', 2),
(3, '语音', '语音消息', 3),
(4, '视频', '视频消息', 4),
(5, '文件', '文件消息', 5);

-- 8. 创建关注系统相关的触发器（可选）
-- 关注时更新统计
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_follow_insert_update_stats
AFTER INSERT ON follows
FOR EACH ROW
BEGIN
    IF NEW.status = 1 THEN
        -- 更新关注者的关注数
        UPDATE ba_user SET following_count = following_count + 1 WHERE id = NEW.follower_id;
        -- 更新被关注者的粉丝数
        UPDATE ba_user SET follower_count = follower_count + 1 WHERE id = NEW.following_id;
    END IF;
END$$

-- 取消关注时更新统计
CREATE TRIGGER IF NOT EXISTS tr_follow_update_stats
AFTER UPDATE ON follows
FOR EACH ROW
BEGIN
    IF OLD.status = 1 AND NEW.status = 0 THEN
        -- 取消关注：减少统计
        UPDATE ba_user SET following_count = GREATEST(following_count - 1, 0) WHERE id = NEW.follower_id;
        UPDATE ba_user SET follower_count = GREATEST(follower_count - 1, 0) WHERE id = NEW.following_id;
    ELSEIF OLD.status = 0 AND NEW.status = 1 THEN
        -- 重新关注：增加统计
        UPDATE ba_user SET following_count = following_count + 1 WHERE id = NEW.follower_id;
        UPDATE ba_user SET follower_count = follower_count + 1 WHERE id = NEW.following_id;
    END IF;
END$$
DELIMITER ;

-- 9. 创建消息系统相关的触发器（可选）
-- 发送消息时更新会话
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_message_insert_update_conversation
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    DECLARE conv_id BIGINT;
    DECLARE user1_id BIGINT;
    DECLARE user2_id BIGINT;
    
    -- 确定用户ID顺序（小的在前）
    IF NEW.sender_id < NEW.receiver_id THEN
        SET user1_id = NEW.sender_id;
        SET user2_id = NEW.receiver_id;
    ELSE
        SET user1_id = NEW.receiver_id;
        SET user2_id = NEW.sender_id;
    END IF;
    
    -- 更新或创建会话
    INSERT INTO conversations (user1_id, user2_id, last_message_id, last_message_time, 
                              user1_unread_count, user2_unread_count)
    VALUES (user1_id, user2_id, NEW.id, NEW.create_time, 
            CASE WHEN NEW.receiver_id = user1_id THEN 1 ELSE 0 END,
            CASE WHEN NEW.receiver_id = user2_id THEN 1 ELSE 0 END)
    ON DUPLICATE KEY UPDATE
        last_message_id = NEW.id,
        last_message_time = NEW.create_time,
        user1_unread_count = CASE WHEN NEW.receiver_id = user1_id THEN user1_unread_count + 1 ELSE user1_unread_count END,
        user2_unread_count = CASE WHEN NEW.receiver_id = user2_id THEN user2_unread_count + 1 ELSE user2_unread_count END,
        update_time = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- 10. 初始化现有用户的关注统计（可选）
UPDATE ba_user u SET 
    following_count = (
        SELECT COUNT(*) FROM follows f 
        WHERE f.follower_id = u.id AND f.status = 1 AND f.is_delete = 0
    ),
    follower_count = (
        SELECT COUNT(*) FROM follows f 
        WHERE f.following_id = u.id AND f.status = 1 AND f.is_delete = 0
    );

-- 11. 创建一些测试数据（可选）
-- 插入一些关注关系
INSERT IGNORE INTO follows (follower_id, following_id, status) VALUES
(1, 2, 1),
(1, 3, 1),
(2, 1, 1),
(2, 3, 1),
(3, 1, 1);

-- 插入一些测试消息
INSERT IGNORE INTO conversations (user1_id, user2_id, last_message_time) VALUES
(1, 2, NOW()),
(1, 3, NOW());

-- ========================================
-- 执行完成提示
-- ========================================
SELECT '阶段三数据库更新完成！' AS message,
       '已创建关注系统和私信系统相关表' AS details,
       NOW() AS completion_time;
