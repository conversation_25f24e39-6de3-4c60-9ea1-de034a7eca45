<template>
	<view class="user" :style="{ '--qjbutton-color': qjbutton }" v-if="loding">
		<u-navbar :is-back="false" title="个人中心" :background="{ background: 'rgba(255,255, 255,' + navBg + ')' }"
			:border-bottom="false" :title-color="navBg==1?'#333':'#333' " title-size="32">
		</u-navbar>
		
		<view class="min_one">
			
			<image src="/static/images/toux.png" mode="aspectFill" class="min_one_l" style="border:1px solid #e4b2b2" v-if="!isLogined"></image>
			<image :src="userInfo.avatar == '' ? '/static/images/toux.png' : imgbaseUrl + userInfo.avatar" mode="aspectFill" class="min_one_l" style="border:1px solid #e4b2b2" v-if="isLogined"></image>
			<view class="min_one_c" @click="navTo('/pages/mine/editinformation')">
				<view class="min_one_c_a" v-if="isLogined">{{userInfo.nickname == '' ? '微信昵称' : userInfo.nickname}}<image src="/static/images/icon1.png" v-if="userInfo.is_member > 0"></image></view>
				<view class="min_one_c_b" v-if="isLogined">今天是FOX陪伴你的第<text>{{userInfo.day}}</text>天</view>
				<view class="min_one_c_a" v-else>登录体验更多功能</view>
			</view>
			<view class="min_one_r" v-if="isLogined" @click="zsewmToggle = !zsewmToggle">
				<image src='/static/images/icon2.png'></image>
				<image src='/static/images/index_shop_more.png'></image>
			</view>
		</view>
		
		<view class="min_two">
			<view class="min_two_li">
				<view>{{isLogined ? userInfo.train_count : '0'}}次</view>
				<view>训练次数</view>
				<text></text>
			</view>
			<view class="min_two_li">
				<view>{{isLogined ? userInfo.train_day : '0'}}天</view>
				<view>训练天数</view>
				<text></text>
			</view>
			<view class="min_two_li">
				<view>{{isLogined ? userInfo.train_time : '0'}}小时</view>
				<view>训练时长</view>
			</view>
		</view>
		
		<view class="min_thr">
			<view class="min_thr_l" @click="navTo('/pages/mine/memberCard/myMemberCard?avatar=' + userInfo.avatar)">
				<view class="min_thr_l_t">
					<view><text>我的会员</text><text style="opacity:0;"></text></view>
					<image src="/static/images/icon6.png"></image>
				</view>
				<image src="/static/images/icon3.png" class="min_thr_l_b"></image>
			</view>
			<view class="min_thr_r">
				<view class="min_thr_r_t" @click="navTo('/pages/mine/order/order')">
					<view class="min_thr_l_t">
						<view><text>我的订单</text><text style="opacity:0;"></text></view>
						<image src="/static/images/icon6.png"></image>
					</view>
					<image src="/static/images/icon4.png" class="min_thr_l_b"></image>
				</view>
				<view class="min_thr_r_t" @click="navTo('/pages/mine/myCourse/myCourse')">
					<view class="min_thr_l_t">
						<view><text>我的课程</text><text style="opacity:0;"></text></view>
						<image src="/static/images/icon6.png"></image>
					</view>
					<image src="/static/images/icon5.png" class="min_thr_l_b"></image>
				</view>
			</view>
		</view>
		<!-- <view @click="navTo('/pages/Schedule/confirmOrder?id=1&storeid=1')">测试</view> -->
		<view class="min_fou">
			<!-- <view class="min_fou_l">
				<view class="min_fou_l_t"><view>我的积分</view><text>兑好礼</text></view>
				<view class="min_fou_l_b" @click="navTo('/pages/mine/integral')">
					<view class="min_fou_l_b_a">当前积分</view>
					<view class="min_fou_l_b_b">{{isLogined ? userInfo.score : 0}}</view>
					<view class="min_fou_l_b_c">查看详情</view>
				</view>
			</view> -->
			<view class="min_fou_l" style="width: 100%;">
				<view class="min_fou_l_t"><view>邀请有礼</view></view>
				<view class="min_fou_r_b" @click="navTo('/pages/mine/invitation')">
					<view class="min_fou_r_b_a">邀请新用户</view>
					<view class="min_fou_r_b_b">超多福利等你来拿!</view>
					<view class="min_fou_r_b_c">立即邀请</view>
					<image src="/static/images/icon15.png"></image>
				</view>
			</view>
		</view>
		
		<view class="min_five">
			<view class="min_five_t">更多功能</view>
			<view class="min_five_b"> 
				<view @click="userReportTap"><image src="/static/images/icon7.png"></image><text>用户报告</text></view>
				<view @click="navTo('/pages/mine/lessonPackage/lessonPackage')"><image src="/static/images/icon8.png"></image><text>我的课包</text></view>
				<view @click="navTo('/pages/mine/couponbag')"><image src="/static/images/icon9.png"></image><text>我的券包</text></view>
				<view @click="navTo('/pages/mine/ranking')"><image src="/static/images/icon10.png"></image><text>排行榜</text></view>
				<view @click="navTo('/pages/mine/messageCenter')"><image src="/static/images/icon11.png"></image><text>消息中心</text></view>
				<!-- <view><image src="/static/images/icon12.png"></image><text>联系客服</text><button open-type="contact"></button></view> -->
				<view @click="navTo('/pages/mine/leave/leave')"><image src="/static/images/icon13.png"></image><text>请假</text></view>
				<view @click="navTo('/pages/mine/settings/settings')"><image src="/static/images/icon14.png"></image><text>设置</text></view>
			</view>
		</view>
		
		<view style="width: 100%;height:220rpx;"></view>
		
		<!-- 专属个人码 go -->
		<view class="gg_rgba" v-if="zsewmToggle"></view>
		<view class="min_ewm" v-if="zsewmToggle">
			<view class="min_ewm_t">专属个人码<image src="/static/images/icon16.png" @click="zsewmToggle = !zsewmToggle"></image></view>
			<!-- <image :src="imgbaseUrl + userInfo.share" class="min_ewm_b"></image> -->
			<image :src="userInfo.share" class="min_ewm_b"></image>
			<view class="min_ewm_c" @click="imgUploadTap">保存</view>
		</view>
		<!-- 专属个人码 end -->
		
		<tabbar ref="tabbar" :current="4"></tabbar>
	</view>
</template>


<script>
import {
	userInfoApi,
} from '@/config/http.achieve.js'
import tabbar from '@/components/tabbar.vue'
export default {
	components: {
		tabbar,
	},
	data() {
		return {
			loding:false,
			isLogined:true,
			navBg:'',
			zsewmToggle:false,//专属二维码
			userInfo:{
				avatar:'',
				train_count:0,
				train_day:0,
				train_time:0,
				score:0,
			},
			imgbaseUrl:'',//图片地址
			qjbutton:'#131315',
		}
	},
	onPageScroll(e) {
		const top = uni.upx2px(100)
		const {
			scrollTop
		} = e
		let percent = scrollTop / top > 1 ? 1 : scrollTop / top
		this.navBg = percent
	},
	onLoad() {
		// var xx = 0.0099999997
		// console.log(xx.toFixed(2)*1,'测试')
		this.qjbutton = uni.getStorageSync('storeInfo').button
		uni.hideTabBar()
	},
	onShow() {
		
		uni.hideTabBar()
		// uni.setStorageSync('token','7cf9abb4-a9cd-4fcf-a466-a30a9389bd71')
		this.imgbaseUrl = this.$baseUrl;
		this.isLogined = uni.getStorageSync('token') ? true : false;
		if(this.isLogined){
			this.userData();//个人信息
			console.log(this.$baseUrl,'this.$baseUrl')
		}else{
			this.loding = true;
		}
		
		if(uni.getStorageSync('selectCards')){
			uni.removeStorageSync('selectCards')
		}
		if(this.$refs.tabbar){
			this.$refs.tabbar.setColor();
		}
	},
	methods: {
		//个人信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userInfoApi({}).then(res => {
				console.log('个人中心',res)
				if (res.code == 1) {
					that.loding = true;
					that.userInfo = res.data;
					uni.hideLoading();
				}
			})
		},
		//保存图片
		imgUploadTap(){
			var that = this;
			uni.showLoading({
				title: '加载中'
			});
			// this.imgbaseUrl + 
			uni.downloadFile({
			  url: this.userInfo.share,
			  success: res => {
			    uni.saveImageToPhotosAlbum({
			      filePath: res.tempFilePath,
			      success() {
					uni.hideLoading();
			        uni.showToast({
			          title: '保存成功'
			        })
			      },
				  fail: function(e) {
				  	uni.hideLoading();
				  	console.log(e, '保存失败');
				  	uni.showToast({
				  		icon: 'none',
				  		mask: true,
				  		title: '保存失败', //保存路径
				  		duration: 3000
				  	});
				  }
			    })
			  }
			})
		},
		//用户报告跳转
		userReportTap(){
			if(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				//setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				//},1000)
				return false;
			}
			uni.navigateTo({
				url:'/pages/mine/userReport/userReport'
			})
			/*var type = 0
			uni.navigateTo({
				url:type == 0 ? '/pages/mine/userReport/weeksUserReport?id=' + item.id : type == 1 ? '/pages/mine/userReport/monthUserReport?id=' + item.id : type == 2 ? '/pages/mine/userReport/yearsUserReport?id=' + item.id : ''
			})*/
		},
		navTo(url,ismd){
			if(ismd){
				uni.navigateTo({
					url:url
				});
				return false;
			}
			var that = this;
			if(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				//setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				//},1000)
			}else{
				uni.navigateTo({
					url:url
				})
			}
		},
	}
}
</script>

<style scoped lang="scss">
page{background-image: linear-gradient(130deg,#E3D5D4,#F6F6F6);padding-bottom: 0;background-color: transparent!important;}
// page{background-image: linear-gradient(130deg,red,yellow);}
</style>