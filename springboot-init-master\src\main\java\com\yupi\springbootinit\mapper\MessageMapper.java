package com.yupi.springbootinit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yupi.springbootinit.model.entity.Message;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 私信消息映射器
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface MessageMapper extends BaseMapper<Message> {

    /**
     * 获取用户未读消息数
     *
     * @param userId 用户ID
     * @return 未读消息数
     */
    @Select("SELECT COUNT(*) FROM messages WHERE receiver_id = #{userId} AND is_read = 0 AND is_delete = 0")
    Integer getUnreadCount(@Param("userId") Long userId);

    /**
     * 获取会话未读消息数
     *
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 未读消息数
     */
    @Select("SELECT COUNT(*) FROM messages WHERE conversation_id = #{conversationId} AND receiver_id = #{userId} AND is_read = 0 AND is_delete = 0")
    Integer getConversationUnreadCount(@Param("conversationId") Long conversationId, @Param("userId") Long userId);

    /**
     * 获取两个用户之间的最后一条消息
     *
     * @param userId1 用户1 ID
     * @param userId2 用户2 ID
     * @return 最后一条消息
     */
    @Select("SELECT * FROM messages " +
            "WHERE ((sender_id = #{userId1} AND receiver_id = #{userId2}) " +
            "OR (sender_id = #{userId2} AND receiver_id = #{userId1})) " +
            "AND is_delete = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    Message getLastMessage(@Param("userId1") Long userId1, @Param("userId2") Long userId2);

    /**
     * 批量标记消息已读
     *
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 影响行数
     */
    @Update("<script>" +
            "UPDATE messages SET is_read = 1, read_time = NOW(), status = 4 " +
            "WHERE receiver_id = #{userId} AND is_read = 0 AND id IN " +
            "<foreach collection='messageIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    Integer batchMarkAsRead(@Param("messageIds") List<Long> messageIds, @Param("userId") Long userId);

    /**
     * 标记会话所有消息已读
     *
     * @param currentUserId 当前用户ID
     * @param otherUserId 对方用户ID
     * @return 影响行数
     */
    @Update("UPDATE messages SET is_read = 1, read_time = NOW(), status = 4 " +
            "WHERE sender_id = #{otherUserId} AND receiver_id = #{currentUserId} AND is_read = 0 AND is_delete = 0")
    Integer markConversationAsRead(@Param("currentUserId") Long currentUserId, @Param("otherUserId") Long otherUserId);

    /**
     * 获取用户的会话列表（基于最后消息时间排序）
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 会话用户ID列表
     */
    @Select("SELECT CASE " +
            "WHEN sender_id = #{userId} THEN receiver_id " +
            "ELSE sender_id END as other_user_id, " +
            "MAX(create_time) as last_message_time " +
            "FROM messages " +
            "WHERE (sender_id = #{userId} OR receiver_id = #{userId}) AND is_delete = 0 " +
            "GROUP BY other_user_id " +
            "ORDER BY last_message_time DESC " +
            "LIMIT #{limit}")
    List<Long> getConversationUserIds(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 获取用户发送的消息数
     *
     * @param userId 用户ID
     * @return 发送消息数
     */
    @Select("SELECT COUNT(*) FROM messages WHERE sender_id = #{userId} AND is_delete = 0")
    Integer getSentMessageCount(@Param("userId") Long userId);

    /**
     * 获取用户接收的消息数
     *
     * @param userId 用户ID
     * @return 接收消息数
     */
    @Select("SELECT COUNT(*) FROM messages WHERE receiver_id = #{userId} AND is_delete = 0")
    Integer getReceivedMessageCount(@Param("userId") Long userId);

    /**
     * 搜索消息
     *
     * @param userId 用户ID
     * @param keyword 关键词
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 消息列表
     */
    @Select("SELECT * FROM messages " +
            "WHERE (sender_id = #{userId} OR receiver_id = #{userId}) " +
            "AND content LIKE CONCAT('%', #{keyword}, '%') " +
            "AND is_delete = 0 " +
            "ORDER BY create_time DESC " +
            "LIMIT #{offset}, #{limit}")
    List<Message> searchMessages(@Param("userId") Long userId, 
                                @Param("keyword") String keyword, 
                                @Param("offset") Integer offset, 
                                @Param("limit") Integer limit);

    /**
     * 删除会话（软删除）
     *
     * @param currentUserId 当前用户ID
     * @param otherUserId 对方用户ID
     * @return 影响行数
     */
    @Update("UPDATE messages SET is_delete = 1 " +
            "WHERE ((sender_id = #{currentUserId} AND receiver_id = #{otherUserId}) " +
            "OR (sender_id = #{otherUserId} AND receiver_id = #{currentUserId})) " +
            "AND is_delete = 0")
    Integer deleteConversation(@Param("currentUserId") Long currentUserId, @Param("otherUserId") Long otherUserId);
}
