<template>
	<view class="confirmOrder jpdh" :style="{ '--qjbutton-color': qjbutton }" v-if="productxq.jpid">
		
		<view class="qrdd_a" @click="goToAddr('diancan')" v-if="shouAddr.area == ''"><image src="/static/images/icon33.png"></image>添加收货地址</view>
		<view class="qrdd_b" @click="goToAddr('diancan')" v-else>
			<view class="qrdd_b_a"><image src="/static/images/icon38.png"></image>{{shouAddr.name}} {{shouAddr.phone}}</view>
			<view class="qrdd_b_b">{{shouAddr.area+shouAddr.detail}}</view>
			<image src="/static/images/index_shop_more.png" class="qrdd_b_jt"></image>
		</view>
		
		<view class="qrdd_c">
			<view class="qrdd_c_li">
				<image :src="imgbaseUrl + productxq.image" mode="aspectFill" class="qrdd_c_li_l"></image>
				<!-- <image src="/static/images/icon23.jpg" mode="aspectFill" class="qrdd_c_li_l"></image> -->
				<view class="qrdd_c_li_r">
					<view class="qrdd_c_li_r_a">{{productxq.name}}</view>
					<!-- <view class="qrdd_c_li_r_b">已选：420g</view> -->
					<view class="qrdd_c_li_r_c"><view></view><text>x1</text></view>
				</view>
			</view>
		</view>
		
		<view class="qrdd_d">
			<view>运费</view>
			<input type="text" :disabled="true" value="包邮"  placeholder-style="color: #999999;" />
		</view>
		<view class="qrdd_d qrdd_bz">
			<view>备注</view>
			<input type="text" placeholder="如有特殊要求，请填写" v-model="remark" placeholder-style="color: #999999;" />
		</view>
		
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<view class="peodex_foo">
			<view class="peodex_foo_l">共1件<text></text></view>
			<view class="peodex_foo_r" @click="dhSubTap">兑换</view>
		</view>
		
		
	</view>
</template>


<script>
import {
	exchangeGoodsApi,
	addrList,
	exchangeGoodsLevelApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			productxq:{
				name:'',
				image:'',
				jpid:0
			},
			imgbaseUrl:'',
			remark:'',
			shouAddr:{area:''},
			qjbutton:'#131315',
		}
	},
	onShow() {
		if (uni.getStorageSync('diancan')) {
			this.shouAddr = uni.getStorageSync('diancan')
		}else{
			this.shouAddr = {area:''}
		}
	},
	onLoad(option) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.imgbaseUrl = this.$baseUrl;
		// this.productxq = JSON.parse(option.productxq);
		this.addressData();
		this.productxq = uni.getStorageSync('dhspGoods')
	},
	methods: {
		//提交兑换
		dhSubTap(){
			if(this.shouAddr.area == ''){
				uni.showToast({
					icon:'none',
					title:'请选择收货地址~',
					duration: 2000
				});
				return false;
			}
			var that = this;
			uni.showLoading({
				title: '加载中'
			});
			if(uni.getStorageSync('dy_type') == 'cj'){
				exchangeGoodsApi({
					id:uni.getStorageSync('dhspGoods').jpid,
					addr_id:that.shouAddr.id,
					remark:that.remark
				}).then(res => {
					console.log('抽奖-提交兑换',res)
					if (res.code == 1) {
						uni.hideLoading();
						uni.redirectTo({
							url:'/pages/prizedraw/success'
						})
					}
				})
			}
			if(uni.getStorageSync('dy_type') == 'dj'){
				exchangeGoodsLevelApi({
					id:uni.getStorageSync('dhspGoods').jpid,
					addr_id:that.shouAddr.id,
					remark:that.remark
				}).then(res => {
					console.log('等级-提交兑换',res)
					if (res.code == 1) {
						uni.hideLoading();
						uni.redirectTo({
							url:'/pages/prizedraw/success'
						})
					}
				})
			}
		},
		//收货地址
		goToAddr(type) {
			uni.navigateTo({
				url: `/pages/mine/address?type=${type}`
			})
		},
		//收货地址
		addressData(){
			addrList({page: 1,size: 999,}).then(res => {
				if (res.code == 1) {
					console.log(res,'地址列表')
					var arrdArr = [];
					for(var i=0;i<res.data.length;i++){
						if(res.data[i].is_default == 1){
							arrdArr.push(res.data[i])
						}
					}
					if(arrdArr.length == 0){
						this.shouAddr = {area:''}
					}else{
						this.shouAddr = arrdArr[0]
						uni.setStorageSync('diancan',arrdArr[0])
					}
				} else {
					this.mescroll.endErr()
					// this.mescroll.endSuccess();
				}
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.confirmOrder{overflow: hidden;}
page{padding-bottom: 0;}
</style>