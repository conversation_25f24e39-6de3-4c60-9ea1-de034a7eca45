<view class="user-profile-container data-v-9e24a6bc"><view class="header data-v-9e24a6bc"><view class="header-content data-v-9e24a6bc"><u-icon vue-id="6cc868a4-1" name="arrow-left" size="24" color="#333" data-event-opts="{{[['^click',[['goBack']]]]}}" bind:click="__e" class="data-v-9e24a6bc" bind:__l="__l"></u-icon><text class="title data-v-9e24a6bc">用户主页</text><u-icon vue-id="6cc868a4-2" name="more-dot-fill" size="24" color="#333" data-event-opts="{{[['^click',[['showMoreActions']]]]}}" bind:click="__e" class="data-v-9e24a6bc" bind:__l="__l"></u-icon></view></view><scroll-view class="content data-v-9e24a6bc" scroll-y="{{true}}"><view class="user-section data-v-9e24a6bc"><view class="user-info data-v-9e24a6bc"><u-avatar vue-id="6cc868a4-3" src="{{userInfo.avatar}}" size="80" class="data-v-9e24a6bc" bind:__l="__l"></u-avatar><view class="user-details data-v-9e24a6bc"><text class="nickname data-v-9e24a6bc">{{userInfo.nickname}}</text><text class="user-id data-v-9e24a6bc">{{"ID: "+userInfo.userId}}</text><text class="bio data-v-9e24a6bc">{{userInfo.bio||'这个人很懒，什么都没有留下...'}}</text></view><u-button vue-id="6cc868a4-4" type="{{userInfo.isFollowed?'default':'primary'}}" size="small" text="{{userInfo.isFollowed?'已关注':'关注'}}" data-event-opts="{{[['^click',[['toggleFollow']]]]}}" bind:click="__e" class="data-v-9e24a6bc" bind:__l="__l"></u-button></view><view class="stats-section data-v-9e24a6bc"><view class="stat-item data-v-9e24a6bc"><text class="stat-number data-v-9e24a6bc">{{userInfo.postCount}}</text><text class="stat-label data-v-9e24a6bc">帖子</text></view><view class="stat-item data-v-9e24a6bc"><text class="stat-number data-v-9e24a6bc">{{userInfo.followingCount}}</text><text class="stat-label data-v-9e24a6bc">关注</text></view><view class="stat-item data-v-9e24a6bc"><text class="stat-number data-v-9e24a6bc">{{userInfo.followersCount}}</text><text class="stat-label data-v-9e24a6bc">粉丝</text></view><view class="stat-item data-v-9e24a6bc"><text class="stat-number data-v-9e24a6bc">{{userInfo.likeCount}}</text><text class="stat-label data-v-9e24a6bc">获赞</text></view></view></view><view class="posts-section data-v-9e24a6bc"><view class="section-header data-v-9e24a6bc"><text class="section-title data-v-9e24a6bc">TA的帖子</text></view><view class="post-grid data-v-9e24a6bc"><block wx:for="{{userPosts}}" wx:for-item="post" wx:for-index="__i0__" wx:key="id"><post-card class="post-card-item data-v-9e24a6bc" vue-id="{{'6cc868a4-5-'+__i0__}}" post="{{post}}" data-event-opts="{{[['^click',[['goPostDetail']]],['^userClick',[['goUserProfile']]],['^like',[['onPostLike']]]]}}" bind:click="__e" bind:userClick="__e" bind:like="__e" bind:__l="__l"></post-card></block></view><block wx:if="{{!$root.g0}}"><view class="empty-state data-v-9e24a6bc"><u-icon vue-id="6cc868a4-6" name="file-text" color="#ccc" size="60" class="data-v-9e24a6bc" bind:__l="__l"></u-icon><text class="empty-text data-v-9e24a6bc">暂无帖子</text></view></block></view></scroll-view></view>