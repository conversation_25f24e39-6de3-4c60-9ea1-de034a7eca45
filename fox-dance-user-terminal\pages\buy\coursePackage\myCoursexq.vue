<template>
	<view class="myCoursexq" :style="{ '--qjbutton-color': qjbutton }" v-if="coursePackageInfo.id">
		<view class="kcxq_video">
			<!-- <video src="https://www.runoob.com/try/demo_source/movie.mp4" controls></video> -->
			<!-- <image style="display: block;width: 100%;" :src="imgbaseUrl + coursePackageInfo.image" mode="widthFix"></image> -->
			
			<swiper class="swiper" autoplay="1500" :indicator-dots="true" :circular='true' indicator-active-color="#ffffff" indicator-color="#cccccc">
				<swiper-item class="swiper-wrap" v-for="(item,index) in coursePackageInfo.images" :key="index">
					<image :src="imgbaseUrl + item" mode="aspectFill"></image>
				</swiper-item>
			</swiper>
			
		</view>
		 
		<view class="kcxq_one">
			<view class="kcxq_one_a">{{coursePackageInfo.name}}</view>
			<view class="kcxq_one_a kcxq_one_a_bq"><text v-if="coursePackageInfo.levelTable">{{coursePackageInfo.levelTable.name}}</text><text v-if="coursePackageInfo.danceTable">{{coursePackageInfo.danceTable.name}}</text></view>
			<view class="kcxq_one_b" v-if="coursePackageInfo.teacher">
				<image :src="imgbaseUrl + coursePackageInfo.teacher.image" class="kcxq_one_b_l"></image>
				<view class="kcxq_one_b_r">
					<view class="kcxq_one_b_r_l"><view>{{coursePackageInfo.teacher.name}}</view><text v-if="coursePackageInfo.teacher.work_year*1 > 0">{{coursePackageInfo.teacher.work_year}}年经验</text></view>
					<view class="kcxq_one_b_r_r" @click="navTo('/pages/buy/coursePackage/teacherDetails?id=' + coursePackageInfo.teacher.id,'1')">讲师详情<image src="/static/images/introduce_more.png"></image></view>
				</view>
			</view>
			<view class="kcxq_one_c" v-if="coursePackageInfo.teacher">
				<!-- <view>上课时间：2024-8-14 11:00</view> -->
				<view>课程时长：{{coursePackageInfo.duration*1}}分钟</view>
				<!-- <view>上课地址：河南省郑州市中原区街道123<image src="/static/images/icon18.png"></image></view> -->
			</view>
		</view>
		
		
		<view class="kcxq_two">
			<view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>课程介绍</text><text></text></view></view>
			<view class="kcxq_thr_b">
				<text style="display: block;margin:20rpx;font-size:26rpx;color:#333;">{{coursePackageInfo.introduce}}</text>
				<!-- <video src="https://www.runoob.com/try/demo_source/movie.mp4" controls style="display: block;width: 100%;"></video> -->
				<video  v-if="coursePackageInfo.introduce_video" :src="coursePackageInfo.isoss ? coursePackageInfo.introduce_video : imgbaseUrl + coursePackageInfo.introduce_video" controls style="display: block;width: 100%;"></video>
			</view>
		</view>	
		
		<view class="kcxq_ten">
			<view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>部分目录</text><text></text></view></view>
			<view class="kbxq_ml">
				<view class="kbxq_ml_li" v-for="(item,index) in kmLists" :key="index">
					<view class="kbxq_ml_li_t" @click="mlTap(index)">
						<view>{{index+1}}.{{item.name}}</view><text
							:style="item.toggle ? 'border-bottom: 14rpx solid #999;border-top:none' : ''"></text>
					</view>
					<view class="kbxq_ml_li_b" v-if="item.toggle">
						<view class="kbxq_ml_li_b_li" v-for="(itemerj,indexerj) in item.catalog" :key="indexerj" v-if="item.catalog.length > 0">
							<view>{{itemerj.name}}</view>
							<!-- <text>已看</text> -->
						</view>
						<view style="width: 100%;text-align:center;font-size: 26rpx;margin-top:30rpx;" v-if="item.catalog.length == 0">暂无目录</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<view class="kcxq_foo">
			<view class="kcxq_foo_l">
				<view @click="homeTap"><image src="/static/tabbar/tab_home.png"></image>首页</view>
			</view>
			<view class="kcxq_foo_r">
				<view class="kcxq_foo_r_sj">仅售：<text>￥{{coursePackageInfo.price*1}}</text></view>
				<view class="back" @click="navTo('/pages/buy/coursePackage/confirmOrder?id=' + coursePackageInfo.id)" v-if="coursePackageInfo.buy_state == 0">购买</view>
				<view class="" style="background:#e6e6e6!important;" v-else>已购</view>
			</view>
		</view>
	</view>
</template>


<script>
import {
	CoursePackageListsxqApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			navLists:['全部','等位中','待开课','授课中','已完成'],
			kmLists: [{
					name: '拉丁舞的好处',
					toggle: true,
					lists: [{
						name: '拉丁舞的由来'
					}, {
						name: '叶子学堂学员作品合集'
					}]
				},
				{
					name: '拉丁舞基本功',
					toggle: false,
					lists: [{
						name: '拉丁舞的由来'
					}, {
						name: '叶子学堂学员作品合集'
					}]
				},
				{
					name: '拉丁舞基础练习',
					toggle: false,
					lists: [{
						name: '拉丁舞的由来'
					}, {
						name: '叶子学堂学员作品合集'
					}]
				},
				{
					name: '拉丁舞的进阶',
					toggle: false,
					lists: [{
						name: '拉丁舞的由来'
					}, {
						name: '叶子学堂学员作品合集'
					}]
				},
			],
			coursePackageInfo:{id:0},
			imgbaseUrl:"",
			qjbutton:'#131315',
		}
	},
	onShow() {
		this.imgbaseUrl = this.$baseUrl;
		this.isLogined = uni.getStorageSync('token') ? true : false;
		this.coursePackageData();//课包详情
	},
	onLoad(option) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.pageId = option.id
	},
	methods: {
		//课包详情
		coursePackageData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			CoursePackageListsxqApi({id:that.pageId}).then(res => {
				console.log('课包详情',res)
				if (res.code == 1) {
					for(var i=0;i<res.data.catalog.length;i++){
						res.data.catalog[i].toggle = false;
					}
					if(res.data.catalog.length > 0){
						res.data.catalog[0].toggle = true;
					}
					if(res.data.introduce_video){
						res.data.isoss = res.data.introduce_video.substring(0,5) == 'https' ? true : false
					}
					that.coursePackageInfo = res.data;
					that.kmLists = res.data.catalog
					uni.hideLoading();
				}
			})
		},
		mlTap(index) {
			this.kmLists[index].toggle = !this.kmLists[index].toggle
		},
		homeTap(){
			uni.switchTab({
				url:'/pages/index/index'
			})
		},
		navTo(url,ismd){
			if(ismd){
				uni.navigateTo({
					url:url
				});
				return false;
			}
			var that = this;
			if(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
			}else{
				uni.navigateTo({
					url:url
				})
			}
		}
		
	}
}
</script>

<style lang="scss">
page{padding-bottom: 0;}
</style>