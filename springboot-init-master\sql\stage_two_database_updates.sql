-- ========================================
-- 阶段二：核心功能接口开发 - 数据库更新脚本
-- 执行时间：2025-07-17
-- 说明：为支持搜索、话题、消息通知功能添加必要的数据库字段
-- ========================================

-- 1. 为posts表添加封面图片字段
-- 用于搜索结果展示和帖子列表显示
ALTER TABLE posts ADD COLUMN cover_image VARCHAR(500) COMMENT '封面图片URL，用于搜索结果和列表展示' AFTER images;

-- 2. 为posts表添加标题字段（如果不存在）
-- 用于搜索功能
ALTER TABLE posts ADD COLUMN title VARCHAR(200) COMMENT '帖子标题，用于搜索和展示' AFTER content;

-- 3. 为posts表添加是否公开字段（如果不存在）
-- 用于搜索时过滤私密帖子
-- 注意：如果该字段已存在，请跳过此语句
-- ALTER TABLE posts ADD COLUMN is_public TINYINT(1) DEFAULT 1 COMMENT '是否公开：0-私密，1-公开' AFTER view_count;

-- 4. 为ba_user表添加索引以优化搜索性能
-- 为昵称添加索引
CREATE INDEX idx_ba_user_nickname ON ba_user(nickname);

-- 为个人简介添加全文索引（如果支持）
-- ALTER TABLE ba_user ADD FULLTEXT(bio);

-- 为舞种添加索引
CREATE INDEX idx_ba_user_dance_type ON ba_user(dance_type);

-- 5. 为posts表添加搜索相关索引
-- 为内容添加全文索引（如果支持）
-- ALTER TABLE posts ADD FULLTEXT(content);

-- 为标题添加索引
CREATE INDEX idx_posts_title ON posts(title);

-- 为用户ID添加索引（如果不存在）
CREATE INDEX idx_posts_user_id ON posts(user_id);

-- 为创建时间添加索引（用于排序）
CREATE INDEX idx_posts_create_time ON posts(create_time);

-- 为公开状态添加索引
CREATE INDEX idx_posts_is_public ON posts(is_public);

-- 6. 为notifications表添加索引以优化查询性能
-- 为用户ID添加索引
CREATE INDEX idx_notifications_user_id ON notifications(user_id);

-- 为发送者ID添加索引
CREATE INDEX idx_notifications_sender_id ON notifications(sender_id);

-- 为消息类型添加索引
CREATE INDEX idx_notifications_type ON notifications(type);

-- 为已读状态添加索引
CREATE INDEX idx_notifications_is_read ON notifications(is_read);

-- 为创建时间添加索引
CREATE INDEX idx_notifications_create_time ON notifications(create_time);

-- 复合索引：用户ID + 类型 + 已读状态（用于分类查询）
CREATE INDEX idx_notifications_user_type_read ON notifications(user_id, type, is_read);

-- 7. 创建搜索历史表（可选，用于存储用户搜索历史）
CREATE TABLE IF NOT EXISTS search_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '搜索历史ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    keyword VARCHAR(100) NOT NULL COMMENT '搜索关键词',
    search_count INT DEFAULT 1 COMMENT '搜索次数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_search_history_user_id (user_id),
    INDEX idx_search_history_keyword (keyword),
    INDEX idx_search_history_create_time (create_time),
    UNIQUE KEY uk_user_keyword (user_id, keyword)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户搜索历史表';

-- 8. 创建热门搜索词统计表（可选）
CREATE TABLE IF NOT EXISTS hot_keywords (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '热门关键词ID',
    keyword VARCHAR(100) NOT NULL COMMENT '搜索关键词',
    search_count INT DEFAULT 0 COMMENT '搜索总次数',
    daily_count INT DEFAULT 0 COMMENT '今日搜索次数',
    weekly_count INT DEFAULT 0 COMMENT '本周搜索次数',
    monthly_count INT DEFAULT 0 COMMENT '本月搜索次数',
    last_search_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后搜索时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_keyword (keyword),
    INDEX idx_hot_keywords_search_count (search_count),
    INDEX idx_hot_keywords_daily_count (daily_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='热门搜索词统计表';

-- 9. 创建话题关注表（可选，用于用户关注话题功能）
CREATE TABLE IF NOT EXISTS tag_follows (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关注ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    tag_id BIGINT NOT NULL COMMENT '话题ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
    INDEX idx_tag_follows_user_id (user_id),
    INDEX idx_tag_follows_tag_id (tag_id),
    UNIQUE KEY uk_user_tag (user_id, tag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注话题表';

-- 10. 为现有数据设置默认封面图片（可选）
-- 将第一张图片设为封面图片
UPDATE posts 
SET cover_image = JSON_UNQUOTE(JSON_EXTRACT(images, '$[0]')) 
WHERE images IS NOT NULL 
  AND JSON_LENGTH(images) > 0 
  AND (cover_image IS NULL OR cover_image = '');

-- 11. 插入一些测试用的热门搜索词（可选）
INSERT IGNORE INTO hot_keywords (keyword, search_count, daily_count, weekly_count, monthly_count) VALUES
('街舞', 1000, 50, 300, 1000),
('现代舞', 800, 40, 250, 800),
('芭蕾', 600, 30, 180, 600),
('拉丁舞', 500, 25, 150, 500),
('爵士舞', 450, 22, 130, 450),
('民族舞', 400, 20, 120, 400),
('古典舞', 350, 18, 100, 350),
('舞蹈教学', 300, 15, 90, 300),
('舞蹈比赛', 250, 12, 75, 250),
('舞蹈培训', 200, 10, 60, 200);

-- ========================================
-- 执行完成提示
-- ========================================
SELECT '阶段二数据库更新完成！' AS message,
       '已添加封面图片字段、搜索索引、搜索历史表等' AS details,
       NOW() AS completion_time;
