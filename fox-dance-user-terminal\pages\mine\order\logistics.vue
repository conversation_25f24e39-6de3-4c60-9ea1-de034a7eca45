<template>
	<view class="logistics" v-if="loding">
		
		<view class="log_con">
			<view class="log_con_t">
				<view class="ord_con_li_b_li">
					<image :src="imgbaseUrl + images" mode="scaleToFill" class="ord_con_li_b_li_l"></image>
					<view class="ord_con_li_b_li_r">
						<view class="ord_con_li_b_li_r_a" style="font-size: 32rpx;">{{logisticsData.status == 1 ? '待发货' : logisticsData.status == 2 ? '运输中' : logisticsData.status == 3 ? '已完成' : ''}}</view>
						<!-- <view class="ord_con_li_b_li_r_b"><text>￥12.99</text>/份</view> -->
						<view class="ord_con_li_b_li_r_c">{{name}}</view>
					</view>
				</view>
			</view> 
			<view class="log_con_c">{{logisticsData.expressName}} {{logisticsData.number}}</view>
			<view class="log_con_b">
				
				<view class="wl_tanc_b">
					<view class="orde_thr_f_li" v-for="(item,index) in logisticsData.logisticsList" :key="index">
						<view class="orde_thr_f_li_l">
							<text></text> 
							<view></view> 
						</view>
						<view class="orde_thr_f_li_r">
							<view class="orde_thr_f_li_r_a">{{item.context}}</view>
							<view class="orde_thr_f_li_r_b">{{item.time}}</view>
						</view> 
					</view>           
				</view>
				
			</view>
		</view>
	</view>
</template>


<script>
import {
	expressApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			name:'',
			images:'',
			imgbaseUrl:'',
			logisticsLists:[
				{},
				{},
				{},
				{},
				{},
			],
			loding:false,
			logisticsData:{}
		}
	},
	onShow() {
		
	},
	onLoad(option) {
		console.log(option,'option')
		this.imgbaseUrl = this.$baseUrl;
		this.name = option.name;
		this.images = option.images;
		this.wlData(option.id,option.type);//物流信息
	},
	methods: {
		//物流信息
		wlData(id,type) {
			var that = this;
			uni.showLoading({
				title: '加载中'
			});
			expressApi({
				id: id,
				type:type ? 2 : 1
			}).then(res => {
				console.log('物流信息', res)
				if (res.code == 1) {
					that.loding = true;
					that.logisticsData = res.data;
					uni.hideLoading();
				}else{
					uni.hideLoading();
					uni.showToast({
						icon: 'none',
						title: '暂无物流信息', //保存路径
						duration: 2000
					});
					setTimeout(function(){
						uni.navigateBack({})
					},2000)
				}
			})
		},
		
	}
}
</script>

<style scoped lang="scss">
.logistics{overflow:hidden;}
</style>