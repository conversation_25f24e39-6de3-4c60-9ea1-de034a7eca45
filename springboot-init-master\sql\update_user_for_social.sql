-- =============================================
-- 社交模块 - 用户表结构更新
-- 创建时间: 2025-07-18
-- 描述: 为社交功能扩展用户表结构
-- =============================================

USE admin_foxdance_c;

-- 添加个人简介字段
ALTER TABLE ba_user ADD COLUMN IF NOT EXISTS bio VARCHAR(500) DEFAULT '' COMMENT '个人简介';

-- 添加舞种字段
ALTER TABLE ba_user ADD COLUMN IF NOT EXISTS dance_type VARCHAR(50) DEFAULT '' COMMENT '舞种';

-- 确保user_stats表包含所需字段
CREATE TABLE IF NOT EXISTS user_stats (
    user_id BIGINT PRIMARY KEY COMMENT '用户ID',
    following_count INT NOT NULL DEFAULT 0 COMMENT '关注数',
    follower_count INT NOT NULL DEFAULT 0 COMMENT '粉丝数',
    post_count INT NOT NULL DEFAULT 0 COMMENT '帖子数',
    like_received_count INT NOT NULL DEFAULT 0 COMMENT '收到的点赞数',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_following_count(following_count),
    INDEX idx_follower_count(follower_count),
    INDEX idx_post_count(post_count)
) COMMENT='用户统计表' COLLATE=utf8mb4_unicode_ci;

-- 为现有用户创建统计记录（如果不存在）
INSERT IGNORE INTO user_stats (user_id, following_count, follower_count, post_count, like_received_count)
SELECT id, 0, 0, 0, 0 FROM ba_user WHERE id NOT IN (SELECT user_id FROM user_stats); 