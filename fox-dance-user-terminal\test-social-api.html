<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社交API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>社交API测试页面</h1>
    
    <div class="test-section">
        <h3>1. 健康检查</h3>
        <button onclick="testHealth()">测试健康检查</button>
        <div id="health-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 帖子列表</h3>
        <button onclick="testPostList()">测试帖子列表</button>
        <div id="post-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 热门话题</h3>
        <button onclick="testHotTags()">测试热门话题</button>
        <div id="tags-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 评论列表</h3>
        <button onclick="testComments()">测试评论列表</button>
        <div id="comments-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8101/api';
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw error;
            }
        }
        
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const result = await makeRequest('/social/test/health');
                resultDiv.innerHTML = `<span class="success">✓ 成功</span>\n${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 失败</span>\n${error.message}`;
            }
        }
        
        async function testPostList() {
            const resultDiv = document.getElementById('post-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const result = await makeRequest('/post/list', {
                    method: 'POST',
                    body: JSON.stringify({
                        current: 1,
                        pageSize: 5,
                        sortField: 'createTime',
                        sortOrder: 'desc'
                    })
                });
                resultDiv.innerHTML = `<span class="success">✓ 成功</span>\n找到 ${result.data?.total || 0} 个帖子\n${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 失败</span>\n${error.message}`;
            }
        }
        
        async function testHotTags() {
            const resultDiv = document.getElementById('tags-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const result = await makeRequest('/tag/hot?limit=5');
                resultDiv.innerHTML = `<span class="success">✓ 成功</span>\n找到 ${result.data?.length || 0} 个热门话题\n${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 失败</span>\n${error.message}`;
            }
        }
        
        async function testComments() {
            const resultDiv = document.getElementById('comments-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const result = await makeRequest('/comments?userId=1&contentId=1&filter=hot&page=1&size=5');
                resultDiv.innerHTML = `<span class="success">✓ 成功</span>\n找到 ${result.data?.total || 0} 条评论\n${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 失败</span>\n${error.message}`;
            }
        }
        
        // 页面加载时自动测试健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
