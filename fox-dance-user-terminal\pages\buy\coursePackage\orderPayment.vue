<template>
	<view class="orderPayment" :style="{ '--qjbutton-color': qjbutton }">
		
		<view class="ordzf_one">￥<text>{{price*1}}</text></view>
		<!-- <view class="ordzf_two">订单29:59分钟后未支付自动关闭</view> -->
		
		<view class="ordzf_thr">
			<view class="ordzf_thr_li">
				<image src="/static/images/icon34.png" class="ordzf_thr_li_l"></image>
				<view class="ordzf_thr_li_c"><view>微信支付</view><text>使用微信支付</text></view>
				<image src="/static/images/icon51.png" class="ordzf_thr_li_r"></image>
			</view>
		</view>
		
		<view class="ordzf_foo" @click="wxpayTap">确认支付</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	kbbuySubApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			coupon_id:0,
			price:0,
			kbId:0,
			jinzLd:true,//支付禁止连点
			qjbutton:'#131315',
		}
	},
	onShow() {
		
	},
	onLoad(option) {
		console.log(option,'option')
		this.coupon_id = option.couponid;
		this.price = option.price;
		this.kbId = option.id;
		this.qjbutton = uni.getStorageSync('storeInfo').button
	},
	methods: {
		//微信支付
		wxpayTap(){
			var that = this;
			if(!that.jinzLd){
			  uni.showToast({
				icon:'none',
				title:'您点击的太快了~',
				duration: 2000
			  });
			  return false;
			}
			that.jinzLd = false;
			uni.showLoading({
			  title:'支付中...',
			});
			kbbuySubApi({
				id: that.kbId,
				coupon_id: that.coupon_id, 
			}).then(res => {
				console.log('拉起支付',res);
				if (res.code == 1) {
					/*uni.hideLoading();
					that.jinzLd = true
					uni.redirectTo({
						url:'/pages/buy/coursePackage/success'
					})
					return false;*/
					//调取微信支付
					uni.requestPayment({
						timeStamp:res.data.timeStamp,
						nonceStr:res.data.nonceStr,
						package:res.data.package,
						signType:res.data.signType,
						paySign:res.data.paySign,
						success: function (res) {
							//支付成功
							uni.hideLoading();
							/*uni.showToast({
							  icon:'success',
							  title: '支付成功',
							  duration: 2000
							});*/
							that.jinzLd = true
							uni.redirectTo({
								url:'/pages/buy/coursePackage/success'
							})
						},
						fail: function (err) {
							console.log('fail:' + JSON.stringify(err),'不回话接口');
							if (err.errMsg == "requestPayment:fail cancel") {
								that.jinzLd = true
								  uni.hideLoading();
								  uni.showToast({
									  icon:'none',
									  title: '支付取消',
									  duration: 2000
								  });
								  // setTimeout(function(){
								  // 	uni.switchTab({
								  // 		url:'/pages/order/order'
								  // 	})
								  // },1500)
							}
						}
					});
					
				}else{
					that.jinzLd = true
					uni.hideLoading();
					uni.showToast({
						icon:'none',
						title: res.msg,
						duration: 2000
					});
				}
				
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.orderPayment{overflow: hidden;}
page{padding-bottom: 0;}
</style>