<template>
	<view class="address" v-if="loding" :style="{ '--qjbutton-color': qjbutton }">
		<!-- <mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption">
			<view class="list">
				<view class="li" v-for="(item,index) in lists" :key="index" @click="checkAddr(item)">
					<view class="li_t">
						<view class="li_t_info">
							{{item.userName}}({{item.userSex==1?'先生':'女士'}})&nbsp;&nbsp; {{item.userPhone}}
						</view>
						<view class="li_t_detail">{{item.addressDetail}}</view>
					</view>
					<view class="li_d flex row-between">
						<view class="li_d_l flex" @click.stop="isDefault(item)">
							<image v-if="item.addressDefault == 1" src="/static/images/Mr_x2.png" mode="scaleToFill">
							</image>
							<image v-else src="/static/images/Mr.png" mode="scaleToFill"></image>
							默认地址
						</view>
						<view class="li_d_use flex">
							<view class="li_d_li flex" @click.stop="goTo(item)">
								<image src="/static/images/addr_edit.png" mode="scaleToFill"></image>
								编辑
							</view>
							<view class="li_d_li flex" @click.stop="nowId = item.addressId,showPopup=true">
								<image src="/static/images/addr_del.png" mode="scaleToFill"></image>
								删除
							</view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body> -->
		
		<view class="list" style="margin-bottom: 128rpx;" v-if="lists.length > 0">
			<view class="li" v-for="(item,index) in lists" :key="index" @click="checkAddr(item)">
				<view class="li_t">
					<view class="li_t_info">
						{{item.name}}({{item.gender==1?'先生':'女士'}})&nbsp;&nbsp; {{item.phone}}
					</view>
					<view class="li_t_detail">{{item.area+item.detail}}</view>
				</view>
				<view class="li_d flex row-between">
					<view class="li_d_l flex" @click.stop="isDefault(item)">
						<image v-if="item.is_default == 1" src="/static/images/dzxz-11.png" mode="scaleToFill" class="ac">
						</image>
						<image v-else src="/static/images/dzxz.png" mode="scaleToFill"></image>
						默认地址
					</view>
					<view class="li_d_use flex">
						<view class="li_d_li flex" @click.stop="goTo(1,item)">
							<image src="/static/images/addr_edit.png" mode="scaleToFill"></image>
							编辑
						</view>
						<view class="li_d_li flex" @click.stop="delAddress(item.id)">
							<image src="/static/images/addr_del.png" mode="scaleToFill"></image>
							删除
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="gg_zwsj_w" v-if="lists.length == 0">
			<image src="/static/images/addr_kong.png"></image>
			<text style="display:block;">收货地址空空如也~</text>
		</view>
		
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<view class="add_foo"><view class="" @click="goTo(0)">添加地址</view></view>
		
		<!-- 弹窗 -->
		<u-popup v-model="showPopup" mode="center" border-radius="20">
			<view class="prompt">
				<view class="prompt_t">
					<view class="prompt_t_img">
						<image src="/static/images/popup-icon.png" mode="scaleToFill"></image>
					</view>
					<view class="prompt_t_text">
						提示
					</view>
				</view>
				<view class="prompt_c">
					确定退出当前删除？
				</view>
				<view class="prompt_d">
					<view class="prompt_d_l" @click="showPopup=  false">
						取消
					</view>
					<view class="prompt_d_r" @click="confirmDel">
						确定
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import MescrollCompMixin from "@/components/mescroll-uni/mixins/mescroll-comp";
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins";
	import {
		addrList,
		addrDel,
		addrEdit,
		addrmor
	} from '@/config/http.achieve.js'
	export default {
		mixins: [MescrollMixin, MescrollCompMixin],
		data() {
			return {
				loding:false,
				lists: [],
				nowId: '',
				showPopup: false,
				shopTip: false,
				upOption: {
					empty: {
						icon: '/static/images/addr_kong.png',
						tip: '还未添加收货地址~'
					}
				},
				type: '',
				qjbutton:'#131315',
			}
		},
		onLoad(opt) {
			this.qjbutton = uni.getStorageSync('storeInfo').button
			if (opt.type) {
				this.type = opt.type
			}
			console.log(opt,'opt哈哈')
		},
		onShow() {
			if (uni.getStorageSync('pageAddr')) {
				// 已经有记录，增加进入次数
				let count = uni.getStorageSync('pageAddr') + 1;
				uni.setStorageSync('pageAddr', count);

				// 第二次进入
				if (count != 1) {
					// this.onRefresh()
				}
			} else {
				// 第一次进入，记录次数
				uni.setStorageSync('pageAddr', 1);
			}
			
			this.addressData()//地址列表
		},
		onUnload() {
			uni.removeStorageSync('pageAddr')
		},
		methods: {
			//地址列表
			addressData(){
				uni.showLoading({
					title: '加载中'
				});
				var that = this;
				addrList({}).then(res => {
					if (res.code == 1) {
						uni.hideLoading()
						that.lists = res.data;
						that.loding = true;
					}
				})
			},
			checkAddr(item) {
				console.log(this.type,'this.type ')
				if (this.type == '') return
				if (this.type == 'qu') {
					uni.setStorageSync('qu', item)
					uni.navigateBack()
					console.log('qu')
				} else if (this.type == 'shou') {
					uni.setStorageSync('shou', item)
					uni.navigateBack()
					console.log('shou')
				}else if (this.type == 'diancan') {
					uni.setStorageSync('diancan', item)
					uni.navigateBack()
				}
			},

			 
			// 跳转编辑页面
			goTo(type,item) {
				uni.navigateTo({
					url: `/pages/mine/add_address?type=${type}&data=${JSON.stringify(item)}`
				})
			},
			// 确认删除
			confirmDel(item) {
				addrDel({
					addressId: this.nowId
				}).then(res => {
					if (res.code == 1) {
						
						if (uni.getStorageSync('diancan')) {
							if(uni.getStorageSync('diancan').id == this.nowId){
								uni.removeStorageSync('diancan')
							}
						}
						
						this.$toast({
							title: "删除成功"
						})
						this.showPopup = false
						setTimeout(() => {
							this.onRefresh()
						}, 500)
					}
				})
			},
			//删除收货地址
			delAddress(id){
				var that = this;
				
				uni.showModal({
					title: '提示',
					content: '确定要删除该地址吗？',
					success: function (res) {
						if (res.confirm) {
							
							uni.showLoading({
								title: '加载中'
							}); 
							addrDel({
								id: id
							}).then(res => {
								if (res.code == 1) {
									
									if (uni.getStorageSync('diancan')) {
										if(uni.getStorageSync('diancan').id == id){
											uni.removeStorageSync('diancan')
										}
									}
									uni.hideLoading();
									that.addressData();//收货地址
									uni.showToast({
										title: res.msg,
										duration: 2000
									});
								}
							})
							
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
				
			},

			// 设置默认
			isDefault(item) {
				addrmor({
					id: item.id,
					is_default: item.is_default == 0 ? 1 : 0
				}).then(res => {
					if (res.code == 1) {
						this.addressData()
						this.$toast({
							title: "设置成功"
						})
					}
				})

			},

			onRefresh() {
				this.mescroll.resetUpScroll();
			},
			async upCallback(page) {
				// uni.showLoading()
				// this.TopListFun()
				if (page.num == 1) {}
				let pageNum = page.num;
				let pageSize = page.size;
				const params = {
					page: pageNum,
					size: pageSize,
				}
				// this.mescroll.endErr()
				// this.mescroll.endSuccess(0, 0);
				addrList(params).then(res => {
					if (res.code == 1) {
						let curPageData = res.data;
						let curPageLen = curPageData.length;
						let totalSize = res.total;
						if (page.num == 1) {
							this.lists = [];
						}
						this.lists = this.lists.concat(curPageData);
						this.mescroll.endSuccess(curPageLen, totalSize);

					} else {
						this.mescroll.endErr()
						// this.mescroll.endSuccess();
					}
				})
			},
			navTo(url) {
				uni.navigateTo({
					url: url
				})
			}
		}
	}
</script>

<style lang="scss">
	.list {
		.li {
			margin: 28rpx auto 0;
			width: 686rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			padding: 26rpx;

			.li_t {
				.li_t_info {
					font-size: 26rpx;
					color: #333333;
					line-height: 30rpx;
				}

				.li_t_detail {
					margin-top: 8rpx;
					font-size: 26rpx;
					color: #999999;
					line-height: 30rpx;
				}

				padding-bottom: 34rpx;
				border-bottom: 2rpx dashed rgba(167, 167, 167, 0.2);
			}

			.li_d {
				padding-top: 30rpx;

				.li_d_l {
					image {
						width: 40rpx;
						height: 40rpx;
						margin-right: 8rpx;
					}

					font-size: 26rpx;
					color: #999999;
					line-height: 30rpx;

				}

				.li_d_use {
					.li_d_li {
						image {
							width: 32rpx;
							height: 32rpx;
							margin-right: 6rpx;
						}

						font-size: 26rpx;
						color: #999999;
						line-height: 30rpx;
						margin-left: 20rpx;
					}
				}
			}
		}
	}

	.tip_box {
		width: 480rpx;
		height: 416rpx;
		padding: 50rpx 36rpx;

		.tip_box_title {
			text-align: center;
			font-weight: 500;
			font-size: 32rpx;
			color: #333333;
			line-height: 39rpx;
		}

		.tip_box_cont {
			margin: 0 auto;
			width: 380rpx;
			text-align: center;
			margin-top: 54rpx;
			font-size: 26rpx;
			color: #818181;
			line-height: 36rpx;
		}

		.tip_box_btn {
			margin: 0 auto;
			margin-top: 54rpx;
			font-size: 26rpx;
			color: #818181;
			line-height: 30rpx;
			color: #fff;
			width: 144rpx;
			height: 60rpx;
			border-radius: 30rpx;
		}
	}


</style>