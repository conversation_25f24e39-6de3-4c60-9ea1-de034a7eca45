{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-input/u-input.vue?c325", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-input/u-input.vue?7ded", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-input/u-input.vue?e666", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-input/u-input.vue?3fbd", "uni-app:///components/uview-ui/components/u-input/u-input.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-input/u-input.vue?849f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-input/u-input.vue?15ac"], "names": ["name", "mixins", "props", "value", "type", "default", "inputAlign", "placeholder", "disabled", "maxlength", "placeholder<PERSON><PERSON><PERSON>", "confirmType", "customStyle", "fixed", "focus", "passwordIcon", "border", "borderColor", "autoHeight", "selectOpen", "height", "clearable", "cursorSpacing", "selectionStart", "selectionEnd", "trim", "showConfirmbar", "adjustPosition", "data", "defaultValue", "inputHeight", "textareaHeight", "validateState", "focused", "showPassword", "lastValue", "watch", "detail", "computed", "inputMaxlength", "getStyle", "style", "getCursorSpacing", "uSelectionStart", "uSelectionEnd", "created", "methods", "handleInput", "setTimeout", "handleBlur", "onFormItemError", "onFocus", "onConfirm", "onClear", "inputClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgM;AAChM,gBAAgB,8LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAqvB,CAAgB,ssBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC4EzwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,gBA0BA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;QACA;MACA;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;IACA;EACA;EACAuB;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAjC;MACA;MACA;MACA;QACAkC;UACAlC;QACA;MACA;IACA;EACA;EACAmC;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC,gFACA;MACAA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;;QAKA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACAD;QACA;MACA;MACA;MACA;MACAA;QACA;;QAKA;QACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACtVA;AAAA;AAAA;AAAA;AAA46C,CAAgB,ixCAAG,EAAC,C;;;;;;;;;;;ACAh8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-input/u-input.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-input.vue?vue&type=template&id=0b0f3b4e&scoped=true&\"\nvar renderjs\nimport script from \"./u-input.vue?vue&type=script&lang=js&\"\nexport * from \"./u-input.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-input.vue?vue&type=style&index=0&id=0b0f3b4e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0b0f3b4e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-input/u-input.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=template&id=0b0f3b4e&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.type == \"textarea\" ? _vm.__get_style([_vm.getStyle]) : null\n  var s1 = !(_vm.type == \"textarea\") ? _vm.__get_style([_vm.getStyle]) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showPassword = !_vm.showPassword\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t\tclass=\"u-input\"\r\n\t\t:class=\"{\r\n\t\t\t'u-input--border': border,\r\n\t\t\t'u-input--error': validateState\r\n\t\t}\"\r\n\t\t:style=\"{\r\n\t\t\tpadding: `0 ${border ? 20 : 0}rpx`,\r\n\t\t\tborderColor: borderColor,\r\n\t\t\ttextAlign: inputAlign\r\n\t\t}\"\r\n\t\**********=\"inputClick\"\r\n\t>\r\n\t\t<textarea\r\n\t\t\tv-if=\"type == 'textarea'\"\r\n\t\t\tclass=\"u-input__input u-input__textarea\"\r\n\t\t\t:style=\"[getStyle]\"\r\n\t\t\t:value=\"defaultValue\"\r\n\t\t\t:placeholder=\"placeholder\"\r\n\t\t\t:placeholderStyle=\"placeholderStyle\"\r\n\t\t\t:disabled=\"disabled\"\r\n\t\t\t:maxlength=\"inputMaxlength\"\r\n\t\t\t:fixed=\"fixed\"\r\n\t\t\t:focus=\"focus\"\r\n\t\t\t:autoHeight=\"autoHeight\"\r\n\t\t\t:selection-end=\"uSelectionEnd\"\r\n\t\t\t:selection-start=\"uSelectionStart\"\r\n\t\t\t:cursor-spacing=\"getCursorSpacing\"\r\n\t\t\t:show-confirm-bar=\"showConfirmbar\"\r\n      :adjust-position=\"adjustPosition\"\r\n\t\t\t@input=\"handleInput\"\r\n\t\t\t@blur=\"handleBlur\"\r\n\t\t\t@focus=\"onFocus\"\r\n\t\t\t@confirm=\"onConfirm\"\r\n\t\t/>\r\n\t\t<input\r\n\t\t\tv-else\r\n\t\t\tclass=\"u-input__input\"\r\n\t\t\t:type=\"type == 'password' ? 'text' : type\"\r\n\t\t\t:style=\"[getStyle]\"\r\n\t\t\t:value=\"defaultValue\"\r\n\t\t\t:password=\"type == 'password' && !showPassword\"\r\n\t\t\t:placeholder=\"placeholder\"\r\n\t\t\t:placeholderStyle=\"placeholderStyle\"\r\n\t\t\t:disabled=\"disabled || type === 'select'\"\r\n\t\t\t:maxlength=\"inputMaxlength\"\r\n\t\t\t:focus=\"focus\"\r\n\t\t\t:confirmType=\"confirmType\"\r\n\t\t\t:cursor-spacing=\"getCursorSpacing\"\r\n\t\t\t:selection-end=\"uSelectionEnd\"\r\n\t\t\t:selection-start=\"uSelectionStart\"\r\n\t\t\t:show-confirm-bar=\"showConfirmbar\"\r\n\t\t\t:adjust-position=\"adjustPosition\"\r\n\t\t\t@focus=\"onFocus\"\r\n\t\t\t@blur=\"handleBlur\"\r\n\t\t\t@input=\"handleInput\"\r\n\t\t\t@confirm=\"onConfirm\"\r\n\t\t/>\r\n\t\t<view class=\"u-input__right-icon u-flex\">\r\n\t\t\t<view class=\"u-input__right-icon__clear u-input__right-icon__item\" @tap=\"onClear\" v-if=\"clearable && value != '' && focused\">\r\n\t\t\t\t<u-icon size=\"32\" name=\"close-circle-fill\" color=\"#c0c4cc\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"u-input__right-icon__clear u-input__right-icon__item\" v-if=\"passwordIcon && type == 'password'\">\r\n\t\t\t\t<u-icon size=\"32\" :name=\"!showPassword ? 'eye' : 'eye-fill'\" color=\"#c0c4cc\" @click=\"showPassword = !showPassword\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"u-input__right-icon--select u-input__right-icon__item\" v-if=\"type == 'select'\" :class=\"{\r\n\t\t\t\t'u-input__right-icon--select--reverse': selectOpen\r\n\t\t\t}\">\r\n\t\t\t\t<u-icon name=\"arrow-down-fill\" size=\"26\" color=\"#c0c4cc\"></u-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport Emitter from '../../libs/util/emitter.js';\r\n\r\n/**\r\n * input 输入框\r\n * @description 此组件为一个输入框，默认没有边框和样式，是专门为配合表单组件u-form而设计的，利用它可以快速实现表单验证，输入内容，下拉选择等功能。\r\n * @tutorial http://uviewui.com/components/input.html\r\n * @property {String} type 模式选择，见官网说明\r\n * @property {Boolean} clearable 是否显示右侧的清除图标(默认true)\r\n * @property {} v-model 用于双向绑定输入框的值\r\n * @property {String} input-align 输入框文字的对齐方式(默认left)\r\n * @property {String} placeholder placeholder显示值(默认 '请输入内容')\r\n * @property {Boolean} disabled 是否禁用输入框(默认false)\r\n * @property {String Number} maxlength 输入框的最大可输入长度(默认140)\r\n * @property {String Number} selection-start 光标起始位置，自动聚焦时有效，需与selection-end搭配使用（默认-1）\r\n * @property {String Number} maxlength 光标结束位置，自动聚焦时有效，需与selection-start搭配使用（默认-1）\r\n * @property {String Number} cursor-spacing 指定光标与键盘的距离，单位px(默认0)\r\n * @property {String} placeholderStyle placeholder的样式，字符串形式，如\"color: red;\"(默认 \"color: #c0c4cc;\")\r\n * @property {String} confirm-type 设置键盘右下角按钮的文字，仅在type为text时生效(默认done)\r\n * @property {Object} custom-style 自定义输入框的样式，对象形式\r\n * @property {Boolean} focus 是否自动获得焦点(默认false)\r\n * @property {Boolean} fixed 如果type为textarea，且在一个\"position:fixed\"的区域，需要指明为true(默认false)\r\n * @property {Boolean} password-icon type为password时，是否显示右侧的密码查看图标(默认true)\r\n * @property {Boolean} border 是否显示边框(默认false)\r\n * @property {String} border-color 输入框的边框颜色(默认#dcdfe6)\r\n * @property {Boolean} auto-height 是否自动增高输入区域，type为textarea时有效(默认true)\r\n * @property {String Number} height 高度，单位rpx(text类型时为70，textarea时为100)\r\n * @example <u-input v-model=\"value\" :type=\"type\" :border=\"border\" />\r\n */\r\nexport default {\r\n\tname: 'u-input',\r\n\tmixins: [Emitter],\r\n\tprops: {\r\n\t\tvalue: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 输入框的类型，textarea，text，number\r\n\t\ttype: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'text'\r\n\t\t},\r\n\t\tinputAlign: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'left'\r\n\t\t},\r\n\t\tplaceholder: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '请输入内容'\r\n\t\t},\r\n\t\tdisabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tmaxlength: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 140\r\n\t\t},\r\n\t\tplaceholderStyle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'color: #c0c4cc;'\r\n\t\t},\r\n\t\tconfirmType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'done'\r\n\t\t},\r\n\t\t// 输入框的自定义样式\r\n\t\tcustomStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {};\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true\r\n\t\tfixed: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 是否自动获得焦点\r\n\t\tfocus: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 密码类型时，是否显示右侧的密码图标\r\n\t\tpasswordIcon: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// input|textarea是否显示边框\r\n\t\tborder: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 输入框的边框颜色\r\n\t\tborderColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#dcdfe6'\r\n\t\t},\r\n\t\tautoHeight: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// type=select时，旋转右侧的图标，标识当前处于打开还是关闭select的状态\r\n\t\t// open-打开，close-关闭\r\n\t\tselectOpen: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 高度，单位rpx\r\n\t\theight: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 是否可清空\r\n\t\tclearable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 指定光标与键盘的距离，单位 px\r\n\t\tcursorSpacing: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 光标起始位置，自动聚焦时有效，需与selection-end搭配使用\r\n\t\tselectionStart: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: -1\r\n\t\t},\r\n\t\t// 光标结束位置，自动聚焦时有效，需与selection-start搭配使用\r\n\t\tselectionEnd: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: -1\r\n\t\t},\r\n\t\t// 是否自动去除两端的空格\r\n\t\ttrim: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 是否显示键盘上方带有”完成“按钮那一栏\r\n\t\tshowConfirmbar:{\r\n\t\t\ttype:Boolean,\r\n\t\t\tdefault:true\r\n\t\t},\r\n\t\t// 弹出键盘时是否自动调节高度，uni-app默认值是true\r\n\t\tadjustPosition: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tdefaultValue: this.value,\r\n\t\t\tinputHeight: 70, // input的高度\r\n\t\t\ttextareaHeight: 100, // textarea的高度\r\n\t\t\tvalidateState: false, // 当前input的验证状态，用于错误时，边框是否改为红色\r\n\t\t\tfocused: false, // 当前是否处于获得焦点的状态\r\n\t\t\tshowPassword: false, // 是否预览密码\r\n\t\t\tlastValue: '', // 用于头条小程序，判断@input中，前后的值是否发生了变化，因为头条中文下，按下键没有输入内容，也会触发@input时间\r\n\t\t};\r\n\t},\r\n\twatch: {\r\n\t\tvalue(nVal, oVal) {\r\n\t\t\tthis.defaultValue = nVal;\r\n\t\t\t// 当值发生变化，且为select类型时(此时input被设置为disabled，不会触发@input事件)，模拟触发@input事件\r\n\t\t\tif(nVal != oVal && this.type == 'select') this.handleInput({\r\n\t\t\t\tdetail: {\r\n\t\t\t\t\tvalue: nVal\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t},\r\n\tcomputed: {\r\n\t\t// 因为uniapp的input组件的maxlength组件必须要数值，这里转为数值，给用户可以传入字符串数值\r\n\t\tinputMaxlength() {\r\n\t\t\treturn Number(this.maxlength);\r\n\t\t},\r\n\t\tgetStyle() {\r\n\t\t\tlet style = {};\r\n\t\t\t// 如果没有自定义高度，就根据type为input还是textarea来分配一个默认的高度\r\n\t\t\tstyle.minHeight = this.height ? this.height + 'rpx' : this.type == 'textarea' ?\r\n\t\t\t\tthis.textareaHeight + 'rpx' : this.inputHeight + 'rpx';\r\n\t\t\tstyle = Object.assign(style, this.customStyle);\r\n\t\t\treturn style;\r\n\t\t},\r\n\t\t//\r\n\t\tgetCursorSpacing() {\r\n\t\t\treturn Number(this.cursorSpacing);\r\n\t\t},\r\n\t\t// 光标起始位置\r\n\t\tuSelectionStart() {\r\n\t\t\treturn String(this.selectionStart);\r\n\t\t},\r\n\t\t// 光标结束位置\r\n\t\tuSelectionEnd() {\r\n\t\t\treturn String(this.selectionEnd);\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\t// 监听u-form-item发出的错误事件，将输入框边框变红色\r\n\t\tthis.$on('on-form-item-error', this.onFormItemError);\r\n\t},\r\n\tmethods: {\r\n\t\t/**\r\n\t\t * change 事件\r\n\t\t * @param event\r\n\t\t */\r\n\t\thandleInput(event) {\r\n\t\t\tlet value = event.detail.value;\r\n\t\t\t// 判断是否去除空格\r\n\t\t\tif(this.trim) value = this.$u.trim(value);\r\n\t\t\t// vue 原生的方法 return 出去\r\n\t\t\tthis.$emit('input', value);\r\n\t\t\t// 当前model 赋值\r\n\t\t\tthis.defaultValue = value;\r\n\t\t\t// 过一个生命周期再发送事件给u-form-item，否则this.$emit('input')更新了父组件的值，但是微信小程序上\r\n\t\t\t// 尚未更新到u-form-item，导致获取的值为空，从而校验混论\r\n\t\t\t// 这里不能延时时间太短，或者使用this.$nextTick，否则在头条上，会造成混乱\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\t// 头条小程序由于自身bug，导致中文下，每按下一个键(尚未完成输入)，都会触发一次@input，导致错误，这里进行判断处理\r\n\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\tif(this.$u.trim(value) == this.lastValue) return ;\r\n\t\t\t\tthis.lastValue = value;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// 将当前的值发送到 u-form-item 进行校验\r\n\t\t\t\tthis.dispatch('u-form-item', 'on-form-change', value);\r\n\t\t\t}, 40)\r\n\t\t},\r\n\t\t/**\r\n\t\t * blur 事件\r\n\t\t * @param event\r\n\t\t */\r\n\t\thandleBlur(event) {\r\n\t\t\t// 最开始使用的是监听图标@touchstart事件，自从hx2.8.4后，此方法在微信小程序出错\r\n\t\t\t// 这里改为监听点击事件，手点击清除图标时，同时也发生了@blur事件，导致图标消失而无法点击，这里做一个延时\r\n\t\t\tlet value = event.detail.value;\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.focused = false;\r\n\t\t\t}, 100)\r\n\t\t\t// vue 原生的方法 return 出去\r\n\t\t\tthis.$emit('blur', value);\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\t// 头条小程序由于自身bug，导致中文下，每按下一个键(尚未完成输入)，都会触发一次@input，导致错误，这里进行判断处理\r\n\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\tif(this.$u.trim(value) == this.lastValue) return ;\r\n\t\t\t\tthis.lastValue = value;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// 将当前的值发送到 u-form-item 进行校验\r\n\t\t\t\tthis.dispatch('u-form-item', 'on-form-blur', value);\r\n\t\t\t}, 40)\r\n\t\t},\r\n\t\tonFormItemError(status) {\r\n\t\t\tthis.validateState = status;\r\n\t\t},\r\n\t\tonFocus(event) {\r\n\t\t\tthis.focused = true;\r\n\t\t\tthis.$emit('focus');\r\n\t\t},\r\n\t\tonConfirm(e) {\r\n\t\t\tthis.$emit('confirm', e.detail.value);\r\n\t\t},\r\n\t\tonClear(event) {\r\n\t\t\tthis.$emit('input', '');\r\n\t\t},\r\n\t\tinputClick() {\r\n\t\t\tthis.$emit('click');\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"../../libs/css/style.components.scss\";\r\n\r\n.u-input {\r\n\tposition: relative;\r\n\tflex: 1;\r\n\t@include vue-flex;\r\n\r\n\t&__input {\r\n\t\t//height: $u-form-item-height;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: $u-main-color;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t&__textarea {\r\n\t\twidth: auto;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: $u-main-color;\r\n\t\tpadding: 10rpx 0;\r\n\t\tline-height: normal;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t&--border {\r\n\t\tborder-radius: 6rpx;\r\n\t\tborder-radius: 4px;\r\n\t\tborder: 1px solid $u-form-item-border-color;\r\n\t}\r\n\r\n\t&--error {\r\n\t\tborder-color: $u-type-error!important;\r\n\t}\r\n\r\n\t&__right-icon {\r\n\r\n\t\t&__item {\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t}\r\n\r\n\t\t&--select {\r\n\t\t\ttransition: transform .4s;\r\n\r\n\t\t\t&--reverse {\r\n\t\t\t\ttransform: rotate(-180deg);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=style&index=0&id=0b0f3b4e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=style&index=0&id=0b0f3b4e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752725567115\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}