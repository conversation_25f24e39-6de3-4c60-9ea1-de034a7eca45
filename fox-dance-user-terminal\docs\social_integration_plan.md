# 社交模块前后端对接详细计划

## 📋 项目概述

本文档详细描述了社交模块前后端对接的完整计划。前端已完成基础页面开发，后端已有完善的数据库设计和部分接口实现。

### 技术栈
- **前端**: uni-app (微信小程序)
- **后端**: SpringBoot
- **数据库**: MySQL
- **API基础路径**: `vote_baseUrl` (http://localhost:8101)

## 🔍 现状分析

### 前端已完成页面
- ✅ 首页 (`/home/<USER>
- ✅ 发布页 (`/publish/index.vue`) - 帖子发布功能
- ✅ 个人主页 (`/profile/index.vue`) - 用户资料和帖子管理
- ✅ 用户主页 (`/user/profile.vue`) - 其他用户资料查看
- ✅ 帖子详情 (`/post/detail.vue`) - 帖子详情和评论
- ✅ 消息中心 (`/message/index.vue`) - 消息列表
- ✅ 聊天详情 (`/chat/detail.vue`) - 私信聊天
- ✅ 搜索页 (`/search/index.vue`) - 搜索功能
- ✅ 发现页 (`/discover/index.vue`) - 内容发现
- ✅ 话题页 (`/topic/detail.vue`) - 话题详情

### 后端已完成
- ✅ 完整的数据库设计 (social_posts_system.sql)
- ✅ 实体类和DTO设计
- ✅ 基础的Controller和Service架构
- ✅ 部分核心接口实现

## 🗄️ 数据库更新需求

### 1. ba_user表扩展 ⚠️ 需要更新
```sql
-- 已在设计中包含，需确认是否已执行
ALTER TABLE ba_user ADD COLUMN bio VARCHAR(500) DEFAULT '' COMMENT '个人简介';
ALTER TABLE ba_user ADD COLUMN dance_type VARCHAR(50) DEFAULT '' COMMENT '学习舞种';
```

### 2. 评论表关联 ⚠️ 需要更新
```sql
-- 为现有comments表添加帖子关联
ALTER TABLE comments ADD COLUMN post_id BIGINT DEFAULT NULL COMMENT '帖子ID' AFTER topic_id;
ALTER TABLE comments ADD INDEX idx_post_id(post_id);
```

### 3. 数据库状态
- ✅ 帖子相关表 (posts, post_stats, post_likes, post_favorites, post_shares)
- ✅ 用户关系表 (user_follows, user_stats)
- ✅ 标签系统 (tags, post_tags)
- ✅ 消息系统 (notifications, private_conversations, private_messages)
- ✅ 系统管理 (system_configs, reports)

## 🔌 API接口分析

### 可直接使用的接口 ✅
根据后端设计文档，以下接口已实现可直接使用：

#### 帖子管理
- `POST /api/post/create` - 创建帖子
- `POST /api/post/update` - 更新帖子
- `POST /api/post/delete` - 删除帖子
- `GET /api/post/detail` - 获取帖子详情

#### 帖子查询
- `POST /api/post/list` - 分页查询帖子
- `GET /api/post/user` - 获取用户帖子
- `GET /api/post/nearby` - 获取附近帖子
- `GET /api/post/hot` - 获取热门帖子
- `GET /api/post/following` - 获取关注用户帖子
- `POST /api/post/tags` - 根据标签查询帖子

#### 互动功能
- `POST /api/post/like` - 点赞帖子
- `POST /api/post/unlike` - 取消点赞
- `POST /api/post/favorite` - 收藏帖子
- `POST /api/post/unfavorite` - 取消收藏
- `POST /api/post/share` - 分享帖子
- `GET /api/post/favorites` - 获取收藏帖子

### 需要新增的接口 ⚠️

#### 用户资料接口
- `GET /api/user/profile/{userId}` - 获取用户详情
- `PUT /api/user/profile` - 更新用户资料
- `POST /api/user/avatar` - 上传用户头像
- `GET /api/user/stats/{userId}` - 获取用户统计数据

#### 搜索接口
- `GET /api/search/comprehensive` - 综合搜索
- `GET /api/search/hot-keywords` - 获取热门搜索词
- `POST /api/search/history` - 保存搜索历史
- `GET /api/search/history` - 获取搜索历史

#### 话题标签接口
- `GET /api/tags/hot` - 获取热门话题
- `GET /api/tags/detail/{tagId}` - 获取话题详情
- `GET /api/tags/posts/{tagId}` - 获取话题下的帖子

#### 消息通知接口
- `GET /api/notifications/unread-count` - 获取未读消息数
- `PUT /api/notifications/read` - 标记消息已读
- `GET /api/notifications/list` - 获取消息列表
- `GET /api/notifications/system` - 获取系统消息
- `GET /api/notifications/likes` - 获取点赞消息
- `GET /api/notifications/follows` - 获取关注消息

#### 私信接口
- `GET /api/chat/conversations` - 获取会话列表
- `GET /api/chat/messages/{conversationId}` - 获取聊天消息
- `POST /api/chat/send` - 发送消息
- `PUT /api/chat/read/{conversationId}` - 标记会话已读
- `DELETE /api/chat/clear/{conversationId}` - 清空聊天记录

#### 关注系统接口
- `POST /api/follow` - 关注用户
- `DELETE /api/follow` - 取消关注
- `GET /api/follow/following/{userId}` - 获取关注列表
- `GET /api/follow/followers/{userId}` - 获取粉丝列表
- `GET /api/follow/status/{userId}` - 检查关注状态

#### 举报接口
- `POST /api/report/submit` - 提交举报
- `GET /api/report/reasons` - 获取举报原因列表

## 📝 对接任务清单

### 阶段一：数据库和基础接口准备 (预计2天)

#### 任务1.1：数据库更新
- [ ] 确认ba_user表bio和dance_type字段是否已添加
- [ ] 确认comments表post_id字段是否已添加
- [ ] 执行所有数据库脚本，确保表结构完整
- [ ] 验证触发器和视图是否正常工作

#### 任务1.2：基础接口完善
- [ ] 完善用户资料相关接口
- [ ] 实现文件上传接口（头像、帖子图片）
- [ ] 完善帖子查询接口的分页和筛选功能
- [ ] 测试已有接口的功能完整性

### 阶段二：核心功能接口开发 (预计3天)

#### 任务2.1：搜索功能接口
- [ ] 实现综合搜索接口
- [ ] 实现热门搜索词接口
- [ ] 实现搜索历史管理接口
- [ ] 添加搜索结果排序和筛选

#### 任务2.2：话题标签接口
- [ ] 实现热门话题获取接口
- [ ] 实现话题详情接口
- [ ] 实现话题下帖子查询接口
- [ ] 添加话题统计功能

#### 任务2.3：消息通知接口
- [ ] 实现消息通知CRUD接口
- [ ] 实现未读消息统计接口
- [ ] 实现消息分类查询接口
- [ ] 添加消息推送机制

### 阶段三：社交功能接口开发 (预计2天)

#### 任务3.1：关注系统接口
- [ ] 实现关注/取消关注接口
- [ ] 实现关注列表查询接口
- [ ] 实现关注状态检查接口
- [ ] 优化关注相关的统计更新

#### 任务3.2：私信系统接口
- [ ] 实现会话管理接口
- [ ] 实现消息发送接收接口
- [ ] 实现消息状态管理接口
- [ ] 添加消息类型支持（文字、图片、语音）

### 阶段四：前端对接 (预计4天)

#### 任务4.1：首页对接
- [ ] 对接帖子列表API
- [ ] 对接话题标签API
- [ ] 实现下拉刷新和上拉加载
- [ ] 对接搜索跳转功能

#### 任务4.2：发布页对接
- [ ] 对接图片上传API
- [ ] 对接帖子发布API
- [ ] 对接话题选择API
- [ ] 对接位置定位API

#### 任务4.3：个人主页对接
- [ ] 对接用户资料API
- [ ] 对接用户帖子列表API
- [ ] 对接用户统计数据API
- [ ] 对接资料编辑功能

#### 任务4.4：用户主页对接
- [ ] 对接其他用户资料API
- [ ] 对接关注/取消关注API
- [ ] 对接用户帖子展示API
- [ ] 对接举报功能API

#### 任务4.5：消息中心对接
- [ ] 对接消息列表API
- [ ] 对接未读消息统计API
- [ ] 对接消息分类API
- [ ] 对接消息已读状态API

#### 任务4.6：聊天功能对接
- [ ] 对接会话列表API
- [ ] 对接聊天消息API
- [ ] 对接消息发送API
- [ ] 对接消息状态管理API

### 阶段五：功能完善和测试 (预计2天)

#### 任务5.1：功能测试
- [ ] 完整功能流程测试
- [ ] 接口性能测试
- [ ] 错误处理测试
- [ ] 边界条件测试

#### 任务5.2：优化和修复
- [ ] 修复测试中发现的问题
- [ ] 优化接口响应速度
- [ ] 完善错误提示信息
- [ ] 添加日志记录

## 🔧 技术实现要点

### 前端实现要点
1. **统一API调用**: 使用vote_baseUrl作为基础路径
2. **错误处理**: 统一的错误处理和用户提示
3. **加载状态**: 合理的loading和empty状态展示
4. **数据缓存**: 适当的数据缓存策略
5. **图片处理**: 图片压缩和上传进度显示

### 后端实现要点
1. **统一响应格式**: 保持与现有系统一致的响应格式
2. **分页查询**: 统一的分页参数和响应格式
3. **权限控制**: 用户身份验证和权限检查
4. **数据验证**: 输入参数的验证和安全检查
5. **性能优化**: 数据库查询优化和缓存策略

## 📊 预期时间安排

| 阶段 | 任务 | 预计时间 | 负责人 |
|------|------|----------|--------|
| 阶段一 | 数据库和基础接口 | 2天 | 后端开发 |
| 阶段二 | 核心功能接口 | 3天 | 后端开发 |
| 阶段三 | 社交功能接口 | 2天 | 后端开发 |
| 阶段四 | 前端对接 | 4天 | 前端开发 |
| 阶段五 | 测试和优化 | 2天 | 全栈协作 |
| **总计** | | **13天** | |

## 🚨 风险点和注意事项

### 技术风险
1. **数据库性能**: 大量数据时的查询性能优化
2. **并发处理**: 点赞、关注等高并发操作的处理
3. **文件上传**: 图片上传的大小限制和格式验证
4. **消息推送**: 实时消息通知的实现方案

### 业务风险
1. **数据一致性**: 统计数据的准确性保证
2. **用户体验**: 加载速度和交互流畅性
3. **内容安全**: 用户发布内容的审核机制
4. **隐私保护**: 用户数据的隐私保护

### 解决方案
1. **性能监控**: 添加接口性能监控和慢查询日志
2. **缓存策略**: 合理使用Redis缓存热点数据
3. **异步处理**: 使用消息队列处理耗时操作
4. **分步上线**: 分模块逐步上线，降低风险

## ✅ 验收标准

### 功能验收
- [ ] 所有前端页面功能正常
- [ ] 所有API接口响应正确
- [ ] 数据统计准确无误
- [ ] 用户交互流畅自然

### 性能验收
- [ ] 接口响应时间 < 2秒
- [ ] 页面加载时间 < 3秒
- [ ] 图片上传成功率 > 95%
- [ ] 并发用户支持 > 1000

### 安全验收
- [ ] 用户身份验证正常
- [ ] 数据权限控制有效
- [ ] 输入参数验证完整
- [ ] 敏感信息保护到位

## 📋 详细API接口规范

### 前端页面与API对接映射

#### 首页 (`/home/<USER>
```javascript
// 需要对接的API
- GET /api/post/list - 获取帖子列表
- GET /api/tags/hot - 获取热门话题
- POST /api/post/like - 点赞帖子
- POST /api/post/unlike - 取消点赞

// 数据格式示例
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "userId": 123,
        "username": "张小明",
        "userAvatar": "https://...",
        "content": "帖子内容",
        "coverImage": "https://...",
        "images": ["https://..."],
        "likeCount": 100,
        "commentCount": 20,
        "isLiked": false,
        "createTime": "2025-07-17 10:00:00"
      }
    ],
    "total": 100,
    "hasMore": true
  }
}
```

#### 发布页 (`/publish/index.vue`)
```javascript
// 需要对接的API
- POST /api/upload/image - 上传图片
- POST /api/post/create - 创建帖子
- GET /api/tags/search - 搜索话题标签

// 发布帖子请求格式
{
  "content": "帖子内容",
  "images": ["image_url1", "image_url2"],
  "tags": ["生活", "美食"],
  "locationName": "北京市朝阳区",
  "locationLatitude": 39.9042,
  "locationLongitude": 116.4074,
  "isPublic": 1
}
```

#### 个人主页 (`/profile/index.vue`)
```javascript
// 需要对接的API
- GET /api/user/profile - 获取当前用户资料
- GET /api/user/posts - 获取用户帖子
- GET /api/user/favorites - 获取用户收藏
- GET /api/user/likes - 获取用户点赞的帖子
- PUT /api/user/profile - 更新用户资料
```

#### 用户主页 (`/user/profile.vue`)
```javascript
// 需要对接的API
- GET /api/user/profile/{userId} - 获取用户资料
- GET /api/user/posts/{userId} - 获取用户帖子
- POST /api/follow - 关注用户
- DELETE /api/follow - 取消关注
- GET /api/follow/status/{userId} - 检查关注状态
```

#### 消息中心 (`/message/index.vue`)
```javascript
// 需要对接的API
- GET /api/chat/conversations - 获取会话列表
- GET /api/notifications/unread-count - 获取未读消息数
- GET /api/notifications/system - 获取系统消息
- GET /api/notifications/likes - 获取点赞消息
- GET /api/notifications/follows - 获取关注消息
```

#### 聊天详情 (`/chat/detail.vue`)
```javascript
// 需要对接的API
- GET /api/chat/messages/{conversationId} - 获取聊天消息
- POST /api/chat/send - 发送消息
- PUT /api/chat/read/{conversationId} - 标记已读
- DELETE /api/chat/clear/{conversationId} - 清空聊天记录
- POST /api/report/submit - 提交举报
```

### 统一错误处理格式
```javascript
// 成功响应
{
  "code": 200,
  "message": "success",
  "data": {...}
}

// 错误响应
{
  "code": 400,
  "message": "参数错误",
  "data": null
}

// 常见错误码
- 200: 成功
- 400: 参数错误
- 401: 未登录
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器错误
```

## 🔄 数据同步策略

### 实时数据更新
1. **点赞数统计**: 使用乐观锁更新，避免并发问题
2. **关注数统计**: 通过触发器自动维护
3. **消息未读数**: 实时更新，支持推送通知
4. **在线状态**: 定期心跳更新用户在线状态

### 缓存策略
1. **热门帖子**: Redis缓存1小时
2. **用户资料**: Redis缓存30分钟
3. **话题列表**: Redis缓存2小时
4. **统计数据**: Redis缓存10分钟

## 🛠️ 开发工具和环境

### 开发环境要求
- **前端**: HBuilderX + uni-app
- **后端**: IDEA + SpringBoot 2.x
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **文件存储**: 本地存储或OSS

### 调试工具
- **API测试**: Postman/Apifox
- **数据库管理**: Navicat/DataGrip
- **日志查看**: 控制台日志
- **性能监控**: SpringBoot Actuator

---

**文档版本**: v1.0
**创建时间**: 2025-07-17
**最后更新**: 2025-07-17
**维护人员**: 开发团队
