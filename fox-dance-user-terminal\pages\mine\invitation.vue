<template>
	<view class="invitation" :style="{ '--qjbutton-color': qjbutton }">
		
		<!-- <view class="bak" :style="'top:'+(safeAreaTop+10)+'px'" @click="bakTap"><image src="/static/images/bak.png"></image></view> -->
		
		<view class="inv_one">
			<image :src="baseUrl_admin + '/static/images/icon19.jpg'"></image>
			<!-- <view>每邀请一位好友，得{{inviteInfo.points}}积分</view> -->
			<!-- <view class="inv_one_wz"><view>小小狗 邀请了</view><view>60个人获得60次抽奖机会!</view></view> -->
			<!-- <view class="inv_one_wz"><view style="opacity:0;">　的</view><view>邀请好友，获得抽奖次数</view></view> -->
			<text @click="navTo('/pages/login/xieYi?type=5')">活动规则</text>
		</view>
		
		<view class="inv_news">
			<view class="inv_news_a">
				<view class="inv_news_a_t">分享以下平台</view>
				<view class="inv_news_a_b" style="padding-bottom: 10rpx;">
					<view class="inv_news_a_b_li">
						<button open-type="share">Fenx </button>
						<image src="/static/images/icon81.png" mode="aspectFill"></image>
						<text>微信好友</text>
						<view>发送至微信好友分享</view>
					</view>
					<view class="inv_news_a_b_li" @click="zsewmToggle = true">
						<image src="/static/images/icon82.png" mode="aspectFill"></image>
						<text>保存个人码</text>
						<view>面对面邀请</view>
					</view>
				</view>
			</view>
			
			<view class="inv_news_a yqmx">
				<view class="inv_news_a_t">邀请明细</view>
				<view class="inv_news_a_b">
					<view class="inv_four_li">
						<view class="inv_four_li_a"><text>{{inviteInfo.invite_num}}</text>人</view>
						<view class="inv_four_li_b">已邀请</view>
						<view class="inv_four_li_xian"></view>
					</view>
					<view class="inv_four_li">
						<view class="inv_four_li_a"><text>{{inviteInfo.lottery_num}}</text>次</view>
						<view class="inv_four_li_b">抽奖次数</view>
						<view class="inv_four_li_xian"></view>
					</view>
					<view class="inv_four_li">
						<view class="inv_four_li_a"><text>{{inviteInfo.experience}}</text></view>
						<view class="inv_four_li_b">经验值</view>
					</view>
				</view>
			</view>
			
			<!-- ios底部安全距离 go -->
			<view class="aqjlViw"></view>
			<!-- ios底部安全距离 end -->
		</view>
		
		<!-- <image src="/static/images/icon80.png" style="width:100%;display: block;" mode="widthFix"></image>
		<view class="inv_two">
			<view class="inv_two_li"><button open-type="share">Fenx </button><image src="/static/images/icon20.png"></image><view>微信好友</view><text></text></view>
			<view class="inv_two_li" @click="zsewmToggle = true"><image src="/static/images/icon21.png"></image><view>保存个人码</view></view>
			<view class="inv_two_li"><image src="/static/images/icon22.png"></image><view>微信好友</view></view>
		</view>
		<view class="inv_thr">
			<text class="inv_thr_a"></text>
			<text class="inv_thr_b"></text>
			<view>我的奖励</view>
			<text class="inv_thr_b"></text>
			<text class="inv_thr_a"></text>
		</view>
		<view class="inv_four">
			<view class="inv_four_li">
				<view class="inv_four_li_a"><text>{{inviteInfo.total_points}}</text>分</view>
				<view class="inv_four_li_b">累计积分奖励</view>
				<view class="inv_four_li_xian"></view>
			</view>
			<view class="inv_four_li">
				<view class="inv_four_li_a"><text>{{inviteInfo.invite_num}}</text>人</view>
				<view class="inv_four_li_b">已邀请</view>
			</view>
		</view> -->
		
		<!-- 专属个人码 go -->
		<view class="gg_rgba" v-if="zsewmToggle"></view>
		<view class="min_ewm" v-if="zsewmToggle">
			<view class="min_ewm_t">专属个人码<image src="/static/images/icon16.png" @click="zsewmToggle = !zsewmToggle"></image></view>
			<!-- <image :src="imgbaseUrl + userInfo.share" class="min_ewm_b"></image> -->
			<image :src="userInfo.share" class="min_ewm_b"></image>
			<view class="min_ewm_c" @click="imgUploadTap">保存</view>
		</view>
		<!-- 专属个人码 end -->
		
		
		
	</view>
</template>


<script>
import {
	inviteApi,
	userInfoApi,
	homeDataApi
} from '@/config/http.achieve.js'
import {
	aa
} from '@/config/http.api'
export default {
	data() {
		return {
			isLogined:true,
			safeAreaTop:wx.getWindowInfo().safeArea.top,
			navLists:['全部','等位中','待开课','授课中','已完成'],
			inviteInfo:{
				invite_num: 0,
				points: 0,
				total_points: 0,
			},
			zsewmToggle:false,//专属二维码
			userInfo:{
				avatar:'',
				train_count:0,
				train_day:0,
				train_time:0,
				score:0,
			},
			imgbaseUrl:'',//图片地址
			baseUrl_admin:'',
			qjbutton:'#131315',
			luck_draw_frequency:0,
			experience_value:0,
		}
	},
	onShow() {
		
	},
	onLoad() {
		this.imgbaseUrl = this.$baseUrl;
		this.baseUrl_admin = this.$baseUrl_admin;
		
		this.inviteData();//邀请有奖
		this.userData();//个人信息
		// this.homeData();//首页数据
		this.qjbutton = uni.getStorageSync('storeInfo').button
	},
	methods: {
		//返回
		bakTap(){
			uni.navigateBack({})
		},
		imgUploadTap(){
			var that = this;
			uni.showLoading({
				title: '加载中'
			});
			// this.imgbaseUrl + 
			uni.downloadFile({
			  url: this.userInfo.share,
			  success: res => {
			    uni.saveImageToPhotosAlbum({
			      filePath: res.tempFilePath,
			      success() {
					uni.hideLoading();
			        uni.showToast({
			          title: '保存成功'
			        })
			      },
				  fail: function(e) {
				  	uni.hideLoading();
				  	console.log(e, '保存失败');
				  	uni.showToast({
				  		icon: 'none',
				  		mask: true,
				  		title: '保存失败', //保存路径
				  		duration: 3000
				  	});
				  }
			    })
			  }
			})
		},
		//首页数据
		homeData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			homeDataApi({
				longitude:uni.getStorageSync('postion').longitude,
				latitude:uni.getStorageSync('postion').latitude,
				store_id:uni.getStorageSync('storeInfo') ? uni.getStorageSync('storeInfo').id : 0
			}).then(res => {
				if (res.code == 1) {
					console.log('首页',res);
					that.luck_draw_frequency = res.data.luck_draw_frequency;
					that.experience_value = res.data.experience_value;
					uni.hideLoading();
				}
			})
		},
		//个人信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userInfoApi({}).then(res => {
				console.log('个人中心',res)
				if (res.code == 1) {
					that.loding = true;
					that.userInfo = res.data;
					uni.hideLoading();
				}
			})
		},
		//邀请有奖
		inviteData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			inviteApi({}).then(res => {
				console.log('邀请有奖',res)
				if (res.code == 1) {
					that.inviteInfo = res.data;
					uni.hideLoading();
				}
			})
			
		},
		homeTap(){
			uni.switchTab({
				url:'/pages/index/index'
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	},
	// 分享到微信好友
	onShareAppMessage() {
	  var that = this;
	  return {
		title:'FOX舞蹈，快来和我一起体验吧，超多福利等你来拿!',
		path: '/pages/index/index?pid=' + this.userInfo.id,
		// imageUrl:that.bannerLists[0].images,
	  }
	}
}
</script>

<style lang="scss">
page{padding-bottom: 0;background: #f8f8f8;}
</style>