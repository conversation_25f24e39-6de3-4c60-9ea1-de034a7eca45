{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/leave/leaveCard.vue?4468", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/leave/leaveCard.vue?52fb", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/leave/leaveCard.vue?3f02", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/leave/leaveCard.vue?2879", "uni-app:///pages/mine/leave/leaveCard.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "isLogined", "navBg", "zsewmToggle", "cardsLists", "array_lb", "index_lb", "array_md", "index_md", "onPageScroll", "scrollTop", "e", "onShow", "methods", "bindPickerChange_lb", "console", "bindPickerChange_md", "navTo", "uni", "icon", "title", "setTimeout", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;;;AAGxD;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwuB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC8B5vB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC,aACA,IACA,IACA,IACA,IACA,GACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA,IACAC,YACAC,EADAD;IAEA;IACA;EACA;EACAE;IACA;IACA;MACA;IAAA;EAEA;EACAC;IACA;IACAC;MACAC;MACA;IACA;IACA;IACAC;MACAD;MACA;IACA;IACAE;MACA;QACAC;UACAC;UACAC;QACA;QACAC;UACAH;YACAI;UACA;QACA;MACA;QACAJ;UACAI;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/mine/leave/leaveCard.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/leave/leaveCard.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./leaveCard.vue?vue&type=template&id=5bf981cc&scoped=true&\"\nvar renderjs\nimport script from \"./leaveCard.vue?vue&type=script&lang=js&\"\nexport * from \"./leaveCard.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5bf981cc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/leave/leaveCard.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leaveCard.vue?vue&type=template&id=5bf981cc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leaveCard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leaveCard.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"myMemberCard\">\r\n\t\t<view class=\"mucars_one\">\r\n\t\t\t<view class=\"mucars_one_li\">\r\n\t\t\t\t<picker @change=\"bindPickerChange_lb\" :value=\"index_lb\" :range=\"array_lb\">\r\n\t\t\t\t\t<view class=\"uni-input\">{{array_lb[index_lb]}}<text></text></view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"mucars_one_li\">\r\n\t\t\t\t<picker @change=\"bindPickerChange_md\" :value=\"index_md\" :range=\"array_md\">\r\n\t\t\t\t\t<view class=\"uni-input\">{{array_md[index_md]}}<text></text></view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"mycards_two\">\r\n\t\t\t<view class=\"mycards_two_li\" v-for=\"(item,index) in cardsLists\" :key=\"index\" @click=\"navTo('/pages/mine/memberCard/myMemberCardxq')\">\r\n\t\t\t\t<view class=\"mycards_two_li_t\">\r\n\t\t\t\t\t<image src=\"/static/images/toux.png\" mode=\"aspectFit\" class=\"mycards_two_li_t_l\"></image>\r\n\t\t\t\t\t<view class=\"mycards_two_li_t_r\"><view>会员ID:123123</view><text>次卡：2024.07.27到期</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"mycards_two_li_t_b\">选择</view>\r\n\t\t\t\t<view class=\"mycards_two_li_t_zt\">剩余2次</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport tabbar from '@/components/tabbar.vue'\r\nexport default {\r\n\tcomponents: {\r\n\t\ttabbar,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tnavBg:'',\r\n\t\t\tzsewmToggle:false,//专属二维码\r\n\t\t\tcardsLists:[\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t],\r\n\t\t\tarray_lb: ['卡种类别', '类别1', '类别2', '类别3'],//卡种类别\r\n\t\t\tindex_lb: 0,//卡种类别\r\n\t\t\tarray_md: ['适用门店', '门店1', '门店2', '门店3'],//适用门店\r\n\t\t\tindex_md: 0,//适用门店\r\n\t\t}\r\n\t},\r\n\tonPageScroll(e) {\r\n\t\tconst top = uni.upx2px(100)\r\n\t\tconst {\r\n\t\t\tscrollTop\r\n\t\t} = e\r\n\t\tlet percent = scrollTop / top > 1 ? 1 : scrollTop / top\r\n\t\tthis.navBg = percent\r\n\t},\r\n\tonShow() {\r\n\t\t// this.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t\tif(this.isLogined){\r\n\t\t\t// this.userData();//个人信息\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t//卡种类别\r\n\t\tbindPickerChange_lb: function(e) {\r\n\t\t\tconsole.log('picker发送选择改变，携带值为', e.detail.value)\r\n\t\t\tthis.index_lb = e.detail.value\r\n\t\t},\r\n\t\t//适用门店\r\n\t\tbindPickerChange_md: function(e) {\r\n\t\t\tconsole.log('picker发送选择改变，携带值为', e.detail.value)\r\n\t\t\tthis.index_md = e.detail.value\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tif(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t}else{\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:url\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t\r\n</style>"], "sourceRoot": ""}