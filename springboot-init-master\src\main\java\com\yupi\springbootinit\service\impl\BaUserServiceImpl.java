package com.yupi.springbootinit.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yupi.springbootinit.config.CosClientConfig;
import com.yupi.springbootinit.mapper.BaUserMapper;
import com.yupi.springbootinit.model.dto.UserDTO;
import com.yupi.springbootinit.model.dto.user.UserProfileUpdateRequest;
import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.service.BaUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户服务实现类
 */
@Service
@Slf4j
public class BaUserServiceImpl extends ServiceImpl<BaUserMapper, BaUser> implements BaUserService {

    @Resource
    private CosClientConfig cosClientConfig;

    @Override
    public boolean decrementRemainingVotes(Long userId) {
        // 检查用户是否存在且剩余票数大于0
        BaUser user = this.getById(userId);
        if (user == null) {
            log.error("用户不存在，userId: {}", userId);
            return false;
        }

        if (user.getRemaining_votes() <= 0) {
            log.error("用户剩余投票次数不足，userId: {}, 剩余次数: {}", userId, user.getRemaining_votes());
            return false;
        }

        // 使用乐观锁更新剩余票数
        LambdaUpdateWrapper<BaUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BaUser::getId, userId)
                .gt(BaUser::getRemaining_votes, 0)
                .setSql("remaining_votes = remaining_votes - 1");
        
        return this.update(updateWrapper);
    }
    
    @Override
    public UserDTO getUserInfo(Long userId) {
        BaUser user = this.getById(userId);
        if (user == null) {
            return null;
        }
        
        UserDTO userDTO = new UserDTO();
        userDTO.setId(user.getId());
        userDTO.setNickname(user.getNickname() != null ? user.getNickname() : user.getUsername());
        
        // 处理头像URL
        String avatar = user.getAvatar();
        if (StringUtils.isNotBlank(avatar)) {
            // 如果是相对路径，拼接COS基础URL
            if (avatar.startsWith("/")) {
                avatar = cosClientConfig.getBaseUrl() + avatar;
            }
        }
        userDTO.setAvatar(avatar);
        
        // 设置用户等级
        userDTO.setLevel(user.getLevel() != null ? user.getLevel() : 1);
        
        return userDTO;
    }

    @Override
    public boolean updateUserProfile(Long userId, UserProfileUpdateRequest updateRequest) {
        if (userId == null || userId <= 0 || updateRequest == null) {
            log.error("更新用户资料参数错误 - userId: {}, updateRequest: {}", userId, updateRequest);
            return false;
        }

        try {
            // 使用UpdateWrapper只更新非空字段
            UpdateWrapper<BaUser> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", userId);

            boolean hasUpdate = false;

            // 只更新非空字段，直接传递字符串值
            if (StringUtils.isNotBlank(updateRequest.getNickname())) {
                String nickname = updateRequest.getNickname();
                updateWrapper.set("nickname", nickname);
                hasUpdate = true;
            }

            if (updateRequest.getBio() != null) {
                String bio = updateRequest.getBio();
                updateWrapper.set("bio", bio);
                hasUpdate = true;
            }

            if (StringUtils.isNotBlank(updateRequest.getDanceType())) {
                String danceType = updateRequest.getDanceType();
                updateWrapper.set("dance_type", danceType);
                hasUpdate = true;
            }

            if (StringUtils.isNotBlank(updateRequest.getAvatar())) {
                String avatar = updateRequest.getAvatar();
                updateWrapper.set("avatar", avatar);
                hasUpdate = true;
            }

            // 如果没有需要更新的字段，直接返回成功
            if (!hasUpdate) {
                log.info("用户资料无需更新 - userId: {}", userId);
                return true;
            }

            // 执行更新
            boolean result = this.update(updateWrapper);

            if (result) {
                log.info("用户资料更新成功 - userId: {}", userId);
            } else {
                log.warn("用户资料更新失败 - userId: {}", userId);
            }

            return result;

        } catch (Exception e) {
            log.error("更新用户资料异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return false;
        }
    }
}