package com.yupi.springbootinit.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户统计实体
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@TableName(value = "user_stats")
@Data
public class UserStats implements Serializable {

    /**
     * 用户ID（主键）
     */
    @TableId(value = "user_id")
    private Long userId;

    /**
     * 关注数
     */
    @TableField("following_count")
    private Integer followingCount;

    /**
     * 粉丝数
     */
    @TableField("follower_count")
    private Integer followerCount;

    /**
     * 帖子数
     */
    @TableField("post_count")
    private Integer postCount;

    /**
     * 收到的点赞数
     */
    @TableField("like_received_count")
    private Integer likeReceivedCount;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
