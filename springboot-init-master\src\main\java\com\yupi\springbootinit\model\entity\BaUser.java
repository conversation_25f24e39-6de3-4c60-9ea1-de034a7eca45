package com.yupi.springbootinit.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 员工表
 * @TableName ba_user
 */
@TableName(value ="ba_user")
public class BaUser implements Serializable {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 用户等级
     */
    private Integer level;

    /**
     * 是否是会员 0-否 1-是
     */
    private Integer is_member;

    /**
     * 剩余投票次数
     */
    private Integer remaining_votes;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 舞种
     */
    private String dance_type;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getIs_member() {
        return is_member;
    }

    public void setIs_member(Integer is_member) {
        this.is_member = is_member;
    }

    public Integer getRemaining_votes() {
        return remaining_votes;
    }

    public void setRemaining_votes(Integer remaining_votes) {
        this.remaining_votes = remaining_votes;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getDance_type() {
        return dance_type;
    }

    public void setDance_type(String dance_type) {
        this.dance_type = dance_type;
    }

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}