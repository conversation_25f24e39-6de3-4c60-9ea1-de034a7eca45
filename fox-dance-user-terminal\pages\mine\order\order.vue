<template>
	<view class="order" :style="{ '--qjbutton-color': qjbutton }">
		<view class="ord_nav">
			<view class="ord_nav_li" :class="type == 0 ? 'ord_nav_li_ac' : ''" @click="navTap(0)"><view><text>全部</text><text></text></view></view>
			<view class="ord_nav_li" :class="type == 1 ? 'ord_nav_li_ac' : ''" @click="navTap(1)"><view><text>待收货</text><text></text></view></view>
			<view class="ord_nav_li" :class="type == 2 ? 'ord_nav_li_ac' : ''" @click="navTap(2)"><view><text>已完成</text><text></text></view></view>
		</view> 
		<view class="ord_con">
			<view class="ord_con_li" v-for="(item,index) in orderLists" :key="index">
				<view class="ord_con_li_a"><view>订单号:{{item.order_no}}</view><text>{{item.status == 1 ? '待发货' : item.status == 2 ? '待收货' : item.status == 3 ? '已完成' : '待发货 '}}</text></view>
				<view class="ord_con_li_b">
					<view class="ord_con_li_b_li">
						<image :src="imgbaseUrl + item.image" mode="scaleToFill" class="ord_con_li_b_li_l"></image>
						<view class="ord_con_li_b_li_r">
							<view class="ord_con_li_b_li_r_a">{{item.name}}</view>
							<view class="ord_con_li_b_li_r_bb">规格：{{item.sku_name}}</view>
							<view class="ord_con_li_b_li_r_cc"><view>￥{{item.redeem_points*1}}</view><text>×{{item.num}}</text></view>
							<!-- <view class="ord_con_li_b_li_r_b"><text>￥12.99</text>/份</view> -->
							<!-- <view class="ord_con_li_b_li_r_b">￥<text>{{item.redeem_points*1}}</text></view> -->
							<!-- <view class="ord_con_li_b_li_r_c">已选：420g</view> -->
						</view>
					</view>
				</view>
				<view class="ord_con_li_c" :style="item.status == 2 ? '' : 'padding-bottom:0'">
					<text>共1项</text>
					<view>合计：￥{{item.total_price*1}}</view>
				</view>
				<!-- //状态:1=待发货,2=待收货,3=已完成 -->
				<view class="ord_con_li_d" v-if="item.status == 2">
					<view @click="navTo('/pages/mine/order/logistics?id=' + item.id + '&name=' + item.name + '&images=' + item.image)" v-if="item.status == 2">查看物流</view>
					<view class="back" v-if="item.status == 2" @click.stop="confirmSubTap(item.id)">确认收货</view>
				</view>
			</view>
			<view class="gg_loding" v-if="!zanwsj">
				<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
					<view></view>
					<text>加载中</text>
				</view>
				<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
			</view>
			<view class="gg_zwsj" v-if="zanwsj">
				<view class="gg_zwsj_w">
					<image src="/static/images/wusj.png" mode="widthFix"></image>
					<text>暂无数据</text>
				</view>
			</view>
			
		</view>
	</view>
</template>


<script>
import {
	myOrderApi,
	confirmOrderApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			type:0,
			orderLists:[],//积分明细
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			imgbaseUrl:'',
			qjbutton:'#131315',
		}
	},
	onLoad() {
		this.qjbutton = uni.getStorageSync('storeInfo').button
	},
	onShow() {
		this.imgbaseUrl = this.$baseUrl;
		this.page = 1;
		this.orderLists = [];
		this.orderData()//订单列表
	},
	methods: {
		navTap(index){
			this.type = index;
			this.page = 1;
			this.orderLists = [];
			this.orderData();
		},
		//订单列表
		orderData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			myOrderApi({
				page:that.page,
				size:10,
				type:that.type,
			}).then(res => {
				console.log('订单列表',res)
				if (res.code == 1) {
					/*res.data.data = [
						{
							name:'泡玛特POPMART拉布布Labubu马卡龙坐坐派对搪胶脸盲盒 前方高能3.0三代 ',
							order_no:'123456',
							status:2,
							image:'/storage/default/20250405/画板1拷贝46f915eeeece5349b4921bffacb4dc2adb16ed84.png',
							redeem_points:20,
						},
						{
							name:'泡玛特POPMART拉布布Labubu马卡龙坐坐派对搪胶脸盲盒 前方高能3.0三代 ',
							order_no:'123456',
							status:1,
							image:'/storage/default/20250405/画板1拷贝46f915eeeece5349b4921bffacb4dc2adb16ed84.png',
							redeem_points:20,
						}
					]
					res.data.last_page = 1*/
					var obj = res.data.data;
					that.orderLists = that.orderLists.concat(obj);
					that.zanwsj = that.orderLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.orderLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottom() {
			//console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.orderData();
				}
			}
		},
		onPullDownRefresh: function() {
		    console.log('我被下拉了');
		    this.page = 1;
			this.orderLists = [];
			this.orderData();//订单列表
		},
		//确认收货
		confirmSubTap(id) {
			var that = this;
			
			uni.showModal({
				title: '提示',
				content: '确认要收货吗？',
				success: function(res) {
					if (res.confirm) {
		
						uni.showLoading({
							title: '加载中'
						});
						confirmOrderApi({
							id: id
						}).then(rep => {
							console.log('确认收货', rep)
							if (rep.code == 1) {
								uni.hideLoading();
								that.page = 1;
								that.orderLists = [];
								that.orderData();//订单列表
								uni.showToast({
									icon: 'success',
									title: '收货成功',
									duration: 2000
								});
								
							}
						})
		
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style scoped lang="scss">
	
</style>