<template>
	<view class="winningrecord myCourse" :style="{ '--qjbutton-color': qjbutton }">
		<view class="ord_nav">
			<view class="ord_nav_li" v-for="(item,index) in navLists" :key="index" :class="type == index ? 'ord_nav_li_ac' : ''" @click="navTap(index)"><view><text>{{item}}</text><text></text></view></view>
		</view>
		
		<view class="jlcon"> 
			<view class="jlcon_li" v-for="(item,index) in courseLists" :key="index" @click="goodsSpTo(item)">
				<view class="jlcon_li_zt" :class="item.status == 0 ? 'ddh' : (item.status == 1 || item.status == 2) ? 'ydh' : ''">{{item.status == 0 ? '待兑换' : item.status == 1 ? '已兑换' : item.status == 2 ? '已兑换' : item.status == 3 ? '已过期' : ''}}</view>
				<view class="jlcon_li_l" v-if="item.type == 1"><image :src="imgbaseUrl + item.goods.image" mode="aspectFill"></image></view>
				<view class="jlcon_li_hb" v-if="item.type == 2">
					<view class="pri_two_b_li_hb">
						<!-- <image src='/static/images/icon83.png' class="pri_two_b_li_hb_bj"></image> -->
						<image :src='imgbaseUrl + item.image' class="pri_two_b_li_hb_bj"></image>
						<view class="pri_two_b_li_hb_n">{{item.price*1}}<image src='/static/images/icon83-1.png'></image></view>
					</view>
				</view>
				<!-- <view class="jlcon_li_cards" v-if="item.type == 3">
					<image :src="item.memberCard.type*1 == 0 ? '/static/images/icon85.png' : '/static/images/icon84.png'"></image>
				</view> -->
				<view class="jlcon_li_l" v-if="item.type == 3"><image :src="imgbaseUrl + item.image" mode="aspectFill"></image></view>
				<view class="jlcon_li_r">
					<view class="jlcon_li_r_a">{{item.type*1 == 1 ? item.goods.name : item.type*1 == 2 ? item.price*1 + '元现金红包' : item.type*1 == 3 ? (item.memberCard.type*1 == 0 ? '次卡单店卡' : '时长卡单店卡') : ''}}</view>
					<view class="jlcon_li_r_b">中奖日期：{{item.create_time}}</view>
					<view class="jlcon_li_r_b">有效期：{{item.end_time}}</view>
					<view class="jlcon_li_r_c">×1</view>
					<view class="jlcon_li_r_d" v-if="item.status == 0" @click.stop="dhTap(item)">兑换</view>
					<view class="jlcon_li_r_ckwl"  v-if="item.status == 2" @click.stop="ckwlTap(item)">查看物流</view>
				</view>
			</view>	
		</view>
		<view class="gg_loding" v-if="!zanwsj">
			<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
				<view></view>
				<text>加载中</text>
			</view>
			<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
		</view>
		<view class="gg_zwsj" v-if="zanwsj">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无数据</text>
			</view>
		</view>
	</view>
</template>


<script>
import {
	drawrecordsApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			navLists:['全部','待兑换','已兑换','已过期'],
			type:0,
			
			courseLists:[],//中奖记录
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			imgbaseUrl:'',
			qjbutton:'#131315',
		}
	},
	onLoad() {
		this.qjbutton = uni.getStorageSync('storeInfo').button
	},
	onShow() {
		this.imgbaseUrl = this.$baseUrl;
		this.page = 1;
		this.courseLists = [];
		this.courseData()//中奖记录
		uni.setStorageSync('dy_type','cj')
	},
	methods: {
		//商品跳转详情
		goodsSpTo(item){
			if(item.type*1 == 1){
				uni.navigateTo({
					url:'/pages/prizedraw/winningrecordxq?id=' + item.goods_id + '&jpid=' + item.id + '&status=' + item.status
				})
			}
		},
		//兑换跳转
		dhTap(item){
			if(item.type == 1){
				item.goods.jpid = item.id
				uni.setStorageSync('dhspGoods',item.goods)
				//普通商品兑换
				uni.navigateTo({
					url:'/pages/prizedraw/confirmOrder'
				})
			}
			if(item.type == 2){
				//现金红包兑换
				uni.navigateTo({
					url:'/pages/prizedraw/hb_confirmOrders?jpid=' + item.id + '&price=' + item.price
				})
			}
			if(item.type == 3){
				//次卡兑换
				uni.navigateTo({
					url:'/pages/prizedraw/selectStores?jpid=' + item.id
				})
			}
		},
		//查看物流
		ckwlTap(item){
			uni.navigateTo({
				url:'/pages/mine/order/logistics?id=' + item.id + '&name=' + item.goods.name + '&images=' + item.goods.image + '&type=2'
			})
		},
		navTap(index){
			this.type = index;
			this.page = 1;
			this.courseLists = [];
			this.courseData();
		},
		//中奖记录
		courseData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			drawrecordsApi({
				page:that.page,
				size:10,
				type:that.type,
			}).then(res => {
				console.log('中奖记录',res)
				if (res.code == 1) {
					var obj = res.data.data;
					that.courseLists = that.courseLists.concat(obj);
					that.zanwsj = that.courseLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.courseLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottom() {
			//console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.courseData();
				}
			}
		},
		onPullDownRefresh: function() {
		    console.log('我被下拉了');
		    this.page = 1;
			this.courseLists = [];
			this.courseData();//中奖记录
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
	}
}
</script>

<style scoped lang="scss">
.winningrecord{overflow:hidden;}	
</style>