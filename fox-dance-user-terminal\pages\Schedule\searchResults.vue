<template>
	<view class="searchResults" :style="{ '--qjbutton-color': qjbutton,'--qjziti-color': qjziti }">
		
		<view class="les_search">
			<view class="les_search_l"><image src="/static/images/search.png"></image><input type="text" placeholder-style="color:#999999" placeholder="请搜索课程" v-model="keywords" confirm-type="search" @confirm="searchTap(keywords)" /></view>
			<view class="les_search_r" @click="searchTap(keywords)">搜索</view>
		</view>
		
		<!-- <view class="teaCon">
			<view class="teaCon_li" v-for="(item,index) in 4" :key="index">
				<view class="teaCon_li_a">拉丁舞练习</view>
				<view class="teaCon_li_b">
					<image src="/static/images/icon23.jpg" mode="aspectFill" class="teaCon_li_b_l"></image>
					<view class="teaCon_li_b_c">
						<view class="teaCon_li_b_c_a">16:00-17:00</view>
						<view class="teaCon_li_b_c_b">上课老师：LINDA</view>
						<view class="teaCon_li_b_c_c"><text>入门</text><text>拉丁舞</text></view>
					</view>
					<view class="teaCon_li_b_r">预约</view>
				</view>
				<view class="teaCon_li_c">
					<view class="teaCon_li_c_l">
						<image src="/static/images/toux.png" v-for="(item,index) in 6" :key="index"></image>
					</view>
					<view class="teaCon_li_c_r">已预约：<text>23</text>人;<text>3</text>人在等位</view>
				</view>
			</view>
		</view> -->
		
		<view class="teaCon">
			<view class="teaCon_li" v-for="(item,index) in storeCourseLists" :key="index" @click="storesxqTap(item)">
				<view class="teaCon_li_a">{{item.course.name}}</view>
				<view class="teaCon_li_b">
					<image :src="imgbaseUrl + item.teacher.image" mode="aspectFill" class="teaCon_li_b_l"></image>
					<view class="teaCon_li_b_c">
						<view class="teaCon_li_b_c_a">{{item.start_time}}-{{item.end_time}}</view>
						<view class="teaCon_li_b_c_b">上课老师：{{item.teacher.name}}</view>
						<view class="teaCon_li_b_c_b" v-if="item.frequency*1 > 0">次卡消耗：{{item.frequency*1}}次</view>
						<view class="teaCon_li_b_c_c"><text v-if="item.level_name">{{item.level_name}}</text><text>{{item.dance_name}}</text></view>
					</view>
					
					
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-if="item.status == 1" @click.stop>待开课</view>
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 2" @click.stop>授课中</view>
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 3" @click.stop>已完成</view>
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 4" @click.stop>等位中</view>
					<!-- <view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 6" @click.stop>未开始预约</view> -->
					<view class="teaCon_li_b_r yysj" style="background:#BEBEBE" v-else-if="item.status == 6" @click.stop><text>{{item.start_reservation}}</text><text>开始预约</text></view>
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 7" @click.stop>截止预约</view>
					<!-- 未开启等位并且课程预约爆满 (课程预约满员时开启等位:0=关闭,1=开启)-->
					<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.equivalent*1 == 0 && (item.appointment_number*1 >= item.maximum_reservation*1)" @click.stop="kqhyts">预约</view>
					<view class="teaCon_li_b_r" :style="item.member == 0 ? 'background:#BEBEBE' : ''" v-else-if="item.member == 0" @click.stop="ljtkToggle = true">预约</view>
					<!-- 开启等位 -->
					<view class="teaCon_li_b_r" v-else @click.stop="yypdTo(item,'/pages/Schedule/Schedulexq?id' + item.id)">{{item.waiting_number*1 > 0 ? '去排队' : '预约'}}</view>
					
				</view>
				<view class="teaCon_li_c"  v-if="item.appointment_number > 0">
					<view class="teaCon_li_c_l">
						<!-- /static/images/toux.png -->
						<image :src="imgbaseUrl + item.avatar" v-for="(item,index) in item.appointment_people" :key="index" mode="aspectFit"></image>
					</view>
					<view class="teaCon_li_c_r">已预约：<text>{{item.appointment_number}}</text>人;<template v-if="item.waiting_number*1 > 0"><text>{{item.waiting_number}}</text>人在等位</template></view>
				</view>
			</view>
		</view>
		
		<view class="gg_zwsj" style="margin-bottom:60rpx;" v-if="storeCourseLists.length == 0">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无课程</text>
			</view>
		</view>
		
		<!-- 提示预约弹窗 go -->
		<view class="yytnCon" v-if="ljtkToggle"><view class="yytnCon_n"><image :src="imgbaseUrlOss + '/userreport/icon55.png'"></image><text @click="ljktTap"></text></view><image src="/static/images/icon56.png" @click="ljtkToggle = false"></image></view>
		<!-- 提示预约弹窗 end -->
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	homeDataApi,
	lscxCategoryApi,
	TeachersIntroductionApi,
	storeListsApi,
	storeCourseApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			keywords:'',
			keywords_cunc:'',
			isLogined:true,
			hotLists:['大蒜','胡萝卜','大蒜','胡萝卜','大蒜','胡萝卜'],
			mdId:0,
			storeCourseLists:[],
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			imgbaseUrl:'',
			imgbaseUrlOss:'',
			qjbutton:'#131315',
			qjziti:'#F8F8FA'
		}
	},
	onShow() {
		this.imgbaseUrlOss = this.$baseUrlOss;
		this.imgbaseUrl = this.$baseUrl;
		this.isLogined = uni.getStorageSync('token') ? true : false;
		this.page = 1;
		this.storeCourseLists = [];//门店课程
		this.storeCourseData();//门店课程
	},
	onLoad(option) {
		this.mdId = option.id;
		this.keywords = option.keywords;
		this.keywords_cunc = option.keywords;
		uni.setNavigationBarTitle({
			title:this.keywords
		})
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.qjziti = uni.getStorageSync('storeInfo').written_words
	},
	methods: {
		//搜索
		searchTap(){
			this.keywords_cunc = this.keywords;
			this.page = 1;
			this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
		},
		//门店课程
		/*storeCourseData(){
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			storeCourseApi({
				id:that.mdId,
				name:that.keywords_cunc,
			}).then(res => {
				console.log('门店课程',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.storeCourseLists = res.data;
				}
			})
		},*/
		//门店课程
		storeCourseData(jinz){
			
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			storeCourseApi({
				page:that.page,
				id:that.mdId,
				name:that.keywords_cunc,
			}).then(res => {
				console.log('门店课程',res)
				/*if (res.code == 1) {
					uni.hideLoading();
					that.storeCourseLists = res.data.data;
				}*/
				if (res.code == 1) {
					var obj = res.data.data;
					that.storeCourseLists = that.storeCourseLists.concat(obj);
					that.zanwsj = that.storeCourseLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.storeCourseLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
		},
		onReachBottom() {
			//console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.storeCourseData();//门店课程
				}
			}
		},
		onPullDownRefresh: function() {
		    console.log('我被下拉了');
		    this.page = 1;
		    this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
		},
		//详情跳转
		storesxqTap(item){
			console.log(this.isLogined,'this.isLogined')
			if(!this.isLogined){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
				return false;
			}
			// 未开启会员并且后端设置了必须开通会员方可查看详情
			if(item.course.view_type*1 == 0 && item.member == 0){
				this.ljtkToggle = true
			}else{
				uni.navigateTo({
					// url:'/pages/Schedule/Schedulexq?id=' + item.id
					url:'/pages/mine/myCourse/myCoursexq?id=' + item.id
				})
			}
		},
		//预约约课/排队
		yypdTo(item){
			if(!this.isLogined){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
				return false;
			}
			// 未开启会员
			if(item.member == 0){
				this.ljtkToggle = true
				return false;
			}
			uni.navigateTo({
				url:'/pages/Schedule/confirmOrder?id=' + item.id  + '&storeid=' + this.mdId
			})
		},
		//预约爆满
		kqhyts(){
			uni.showToast({
				title: '预约课程已满',
				icon: 'none',
				duration: 1000
			})
		},
		//立即开通会员
		ljktTap(){
			this.ljtkToggle = false;
			uni.switchTab({
				url:'/pages/buy/buy'
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.searchResults{overflow: hidden;}
page{padding-bottom: 0;}
</style>