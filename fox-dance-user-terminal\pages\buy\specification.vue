<template>
	<view class="specification"  v-if="gwcToggle" @click="gwcToggle = false">
		
		<view class="spe_n" @click.stop>
			<view class="spe_n_a">
				<view class="spe_n_a_l"><image :src="imgbaseUrl + (dqggItem.id == 0 ? goodsDetial.image : dqggItem.image)" mode="aspectFill"></image></view>
				<view class="spe_n_a_r">
					<view class="spe_n_a_r_a">
						<view>{{goodsDetial.name}}</view>
						<image src="/static/images/gb1.png" @click="gwcToggle = false"></image>
					</view>
					<view class="spe_n_a_r_b">已选：{{xuanzText == '' ? '请选择' : xuanzText}}</view>
					<view class="spe_n_a_r_c"><view>￥{{price}}</view><text v-if="false">x1</text></view>
				</view>
			</view>
			<view class="spe_n_b">
				<view class="spe_n_b_l">数量</view>
				<view class="spe_n_b_r">
					<view @click="jian(index)">-</view>
					<input type="number" :disabled="false" maxlength="3" v-model="selnum" />
					<view @click="add(index)">+</view>
				</view>
			</view>
			<view class="spe_n_c">
				<view class="spe_n_c_li" v-for="(item,index) in guige" :key="index" v-if="guige.length != 0">
					<view class="spe_n_c_t">{{item.name}}</view>
					<view class="spe_n_c_t_b">
						<view v-for="(erjitem,erjindex) in item.value" :key="erjindex" :class="item.indexSel == erjindex ? 'sho_con_b_a_b_ac' : ''" @click="selguigClick(index,erjindex)">{{erjitem}}</view>
					</view>
				</view>
			</view>
			<view class="spe_n_d" @click="ljdhTap">立即购买</view>
		</view>
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
export default {
	data() {
		return {
			isLogined:true,
			gwcToggle:false,//购物车弹窗是否显示
			selnum:1,
			xuanzText:'',
			guige:[
				/*{
					title:'颜色',
					indexSel:-1,
					list:[
						{name:'黄色',id:'1-1'},
						{name:'白色',id:'1-2'},
						{name:'蓝色',id:'1-3'},
					]
				},
				{
					title:'尺码',
					indexSel:-1,
					list:[
						{name:'L',id:'2-1'},
						{name:'XL',id:'2-2'},
						{name:'2XL',id:'2-3'}
					]
				}*/
			],
			goodsDetial:{},
			carData:{},
			price:0,//价格
			imgbaseUrl:'',//图片地址
			dqggItem:{id:0},//选中的规格
		}
	},
	onShow() {
		
	},
	methods: {
		//立即兑换
		ljdhTap(){
			var that = this;
			if(this.dqggItem.id == 0){
				uni.showToast({
					icon: 'none',
					title: '请选择完整的规格',
					duration:2000
				});
				return false;
			}
			if(this.dqggItem.stock == 0){
				uni.showToast({
					icon: 'none',
					title: '当前规格暂无库存',
					duration:2000
				});
				return false;
			}
			if(this.selnum > this.dqggItem.stock){
				uni.showToast({
					icon: 'none',
					title: '暂无更多库存',
					duration:2000
				});
				return false;
			}
			var productxq = JSON.stringify({id:this.goodsDetial.id,name:this.goodsDetial.name,image:this.dqggItem.image,redeem_points:this.price,num:this.selnum,xuanztext:this.xuanzText,skuid:this.dqggItem.id})
			uni.navigateTo({
				url:'/pages/buy/pointsMall/confirmOrder?productxq=' + productxq,
				success: function(res) {
					that.gwcToggle = false;
				}
			})
		},
		//开启弹窗
		startTanc(goodsDetial){
			this.selnum = 1;
			this.dqggItem = {id:0}
			for(var i=0;i<goodsDetial.spec_list.length;i++){
				goodsDetial.spec_list[i].indexSel = -1;
			}
			this.imgbaseUrl = this.$baseUrl;
			this.gwcToggle = true;
			this.goodsDetial = goodsDetial;
			this.guige = goodsDetial.spec_list
			this.price = goodsDetial.redeem_points;
			/*
			this.price = goodsDetial.price;
			this.goodsDetial = goodsDetial;
			var arrnews = goodsDetial.param;
			for(var i = 0;i<arrnews.length;i++){
				arrnews[i].indexSel = -1
			}
			this.guige = arrnews;
			*/
			this.xuanzText = '请选择';
			console.log(goodsDetial);
		},
		selguigClick(index,erjIndex){
			console.log(index,erjIndex)
			if(this.guige[index].indexSel == erjIndex){
				this.guige[index].indexSel = -1
			}else{
				this.guige[index].indexSel = erjIndex
			}
			var arr = [];
			var newsArr = []
			for(var i=0;i<this.guige.length;i++){
				if(this.guige[i].indexSel != -1){
					arr.push(this.guige[i].value[this.guige[i].indexSel])
					newsArr.push(this.guige[i].name + ':' +this.guige[i].value[this.guige[i].indexSel])
				}
			}
			// console.log(arr,newsArr,'哈哈123');
			this.xuanzText = arr.join(',')
			
			
			if(newsArr.length == 0 || newsArr.length != this.guige.length){
				this.price = this.goodsDetial.redeem_points
				this.dqggItem = {id:0}
			}else{
				this.goodspriceApi(newsArr);//获取商品价格
			}
			
		},
		//获取商品价格
		goodspriceApi(newsArr){
			console.log(newsArr,'newsArr')
			var ggName = newsArr.join(';');
			var skuList = this.goodsDetial.spec_data.skuList;
			
			for(var i=0;i<skuList.length;i++){
				if(skuList[i].spec == ggName){
					this.dqggItem = skuList[i]
				}
			}
			console.log(this.dqggItem,'this.dqggItem')
		},
		//减商品
		jian(index){
			 if(this.selnum == 1){
				 return false;
			 }
			 this.selnum --;
		},
		//增加商品
		add(index){
			/*if(this.selnum >= this.dqggItem.stock){
				uni.showToast({
					icon: 'none',
					title: '暂无更多库存',
					duration:2000
				});
				return false;
			}*/
			this.selnum ++;
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.specification{overflow: hidden;}
page{padding-bottom: 0;}
</style>