@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.search-container.data-v-2813d7e8 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}
.search-bar.data-v-2813d7e8 {
  padding: 16rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e4e7ed;
}
.discovery-section.data-v-2813d7e8, .search-results.data-v-2813d7e8 {
  padding: 32rpx;
  flex: 1;
  overflow-y: auto;
}
.history-section.data-v-2813d7e8, .hot-searches-section.data-v-2813d7e8 {
  margin-bottom: 40rpx;
}
.section-header.data-v-2813d7e8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-title.data-v-2813d7e8 {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
}
.tags-container.data-v-2813d7e8 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.results-list.data-v-2813d7e8 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.post-card-item.data-v-2813d7e8 {
  width: 100%;
}

