/**
 * 社交功能API接口封装
 * 基于vote_baseUrl配置的后端服务
 */

import { http } from '@/config/http.api.js'

// 获取基础URL
const getBaseUrl = () => {
  return 'http://localhost:8102'
}

// 通用请求方法
const request = (url, options = {}) => {
  const baseUrl = getBaseUrl()
  const fullUrl = `${baseUrl}/api${url}`
  
  return new Promise((resolve, reject) => {
    uni.request({
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        // TODO: 添加认证token
        // 'Authorization': `Bearer ${getToken()}`
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 0) {
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message || '请求失败'))
          }
        } else {
          reject(new Error(`HTTP ${res.statusCode}`))
        }
      },
      fail: (err) => {
        console.error('API请求失败:', err)
        reject(err)
      }
    })
  })
}

// ==================== 帖子相关API ====================

/**
 * 获取帖子列表
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页码
 * @param {number} params.size - 每页大小
 * @param {string} params.sortField - 排序字段
 * @param {string} params.sortOrder - 排序方向
 * @param {string} params.searchText - 搜索关键词
 */
export const getPostList = (params = {}) => {
  const { size, ...otherParams } = params
  return request('/post/list', {
    method: 'POST',
    data: {
      current: 1,
      pageSize: size || 10,
      sortField: 'createTime',
      sortOrder: 'desc',
      ...otherParams
    }
  })
}

/**
 * 获取帖子详情
 * @param {number} id - 帖子ID
 */
export const getPostDetail = (id) => {
  return request(`/post/detail?postId=${id}`)
}

/**
 * 点赞帖子
 * @param {number} postId - 帖子ID
 */
export const likePost = (postId) => {
  return request(`/post/like?postId=${postId}`, {
    method: 'POST'
  })
}

/**
 * 取消点赞帖子
 * @param {number} postId - 帖子ID
 */
export const unlikePost = (postId) => {
  return request(`/post/unlike?postId=${postId}`, {
    method: 'POST'
  })
}

/**
 * 发布帖子
 * @param {Object} postData - 帖子数据
 */
export const createPost = (postData) => {
  return request('/post/add', {
    method: 'POST',
    data: postData
  })
}

// ==================== 搜索相关API ====================

/**
 * 综合搜索
 * @param {Object} params - 搜索参数
 */
export const comprehensiveSearch = (params) => {
  return request('/search/comprehensive', {
    method: 'GET',
    data: params
  })
}

/**
 * 获取热门搜索词
 * @param {number} limit - 限制数量
 */
export const getHotKeywords = (limit = 10) => {
  return request(`/search/hot-keywords?limit=${limit}`)
}

/**
 * 获取搜索历史
 * @param {number} limit - 限制数量
 */
export const getSearchHistory = (limit = 20) => {
  return request(`/search/history?limit=${limit}`)
}

/**
 * 获取搜索建议
 * @param {string} keyword - 关键词
 * @param {number} limit - 限制数量
 */
export const getSearchSuggestions = (keyword, limit = 10) => {
  return request(`/search/suggestions?keyword=${encodeURIComponent(keyword)}&limit=${limit}`)
}

// ==================== 话题相关API ====================

/**
 * 获取热门话题
 * @param {number} limit - 限制数量
 */
export const getHotTags = (limit = 20) => {
  return request(`/tag/hot?limit=${limit}`)
}

/**
 * 获取话题详情
 * @param {number} tagId - 话题ID
 */
export const getTagDetail = (tagId) => {
  return request(`/tag/detail/${tagId}`)
}

/**
 * 获取话题下的帖子
 * @param {number} tagId - 话题ID
 * @param {Object} params - 查询参数
 */
export const getTagPosts = (tagId, params = {}) => {
  const { current = 1, size = 10, sortBy = 'time' } = params
  return request(`/tag/${tagId}/posts?current=${current}&size=${size}&sortBy=${sortBy}`)
}

// ==================== 关注相关API ====================

/**
 * 关注用户
 * @param {number} userId - 用户ID
 */
export const followUser = (userId) => {
  return request(`/follow/user/${userId}`, {
    method: 'POST'
  })
}

/**
 * 取消关注用户
 * @param {number} userId - 用户ID
 */
export const unfollowUser = (userId) => {
  return request(`/follow/user/${userId}`, {
    method: 'DELETE'
  })
}

/**
 * 检查关注状态
 * @param {number} userId - 用户ID
 */
export const checkFollowStatus = (userId) => {
  return request(`/follow/status/${userId}`)
}

/**
 * 获取关注列表
 * @param {number} userId - 用户ID
 * @param {Object} params - 查询参数
 */
export const getFollowingList = (userId, params = {}) => {
  const { current = 1, size = 20 } = params
  return request(`/follow/following/${userId}?current=${current}&size=${size}`)
}

/**
 * 获取粉丝列表
 * @param {number} userId - 用户ID
 * @param {Object} params - 查询参数
 */
export const getFollowersList = (userId, params = {}) => {
  const { current = 1, size = 20 } = params
  return request(`/follow/followers/${userId}?current=${current}&size=${size}`)
}

/**
 * 批量检查关注状态
 * @param {Array} userIds - 用户ID列表
 */
export const batchCheckFollowStatus = (userIds) => {
  return request('/follow/batch-status', {
    method: 'POST',
    data: userIds
  })
}

// ==================== 消息通知相关API ====================

/**
 * 获取未读消息数
 */
export const getUnreadCount = () => {
  return request('/notifications/unread-count')
}

/**
 * 获取消息列表
 * @param {Object} params - 查询参数
 */
export const getNotificationList = (params = {}) => {
  const { type = 'all', current = 1, size = 20 } = params
  return request(`/notifications/list?type=${type}&current=${current}&size=${size}`)
}

/**
 * 标记消息已读
 * @param {Object} params - 参数
 */
export const markNotificationAsRead = (params) => {
  return request('/notifications/read', {
    method: 'PUT',
    data: params
  })
}

// ==================== 私信相关API ====================

/**
 * 发送消息
 * @param {Object} messageData - 消息数据
 */
export const sendMessage = (messageData) => {
  return request('/messages/send', {
    method: 'POST',
    data: messageData
  })
}

/**
 * 获取会话列表
 * @param {Object} params - 查询参数
 */
export const getConversations = (params = {}) => {
  const { current = 1, size = 20 } = params
  return request(`/messages/conversations?current=${current}&size=${size}`)
}

/**
 * 获取会话消息
 * @param {number} userId - 对方用户ID
 * @param {Object} params - 查询参数
 */
export const getConversationMessages = (userId, params = {}) => {
  const { current = 1, size = 20 } = params
  return request(`/messages/conversation/${userId}?current=${current}&size=${size}`)
}

/**
 * 标记消息已读
 * @param {Object} params - 参数
 */
export const markMessageAsRead = (params) => {
  return request('/messages/read', {
    method: 'PUT',
    data: params
  })
}

// ==================== 用户相关API ====================

/**
 * 获取用户资料
 * @param {number} userId - 用户ID
 */
export const getUserProfile = (userId) => {
  return request(`/user/profile/${userId}`)
}

/**
 * 更新用户资料
 * @param {Object} profileData - 用户资料数据
 */
export const updateUserProfile = (profileData) => {
  return request('/user/profile/update', {
    method: 'PUT',
    data: profileData
  })
}

// ==================== 测试相关API ====================

/**
 * 健康检查
 */
export const healthCheck = () => {
  return request('/social/test/health')
}

/**
 * 测试阶段三功能
 */
export const testStageThree = () => {
  return request('/social/test/stage-three')
}

// ==================== 评论相关API ====================

/**
 * 获取评论列表
 * @param {Object} params - 参数对象
 * @param {number} params.userId - 用户ID
 * @param {string} params.contentId - 内容ID
 * @param {string} params.filter - 排序方式 (hot/time)
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页数量
 */
export const getCommentList = (params = {}) => {
  const { userId, contentId, filter = 'hot', page = 1, size = 10 } = params
  return request(`/comments?userId=${userId}&contentId=${contentId}&filter=${filter}&page=${page}&size=${size}`)
}

/**
 * 发表评论
 * @param {Object} commentData - 评论数据
 */
export const createComment = (commentData) => {
  return request('/comments', {
    method: 'POST',
    data: commentData
  })
}

/**
 * 点赞评论
 * @param {number} commentId - 评论ID
 * @param {Object} params - 参数
 */
export const likeComment = (commentId, params = {}) => {
  return request(`/comments/${commentId}/like`, {
    method: 'POST',
    data: params
  })
}

/**
 * 回复评论
 * @param {number} commentId - 评论ID
 * @param {Object} replyData - 回复数据
 */
export const replyComment = (commentId, replyData) => {
  return request(`/comments/${commentId}/replies`, {
    method: 'POST',
    data: replyData
  })
}

export default {
  // 帖子相关
  getPostList,
  getPostDetail,
  likePost,
  unlikePost,
  createPost,
  
  // 搜索相关
  comprehensiveSearch,
  getHotKeywords,
  getSearchHistory,
  getSearchSuggestions,
  
  // 话题相关
  getHotTags,
  getTagDetail,
  getTagPosts,
  
  // 关注相关
  followUser,
  unfollowUser,
  checkFollowStatus,
  getFollowingList,
  getFollowersList,
  batchCheckFollowStatus,
  
  // 消息通知相关
  getUnreadCount,
  getNotificationList,
  markNotificationAsRead,
  
  // 私信相关
  sendMessage,
  getConversations,
  getConversationMessages,
  markMessageAsRead,
  
  // 用户相关
  getUserProfile,
  updateUserProfile,
  
  // 测试相关
  healthCheck,
  testStageThree
}
