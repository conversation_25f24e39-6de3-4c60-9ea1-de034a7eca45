<template>
	<view class="teacherDetails" :style="{ '--qjbutton-color': qjbutton }" v-if="loding">
		
		<u-navbar title="讲师详情" :back-icon-name="'arrow-left'" back-icon-color="#ffffff" :background="{ background: 'rgba(26,26, 26,' + navBg + ')' }"
			:border-bottom="false" :title-color="navBg==1?'#fff':'#fff' " title-size="32"></u-navbar>
		
		<view class="lsxq_ban" :style="'margin-top:-'+(safeAreaTop+20+menuButtonInfoHeight)+'px;'"><image :src="imgbaseUrl + teacherImage" mode="aspectFill"></image></view>
		
		
		<view class="lsxq_title"><text>{{teacherName}}</text></view>
		<view class="lsxq_two">
			<!-- <view class="cour_two">
				<view class="cour_two_li" v-for="(item,index) in 6" :key="index" @click="navTo('/pages/buy/coursePackage/myCoursexq?id=' + item.id)">
					<view class="cour_two_li_l"><image src="/static/images/icon23.jpg" mode="aspectFill"></image></view>
					<view class="cour_two_li_r">
						<view class="cour_two_li_r_a">拉丁舞线上课拉丁舞线上课拉丁舞线上</view>
						<view class="cour_two_li_r_b">基础/拉丁舞</view>
						<view class="cour_two_li_r_c">课程时长：60分钟</view>
						<view class="cour_two_li_r_d">讲师:LINDA</view>
						<view class="cour_two_li_r_e">
							<view class="cour_two_li_r_e_l">已售1521<text>¥999</text></view>
							<view class="cour_two_li_r_e_r">详情</view>
						</view>
					</view>
				</view>
			</view> -->
			<view class="cour_two">
				<view class="cour_two_li" v-for="(item,index) in coursePackageLists" :key="index" @click="navTo('/pages/buy/coursePackage/myCoursexq?id=' + item.id)">
					<view class="cour_two_li_l"><image :src="imgbaseUrl + item.image" mode="aspectFill"></image></view>
					<view class="cour_two_li_r">
						<view class="cour_two_li_r_a">{{item.name}}</view>
						<view class="cour_two_li_r_b">{{item.levelTable.name}}/{{item.danceTable.name}}</view>
						<view class="cour_two_li_r_c">课程时长：{{item.duration*1}}分钟</view>
						<view class="cour_two_li_r_d">讲师:{{teacherName}}</view>
						<view class="cour_two_li_r_e">
							<view class="cour_two_li_r_e_l">已售{{item.sales_volume*1}}<text>¥{{item.price*1}}</text></view>
							<view class="cour_two_li_r_e_r">详情</view>
						</view>
					</view>
				</view>
				<view class="gg_loding" v-if="!zanwsj">
					<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
						<view></view>
						<text>加载中</text>
					</view>
					<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
				</view>
				<view class="gg_zwsj" v-if="zanwsj">
					<view class="gg_zwsj_w">
						<image src="/static/images/wusj.png" mode="widthFix"></image>
						<text>暂无数据</text>
					</view>
				</view>
			</view>
		</view>
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	teacherDetailApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			loding:false,
			safeAreaTop:wx.getWindowInfo().safeArea.top,
			menuButtonInfoHeight:uni.getMenuButtonBoundingClientRect().height,
			isLogined:true,
			navBg: '',
			coursePackageLists:[],
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			isLogined:false,
			pageId:0,
			teacherImage:'',
			teacherName:'',
			imgbaseUrl:'',
			qjbutton:'#131315',
		}
	},
	onLoad(option) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.pageId = option.id;
	},
	onShow() {
		this.isLogined = uni.getStorageSync('token') ? true : false;
		this.imgbaseUrl = this.$baseUrl;
		this.page = 1;
		this.coursePackageLists = [];
		this.coursePackageData();//课包
	},
	onPageScroll(e) {
		const top = uni.upx2px(100)
		const {
			scrollTop
		} = e
		let percent = scrollTop / top > 1 ? 1 : scrollTop / top
		this.navBg = percent
	},
	methods: {
		//课包列表
		coursePackageData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			teacherDetailApi({
				page:that.page,
				size:10,
				id:that.pageId,
			}).then(res => {
				console.log('课包列表',res)
				if (res.code == 1) {
					// res.data.package.data = []
					that.teacherImage = res.data.image;
					that.teacherName = res.data.name;
					var obj = res.data.package.data;
					that.coursePackageLists = that.coursePackageLists.concat(obj);
					that.zanwsj = that.coursePackageLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.package.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.coursePackageLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottom() {
			// console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.coursePackageData();
				}
			}
		},
		onPullDownRefresh: function() {
		    // console.log('我被下拉了');
		    this.page = 1;
			this.coursePackageLists = [];
			this.coursePackageData();//课包列表
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.teacherDetails{overflow: hidden;}
page{padding-bottom: 0;}
</style>