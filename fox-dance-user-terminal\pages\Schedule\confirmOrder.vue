<template>
	<view class="confirmOrder"  :style="{ '--qjbutton-color': qjbutton,'--qjziti-color': qjziti }" v-if="courseDetail.id">
		 <!-- v-if="courseDetail.id" -->
		<template v-if="!yuekToggle">
			<view class="xsk_one">
				<view class="xsk_one_title">{{courseDetail.course.name}}</view>
				<view class="xsk_one_li">
					<view class="xsk_one_li_l">门店</view>
					<view class="xsk_one_li_r">{{courseDetail.store.name}}</view>
				</view>
				<view class="xsk_one_li">
					<view class="xsk_one_li_l">课程时长</view>
					<view class="xsk_one_li_r">{{courseDetail.duration}}分钟</view>
				</view>
				<view class="xsk_one_li">
					<view class="xsk_one_li_l">上课地址</view>
					<view class="xsk_one_li_r">{{courseDetail.store.address}}</view>
				</view>
				<view class="xsk_one_li">
					<view class="xsk_one_li_l">授课讲师</view>
					<view class="xsk_one_li_r">{{courseDetail.teacher.name}}</view>
				</view>
			</view>
			
			<view class="xsk_one">
				<view class="xsk_one_li">
					<view class="xsk_one_li_l">上课时间</view>
					<view class="xsk_one_li_r">{{courseDetail.start_time}}</view>
				</view>
				<view class="xsk_one_li">
					<view class="xsk_one_li_l">昵称</view>
					<view class="xsk_one_li_r">{{userInfo.nickname == '' ? '微信昵称' : userInfo.nickname}}</view>
				</view>
				<view class="xsk_one_li">
					<view class="xsk_one_li_l">手机号</view>
					<view class="xsk_one_li_r">{{userInfo.mobile}}</view>
				</view>
				<view class="xsk_one_li" @click="yhqTap">
					<view class="xsk_one_li_l">会员卡</view>
					<view class="xsk_one_li_r" style="color:#FF6D5C">{{yhkXzInfo.contract_name == '' ? '请选择会员卡' : yhkXzInfo.contract_name}}<image src="/static/images/right_more.png"></image></view>
				</view>
			</view>
			
			<view class="memk_six">
				<view class="memk_six_a"><image src="/static/images/icon29.png"></image>约课注意事项</view>
				<view class="memk_six_b">{{zysxText}}</view>
			</view>
			
			<!-- <view class="xsk_xy" @click="xyToggle = !xyToggle"><image :src="xyToggle ? '/static/images/xz-1.png' : '/static/images/xz.png'"></image>阅读并同意<text @click.stop="navTo('/pages/login/xieYi?type=3')">《用户授权协议》</text>和<text @click.stop="navTo('/pages/login/xieYi?type=4')">《平台服务协议》</text></view> -->
			
			<view class="ordzf_foo" @click="yukSubTap" style="margin-top: -100rpx;">提交约课</view>
		</template>
		
		<template v-else>
			<view class="yycgCon" v-if="ztType == 2">
				<image src="/static/images/icon54.png"></image>
				<view class="yycgCon_a">排队约课中</view>
				<view class="yycgCon_b">可在个人中心“<text @click="kecGoTap">我的课程</text>”中查看</view>
			</view>
			
			<view class="yycgCon" v-else>
				<image src="/static/images/icon54.png"></image>
				<view class="yycgCon_a">{{ztType == 1 ? '约课成功' : '预约成功'}}</view>
				<view class="yycgCon_b"><template v-if="ztType == 4">当前正在排队中，</template>可在个人中心“<text @click="kecGoTap">我的课程</text>”中查看</view>
			</view>
		</template>
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<!-- <view class="peodex_foo kc_foo">
			<view class="peodex_foo_l">应支付：<text>￥240.00</text></view>
			<view class="peodex_foo_r" @click="buyTap">购买</view>
		</view> -->
		
		
		
		
		
		
		
		
		
		
		<!-- 会员卡 go -->
		<view class="gg_rgba" v-if="yhqToggle" @click="yhqToggle = false"></view>
		<view class="thq_tanc" v-if="yhqToggle">
			<view class="thq_tanc_t"><text>会员卡</text><image src="/static/images/popup_close.png" @click="yhqToggle = false"></image></view>
			<view class="thq_tanc_b">
				<view class="mycards_thr_li" v-for="(item,index) in cardsLists" :key="index">
					<!-- https://file.foxdance.com.cn/storage/default/20250417/3IrxlLQQb7Wc9302dcd60cd7d0cef3b399122418a1926274929a44c.jpg -->
					<image :src="imgbaseUrl + item.image" class="mycards_thr_li_bj"></image>
					<view class="mycards_thr_li_zt">{{item.status == 0 ? '未激活' : item.status == 1 ? '使用中' : item.status == 2 ? '请假中' : item.status == 3 ? '已耗尽' : item.status == 4 ? '已过期' : item.status == 5 ? '已转让' : item.status == 6 ? '已退款' : item.status == 7 ? '已转卡' : ''}}</view>
					<view class="mycards_thr_li_c">
						<view class="mycards_thr_li_c_l"><image :src="imgbaseUrl + userInfo.avatar" mode="aspectFill"></image></view>
						<view class="mycards_thr_li_c_r">
							<view class="mycards_thr_li_c_r_a">{{item.contract_name}}</view>
							<view class="mycards_thr_li_c_r_b" v-if="item.type*1 == 0">剩余<text>{{item.surplus_frequency}}</text>次</view>
							<view class="mycards_thr_li_c_r_b" v-else>{{item.status > 0 ? item.become_time + '到期' : '未激活'}}</view>
						</view>
					</view>
					<view class="mycards_thr_li_c_r_f">
						<view class="mycards_thr_li_c_r_f_l">使用期限:{{item.status == 0 ? '未激活' : item.activation_time + ' - ' + item.become_time}}</view>
						<view class="mycards_thr_li_c_r_f_r" @click="syhykTap(item)">使用</view>
					</view>
				</view>
			</view>
			<view class="thq_tanc_b" v-if="false">
				
				<view class="mycards_two_li" v-for="(item,index) in cardsLists" :key="index">
					<view class="mycards_two_li_t" style="align-items: initial;"> 
						<image :src="imgbaseUrl + userInfo.avatar" mode="aspectFill" class="mycards_two_li_t_l"></image>
						<!-- 会员卡类型:0=次卡,1=年卡,2=月卡 -->
						<view class="mycards_two_li_t_r">
							<view v-if="item.member_card_number">会员ID:{{item.member_card_number}}</view>
							<text v-if="item.contract_name">{{item.contract_name}}</text>
							<text>{{item.type*1 == 0 ? '次卡' : '时长卡'}}：<template v-if="item.type*1 == 0">剩余{{item.surplus_frequency + '次　'}}</template><template>{{item.become_time + '日到期'}}</template></text>
						</view>
						
						<!-- <view v-if="item.contract_name">会员名称:{{item.contract_name}}</view>
						<view v-else>会员ID:{{item.out_trade_no}}</view>
						<text>{{item.type*1 == 0 ? '次卡' : '时长卡'}}：{{item.status > 0 ? item.become_time + '到期　' : '未激活　'}}　<template v-if="item.type*1 == 0">剩余{{item.surplus_frequency}}次</template></text>
						<text>到期时间:{{item.status == 0 ? '未激活' : item.activation_time + ' ~ ' + item.become_time}}</text> -->
					</view>
					<view class="mycards_two_li_t_b" @click="syhykTap(item)">使用</view>
				</view>
				
			</view>
		</view>
		<!-- 会员卡 end -->
		
		<!-- 连续课程弹窗go -->
		 <!-- v-if="storeCourseLists.length > 0 && yuekToggle" -->
		<view class="lxkcCon" v-if="lxykToggle" @click="lxykToggle = false">
			<view class="lxkcCon_n" @click.stop>
				<view class="lxkcCon_t">以下课程与当前课程时间相近，推荐继续预约</view>
				<view class="lxkcCon_c">
					
					<view class="teaCon_li" v-for="(item,index) in storeCourseLists" :key="index" @click="storesxqTap(item)">
						<view class="teaCon_li_a">{{item.course.name}}</view>
						<view class="teaCon_li_b">
							<image :src="imgbaseUrl + item.teacher.image" mode="aspectFill" class="teaCon_li_b_l"></image>
							<view class="teaCon_li_b_c">
								<view class="teaCon_li_b_c_a">{{item.start_time}}-{{item.end_time}}</view>
								<view class="teaCon_li_b_c_b">上课老师：{{item.teacher.name}}</view>
								<view class="teaCon_li_b_c_b" v-if="item.frequency*1 > 0">次卡消耗：{{item.frequency*1}}次</view>
								<view class="teaCon_li_b_c_c"><text v-if="item.level_name">{{item.level_name}}</text><text>{{item.dance_name}}</text></view>
							</view>
							
							
							<view class="teaCon_li_b_r" style="background:#BEBEBE" v-if="item.status == 1" @click.stop>待开课</view>
							<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 2" @click.stop>授课中</view>
							<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 3" @click.stop>已完成</view>
							<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 4" @click.stop>等位中</view>
							<!-- <view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 6" @click.stop>未开始预约</view> -->
							<view class="teaCon_li_b_r yysj" style="background:#BEBEBE" v-else-if="item.status == 6" @click.stop><text>{{item.start_reservation}}</text><text>开始预约</text></view>
							<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 7" @click.stop>截止预约</view>
							<!-- 未开启等位并且课程预约爆满 (课程预约满员时开启等位:0=关闭,1=开启)-->
							<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.equivalent*1 == 0 && (item.appointment_number*1 >= item.maximum_reservation*1)" @click.stop="kqhyts">预约</view>
							<view class="teaCon_li_b_r" :style="item.member == 0 ? 'background:#BEBEBE' : ''" v-else-if="item.member == 0" @click.stop="ljtkToggle = true">预约</view>
							<!-- 开启等位 -->
							<view class="teaCon_li_b_r" v-else @click.stop="yypdTo(item,'/pages/Schedule/Schedulexq?id' + item.id)">{{item.waiting_number*1 > 0 ? '去排队' : '预约'}}</view>
							
						</view>
						<view class="teaCon_li_c" v-if="item.appointment_number > 0">
							<view class="teaCon_li_c_l">
								<!-- /static/images/toux.png -->
								<image :src="imgbaseUrl + item.avatar" v-for="(item,index) in item.appointment_people" :key="index" mode="aspectFit"></image>
							</view>
							<view class="teaCon_li_c_r">已预约：<text>{{item.appointment_number}}</text>人;<template v-if="item.waiting_number*1 > 0"><text>{{item.waiting_number}}</text>人在等位</template></view>
						</view>
					</view>
					
				</view>
				<view class="lxkcCon_f" @click="lxykToggle = false">取消</view>
			</view>
		</view>
		<!-- 连续课程弹窗end -->
		
		<!-- 提示预约弹窗 go -->
		<view class="yytnCon" v-if="ljtkToggle"><view class="yytnCon_n"><image :src="imgbaseUrlOss + '/userreport/icon55.png'"></image><text @click="ljktTap"></text></view><image src="/static/images/icon56.png" @click="ljtkToggle = false"></image></view>
		<!-- 提示预约弹窗 end -->
		
		
	</view>
</template>


<script>
import {
	myCourseXqApi,
	myCourseyuyueApi,
	XieYi,
	userInfoApi,
	getCardApi,
	storeCourseApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			zysxText:'',
			xyToggle:false,
			yuekToggle:false,
			ztType:-1,//0 约课成功 1预约成功
			kcId:0,//课程id
			courseDetail:{id:0},
			userInfo:{
				nickname:'',
				mobile:'',
				avatar:''
			},
			qjbutton:'#131315',
			qjziti:'#F8F8FA',
			store_id:0,//门店id
			cardsLists:[],//可用会员卡
			yhkXzInfo:{contract_name: ""},
			yhqToggle:false,
			imgbaseUrl:'',
			imgbaseUrlOss:'',
			storeCourseLists:[],
			ljtkToggle:false,
			lxykToggle:false,//连续约课弹窗是否开启
		}
	},
	onLoad(option) {
		this.imgbaseUrlOss = this.$baseUrlOss;
		this.imgbaseUrl = this.$baseUrl;
		this.kcId = option.id;
		this.store_id = option.storeid ? option.storeid : 0;
		console.log(option,'option')
		this.courseData();//课程详情
		this.XieYiData();//约课注意事项
		this.userData();//个人信息
		this.getCardData();//获取某个门店会员卡
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.qjziti = uni.getStorageSync('storeInfo').written_words
	},
	onShow() {
		/*if(this.yuekToggle){
			this.storeCourseData();//门店课程
		}*/
		// this.storeCourseData();//门店课程
	},
	methods: {
		//详情跳转
		storesxqTap(item){
			console.log(this.isLogined,'this.isLogined')
			if(!this.isLogined){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
				return false;
			}
			// 未开启会员并且后端设置了必须开通会员方可查看详情
			if(item.course.view_type*1 == 0 && item.member == 0){
				this.ljtkToggle = true
			}else{
				uni.navigateTo({
					// url:'/pages/Schedule/Schedulexq?id=' + item.id
					url:'/pages/mine/myCourse/myCoursexq?id=' + item.id
				})
			}
		},
		//预约约课/排队
		yypdTo(item){
			if(!this.isLogined){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
				return false;
			}
			// 未开启会员
			if(item.member == 0){
				this.ljtkToggle = true
				return false;
			}
			uni.redirectTo({
				url:'/pages/Schedule/confirmOrder?id=' + item.id + '&storeid=' + this.store_id
			})
		},
		//预约爆满
		kqhyts(){
			uni.showToast({
				title: '预约课程已满',
				icon: 'none',
				duration: 1000
			})
		},
		//立即开通会员
		ljktTap(){
			this.ljtkToggle = false;
			uni.switchTab({
				url:'/pages/buy/buy'
			})
		},
		//门店课程
		storeCourseData(){
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			storeCourseApi({
				page:1,
				id:that.store_id,
				continuous_courses_id:that.kcId,
			}).then(res => {
				console.log('门店课程',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.storeCourseLists = res.data.data;
					if(that.storeCourseLists.length > 0 && that.yuekToggle){
						that.lxykToggle = true
					}else{
						that.lxykToggle = false
					}
				}
			})
		},
		syhykTap(item){
			console.log(item)
			this.yhkXzInfo = item;
			this.yhqToggle = false;
		},
		//会员卡点击出现弹窗
		yhqTap(){
			if(this.cardsLists.length == 0){
				uni.showToast({
					title: '暂无可用的会员卡',
					icon: 'none',
					duration: 1000
				})
				return false;
			}
			this.yhqToggle = true;
		},
		//获取某个门店会员卡
		getCardData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			getCardApi({store_id:that.store_id}).then(res => {
				console.log('获取某个门店会员卡',res)
				if (res.code == 1) {
					uni.hideLoading();
					/*res.data = [
						{id:0},
						{id:0},
						{id:0},
						{id:0},
						{id:0},
						{id:0},
						{id:0},
						{id:0},
						{id:0},
						{id:0},
					]*/
					// res.data[0].default = 1
					if(res.data.length > 0){
						if(res.data[0].default == 1){
							that.yhkXzInfo = res.data[0];
						}
					}
					that.cardsLists = res.data;
				}
			})
		},
		//个人信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userInfoApi({}).then(res => {
				console.log('个人中心',res)
				if (res.code == 1) {
					that.loding = true;
					that.userInfo = res.data;
					uni.hideLoading();
				}
			})
		},
		//约课注意事项
		XieYiData(){
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			XieYi({
				type:6,
			}).then(res => {
				console.log('约课注意事项',res)
				if (res.code == 1) {
					that.zysxText = res.data;
					uni.hideLoading();
				}
			})
		},
		//课程详情
		courseData(){
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			myCourseXqApi({
				id:that.kcId,
			}).then(res => {
				console.log('课程详情',res)
				if (res.code == 1) {
					uni.hideLoading();
					// res.data.reservation_type = 1;
					that.courseDetail = res.data;
				}
			})
		},
		//提交约课
		yukSubTap(){
			/*if(!this.xyToggle){
				uni.showToast({
					title: '请先阅读并同意《用户授权协议》和《平台服务协议》',
					icon: 'none',
					duration: 1000
				})
				return false;
			}*/
			if(this.yhkXzInfo.contract_name == ''){
				uni.showToast({
					title: '请选择会员卡',
					icon: 'none',
					duration: 1000
				})
				return false;
			}
			let that = this;
			if(this.courseDetail.reservation_type == 0){
				this.yukSubApiTap();//约课接口提交
			}else{
				uni.showModal({
					title: '提示',
					content: that.courseDetail.reservation_notes,
					success: function (res) {
						if (res.confirm) {
							that.yukSubApiTap();//约课接口提交
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			}
			
			
		},
		//约课接口提交
		yukSubApiTap(){
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			myCourseyuyueApi({
				id:that.kcId,
				card_id:that.yhkXzInfo.id
			}).then(res => {
				console.log('提交约课',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.yuekToggle = true
					that.storeCourseData();//门店课程
					uni.setNavigationBarTitle({
						title:'约课结果'
					});
					that.ztType = res.data;
					// 2 
					// 1约课成功  4 预约成功等位中
					// res.data == 1 ? that.ztType = 0 : that.ztType = 1;//0 约课成功 1预约成功
				}
			})
		},
		kecGoTap(){
			uni.redirectTo({
				url:'/pages/mine/myCourse/myCourse'
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.confirmOrder{overflow: hidden;}
page{padding-bottom: 0;}
</style>