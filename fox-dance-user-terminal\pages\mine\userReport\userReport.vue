<template>
	<view class="myCourse userReport" :style="{ '--qjbutton-color': qjbutton }">
		<view class="ord_nav">
			<view class="ord_nav_li" v-for="(item,index) in navLists" :key="index" :class="type == index ? 'ord_nav_li_ac' : ''" @click="navTap(index)"><view><text>{{item}}</text><text></text></view></view>
		</view>
		<!-- "status": 1, //状态:1=待开课,2=授课中,3=已完成,4=等位中,5=已取消 -->
		<view class="myCourse_con"> 
			<view class="myCourse_con_li" v-for="(item,index) in courseLists" :key="index" @click="userReportTap(item.type)">
				<!-- <view class="myCourse_con_li_zt">{{item.status == 1 ? '待开课' : item.status == 2 ? '授课中' : item.status == 3 ? '已完成' : item.status == 4 ? '等位中' : item.status == 5 ? '已取消' : ''}}</view>
				<view class="myCourse_con_li_a"><text v-if="item.level.name != '' && !item.level">{{item.level.name}}</text>{{item.name}}</view>
				<view class="myCourse_con_li_b"><image src="/static/images/icon17.png"></image>开课时间：{{item.course.start_time}}</view>
				<view class="myCourse_con_li_b" @click.stop="dhTap(item)"><image src="/static/images/icon18.png"></image>地点:{{item.course.store.address}}</view>
				<view class="myCourse_con_li_c">
					<view class="myCourse_con_li_c_l"><template v-if="item.course.teacher"><image :src="imgbaseUrl + item.course.teacher.image" mode="aspectFit"></image>老师:{{item.course.teacher.name}}</template></view>
					<view class="myCourse_con_li_c_r">详情</view>
				</view> -->
				<view class="userbg_t">
					<view class="userbg_t_l">{{item.type == 3 ? '周报' : item.type == 4 ? '月报' : item.type == 5 ? '年报' : ''}}</view>
					<view class="userbg_t_r">查看详情<image src="/static/images/introduce_more.png"></image></view>
				</view>
				<view class="userbg_b">{{item.date}}</view>
			</view>
		</view>
		<view class="gg_loding" v-if="!zanwsj">
			<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
				<view></view>
				<text>加载中</text>
			</view>
			<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
		</view>
		<view class="gg_zwsj" v-if="zanwsj">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无数据</text>
			</view>
		</view>
	</view>
</template>


<script>
import {
	reportListApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			navLists:['全部','周报','月报','年报'],
			type:0,
			
			courseLists:[],//报告列表
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			imgbaseUrl:'',
			qjbutton:'#131315',
		}
	},
	onLoad() {
		this.qjbutton = uni.getStorageSync('storeInfo').button
	},
	onShow() {
		this.imgbaseUrl = this.$baseUrl;
		this.page = 1;
		this.courseLists = [];
		this.courseData()//报告列表
	},
	methods: {
		navTap(index){
			this.type = index;
			this.page = 1;
			this.courseLists = [];
			this.courseData();
		},
		//报告列表
		courseData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			reportListApi({
				page:that.page,
				size:10,
				type:that.type,
			}).then(res => {
				console.log('报告列表',res)
				if (res.code == 1) {
					var obj = res.data.data;
					that.courseLists = that.courseLists.concat(obj);
					that.zanwsj = that.courseLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.courseLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottom() {
			//console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.courseData();
				}
			}
		},
		onPullDownRefresh: function() {
		    console.log('我被下拉了');
		    this.page = 1;
			this.courseLists = [];
			this.courseData();//报告列表
		},
		//导航
		dhTap(item){
			var that = this;
			uni.openLocation({
				name:item.course.store.address,
				latitude: item.course.store.latitude*1,
				longitude: item.course.store.longitude*1,
				success: function () {
					console.log('success');
				}
			});
		},
		//用户报告跳转
		userReportTap(type){
			uni.navigateTo({
				url:type == 3 ? '/pages/mine/userReport/weeksUserReport?id=' + item.id : type == 4 ? '/pages/mine/userReport/monthUserReport?id=' + item.id : type == 5 ? '/pages/mine/userReport/yearsUserReport?id=' + item.id : ''
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
	}
}
</script>

<style scoped lang="scss">
.myCourse{overflow:hidden;}
</style>