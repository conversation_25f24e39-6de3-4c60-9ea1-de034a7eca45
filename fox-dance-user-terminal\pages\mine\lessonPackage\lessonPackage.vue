<template>
	<view class="lessonPackage">
		
		<view class="les_search">
			<view class="les_search_l"><image src="/static/images/search.png"></image><input type="text" placeholder-style="color:#999999" placeholder="搜索课包名称" v-model="keywords" confirm-type="search" @confirm="searchTap" /></view>
			<view class="les_search_r" @click="searchTap">搜索</view>
		</view>
		
		<view class="les_con">
			<view class="les_con_li" v-for="(item,index) in coursePackageLists" :key="index" @click="navTo('/pages/mine/lessonPackage/lessonPackagexq?id=' + item.course_package_id)">
				<image :src="imgbaseUrl + item.package.image" mode="aspectFill" class="les_con_li_l"></image>
				<view class="les_con_li_r">
					<view class="les_con_li_r_a">{{item.package.name}}</view>
					<view class="les_con_li_r_b">课程时长：{{item.duration*1}}分钟</view>
					<view class="les_con_li_r_c">讲师:{{item.teacher_name}}</view>
				</view>
			</view>
		</view>
		<view class="gg_loding" v-if="!zanwsj">
			<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
				<view></view>
				<text>加载中</text>
			</view>
			<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
		</view>
		<view class="gg_zwsj" v-if="zanwsj">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无数据</text>
			</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>

<script>
import {
	myPackageApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:false,//是否登录
			isH5:false,//是否是h5
			type:0,
			date_sj: '请选择',
			
			keywords:'',
			keywords_cunc:'',
			imgbaseUrl:'',//图片地址
			coursePackageLists:[],
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			isLogined:false,
		}
	},
	created(){
		
	},
	onLoad(options) {
		this.imgbaseUrl = this.$baseUrl;
		this.page = 1;
		this.coursePackageLists = [];
		this.coursePackageData();//课包
	},
	methods: {
		//搜索
		searchTap(){
			this.keywords_cunc = this.keywords;
			this.page = 1;
			this.coursePackageLists = [];
			this.coursePackageData();//课包
		},
		//课包列表
		coursePackageData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			myPackageApi({
				page:that.page,
				size:10,
				name:that.keywords_cunc,
			}).then(res => {
				console.log('课包列表',res)
				if (res.code == 1) {
					var obj = res.data.data;
					that.coursePackageLists = that.coursePackageLists.concat(obj);
					that.zanwsj = that.coursePackageLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.coursePackageLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottom() {
			// console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.coursePackageData();
				}
			}
		},
		onPullDownRefresh: function() {
		    // console.log('我被下拉了');
		    this.page = 1;
			this.coursePackageLists = [];
			this.coursePackageData();//课包列表
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		},
		//个人信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userDetailApi().then(res => {
				if (res.code == 0) {
					console.log('个人信息',res);
					uni.hideLoading();
				}
			})
		},
	},
}
</script>

<style lang="less">
page{padding-bottom:0;}
.lessonPackage{
	overflow:hidden;
}
</style>
