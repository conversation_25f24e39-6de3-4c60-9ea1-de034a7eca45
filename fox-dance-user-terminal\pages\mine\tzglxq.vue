<template>
	<view class="tzglxq">
		
		<!-- <view class="tzxq_one">您当前已设置消息，接收通知0条，未能接收12条我们非常在意您的体验，不会肆意骚扰，请放心接受以下提醒</view> -->
		
		<view class="tzxq_two">
			<view class="tzxq_two_t"><text></text>以下通知未生效，点击可恢复接收:</view>
			<view class="tzxq_two_b">
				 <!-- @click="xzTap(index)" -->
				<view class="tzxq_two_b_li" v-for="(item,index) in lists" :key="index">
					{{item.name}}<switch :checked="item.select" @change="switch1Change(index,item)" color="#131315" style="transform:scale(0.7);position: relative;left:14rpx;" />
				</view>
			</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		
		
		
	</view>
</template>


<script>
import {
	detailSettingApi,
	setStatusApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			lists:[
				/*{name:'账号绑定通知',id:1,select:true},
				{name:'开课提醒',id:2,select:false},
				{name:'会员卡过期提醒',id:3,select:false},
				{name:'报名结果通知',id:4,select:false},
				{name:'会员卡状态提醒',id:5,select:false},
				{name:'会员预约课程提醒',id:6,select:false},
				{name:'服务人员变更提醒',id:7,select:false},
				{name:'签到成功通知',id:8,select:false}*/
			],
			xzIndex:-1,
			
		}
	},
	onShow() {
		
	},
	onLoad() {
		this.detailSettingData();//详细设置
	},
	methods: {
		//详细设置
		detailSettingData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			detailSettingApi({}).then(res => {
				console.log('详细设置',res);
				uni.hideLoading();
				if (res.code == 1) {
					for(var i=0;i<res.data.length;i++){
						res.data[i].select = res.data[i].status == 0 ? false : true
					}
					that.lists = res.data;
				}
			})
		},
		xzTap(index){
			console.log(index,'index');
			this.xzIndex = index;
		},
		switch1Change(e,item){
			/*console.log('itemm',item)
			this.lists[e].select = !this.lists[e].select;
			console.log(this.lists,'this.lists')*/
			
			
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			setStatusApi({type:item.type}).then(res => {
				console.log('设置通知状态',res);
				if (res.code == 1) {
					uni.hideLoading();
					that.lists[e].select = !that.lists[e].select
				}
			})
			
		},
		
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.tzglxq{overflow: hidden;}
page{padding-bottom: 0;}
</style>