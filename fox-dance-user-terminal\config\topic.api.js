import $topicHttp from './topic.http'

export default {
  /**
   * 分页获取话题列表
   * @param {Object} params - 请求参数
   * @returns {Promise}
   */
  getTopicList(params) {
    return $topicHttp({
      url: '/api/topic/list/page',
      method: 'POST',
      data: params
    })
  },

  /**
   * 获取话题详情
   * @param {Number} id - 话题ID
   * @param {Number} userId - 用户ID(可选)
   * @returns {Promise}
   */
  getTopicDetail(id, userId) {
    return $topicHttp({
      url: `/api/topic/get`,
      method: 'GET',
      params: { id, userId }
    })
  },

  /**
   * 根据ID获取话题信息（包含图片）
   * @param {Number} id - 话题ID
   * @param {Number} userId - 用户ID(可选)
   * @returns {Promise}
   */
  getTopicById(id, userId) {
    return $topicHttp({
      url: `/api/topic/get`,
      method: 'GET',
      params: { id, userId }
    })
  },

  /**
   * 创建话题
   * @param {Object} data - 请求数据
   * @returns {Promise}
   */
  addTopic(data) {
    return $topicHttp({
      url: '/api/topic/add',
      method: 'POST',
      data
    })
  },

  /**
   * 更新话题
   * @param {Object} data - 请求数据
   * @returns {Promise}
   */
  updateTopic(data) {
    return $topicHttp({
      url: '/api/topic/update',
      method: 'POST',
      data
    })
  },

  /**
   * 删除话题
   * @param {Object} data - 请求数据
   * @returns {Promise}
   */
  deleteTopic(data) {
    return $topicHttp({
      url: '/api/topic/delete',
      method: 'POST',
      data
    })
  },

  /**
   * 获取我创建的话题列表
   * @param {Object} params - 请求参数
   * @returns {Promise}
   */
  getMyTopicList(params) {
    return $topicHttp({
      url: '/api/topic/my/list/page',
      method: 'POST',
      data: params
    })
  },

  /**
   * 更新话题评论人数
   * @param {Number} id - 话题ID
   * @param {Number} userId - 用户ID
   * @returns {Promise}
   */
  updateCommentCount(id, userId) {
    return $topicHttp({
      url: '/api/topic/update/comment/count',
      method: 'POST',
      data: { id, userId }
    })
  },

  /**
   * 获取话题评论列表（支持分页）
   * @param {Number} topicId - 话题ID
   * @param {Number} userId - 用户ID
   * @param {String} filter - 排序方式
   * @param {Number} current - 页码
   * @param {Number} pageSize - 每页大小
   * @returns {Promise}
   */
  getTopicComments(topicId, userId, filter = 'hot', current = 1, pageSize = 10) {
    return $topicHttp({
      url: `/api/comments/topic/${topicId}`,
      method: 'GET',
      params: {
        userId: userId,
        filter: filter,
        current: current,
        pageSize: pageSize
      }
    })
  },

  /**
   * 获取话题评论统计信息
   * @param {Number} topicId - 话题ID
   * @param {Number} userId - 用户ID
   * @returns {Promise}
   */
  getTopicCommentStats(topicId, userId) {
    return $topicHttp({
      url: `/api/comments/topic/${topicId}/stats`,
      method: 'GET',
      params: {
        userId: userId
      }
    })
  },

  /**
   * 上传图片文件
   * @param {String} filePath - 文件路径
   * @param {String} name - 文件参数名（默认'file'）
   * @param {Object} formData - 额外的表单数据
   * @returns {Promise}
   */
  uploadImage(filePath, name = 'file', formData = {}) {
    console.log('🔥 话题图片上传API调用:', { filePath, name, formData });

    return new Promise((resolve, reject) => {
      // 获取用户token
      const token = uni.getStorageSync('bausertoken');

      uni.uploadFile({
        url: 'https://vote.foxdance.com.cn/api/api/file/upload',
        filePath: filePath,
        name: name,
        formData: {
          driver: 'cos',
          ...formData
        },
        header: {
          'bausertoken': token
        },
        success: (res) => {
          console.log('🔥 图片上传成功响应:', res);
          try {
            const responseData = JSON.parse(res.data);
            if (responseData.code === 0) {
              resolve({
                code: 1, // 统一返回格式
                data: responseData.data,
                message: 'success'
              });
            } else {
              reject({
                code: 0,
                message: responseData.message || '上传失败'
              });
            }
          } catch (error) {
            console.error('🔥 解析响应数据失败:', error, res.data);
            reject({
              code: 0,
              message: '响应数据格式错误'
            });
          }
        },
        fail: (error) => {
          console.error('🔥 图片上传失败:', error);
          reject({
            code: 0,
            message: '网络请求失败: ' + (error.errMsg || '未知错误')
          });
        }
      });
    });
  }
}