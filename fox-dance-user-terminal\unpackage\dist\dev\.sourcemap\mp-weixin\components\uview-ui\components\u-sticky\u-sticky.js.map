{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-sticky/u-sticky.vue?73df", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-sticky/u-sticky.vue?0f86", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-sticky/u-sticky.vue?92bb", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-sticky/u-sticky.vue?3d53", "uni-app:///components/uview-ui/components/u-sticky/u-sticky.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-sticky/u-sticky.vue?7dd0", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-sticky/u-sticky.vue?5f0e"], "names": ["name", "props", "offsetTop", "type", "default", "index", "enable", "h5NavHeight", "bgColor", "zIndex", "data", "fixed", "height", "stickyTop", "elClass", "left", "width", "watch", "computed", "uZIndex", "mounted", "methods", "initObserver", "<PERSON><PERSON><PERSON><PERSON>", "thresholds", "contentObserver", "top", "setFixed", "disconnectObserver", "observer", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgM;AAChM,gBAAgB,8LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsvB,CAAgB,usBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoB1wB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,eAcA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAf;MACA;IACA;IACAI;MACA;QACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAY;IACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MAKA;MAGA;MACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;MACA;MACAC;QACAC;MACA;MACAD;QACA;QACA;MACA;MACA;IACA;IACAE;MACA;MACA,gDACA;MACA;IACA;IACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnJA;AAAA;AAAA;AAAA;AAA66C,CAAgB,kxCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-sticky/u-sticky.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-sticky.vue?vue&type=template&id=1bced8d3&scoped=true&\"\nvar renderjs\nimport script from \"./u-sticky.vue?vue&type=script&lang=js&\"\nexport * from \"./u-sticky.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-sticky.vue?vue&type=style&index=0&id=1bced8d3&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1bced8d3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-sticky/u-sticky.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-sticky.vue?vue&type=template&id=1bced8d3&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-sticky.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-sticky.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<view class=\"u-sticky-wrap\" :class=\"[elClass]\" :style=\"{\r\n\t\t\theight: fixed ? height + 'px' : 'auto',\r\n\t\t\tbackgroundColor: bgColor\r\n\t\t}\">\r\n\t\t\t<view class=\"u-sticky\" :style=\"{\r\n\t\t\t\tposition: fixed ? 'fixed' : 'static',\r\n\t\t\t\ttop: stickyTop + 'px',\r\n\t\t\t\tleft: left + 'px',\r\n\t\t\t\twidth: width == 'auto' ? 'auto' : width + 'px',\r\n\t\t\t\tzIndex: uZIndex\r\n\t\t\t}\">\r\n\t\t\t\t<slot></slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * sticky 吸顶\r\n\t * @description 该组件与CSS中position: sticky属性实现的效果一致，当组件达到预设的到顶部距离时， 就会固定在指定位置，组件位置大于预设的顶部距离时，会重新按照正常的布局排列。\r\n\t * @tutorial https://www.uviewui.com/components/sticky.html\r\n\t * @property {String Number} offset-top 吸顶时与顶部的距离，单位rpx（默认0）\r\n\t * @property {String Number} index 自定义标识，用于区分是哪一个组件\r\n\t * @property {Boolean} enable 是否开启吸顶功能（默认true）\r\n\t * @property {String} bg-color 组件背景颜色（默认#ffffff）\r\n\t * @property {String Number} z-index 吸顶时的z-index值（默认970）\r\n\t * @property {String Number} h5-nav-height 导航栏高度，自定义导航栏时(无导航栏时需设置为0)，需要传入此值，单位px（默认44）\r\n\t * @event {Function} fixed 组件吸顶时触发\r\n\t * @event {Function} unfixed 组件取消吸顶时触发\r\n\t * @example <u-sticky offset-top=\"200\"><view>塞下秋来风景异，衡阳雁去无留意</view></u-sticky>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-sticky\",\r\n\t\tprops: {\r\n\t\t\t// 吸顶容器到顶部某个距离的时候，进行吸顶，在H5平台，NavigationBar为44px\r\n\t\t\toffsetTop: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t//列表中的索引值\r\n\t\t\tindex: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 是否开启吸顶功能\r\n\t\t\tenable: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// h5顶部导航栏的高度\r\n\t\t\th5NavHeight: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 44\r\n\t\t\t},\r\n\t\t\t// 吸顶区域的背景颜色\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#ffffff'\r\n\t\t\t},\r\n\t\t\t// z-index值\r\n\t\t\tzIndex: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tfixed: false,\r\n\t\t\t\theight: 'auto',\r\n\t\t\t\tstickyTop: 0,\r\n\t\t\t\telClass: this.$u.guid(),\r\n\t\t\t\tleft: 0,\r\n\t\t\t\twidth: 'auto',\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\toffsetTop(val) {\r\n\t\t\t\tthis.initObserver();\r\n\t\t\t},\r\n\t\t\tenable(val) {\r\n\t\t\t\tif (val == false) {\r\n\t\t\t\t\tthis.fixed = false;\r\n\t\t\t\t\tthis.disconnectObserver('contentObserver');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.initObserver();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tuZIndex() {\r\n\t\t\t\treturn this.zIndex ? this.zIndex : this.$u.zIndex.sticky;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.initObserver();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinitObserver() {\r\n\t\t\t\tif (!this.enable) return;\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tthis.stickyTop = this.offsetTop != 0 ? uni.upx2px(this.offsetTop) + this.h5NavHeight : this.h5NavHeight;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tthis.stickyTop = this.offsetTop != 0 ? uni.upx2px(this.offsetTop) : 0;\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\tthis.disconnectObserver('contentObserver');\r\n\t\t\t\tthis.$uGetRect('.' + this.elClass).then((res) => {\r\n\t\t\t\t\tthis.height = res.height;\r\n\t\t\t\t\tthis.left = res.left;\r\n\t\t\t\t\tthis.width = res.width;\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.observeContent();\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tobserveContent() {\r\n\t\t\t\tthis.disconnectObserver('contentObserver');\r\n\t\t\t\tconst contentObserver = this.createIntersectionObserver({\r\n\t\t\t\t\tthresholds: [0.95, 0.98, 1]\r\n\t\t\t\t});\r\n\t\t\t\tcontentObserver.relativeToViewport({\r\n\t\t\t\t\ttop: -this.stickyTop\r\n\t\t\t\t});\r\n\t\t\t\tcontentObserver.observe('.' + this.elClass, res => {\r\n\t\t\t\t\tif (!this.enable) return;\r\n\t\t\t\t\tthis.setFixed(res.boundingClientRect.top);\r\n\t\t\t\t});\r\n\t\t\t\tthis.contentObserver = contentObserver;\r\n\t\t\t},\r\n\t\t\tsetFixed(top) {\r\n\t\t\t\tconst fixed = top < this.stickyTop;\r\n\t\t\t\tif (fixed) this.$emit('fixed', this.index);\r\n\t\t\t\telse if(this.fixed) this.$emit('unfixed', this.index);\r\n\t\t\t\tthis.fixed = fixed;\r\n\t\t\t},\r\n\t\t\tdisconnectObserver(observerName) {\r\n\t\t\t\tconst observer = this[observerName];\r\n\t\t\t\tobserver && observer.disconnect();\r\n\t\t\t},\r\n\t\t},\r\n\t\tbeforeDestroy() {\r\n\t\t\tthis.disconnectObserver('contentObserver');\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\t\r\n\t.u-sticky {\r\n\t\tz-index: 9999999999;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-sticky.vue?vue&type=style&index=0&id=1bced8d3&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-sticky.vue?vue&type=style&index=0&id=1bced8d3&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752818687335\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}