{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-mask/u-mask.vue?a524", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-mask/u-mask.vue?fbdb", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-mask/u-mask.vue?96f4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-mask/u-mask.vue?c1fb", "uni-app:///components/uview-ui/components/u-mask/u-mask.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-mask/u-mask.vue?71ca", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-mask/u-mask.vue?54c2"], "names": ["name", "props", "show", "type", "default", "zIndex", "customStyle", "zoom", "duration", "maskClickAble", "data", "zoomStyle", "transform", "scale", "watch", "computed", "maskStyle", "style", "methods", "click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACgM;AAChM,gBAAgB,8LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAovB,CAAgB,qsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUxwB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,gBAaA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAZ;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAa;IACAC;MACA;MACAC;MACA,mFACAA;MACAA;MACA;MACA,kFACAA,QACA,iBACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAA26C,CAAgB,gxCAAG,EAAC,C;;;;;;;;;;;ACA/7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-mask/u-mask.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-mask.vue?vue&type=template&id=05e79e1a&scoped=true&\"\nvar renderjs\nimport script from \"./u-mask.vue?vue&type=script&lang=js&\"\nexport * from \"./u-mask.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-mask.vue?vue&type=style&index=0&id=05e79e1a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"05e79e1a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-mask/u-mask.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-mask.vue?vue&type=template&id=05e79e1a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.maskStyle, _vm.zoomStyle])\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-mask.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-mask.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-mask\" hover-stop-propagation :style=\"[maskStyle, zoomStyle]\" @tap=\"click\" @touchmove.stop.prevent=\"() => {}\" :class=\"{\r\n\t\t'u-mask-zoom': zoom,\r\n\t\t'u-mask-show': show\r\n\t}\">\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * mask 遮罩\r\n\t * @description 创建一个遮罩层，用于强调特定的页面元素，并阻止用户对遮罩下层的内容进行操作，一般用于弹窗场景\r\n\t * @tutorial https://www.uviewui.com/components/mask.html\r\n\t * @property {Boolean} show 是否显示遮罩（默认false）\r\n\t * @property {String Number} z-index z-index 层级（默认1070）\r\n\t * @property {Object} custom-style 自定义样式对象，见上方说明\r\n\t * @property {String Number} duration 动画时长，单位毫秒（默认300）\r\n\t * @property {Boolean} zoom 是否使用scale对遮罩进行缩放（默认true）\r\n\t * @property {Boolean} mask-click-able 遮罩是否可点击，为false时点击不会发送click事件（默认true）\r\n\t * @event {Function} click mask-click-able为true时，点击遮罩发送此事件\r\n\t * @example <u-mask :show=\"show\" @click=\"show = false\"></u-mask>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-mask\",\r\n\t\tprops: {\r\n\t\t\t// 是否显示遮罩\r\n\t\t\tshow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 层级z-index\r\n\t\t\tzIndex: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 用户自定义样式\r\n\t\t\tcustomStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 遮罩的动画样式， 是否使用使用zoom进行scale进行缩放\r\n\t\t\tzoom: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 遮罩的过渡时间，单位为ms\r\n\t\t\tduration: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 300\r\n\t\t\t},\r\n\t\t\t// 是否可以通过点击遮罩进行关闭\r\n\t\t\tmaskClickAble: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tzoomStyle: {\r\n\t\t\t\t\ttransform: ''\r\n\t\t\t\t},\r\n\t\t\t\tscale: 'scale(1.2, 1.2)'\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tshow(n) {\r\n\t\t\t\tif(n && this.zoom) {\r\n\t\t\t\t\t// 当展示遮罩的时候，设置scale为1，达到缩小(原来为1.2)的效果\r\n\t\t\t\t\tthis.zoomStyle.transform = 'scale(1, 1)';\r\n\t\t\t\t} else if(!n && this.zoom) {\r\n\t\t\t\t\t// 当隐藏遮罩的时候，设置scale为1.2，达到放大(因为显示遮罩时已重置为1)的效果\r\n\t\t\t\t\tthis.zoomStyle.transform = this.scale;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tmaskStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\tstyle.backgroundColor = \"rgba(0, 0, 0, 0.6)\";\r\n\t\t\t\tif(this.show) style.zIndex = this.zIndex ? this.zIndex : this.$u.zIndex.mask;\r\n\t\t\t\telse style.zIndex = -1;\r\n\t\t\t\tstyle.transition = `all ${this.duration / 1000}s ease-in-out`;\r\n\t\t\t\t// 判断用户传递的对象是否为空，不为空就进行合并\r\n\t\t\t\tif (Object.keys(this.customStyle).length) style = { \r\n\t\t\t\t\t...style,\r\n\t\t\t\t\t...this.customStyle\r\n\t\t\t\t};\r\n\t\t\t\treturn style;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tclick() {\r\n\t\t\t\tif (!this.maskClickAble) return;\r\n\t\t\t\tthis.$emit('click');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\t\r\n\t.u-mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\topacity: 0;\r\n\t\ttransition: transform 0.3s;\r\n\t}\r\n\r\n\t.u-mask-show {\r\n\t\topacity: 1;\r\n\t}\r\n\t\r\n\t.u-mask-zoom {\r\n\t\ttransform: scale(1.2, 1.2);\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-mask.vue?vue&type=style&index=0&id=05e79e1a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-mask.vue?vue&type=style&index=0&id=05e79e1a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752818687504\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}