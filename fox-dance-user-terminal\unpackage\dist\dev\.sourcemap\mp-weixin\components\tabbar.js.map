{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?3acf", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?d56d", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?ba86", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?8a87", "uni-app:///components/tabbar.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?c2fb", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?8160"], "names": ["name", "components", "PreviewCard", "props", "current", "type", "default", "data", "list", "iconPath", "selected<PERSON><PERSON><PERSON><PERSON>", "icon", "text", "count", "isDot", "customIcon", "pagePath", "bottomHeight2", "bottomHeight", "ecology", "noIndex", "showBox", "showFixed", "selColor", "currentPage", "showBoundaryHint", "totalCards", "products", "id", "tag", "image", "targetPage", "pageScrollLocked", "isTabBarHidden", "isTabBarReturning", "scrollLeft", "<PERSON><PERSON><PERSON><PERSON>", "windowWidth", "touchStartX", "touchStartY", "touchStartTime", "touchMoved", "isSwiping", "swipe<PERSON><PERSON><PERSON><PERSON>", "cardTouchTimer", "touchDirection", "directionLocked", "minSwipeDistance", "directionThreshold", "verticalAngleThreshold", "horizontalAngleThreshold", "minVerticalDistance", "rightCardOffsetX", "leftCardOffsetX", "rightCardScale", "leftCardScale", "rightCardOpacity", "leftCardOpacity", "isCardPulling", "pullDistance", "isClosing", "isCardExiting", "cardTipText", "computed", "cardContainerStyle", "transform", "transition", "created", "uni", "success", "that", "e", "scrollTop", "duration", "mounted", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "methods", "onSwiperChange", "setColor", "console", "tabbarChange", "title", "wx", "style", "overflow", "url", "preventScroll", "getApp", "preventTouchMove", "handleCardPulling", "onCardTouchStart", "x", "y", "time", "onCardTouchMove", "onCardTouchEnd", "velocityX", "velocityY", "Math", "onHomeClick", "handleDotTap", "handleContainerClick", "preventCardClick", "swiperBackgroundClick", "closePreview", "getBaseUrl", "getCardTip", "method", "fail"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACuL;AACvL,gBAAgB,8LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAusB,CAAgB,qsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC4F3tB;EACAA;EACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAE;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;MACAC;MAAA;;MAEA;MACAC;MACA;MACA;QAAAC;QAAA5B;QAAA6B;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAA5B;QAAA6B;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAA5B;QAAA6B;QAAAC;QAAAC;MAAA,EACA;MAEA;MACAC;MACA;MACAC;MACA;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAEA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MAEA;QACAC;QACAC;MACA;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACAC;QACA;UACAC;UACAA;QACA;QACA;QACAA;QACAA;MACA;IACA;;IAEA;IACAF;MACA;QACA;QACAG;QACAA;QACA;QACAH;UACAI;UACAC;QACA;QACA;MACA;IACA;;IAEA;IACA;MACA;MACA;MACA;IACA;EACA;EACAC;IAAA;IACA;IACA;MACAC;QACA;UACA;QACA;MACA;IACA;;IAEA;IACA;MACA;MACA;MACA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAR;EACA;EACAS;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;QACAC;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;UACA;UACA;UACAb;YACAc;YACAvE;YACA8D;UACA;UACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACAU;YACAC;cACAC;YACA;UACA;QACA;UACA;QAAA;;QAGA;QACAV;UACA;QACA;;QAEA;QACA;QAEA;MACA;QACAP;UACAkB;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACAC;MACAA;;MAEA;MACA;QACAL;UACAC;YACAC;UACA;QACA;MACA;QACA;MAAA;IAEA;IAEA;IACAI;MACA;MACA;QACA;QACA;UACA;QACA;MACA;MAEA;QACAlB;QACAA;QACA;MACA;IACA;IAEA;IACAmB;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAxB;QACAA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MACA;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MAEA;QACA;QACA;;QAEA;QACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;YACA;YACA;YACA;UACA;QACA;QAEA;MACA;;MAEA;MACA;QACA;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;MAEA;;MAEA;MACA;QACAqB;QACAC;QACAC;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAE;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MAEA;QACA;QACA;QACA;QAEA;UACAC;UACAC;QACA;MACA;;MAEA;MACA,0DACAC;MAEA;QACA;QACA;QACA;QAEA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACApB;;MAEA;MACA;;MAEA;MACAL;QACA;QACA;;QAEA;QACA;UACAQ;YACAC;cACAC;YACA;UACA;QACA;UACA;QAAA;;QAGA;QACA;QACA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;QACA;;QAEA;QACAV;UACA;QACA;MACA;IACA;IAEA;IACA0B;MACA;IACA;IAEA;IACAC;MACAtB;MACA;IACA;IAEA;IACAuB;MACAvB;MACA;MACA;IACA;IAEA;IACAwB;MACAxB;MACAZ;QACAc;QACAvE;MACA;MACA;IACA;IAEA;IACA8F;MACA;IACA;IAEA;IACAC;MAEA;;MAGA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAvC;QACAkB;QACAsB;QACAvC;UACAW;UACA;YACA;YACA;UACA;QACA;QACA6B;UACA7B;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtrBA;AAAA;AAAA;AAAA;AAAk0C,CAAgB,wvCAAG,EAAC,C;;;;;;;;;;;ACAt1C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tabbar.vue?vue&type=template&id=852a8b4e&\"\nvar renderjs\nimport script from \"./tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tabbar.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/tabbar.vue\"\nexport default component.exports", "export * from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabbar.vue?vue&type=template&id=852a8b4e&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.showBox\n    ? _vm.__map(_vm.products, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = Math.abs(_vm.currentPage - index)\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabbar.vue?vue&type=script&lang=js&\"", "<template>\n  <view>\n    <!-- TabBar 仅在卡片不显示时渲染 -->\n    <view class=\"tab-bar\" :class=\"{\n\t\t\t'tab-bar-exit': showBox && !isTabBarReturning,\n\t\t\t'tab-bar-enter': isTabBarReturning,\n\t\t\t'tab-bar-icon-visible': showBox\n\t\t}\">\n      <view class=\"tab_bgi \" :class=\"noIndex == 2 ? 'tab_bgi2' : ''\">\n        <!-- <image src=\"/static/images/<EMAIL>\" mode=\"scaleToFill\"></image> -->\n      </view>\n      <view v-for=\"(item, index) in list\" :key=\"index\" class=\"tab-bar-item\"\n            :class=\"{ 'tab-fixed-visible': index === 2 && showBox }\" @click=\"tabbarChange(item, index)\">\n        <view class=\"\" v-if=\"index != 2\">\n          <!-- <image class=\"tab_img\" :src=\"current == index ? item.selectedIconPath : item.iconPath\"\n            mode=\"aspectFit\">\n          </image> -->\n          <u-icon :name=\"current == index ? item.icon + '-fill' : item.icon\" :color=\"selColor\"\n                  size=\"40\"></u-icon>\n          <view class=\"tab_text\" :style=\"{ color: current == index ? '#131315' : '#131315' }\">\n            {{ item.text }}\n          </view>\n        </view>\n        <view class=\"\" v-if=\"index == 2\">\n          <image class=\"tab_img\" :src=\"current == index ? item.selectedIconPath : item.iconPath\"\n                 mode=\"aspectFit\" style=\"width: 104rpx;height: 104rpx;margin-top: -110rpx;\">\n          </image>\n        </view>\n      </view>\n    </view>\n\n    <!-- 全屏点击区域（为了关闭预览） -->\n    <view v-if=\"showBox\" class=\"preview-mask\" @tap=\"closePreview\">\n      <!-- 全屏底层白色背景 -->\n      <view class=\"fullscreen-background\" :class=\"{ 'background-exit': isCardExiting }\">\n        <!-- 顶部 -->\n        <view class=\"header-container\">\n          <view class=\"header-left\">\n            <image src=\"/static/icon/home.png\" mode=\"aspectFit\" class=\"header-image home-icon\"></image>\n          </view>\n          <image src=\"/static/tabbar/tab_fox1.png\" mode=\"aspectFit\" class=\"header-image\"></image>\n          <view class=\"header-right\">\n            <image src=\"/static/icon/个人中心.png\" mode=\"aspectFit\" class=\"header-image\"></image>\n          </view>\n        </view>\n        <!-- 底部 -->\n        <view class=\"bottom-hint\">\n          <view class=\"fox-logo-container\">\n            <view class=\"fox-line\"></view>\n            <text class=\"fox-text\">FOX DANCE STUDIO</text>\n            <view class=\"fox-line\"></view>\n          </view>\n          <text class=\"fox-subtext\">{{ cardTipText }}</text>\n        </view>\n      </view>\n\n      <!-- 卡片区域（点击时停止冒泡，防止关闭） -->\n      <view class=\"card-container-with-buttons\" :class=\"{ 'show': showBox && !isCardExiting, 'exit': isCardExiting }\"\n            @touchmove.stop=\"onCardTouchMove\" @touchstart.stop=\"onCardTouchStart\" @touchend.stop=\"onCardTouchEnd\">\n        \n        <!-- 轮播图区域 -->\n        <swiper class=\"card-swiper\" :current=\"currentPage\" @change=\"onSwiperChange\" :previous-margin=\"'30rpx'\"\n                :next-margin=\"'30rpx'\" :duration=\"300\" :circular=\"true\" :skip-hidden-item-layout=\"false\"\n                :indicator-dots=\"false\" :class=\"{ 'card-pulling': isCardPulling }\">\n          <swiper-item v-for=\"(item, index) in products\" :key=\"index\" class=\"card-swiper-item\">\n            <!-- 卡片（点击时阻止冒泡） -->\n            <view class=\"card-preview-container card-item\" @tap.stop\n                  :class=\"{ 'active-card': currentPage === index, 'near-active': Math.abs(currentPage - index) === 1 }\"\n                  :data-index=\"index\">\n              <preview-card :title=\"item.name\" :tag=\"item.tag\" :image=\"item.image\"\n                          :targetPage=\"item.targetPage\" @pulling=\"handleCardPulling\" />\n            </view>\n          </swiper-item>\n        </swiper>\n\n        <!-- 分页指示器，固定在页面中 -->\n        <view class=\"page-dots\" @tap.stop>\n          <!-- 使用内联样式固定位置 -->\n          <view v-for=\"i in products.length\" :key=\"i\" class=\"page-dot\" @tap.stop\n                :style=\"{ backgroundColor: currentPage === i ? 'rgb(232,124,174)' : 'rgba(0,0,0,0.2)' }\"\n                @tap=\"handleDotTap(i - 1)\">\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n// 导入PreviewCard组件\nimport PreviewCard from './PreviewCard.vue'\n\nexport default {\n  name: 'tabbar',\n  components: {\n    PreviewCard\n  },\n  props: {\n    current: {\n      type: [Number],\n      default: 0\n    }\n  },\n  data() {\n    return {\n      list: [{\n        iconPath: \"/static/tabbar/tab_home.png\",\n        selectedIconPath: \"/static/tabbar/tab_home_x.png\",\n        icon: 'home',\n        text: '首页',\n        count: 0,\n        isDot: false,\n        customIcon: false,\n        pagePath: \"/pages/index/index\"\n      },\n        {\n          iconPath: \"/static/tabbar/tab_buy.png\",\n          selectedIconPath: \"/static/tabbar/tab_buy_x.png\",\n          icon: 'bag',\n          text: '购买',\n          count: 0,\n          isDot: false,\n          customIcon: false,\n          pagePath: \"/pages/buy/buy\"\n        },\n        {\n          iconPath: \"/static/tabbar/tab_fox1.png\",\n          selectedIconPath: \"/static/tabbar/tab_fox1.png\",\n          text: '作品',\n          count: 0,\n          isDot: false,\n          customIcon: false,\n          pagePath: \"/pages/index/index\"\n        },\n        {\n          iconPath: \"/static/tabbar/tab_schedule.png\",\n          selectedIconPath: \"/static/tabbar/tab_schedule_x.png\",\n          icon: 'tags',\n          text: '约课',\n          count: 0,\n          isDot: false,\n          customIcon: false,\n          pagePath: \"/pages/Schedule/Schedule\"\n        },\n        {\n          iconPath: \"/static/tabbar/tab_mine.png\",\n          selectedIconPath: \"/static/tabbar/tab_mine_x.png\",\n          icon: 'account',\n          text: '我的',\n          count: 0,\n          isDot: false,\n          customIcon: false,\n          pagePath: \"/pages/mine/mine\"\n        },\n      ],\n\n      bottomHeight2: 4,\n      bottomHeight: 10,\n      ecology: '',\n      noIndex: 0,\n      showBox: false,\n      showFixed: false,\n      selColor: uni.getStorageSync('storeInfo') ? uni.getStorageSync('storeInfo').button : '#131315',\n\n      // 卡片相关数据\n      currentPage: 0,  // 当前页面索引\n      showBoundaryHint: false,\n      totalCards: 2,  // 总卡片数量\n\n      // 商品数据\n      products: [\n        //https://dance-**********.cos.ap-guangzhou.myqcloud.com/static/images/vote-card-image.jpg\n        { id: 1, name: '新店投票', tag: 'Fox - New store voting', image: 'https://dance-**********.cos.ap-guangzhou.myqcloud.com/static/images/vote-card-image.jpg', targetPage: '/pagesSub/switch/vote' },\n        { id: 2, name: '话题广场', tag: 'Fox - Topic square', image: 'https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg', targetPage: '/pagesSub/switch/topic-list' },\n        { id: 3, name: '帖子广场', tag: 'Fox - Topic square', image: 'https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg', targetPage: '/pagesSub/social/main/index' }\n      ],\n\n      // 页面滚动锁定标志\n      pageScrollLocked: false,\n      // TabBar显示状态\n      isTabBarHidden: false,\n      // TabBar返回动画标记\n      isTabBarReturning: false,\n\n      // 滚动状态\n      scrollLeft: 0,\n      cardWidth: 0,\n      windowWidth: 0,\n\n      // 触摸状态\n      touchStartX: 0,\n      touchStartY: 0,\n      touchStartTime: 0,\n      touchMoved: false,\n      isSwiping: false,\n      swipeThreshold: 10, // 滑动阈值\n      cardTouchTimer: null,\n      \n      // 添加方向判断相关变量\n      touchDirection: '', // 'horizontal' 或 'vertical'\n      directionLocked: false,\n      minSwipeDistance: 5, // 最小判定为滑动的距离\n      directionThreshold: 0.5, // 方向判定阈值，值越小越容易判定为水平\n      \n      // 调整更严格的角度判断参数\n      verticalAngleThreshold: 5, // 垂直方向的角度阈值(度)，小于这个角度才会被视为垂直\n      horizontalAngleThreshold: 85, // 水平方向的角度阈值(度)，小于这个角度视为水平\n      minVerticalDistance: 20, // 最小垂直滑动距离，小于此距离不触发下拉\n      \n      // 副卡相关状态\n      rightCardOffsetX: 0,\n      leftCardOffsetX: 0,\n      rightCardScale: 0.85,\n      leftCardScale: 0.85,\n      rightCardOpacity: 0.7,\n      leftCardOpacity: 0.7,\n\n      // 添加卡片下拉状态\n      isCardPulling: false,\n      pullDistance: 0,\n\n      // 添加关闭状态\n      isClosing: false,\n\n      // 添加卡片退场状态\n      isCardExiting: false,\n      \n      // 卡片提示文本\n      cardTipText: \"随便点一下，可能会发现新大陆\"\n    }\n  },\n  computed: {\n    // 卡片容器样式\n    cardContainerStyle() {\n      if (!this.showBox) return {}\n\n      return {\n        transform: `translateY(-20px)`,\n        transition: 'transform 450ms cubic-bezier(0.4, 0, 0.2, 1)'\n      }\n    }\n  },\n  created() {\n    let that = this;\n    uni.getSystemInfo({\n      success(res) {\n        if (res.safeAreaInsets.bottom) {\n          that.bottomHeight = res.safeAreaInsets.bottom\n          that.bottomHeight2 = res.safeAreaInsets.bottom - 7;\n        }\n        // 获取屏幕宽度用于卡片定位\n        that.windowWidth = res.windowWidth;\n        that.cardWidth = res.windowWidth * 0.8; // 卡片宽度为窗口的80%\n      }\n    })\n\n    // 全局触摸事件处理\n    uni.$on('touchmove', (e) => {\n      if (this.showBox || this.pageScrollLocked) {\n        // 如果卡片显示或页面被锁定，阻止所有触摸移动\n        e.preventDefault && e.preventDefault();\n        e.stopPropagation && e.stopPropagation();\n        // 如果页面被锁定，则滚动到顶部\n        uni.pageScrollTo({\n          scrollTop: 0,\n          duration: 0\n        });\n        return false;\n      }\n    });\n    \n    // 在微信小程序环境中，需要将方法暴露给页面实例\n    if (typeof this.$scope !== 'undefined' && this.$scope) {\n      this.$scope.preventCardClick = this.preventCardClick;\n      this.$scope.handleContainerClick = this.handleContainerClick;\n      this.$scope.swiperBackgroundClick = this.swiperBackgroundClick;\n    }\n  },\n  mounted() {\n    // 在组件挂载后，确保初始卡片居中显示\n    this.$nextTick(() => {\n      setTimeout(() => {\n        if (this.showBox) {\n          this.scrollToCard(this.currentPage);\n        }\n      }, 300);\n    });\n    \n    // 在微信小程序环境中，确保方法已注册到页面实例\n    if (typeof this.$scope !== 'undefined' && this.$scope) {\n      this.$scope.preventCardClick = this.preventCardClick;\n      this.$scope.handleContainerClick = this.handleContainerClick;\n      this.$scope.swiperBackgroundClick = this.swiperBackgroundClick;\n    }\n    \n    // 获取卡片提示文本\n    this.getCardTip();\n  },\n  beforeDestroy() {\n    // 移除全局触摸事件监听\n    uni.$off('touchmove');\n  },\n  methods: {\n    // 轮播图切换事件\n    onSwiperChange(e) {\n      this.currentPage = e.detail.current;\n    },\n\n    // 切换完门店执行切换按钮颜色\n    setColor(ecology) {\n      try {\n        this.ecology = ecology ? ecology : '敬请期待~'\n        const storeInfo = uni.getStorageSync('storeInfo')\n        this.selColor = (storeInfo && storeInfo.button) ? storeInfo.button : '#131315'\n      } catch (error) {\n        console.warn('setColor error:', error)\n        this.ecology = '敬请期待~'\n        this.selColor = '#131315'\n      }\n    },\n    tabbarChange(item, index) {\n      this.noIndex = index\n      if (index == 2) {\n        // 如果当前是约课页面(current === 3)或购买页面(current === 1)，禁止点击中间Tab进入卡片预览\n        if(this.current === 3 || this.current === 1) {\n          // 在约课页面或购买页面，禁止打开卡片预览\n          let pageName = this.current === 3 ? '约课' : '购买'\n          uni.showToast({\n            title: `${pageName}页面不可用`,\n            icon: 'none',\n            duration: 2000\n          })\n          return\n        }\n\n        this.noIndex = index\n        // 入场阶段：TabBar和卡片同步动画\n        this.showBox = true\n        this.showFixed = true\n        this.isTabBarReturning = false\n        // 阻止页面滚动\n        this.preventScroll(true)\n\n        // 尝试使用微信小程序API控制弹性效果\n        try {\n          wx.setPageStyle({\n            style: {\n              overflow: 'hidden'\n            }\n          })\n        } catch(e) {\n          // 忽略错误\n        }\n\n        // 修改：保留图标可见\n        setTimeout(() => {\n          this.isTabBarHidden = false\n        }, 400)\n        \n        // 获取卡片提示文本\n        this.getCardTip();\n\n        return\n      } else {\n        uni.switchTab({\n          url: item.pagePath\n        })\n      }\n    },\n\n    // 改进防止滚动的方法\n    preventScroll(prevent) {\n      // 通过页面状态控制滚动\n      this.pageScrollLocked = prevent;\n\n      // 使用全局变量控制滚动状态\n      getApp().globalData = getApp().globalData || {};\n      getApp().globalData.pageScrollLocked = prevent;\n\n      // 尝试使用微信小程序原生API控制滚动\n      try {\n        wx.setPageStyle({\n          style: {\n            overflow: prevent ? 'hidden' : 'auto'\n          }\n        })\n      } catch(e) {\n        // 忽略错误\n      }\n    },\n\n    // 更强的触摸阻止函数\n    preventTouchMove(e) {\n      // 如果已经确定了滑动方向\n      if (this.directionDetermined) {\n        // 如果是水平滑动，不阻止默认行为\n        if (this.initialSwipeDirection === 'horizontal') {\n          return true;\n        }\n      }\n      \n      if (this.pageScrollLocked) {\n        e.preventDefault && e.preventDefault();\n        e.stopPropagation && e.stopPropagation();\n        return false;\n      }\n    },\n\n    // 添加卡片下拉处理方法\n    handleCardPulling(data) {\n      // 只有当明确判定为垂直方向滑动时才触发下拉效果\n      if (this.touchDirection === 'vertical') {\n        this.isCardPulling = true;\n        this.pullDistance = data.distance;\n      }\n    },\n\n    // 添加触摸开始事件处理\n    onCardTouchStart(e) {\n      const touch = e.touches[0];\n      this.touchStartX = touch.clientX;\n      this.touchStartY = touch.clientY;\n      this.touchStartTime = Date.now();\n      this.touchMoved = false;\n      this.directionLocked = false;\n      this.touchDirection = '';\n      this.sampledTouches = []; // 重置采样点数组\n      \n      // 重置滑动判断相关变量\n      this.initialSwipeDirection = '';\n      this.directionDetermined = false;\n      this.lastDeltaX = 0;\n      this.lastDeltaY = 0;\n      this.accumulatedHorizontalDistance = 0;\n      this.accumulatedVerticalDistance = 0;\n      this.lastTouchTime = Date.now();\n      \n      // 添加首个采样点\n      this.sampledTouches.push({\n        x: touch.clientX,\n        y: touch.clientY,\n        time: Date.now()\n      });\n    },\n\n    // 添加触摸移动事件处理\n    onCardTouchMove(e) {\n      // 防止页面滚动\n      if (this.pageScrollLocked) {\n        e.preventDefault && e.preventDefault();\n        e.stopPropagation && e.stopPropagation();\n      }\n      \n      // 实现事件节流，避免过于频繁处理造成卡顿\n      const now = Date.now();\n      if (now - this.lastTouchTime < this.touchThrottleDelay) {\n        return; // 如果距离上次处理时间太短，则跳过本次处理\n      }\n      this.lastTouchTime = now;\n\n      const touch = e.touches[0];\n      const currentX = touch.clientX;\n      const currentY = touch.clientY;\n      const deltaX = currentX - this.touchStartX;\n      const deltaY = currentY - this.touchStartY;\n      \n      // 计算相对于上次位移的差值\n      const diffX = Math.abs(deltaX) - Math.abs(this.lastDeltaX);\n      const diffY = Math.abs(deltaY) - Math.abs(this.lastDeltaY);\n      \n      // 更新累计位移\n      if (diffX > 0) this.accumulatedHorizontalDistance += diffX;\n      if (diffY > 0) this.accumulatedVerticalDistance += diffY;\n      \n      // 更新上次位移记录\n      this.lastDeltaX = Math.abs(deltaX);\n      this.lastDeltaY = Math.abs(deltaY);\n      \n      // 仅当滑动距离超过阈值时才进行方向判断\n      const totalDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n      \n      if (!this.directionDetermined && totalDistance >= this.swipeStartDistance) {\n        // 计算水平与垂直方向的比值\n        const horizontalRatio = Math.abs(deltaX) / (Math.abs(deltaY) || 0.1); // 避免除以0\n        \n        // 判断初始滑动方向\n        if (horizontalRatio >= this.horizontalDirectionThreshold) {\n          // 水平方向优先，判定为水平滑动\n          this.initialSwipeDirection = 'horizontal';\n          this.touchDirection = 'horizontal';\n          this.directionLocked = true;\n        } else if (horizontalRatio <= (1 / this.horizontalDirectionThreshold)) {\n          // 垂直方向优先，判定为垂直滑动\n          this.initialSwipeDirection = 'vertical';\n          this.touchDirection = 'vertical';\n          this.directionLocked = true;\n        } else {\n          // 优先考虑累计方向\n          if (this.accumulatedHorizontalDistance > this.accumulatedVerticalDistance) {\n            this.initialSwipeDirection = 'horizontal';\n            this.touchDirection = 'horizontal';\n            this.directionLocked = true;\n          }\n        }\n        \n        this.directionDetermined = true;\n      }\n      \n      // 如果已经确定方向是水平，继续处理水平滑动逻辑\n      if (this.touchDirection === 'horizontal') {\n        // 清除任何可能的下拉状态\n        if (this.isCardPulling) {\n          this.isCardPulling = false;\n          this.pullDistance = 0;\n        }\n      }\n      \n      // 如果确定方向是垂直，处理垂直滑动逻辑\n      if (this.touchDirection === 'vertical' && deltaY > 0) {\n        this.isCardPulling = true;\n        this.pullDistance = deltaY;\n      }\n      \n      this.touchMoved = true;\n      \n      // 添加当前触摸点到采样数组，用于更精确的速度计算\n      this.sampledTouches.push({\n        x: currentX,\n        y: currentY,\n        time: now\n      });\n      \n      // 控制采样点数量\n      if (this.sampledTouches.length > 5) {\n        this.sampledTouches.shift();\n      }\n    },\n\n    // 添加触摸结束事件处理\n    onCardTouchEnd(e) {\n      if (!this.touchMoved) return;\n      \n      // 计算触摸持续时间\n      const touchDuration = Date.now() - this.touchStartTime;\n      \n      // 计算最终速度\n      let velocityX = 0;\n      let velocityY = 0;\n      \n      if (this.sampledTouches.length >= 2) {\n        const newest = this.sampledTouches[this.sampledTouches.length - 1];\n        const oldest = this.sampledTouches[0];\n        const timeSpan = newest.time - oldest.time;\n        \n        if (timeSpan > 0) {\n          velocityX = (newest.x - oldest.x) / timeSpan; // 每毫秒移动的像素数\n          velocityY = (newest.y - oldest.y) / timeSpan;\n        }\n      }\n      \n      // 如果是快速水平滑动，可以添加翻页效果\n      const isQuickHorizontalSwipe = Math.abs(velocityX) > 0.5 && \n                                    Math.abs(velocityX) > Math.abs(velocityY) * 1.5;\n      \n      if (isQuickHorizontalSwipe) {\n        // 根据滑动方向和速度决定切换到下一页或上一页\n        const direction = velocityX < 0 ? 1 : -1; // 负值表示向左滑，正值表示向右滑\n        const targetPage = Math.max(0, Math.min(this.products.length - 1, this.currentPage + direction));\n        \n        if (targetPage !== this.currentPage) {\n          this.currentPage = targetPage;\n        }\n      }\n      \n      // 重置下拉状态\n      if (this.isCardPulling) {\n        this.isCardPulling = false;\n        this.pullDistance = 0;\n      }\n      \n      // 重置方向判断状态\n      this.directionLocked = false;\n      this.touchDirection = '';\n      this.touchMoved = false;\n      this.sampledTouches = [];\n      this.directionDetermined = false;\n      this.initialSwipeDirection = '';\n    },\n\n    // 专门处理home图标点击的方法\n    onHomeClick() {\n      console.log('点击背景关闭预览');\n      \n      // 先触发卡片退场动画\n      this.isCardExiting = true;\n      \n      // 等待动画结束后再隐藏\n      setTimeout(() => {\n        // 立即恢复页面滚动\n        this.preventScroll(false);\n        \n        // 尝试使用微信小程序API恢复滚动效果\n        try {\n          wx.setPageStyle({\n            style: {\n              overflow: 'auto'\n            }\n          });\n        } catch(e) {\n          // 忽略错误\n        }\n        \n        // 关闭所有遮罩和卡片\n        this.showBox = false;\n        this.showFixed = false;\n        this.isCardExiting = false;\n        \n        // 重置TabBar状态\n        this.isTabBarReturning = true;\n        this.isTabBarHidden = false;\n        \n        // 重置其他状态\n        this.currentPage = 0;\n        this.noIndex = this.current;\n        \n        // 稍后重置TabBar动画状态\n        setTimeout(() => {\n          this.isTabBarReturning = false;\n        }, 400);\n      }, 300); // 等待300ms让退场动画完成\n    },\n\n    // 添加点击指示点切换页面的方法\n    handleDotTap(index) {\n      this.currentPage = index;\n    },\n    \n    // 添加卡片容器点击处理方法\n    handleContainerClick() {\n      console.log('容器点击关闭预览');\n      this.onHomeClick();\n    },\n    \n    // 专门给微信小程序使用的阻止冒泡方法\n    preventCardClick() {\n      console.log('卡片点击，阻止冒泡');\n      // 仅阻止事件冒泡，不关闭预览\n      return false;\n    },\n\n    // 添加swiper背景点击处理方法 - 微信小程序原生方法\n    swiperBackgroundClick() {\n      console.log('swiper背景点击，关闭预览');\n      uni.showToast({\n        title: '点击了背景区域',\n        icon: 'none'\n      });\n      this.onHomeClick();\n    },\n\n    // 添加关闭预览的方法\n    closePreview() {\n      this.onHomeClick();\n    },\n    \n          // 获取基础URL\n      getBaseUrl() {\n        // #ifdef MP-WEIXIN\n        return 'https://vote.foxdance.com.cn' // 微信小程序环境使用外部域名\n        // #endif\n\n        // 非小程序环境使用本地开发地址\n        return 'https://vote.foxdance.com.cn'\n      },\n      \n      // 获取卡片提示文本\n      getCardTip() {\n        const BASE_URL = this.getBaseUrl()\n        uni.request({\n          url: `${BASE_URL}/api/vote-info/1/card-tip`,\n          method: 'GET',\n          success: (res) => {\n            console.log('获取卡片提示成功:', res);\n            if (res.data && res.data.code === 0 && res.data.data) {\n              // 更新卡片提示文本\n              this.cardTipText = res.data.data;\n            }\n          },\n          fail: (err) => {\n            console.error('获取卡片提示失败:', err);\n          }\n        });\n      }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n/* 预览遮罩 - 全屏覆盖，点击关闭预览 */\n.preview-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: 100vw;\n  height: 100vh;\n  z-index: 997;\n  background-color: transparent;\n}\n\n/* 全屏底层白色背景 */\n.fullscreen-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: 100vw;\n  height: 100vh;\n  background-color: #FFFFFF;\n  z-index: 997;\n  animation: fadeIn 300ms ease;\n  touch-action: none;\n  transition: opacity 300ms ease-out;\n}\n\n/* 背景退场动画 */\n.background-exit {\n  opacity: 0;\n}\n\n/* 底部提示样式 */\n.bottom-hint {\n  width: 100%;\n  position: absolute;\n  bottom: 80rpx;\n  left: 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.fox-logo-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16rpx;\n  width: 100%;\n  padding: 10rpx 0;\n}\n\n.fox-line {\n  width: 125rpx;\n  height: 1px;\n  background-color: #CCCCCC;\n  margin: 0 16rpx;\n}\n\n.fox-text {\n  font-size: 26rpx;\n  color: #666666;\n  letter-spacing: 2rpx;\n  font-weight: 500;\n}\n\n.fox-subtext {\n  font-size: 22rpx;\n  color: #999999;\n  letter-spacing: 0.5rpx;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n\n  to {\n    opacity: 1;\n  }\n}\n\n/* 整体卡片容器 - 包含卡片和按钮 */\n.card-container-with-buttons {\n  position: fixed;\n  top: 56%;\n  left: 50%;\n  transform: translate(-50%, -50%); /* 修改为-50%确保垂直居中 */\n  width: 100%;\n  margin-top: 0; /* 移除上边距，让transform居中生效 */\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  z-index: 999; /* 确保卡片在点击层(998)之上 */\n  will-change: transform, opacity;\n  height: auto;\n  opacity: 0;\n  pointer-events: auto;\n  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);\n  touch-action: none;\n  overflow: visible;\n  transform: translate(-50%, -50%) translateY(20px); /* 修改为-50%确保垂直居中 */\n}\n\n.card-container-with-buttons.show {\n  opacity: 1;\n  transform: translate(-50%, -50%) translateY(0);\n  /* 向上淡入浮现 */\n   pointer-events: auto;\n   overflow: visible;\n}\n\n/* swiper样式 */\n.card-swiper {\n  width: 100%;\n  height: 73vh;\n  touch-action: pan-x pan-y;\n  overflow: visible;\n  /* 确保卡片可以超出容器 */\n  position: relative;\n  top: -55rpx;\n  z-index: 1000;\n  background-color: transparent; /* 蓝色调试背景 */\n}\n\n.card-swiper-item {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  position: relative;\n  top: -90rpx; /* 改为0，让其位于垂直中心 */\n  box-sizing: border-box;\n  transform: scale(1);\n  /* 为阴影添加额外空间 */\n  overflow: visible;\n  /* 确保卡片可以超出容器 */\n}\n\n.card-item {\n  width: 82%;\n  height: 100%;\n  max-height: 900rpx;\n  display: inline-block;\n  border-radius: 66rpx;\n  box-shadow: none;\n  /* 移除阴影效果 */\n  transition: all 0.3s ease;\n  transform: scale(0.85) translateY(5rpx);\n  opacity: 0.9;\n  position: relative;\n  /* 确保为绝对定位的子元素提供参考 */\n  z-index: 1002;\n  /* 提高卡片的z-index确保在背景区域之上 */\n  overflow: visible;\n  /* 改为visible使阴影可以超出容器 */\n  transform-style: preserve-3d;\n  -webkit-transform-style: preserve-3d;\n}\n\n.card-item.active-card {\n  transform: scale(1) translateY(-15rpx);\n  opacity: 1;\n  box-shadow: none;\n  overflow: visible;\n  z-index: 1003;\n}\n\n.card-item.near-active {\n  transform: scale(0.9) translateY(0);\n  opacity: 0.9;\n  box-shadow: none;\n}\n\n/* 修改swiper组件样式，确保其不会裁剪子元素 */\n.card-swiper .wx-swiper-dots {\n  position: relative;\n  z-index: 98;\n}\n\n/* TabBar样式，包含入场和退场动画 */\n.tab-bar {\n  .tab_bgi {\n    position: absolute;\n    z-index: -1;\n    left: 0;\n    top: 0;\n    width: 100%;\n    height: 100%;\n    background-image: url('@/static/tabbar/tab_big1.png');\n    background-size: 100% 100%;\n  }\n\n  display: flex;\n  justify-content: center;\n  align-items: end;\n  width: 702rpx;\n  height: 170rpx;\n  height: 98rpx;\n  border-radius: 66rpx;\n  z-index: 996;\n  position: fixed;\n  bottom: 56rpx;\n  left: 50%;\n  transform: translateX(-50%) translateY(0);\n  opacity: 1;\n  transition: transform 400ms ease-in-out,\n  opacity 400ms ease-in-out;\n  will-change: transform,\n  opacity;\n\n  .tab-bar-item {\n    flex: 1;\n    height: 100%;\n    text-align: center;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    flex-direction: column;\n\n    &:nth-child(4) {\n      padding-top: 0;\n    }\n\n    .tab_img {\n      width: 44rpx;\n      height: 44rpx;\n      display: block;\n      margin: auto;\n    }\n\n    .tab_text {\n      margin-top: 2rpx;\n      color: #333;\n      font-size: 26rpx;\n      color: #945048;\n      line-height: 30rpx;\n      text-align: center;\n    }\n  }\n}\n\n/* TabBar退出动画 */\n.tab-bar-exit {\n  transform: translateX(-50%) translateY(100%);\n  /* 向下淡出位移 */\n  opacity: 0;\n  transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);\n  /* 确保动画持续400ms */\n  will-change: transform, opacity;\n}\n\n/* TabBar返回动画 */\n.tab-bar-enter {\n  transform: translateX(-50%) translateY(0);\n  /* 向上淡入复位 */\n  opacity: 1;\n  transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);\n  /* 确保动画持续400ms */\n  will-change: transform, opacity;\n}\n\n/* 添加图标可见的样式 */\n.tab-bar-icon-visible .tab-bar-item:nth-child(3) {\n  opacity: 1 !important;\n  z-index: 998;\n}\n\n/* 固定显示的tab图标 */\n.tab-fixed-visible {\n  position: relative;\n  z-index: 998;\n  opacity: 1 !important;\n  transform: translateY(0) !important;\n}\n\n/* 卡片通用样式 */\n.card-preview-container {\n  background: #FFF;\n  border-radius: 66rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n  transform-style: preserve-3d;\n  user-select: none;\n  touch-action: pan-x pan-y;\n  overflow: visible;\n  /* 改为visible使阴影可以超出容器 */\n  position: relative;\n  z-index: 1002; /* 提高卡片的z-index确保在点击层之上 */\n  /* 确保显示在白色背景之上 */\n}\n\n.header-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: fixed;\n  top: 156rpx;\n  left: 0;\n  right: 0;\n  width: 100%;\n  height: 100rpx;\n  z-index: 998;\n}\n\n.header-image {\n  width: 100rpx;\n  height: 100rpx;\n}\n\n.header-left image {\n  width: 48rpx;\n  height: 48rpx;\n  padding-left: 20rpx;\n}\n\n.header-right image {\n  width: 48rpx;\n  height: 48rpx;\n  padding-right: 20rpx;\n}\n\n/* 简单稳定的分页指示器样式 */\n.page-dots {\n  position: fixed;\n  bottom: 120rpx;\n  left: 0;\n  width: 100%;\n  height: 20rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: -1;\n}\n\n.page-dot {\n  width: 20rpx;\n  height: 20rpx;\n  border-radius: 50%;\n  margin: 0 10rpx;\n}\n\n/* 卡片退场动画 - 更自然的淡出效果 */\n.card-container-with-buttons.exit {\n  opacity: 0;\n  transform: translate(-50%, -50%);\n  transition: opacity 300ms ease-out;\n}\n</style>", "import mod from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabbar.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabbar.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752725568663\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}