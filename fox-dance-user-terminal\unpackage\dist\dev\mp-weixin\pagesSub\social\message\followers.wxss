@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.followers-container.data-v-2eeb3bc0 {
  height: 100vh;
  background: #f5f5f5;
}
.message-list.data-v-2eeb3bc0 {
  height: 100%;
  padding: 32rpx 0;
}
.message-card.data-v-2eeb3bc0 {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  margin: 0 32rpx 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}
.user-avatar.data-v-2eeb3bc0 {
  margin-right: 24rpx;
  flex-shrink: 0;
}
.user-info.data-v-2eeb3bc0 {
  flex: 1;
  margin-right: 24rpx;
}
.user-header.data-v-2eeb3bc0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}
.user-name.data-v-2eeb3bc0 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.follow-time.data-v-2eeb3bc0 {
  font-size: 24rpx;
  color: #999;
}
.user-desc.data-v-2eeb3bc0 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: block;
}
.user-tags.data-v-2eeb3bc0 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.tag-item.data-v-2eeb3bc0 {
  margin: 0;
}
.follow-actions.data-v-2eeb3bc0 {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}
.unread-dot.data-v-2eeb3bc0 {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
}
.empty-state.data-v-2eeb3bc0 {
  display: flex;
  justify-content: center;
  padding: 120rpx 32rpx;
}
.load-more.data-v-2eeb3bc0 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}
.load-text.data-v-2eeb3bc0 {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

