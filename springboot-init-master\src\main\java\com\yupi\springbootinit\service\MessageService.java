package com.yupi.springbootinit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yupi.springbootinit.model.dto.message.MessageSendRequest;
import com.yupi.springbootinit.model.entity.Message;
import com.yupi.springbootinit.model.vo.ConversationVO;
import com.yupi.springbootinit.model.vo.MessageVO;

import java.util.List;
import java.util.Map;

/**
 * 私信消息服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface MessageService extends IService<Message> {

    /**
     * 发送消息
     *
     * @param sendRequest 发送请求
     * @return 消息视图对象
     */
    MessageVO sendMessage(MessageSendRequest sendRequest);

    /**
     * 获取会话列表
     *
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @return 会话列表
     */
    List<ConversationVO> getConversations(Long userId, Integer current, Integer size);

    /**
     * 获取会话消息
     *
     * @param currentUserId 当前用户ID
     * @param otherUserId 对方用户ID
     * @param current 页码
     * @param size 每页大小
     * @return 消息列表
     */
    List<MessageVO> getConversationMessages(Long currentUserId, Long otherUserId, Integer current, Integer size);

    /**
     * 标记消息已读
     *
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean markMessageAsRead(Long messageId, Long userId);

    /**
     * 标记会话所有消息已读
     *
     * @param currentUserId 当前用户ID
     * @param otherUserId 对方用户ID
     * @return 是否成功
     */
    boolean markConversationAsRead(Long currentUserId, Long otherUserId);

    /**
     * 删除消息
     *
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteMessage(Long messageId, Long userId);

    /**
     * 删除会话
     *
     * @param currentUserId 当前用户ID
     * @param otherUserId 对方用户ID
     * @return 是否成功
     */
    boolean deleteConversation(Long currentUserId, Long otherUserId);

    /**
     * 获取未读消息数
     *
     * @param userId 用户ID
     * @return 未读消息数统计
     */
    Map<String, Integer> getUnreadCount(Long userId);

    /**
     * 搜索消息
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @param current 页码
     * @param size 每页大小
     * @return 消息列表
     */
    List<MessageVO> searchMessages(Long userId, String keyword, Integer current, Integer size);

    /**
     * 获取或创建会话ID
     *
     * @param userId1 用户1 ID
     * @param userId2 用户2 ID
     * @return 会话ID
     */
    Long getOrCreateConversationId(Long userId1, Long userId2);

    /**
     * 获取会话未读消息数
     *
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 未读消息数
     */
    Integer getConversationUnreadCount(Long conversationId, Long userId);

    /**
     * 批量标记消息已读
     *
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean batchMarkAsRead(List<Long> messageIds, Long userId);

    /**
     * 获取最近联系人
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近联系人列表
     */
    List<ConversationVO> getRecentContacts(Long userId, Integer limit);

    /**
     * 检查消息发送权限
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @return 是否有权限发送
     */
    boolean checkSendPermission(Long senderId, Long receiverId);

    /**
     * 获取消息发送统计
     *
     * @param userId 用户ID
     * @return 发送统计
     */
    Map<String, Integer> getMessageStats(Long userId);
}
