package com.yupi.springbootinit.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.exception.BusinessException;
import com.yupi.springbootinit.exception.ThrowUtils;
import com.yupi.springbootinit.model.entity.Tag;

import com.yupi.springbootinit.service.TagService;
import com.yupi.springbootinit.service.PostTagService;
import com.yupi.springbootinit.service.UserService;
import com.yupi.springbootinit.service.BaUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.List;

/**
 * 标签管理接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/tag")
@Slf4j
@Api(tags = "标签管理")
public class TagController {

    @Resource
    private TagService tagService;

    @Resource
    private PostTagService postTagService;

    @Resource
    private UserService userService;

    @Resource
    private BaUserService baUserService;

    /**
     * 创建标签
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建标签")
    public BaseResponse<Long> createTag(@RequestParam String name,
                                       @RequestParam(required = false) String description,
                                       @RequestParam(required = false) String color,
                                       @RequestParam Long userId) {
        ThrowUtils.throwIf(name == null || name.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "标签名称不能为空");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Long result = tagService.createTag(name.trim(), description, color);
        return ResultUtils.success(result);
    }

    /**
     * 获取热门标签
     */
    @GetMapping("/hot")
    @ApiOperation(value = "获取热门标签")
    public BaseResponse<List<Tag>> getHotTags(@RequestParam(defaultValue = "20") Integer limit) {
        ThrowUtils.throwIf(limit == null || limit <= 0 || limit > 100, ErrorCode.PARAMS_ERROR);
        List<Tag> result = tagService.getHotTags(limit);
        return ResultUtils.success(result);
    }

    /**
     * 获取使用次数最多的标签
     */
    @GetMapping("/top-used")
    @ApiOperation(value = "获取使用次数最多的标签")
    public BaseResponse<List<Tag>> getTopUsedTags(@RequestParam(defaultValue = "20") Integer limit) {
        ThrowUtils.throwIf(limit == null || limit <= 0 || limit > 100, ErrorCode.PARAMS_ERROR);
        List<Tag> result = tagService.getTopUsedTags(limit);
        return ResultUtils.success(result);
    }

    /**
     * 搜索标签
     */
    @GetMapping("/search")
    @ApiOperation(value = "搜索标签")
    public BaseResponse<Page<Tag>> searchTags(@RequestParam String keyword,
                                             @RequestParam(defaultValue = "1") Integer current,
                                             @RequestParam(defaultValue = "20") Integer pageSize) {
        ThrowUtils.throwIf(keyword == null || keyword.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "搜索关键词不能为空");
        Page<Tag> result = tagService.searchTags(keyword.trim(), current, pageSize);
        return ResultUtils.success(result);
    }

    /**
     * 获取推荐标签
     */
    @GetMapping("/recommend")
    @ApiOperation(value = "获取推荐标签")
    public BaseResponse<List<Tag>> getRecommendTags(@RequestParam(defaultValue = "10") Integer limit,
                                                   @RequestParam Long userId) {
        ThrowUtils.throwIf(limit == null || limit <= 0 || limit > 50, ErrorCode.PARAMS_ERROR, "限制数量无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        List<Tag> result = tagService.getRecommendTags(userId, limit);
        return ResultUtils.success(result);
    }

    /**
     * 获取帖子的标签
     */
    @GetMapping("/post")
    @ApiOperation(value = "获取帖子的标签")
    public BaseResponse<List<Tag>> getPostTags(@RequestParam Long postId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        List<Tag> result = postTagService.getPostTags(postId);
        return ResultUtils.success(result);
    }

    /**
     * 为帖子添加标签
     */
    @PostMapping("/post/add")
    @ApiOperation(value = "为帖子添加标签")
    public BaseResponse<Boolean> addTagsToPost(@RequestParam Long postId,
                                              @RequestBody List<String> tagNames,
                                              @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(tagNames == null || tagNames.isEmpty(), ErrorCode.PARAMS_ERROR, "标签列表不能为空");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postTagService.addTagsToPostByNames(postId, tagNames);
        return ResultUtils.success(result);
    }

    /**
     * 更新帖子的标签
     */
    @PostMapping("/post/update")
    @ApiOperation(value = "更新帖子的标签")
    public BaseResponse<Boolean> updatePostTags(@RequestParam Long postId,
                                               @RequestBody List<String> tagNames,
                                               @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postTagService.updatePostTagsByNames(postId, tagNames);
        return ResultUtils.success(result);
    }

    /**
     * 移除帖子的所有标签
     */
    @PostMapping("/post/remove-all")
    @ApiOperation(value = "移除帖子的所有标签")
    public BaseResponse<Boolean> removeAllTagsFromPost(@RequestParam Long postId,
                                                      @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postTagService.removeAllTagsFromPost(postId);
        return ResultUtils.success(result);
    }

    /**
     * 获取标签下的帖子数量
     */
    @GetMapping("/post-count")
    @ApiOperation(value = "获取标签下的帖子数量")
    public BaseResponse<Integer> getTagPostCount(@RequestParam Long tagId) {
        ThrowUtils.throwIf(tagId == null || tagId <= 0, ErrorCode.PARAMS_ERROR);
        Integer result = postTagService.countPostsByTag(tagId);
        return ResultUtils.success(result);
    }

    /**
     * 更新标签热门状态（管理员功能）
     */
    @PostMapping("/admin/hot-status")
    @ApiOperation(value = "更新标签热门状态")
    public BaseResponse<Boolean> updateHotStatus(@RequestParam Long tagId,
                                                @RequestParam Boolean isHot,
                                                @RequestParam Long userId) {
        ThrowUtils.throwIf(tagId == null || tagId <= 0, ErrorCode.PARAMS_ERROR, "标签ID无效");
        ThrowUtils.throwIf(isHot == null, ErrorCode.PARAMS_ERROR, "热门状态无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        // 这里应该检查管理员权限
        Boolean result = tagService.updateHotStatus(tagId, isHot);
        return ResultUtils.success(result);
    }

    // ==================== 社交功能相关接口 ====================

    /**
     * 获取话题详情
     */
    @GetMapping("/detail/{tagId}")
    @ApiOperation(value = "获取话题详情")
    public BaseResponse<Object> getTagDetail(@PathVariable Long tagId,
                                            @RequestParam(required = false) Long userId) {
        if (tagId == null || tagId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "话题ID无效");
        }

        try {
            // 如果传入了userId，验证用户是否存在
            if (userId != null && userId > 0) {
                if (baUserService.getById(userId) == null) {
                    throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
                }
            }

            Object tagDetail = tagService.getTagDetail(tagId, userId);
            if (tagDetail == null) {
                return ResultUtils.error(ErrorCode.NOT_FOUND_ERROR, "话题不存在");
            }

            log.info("获取话题详情成功 - tagId: {}, userId: {}", tagId, userId);
            return ResultUtils.success(tagDetail);

        } catch (Exception e) {
            log.error("获取话题详情失败 - tagId: {}, error: {}", tagId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取话题详情失败");
        }
    }



    /**
     * 获取话题下的帖子
     */
    @GetMapping("/{tagId}/posts")
    @ApiOperation(value = "获取话题下的帖子")
    public BaseResponse<List<Object>> getTagPosts(@PathVariable Long tagId,
                                                 @RequestParam(defaultValue = "1") Integer current,
                                                 @RequestParam(defaultValue = "10") Integer size,
                                                 @RequestParam(defaultValue = "time") String sortBy) {
        if (tagId == null || tagId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "话题ID无效");
        }

        try {
            List<Object> posts = tagService.getTagPosts(tagId, current, size, sortBy);
            log.info("获取话题帖子成功 - tagId: {}, count: {}", tagId, posts.size());
            return ResultUtils.success(posts);

        } catch (Exception e) {
            log.error("获取话题帖子失败 - tagId: {}, error: {}", tagId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取话题帖子失败");
        }
    }
}
