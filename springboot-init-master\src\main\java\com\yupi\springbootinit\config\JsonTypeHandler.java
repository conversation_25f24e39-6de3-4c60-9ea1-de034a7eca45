package com.yupi.springbootinit.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * JSON类型处理器
 */
@MappedTypes({List.class, Map.class})
public class JsonTypeHandler<T> extends BaseTypeHandler<T> {

    private static final ObjectMapper MAPPER = new ObjectMapper();
    private final Class<T> type;

    public JsonTypeHandler(Class<T> type) {
        if (type == null) {
            throw new IllegalArgumentException("Type argument cannot be null");
        }
        this.type = type;
    }
    
    public JsonTypeHandler() {
        this.type = null;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        try {
            ps.setString(i, MAPPER.writeValueAsString(parameter));
        } catch (JsonProcessingException e) {
            throw new SQLException("Error converting object to JSON", e);
        }
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJson(rs.getString(columnName));
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJson(rs.getString(columnIndex));
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJson(cs.getString(columnIndex));
    }

    @SuppressWarnings("unchecked")
    private T parseJson(String json) {
        if (json == null) {
            return null;
        }
        try {
            if (type == null) {
                // 如果type为null，尝试作为普通Object返回
                return (T) MAPPER.readValue(json, Object.class);
            } else if (type.equals(List.class)) {
                // 如果type是List，使用TypeReference处理泛型擦除问题
                return (T) MAPPER.readValue(json, new TypeReference<List<Object>>() {});
            } else if (type.equals(Map.class)) {
                // 如果type是Map，使用TypeReference处理泛型擦除问题
                return (T) MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {});
            } else {
                // 使用具体类型
                return MAPPER.readValue(json, type);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error parsing JSON", e);
        }
    }
} 