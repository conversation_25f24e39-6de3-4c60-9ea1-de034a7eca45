<template>
  <view class="comment-item">
    <u-avatar :src="comment.userAvatar" size="36" @click="goUserProfile"></u-avatar>
    
    <view class="comment-content">
      <view class="comment-header">
        <text class="comment-username">{{ comment.username }}</text>
        <text class="comment-time">{{ formatTime(comment.createTime) }}</text>
      </view>
      
      <text class="comment-text">{{ comment.content }}</text>
      
      <!-- 回复列表 -->
      <view v-if="comment.replies && comment.replies.length" class="replies">
        <view 
          v-for="reply in comment.replies" 
          :key="reply.id"
          class="reply-item"
          @click="replyToReply(reply)"
        >
          <text class="reply-user">{{ reply.username }}</text>
          <text v-if="reply.replyToUser" class="reply-to">回复 {{ reply.replyToUser }}</text>
          <text class="reply-text">: {{ reply.content }}</text>
        </view>
        
        <!-- 查看更多回复 -->
        <view 
          v-if="comment.totalReplies > comment.replies.length" 
          class="more-replies"
          @click="loadMoreReplies"
        >
          <text class="more-text">
            查看更多 {{ comment.totalReplies - comment.replies.length }} 条回复
          </text>
          <u-icon name="arrow-down" size="12" color="#2979ff"></u-icon>
        </view>
      </view>

      <view class="comment-actions">
        <view class="comment-action" @click="toggleLike">
          <u-icon 
            :name="comment.isLiked ? 'heart-fill' : 'heart'" 
            :color="comment.isLiked ? '#ff4757' : '#999'"
            size="16"
          ></u-icon>
          <text class="action-count">{{ comment.likeCount || '' }}</text>
        </view>
        
        <view class="comment-action" @click="replyComment">
          <u-icon name="chat" color="#999" size="16"></u-icon>
          <text class="action-text">回复</text>
        </view>
        
        <view class="comment-action" @click="showMoreActions">
          <u-icon name="more-dot-fill" color="#999" size="16"></u-icon>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CommentItem',
  props: {
    comment: {
      type: Object,
      required: true
    }
  },
  methods: {
    formatTime(time) {
      const now = new Date()
      const diff = now - new Date(time)
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      return `${days}天前`
    },

    toggleLike() {
      this.comment.isLiked = !this.comment.isLiked
      this.comment.likeCount += this.comment.isLiked ? 1 : -1
      this.$emit('like', this.comment)
    },

    replyComment() {
      this.$emit('reply', this.comment)
    },

    replyToReply(reply) {
      this.$emit('reply-to-reply', {
        comment: this.comment,
        reply: reply
      })
    },

    goUserProfile() {
      this.$emit('user-click', this.comment)
    },

    loadMoreReplies() {
      this.$emit('load-more-replies', this.comment)
    },

    showMoreActions() {
      this.$emit('more-actions', this.comment)
    }
  }
}
</script>

<style lang="scss" scoped>
.comment-item {
  display: flex;
  margin-bottom: 16px;
}

.comment-content {
  flex: 1;
  margin-left: 12px;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.comment-username {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 8px;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-text {
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.replies {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 8px 12px;
  margin-bottom: 8px;
}

.reply-item {
  margin-bottom: 4px;
  line-height: 1.4;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-user {
  font-size: 13px;
  color: #2979ff;
  font-weight: 500;
}

.reply-to {
  font-size: 13px;
  color: #666;
}

.reply-text {
  font-size: 13px;
  color: #333;
}

.more-replies {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e4e7ed;
}

.more-text {
  font-size: 12px;
  color: #2979ff;
  margin-right: 4px;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.comment-action {
  display: flex;
  align-items: center;
}

.action-count, .action-text {
  font-size: 12px;
  color: #999;
  margin-left: 4px;
}
</style>
