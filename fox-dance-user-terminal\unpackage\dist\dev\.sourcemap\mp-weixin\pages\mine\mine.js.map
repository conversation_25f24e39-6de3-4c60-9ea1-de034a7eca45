{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/mine.vue?9a24", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/mine.vue?1fbf", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/mine.vue?d8a8", "uni-app:///pages/mine/mine.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/mine.vue?b80d", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/mine.vue?14ab"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "loding", "isLogined", "navBg", "zsewmToggle", "userInfo", "avatar", "train_count", "train_day", "train_time", "score", "imgbaseUrl", "qj<PERSON>ton", "onPageScroll", "scrollTop", "e", "onLoad", "uni", "onShow", "console", "methods", "userData", "title", "that", "imgUploadTap", "url", "success", "filePath", "fail", "icon", "mask", "duration", "userReportTap", "navTo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAotB,CAAgB,msBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACoHxuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA,IACAC,YACAC,EADAD;IAEA;IACA;EACA;EACAE;IACA;IACA;IACA;IACAC;EACA;EACAC;IAEAD;IACA;IACA;IACA;IACA;MACA;MACAE;IACA;MACA;IACA;IAEA;MACAF;IACA;IACA;MACA;IACA;EACA;EACAG;IACA;IACAC;MACAJ;QACAK;MACA;MACA;MACA;QACAH;QACA;UACAI;UACAA;UACAN;QACA;MACA;IACA;IACA;IACAO;MACA;MACAP;QACAK;MACA;MACA;MACAL;QACAQ;QACAC;UACAT;YACAU;YACAD;cACAT;cACAA;gBACAK;cACA;YACA;YACAM;cACAX;cACAE;cACAF;gBACAY;gBACAC;gBACAR;gBAAA;gBACAS;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACAf;UACAY;UACAP;QACA;QACA;QACAL;UACAQ;QACA;QACA;QACA;MACA;MACAR;QACAQ;MACA;MACA;AACA;AACA;AACA;IACA;IACAQ;MACA;QACAhB;UACAQ;QACA;QACA;MACA;MACA;MACA;QACAR;UACAY;UACAP;QACA;QACA;QACAL;UACAQ;QACA;QACA;MACA;QACAR;UACAQ;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9QA;AAAA;AAAA;AAAA;AAAm3C,CAAgB,8wCAAG,EAAC,C;;;;;;;;;;;ACAv4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/mine.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/mine.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mine.vue?vue&type=template&id=dcbcfe34&scoped=true&\"\nvar renderjs\nimport script from \"./mine.vue?vue&type=script&lang=js&\"\nexport * from \"./mine.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mine.vue?vue&type=style&index=0&id=dcbcfe34&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"dcbcfe34\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/mine.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=template&id=dcbcfe34&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-navbar/u-navbar\" */ \"@/components/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var a0 = _vm.loding\n    ? {\n        background: \"rgba(255,255, 255,\" + _vm.navBg + \")\",\n      }\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.zsewmToggle = !_vm.zsewmToggle\n    }\n    _vm.e1 = function ($event) {\n      _vm.zsewmToggle = !_vm.zsewmToggle\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        a0: a0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"user\" :style=\"{ '--qjbutton-color': qjbutton }\" v-if=\"loding\">\r\n\t\t<u-navbar :is-back=\"false\" title=\"个人中心\" :background=\"{ background: 'rgba(255,255, 255,' + navBg + ')' }\"\r\n\t\t\t:border-bottom=\"false\" :title-color=\"navBg==1?'#333':'#333' \" title-size=\"32\">\r\n\t\t</u-navbar>\r\n\t\t\r\n\t\t<view class=\"min_one\">\r\n\t\t\t\r\n\t\t\t<image src=\"/static/images/toux.png\" mode=\"aspectFill\" class=\"min_one_l\" style=\"border:1px solid #e4b2b2\" v-if=\"!isLogined\"></image>\r\n\t\t\t<image :src=\"userInfo.avatar == '' ? '/static/images/toux.png' : imgbaseUrl + userInfo.avatar\" mode=\"aspectFill\" class=\"min_one_l\" style=\"border:1px solid #e4b2b2\" v-if=\"isLogined\"></image>\r\n\t\t\t<view class=\"min_one_c\" @click=\"navTo('/pages/mine/editinformation')\">\r\n\t\t\t\t<view class=\"min_one_c_a\" v-if=\"isLogined\">{{userInfo.nickname == '' ? '微信昵称' : userInfo.nickname}}<image src=\"/static/images/icon1.png\" v-if=\"userInfo.is_member > 0\"></image></view>\r\n\t\t\t\t<view class=\"min_one_c_b\" v-if=\"isLogined\">今天是FOX陪伴你的第<text>{{userInfo.day}}</text>天</view>\r\n\t\t\t\t<view class=\"min_one_c_a\" v-else>登录体验更多功能</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"min_one_r\" v-if=\"isLogined\" @click=\"zsewmToggle = !zsewmToggle\">\r\n\t\t\t\t<image src='/static/images/icon2.png'></image>\r\n\t\t\t\t<image src='/static/images/index_shop_more.png'></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"min_two\">\r\n\t\t\t<view class=\"min_two_li\">\r\n\t\t\t\t<view>{{isLogined ? userInfo.train_count : '0'}}次</view>\r\n\t\t\t\t<view>训练次数</view>\r\n\t\t\t\t<text></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"min_two_li\">\r\n\t\t\t\t<view>{{isLogined ? userInfo.train_day : '0'}}天</view>\r\n\t\t\t\t<view>训练天数</view>\r\n\t\t\t\t<text></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"min_two_li\">\r\n\t\t\t\t<view>{{isLogined ? userInfo.train_time : '0'}}小时</view>\r\n\t\t\t\t<view>训练时长</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"min_thr\">\r\n\t\t\t<view class=\"min_thr_l\" @click=\"navTo('/pages/mine/memberCard/myMemberCard?avatar=' + userInfo.avatar)\">\r\n\t\t\t\t<view class=\"min_thr_l_t\">\r\n\t\t\t\t\t<view><text>我的会员</text><text style=\"opacity:0;\"></text></view>\r\n\t\t\t\t\t<image src=\"/static/images/icon6.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<image src=\"/static/images/icon3.png\" class=\"min_thr_l_b\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"min_thr_r\">\r\n\t\t\t\t<view class=\"min_thr_r_t\" @click=\"navTo('/pages/mine/order/order')\">\r\n\t\t\t\t\t<view class=\"min_thr_l_t\">\r\n\t\t\t\t\t\t<view><text>我的订单</text><text style=\"opacity:0;\"></text></view>\r\n\t\t\t\t\t\t<image src=\"/static/images/icon6.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"/static/images/icon4.png\" class=\"min_thr_l_b\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"min_thr_r_t\" @click=\"navTo('/pages/mine/myCourse/myCourse')\">\r\n\t\t\t\t\t<view class=\"min_thr_l_t\">\r\n\t\t\t\t\t\t<view><text>我的课程</text><text style=\"opacity:0;\"></text></view>\r\n\t\t\t\t\t\t<image src=\"/static/images/icon6.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"/static/images/icon5.png\" class=\"min_thr_l_b\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- <view @click=\"navTo('/pages/Schedule/confirmOrder?id=1&storeid=1')\">测试</view> -->\r\n\t\t<view class=\"min_fou\">\r\n\t\t\t<!-- <view class=\"min_fou_l\">\r\n\t\t\t\t<view class=\"min_fou_l_t\"><view>我的积分</view><text>兑好礼</text></view>\r\n\t\t\t\t<view class=\"min_fou_l_b\" @click=\"navTo('/pages/mine/integral')\">\r\n\t\t\t\t\t<view class=\"min_fou_l_b_a\">当前积分</view>\r\n\t\t\t\t\t<view class=\"min_fou_l_b_b\">{{isLogined ? userInfo.score : 0}}</view>\r\n\t\t\t\t\t<view class=\"min_fou_l_b_c\">查看详情</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"min_fou_l\" style=\"width: 100%;\">\r\n\t\t\t\t<view class=\"min_fou_l_t\"><view>邀请有礼</view></view>\r\n\t\t\t\t<view class=\"min_fou_r_b\" @click=\"navTo('/pages/mine/invitation')\">\r\n\t\t\t\t\t<view class=\"min_fou_r_b_a\">邀请新用户</view>\r\n\t\t\t\t\t<view class=\"min_fou_r_b_b\">超多福利等你来拿!</view>\r\n\t\t\t\t\t<view class=\"min_fou_r_b_c\">立即邀请</view>\r\n\t\t\t\t\t<image src=\"/static/images/icon15.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"min_five\">\r\n\t\t\t<view class=\"min_five_t\">更多功能</view>\r\n\t\t\t<view class=\"min_five_b\"> \r\n\t\t\t\t<view @click=\"userReportTap\"><image src=\"/static/images/icon7.png\"></image><text>用户报告</text></view>\r\n\t\t\t\t<view @click=\"navTo('/pages/mine/lessonPackage/lessonPackage')\"><image src=\"/static/images/icon8.png\"></image><text>我的课包</text></view>\r\n\t\t\t\t<view @click=\"navTo('/pages/mine/couponbag')\"><image src=\"/static/images/icon9.png\"></image><text>我的券包</text></view>\r\n\t\t\t\t<view @click=\"navTo('/pages/mine/ranking')\"><image src=\"/static/images/icon10.png\"></image><text>排行榜</text></view>\r\n\t\t\t\t<view @click=\"navTo('/pages/mine/messageCenter')\"><image src=\"/static/images/icon11.png\"></image><text>消息中心</text></view>\r\n\t\t\t\t<!-- <view><image src=\"/static/images/icon12.png\"></image><text>联系客服</text><button open-type=\"contact\"></button></view> -->\r\n\t\t\t\t<view @click=\"navTo('/pages/mine/leave/leave')\"><image src=\"/static/images/icon13.png\"></image><text>请假</text></view>\r\n\t\t\t\t<view @click=\"navTo('/pages/mine/settings/settings')\"><image src=\"/static/images/icon14.png\"></image><text>设置</text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view style=\"width: 100%;height:220rpx;\"></view>\r\n\t\t\r\n\t\t<!-- 专属个人码 go -->\r\n\t\t<view class=\"gg_rgba\" v-if=\"zsewmToggle\"></view>\r\n\t\t<view class=\"min_ewm\" v-if=\"zsewmToggle\">\r\n\t\t\t<view class=\"min_ewm_t\">专属个人码<image src=\"/static/images/icon16.png\" @click=\"zsewmToggle = !zsewmToggle\"></image></view>\r\n\t\t\t<!-- <image :src=\"imgbaseUrl + userInfo.share\" class=\"min_ewm_b\"></image> -->\r\n\t\t\t<image :src=\"userInfo.share\" class=\"min_ewm_b\"></image>\r\n\t\t\t<view class=\"min_ewm_c\" @click=\"imgUploadTap\">保存</view>\r\n\t\t</view>\r\n\t\t<!-- 专属个人码 end -->\r\n\t\t\r\n\t\t<tabbar ref=\"tabbar\" :current=\"4\"></tabbar>\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tuserInfoApi,\r\n} from '@/config/http.achieve.js'\r\nimport tabbar from '@/components/tabbar.vue'\r\nexport default {\r\n\tcomponents: {\r\n\t\ttabbar,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloding:false,\r\n\t\t\tisLogined:true,\r\n\t\t\tnavBg:'',\r\n\t\t\tzsewmToggle:false,//专属二维码\r\n\t\t\tuserInfo:{\r\n\t\t\t\tavatar:'',\r\n\t\t\t\ttrain_count:0,\r\n\t\t\t\ttrain_day:0,\r\n\t\t\t\ttrain_time:0,\r\n\t\t\t\tscore:0,\r\n\t\t\t},\r\n\t\t\timgbaseUrl:'',//图片地址\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonPageScroll(e) {\r\n\t\tconst top = uni.upx2px(100)\r\n\t\tconst {\r\n\t\t\tscrollTop\r\n\t\t} = e\r\n\t\tlet percent = scrollTop / top > 1 ? 1 : scrollTop / top\r\n\t\tthis.navBg = percent\r\n\t},\r\n\tonLoad() {\r\n\t\t// var xx = 0.0099999997\r\n\t\t// console.log(xx.toFixed(2)*1,'测试')\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tuni.hideTabBar()\r\n\t},\r\n\tonShow() {\r\n\t\t\r\n\t\tuni.hideTabBar()\r\n\t\t// uni.setStorageSync('token','7cf9abb4-a9cd-4fcf-a466-a30a9389bd71')\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t\tif(this.isLogined){\r\n\t\t\tthis.userData();//个人信息\r\n\t\t\tconsole.log(this.$baseUrl,'this.$baseUrl')\r\n\t\t}else{\r\n\t\t\tthis.loding = true;\r\n\t\t}\r\n\t\t\r\n\t\tif(uni.getStorageSync('selectCards')){\r\n\t\t\tuni.removeStorageSync('selectCards')\r\n\t\t}\r\n\t\tif(this.$refs.tabbar){\r\n\t\t\tthis.$refs.tabbar.setColor();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t//个人信息\r\n\t\tuserData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tuserInfoApi({}).then(res => {\r\n\t\t\t\tconsole.log('个人中心',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tthat.userInfo = res.data;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//保存图片\r\n\t\timgUploadTap(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\t// this.imgbaseUrl + \r\n\t\t\tuni.downloadFile({\r\n\t\t\t  url: this.userInfo.share,\r\n\t\t\t  success: res => {\r\n\t\t\t    uni.saveImageToPhotosAlbum({\r\n\t\t\t      filePath: res.tempFilePath,\r\n\t\t\t      success() {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t        uni.showToast({\r\n\t\t\t          title: '保存成功'\r\n\t\t\t        })\r\n\t\t\t      },\r\n\t\t\t\t  fail: function(e) {\r\n\t\t\t\t  \tuni.hideLoading();\r\n\t\t\t\t  \tconsole.log(e, '保存失败');\r\n\t\t\t\t  \tuni.showToast({\r\n\t\t\t\t  \t\ticon: 'none',\r\n\t\t\t\t  \t\tmask: true,\r\n\t\t\t\t  \t\ttitle: '保存失败', //保存路径\r\n\t\t\t\t  \t\tduration: 3000\r\n\t\t\t\t  \t});\r\n\t\t\t\t  }\r\n\t\t\t    })\r\n\t\t\t  }\r\n\t\t\t})\r\n\t\t},\r\n\t\t//用户报告跳转\r\n\t\tuserReportTap(){\r\n\t\t\tif(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\t//setTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t//},1000)\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/pages/mine/userReport/userReport'\r\n\t\t\t})\r\n\t\t\t/*var type = 0\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:type == 0 ? '/pages/mine/userReport/weeksUserReport?id=' + item.id : type == 1 ? '/pages/mine/userReport/monthUserReport?id=' + item.id : type == 2 ? '/pages/mine/userReport/yearsUserReport?id=' + item.id : ''\r\n\t\t\t})*/\r\n\t\t},\r\n\t\tnavTo(url,ismd){\r\n\t\t\tif(ismd){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:url\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tif(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\t//setTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t//},1000)\r\n\t\t\t}else{\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:url\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\npage{background-image: linear-gradient(130deg,#E3D5D4,#F6F6F6);padding-bottom: 0;background-color: transparent!important;}\r\n// page{background-image: linear-gradient(130deg,red,yellow);}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=style&index=0&id=dcbcfe34&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=style&index=0&id=dcbcfe34&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752890467906\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}