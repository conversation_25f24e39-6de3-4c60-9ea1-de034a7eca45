<template>
	<view class="myMemberCard">
		<view class="mucars_one">
			<view class="mucars_one_li">
				<picker @change="bindPickerChange_lb" :value="index_lb" :range="array_lb">
					<view class="uni-input">{{array_lb[index_lb]}}<text></text></view>
				</picker>
			</view>
			<view class="mucars_one_li">
				<picker @change="bindPickerChange_md" :value="index_md" :range="array_md">
					<view class="uni-input">{{array_md[index_md]}}<text></text></view>
				</picker>
			</view>
		</view>
		<view class="mycards_two">
			<view class="mycards_two_li" v-for="(item,index) in cardsLists" :key="index" @click="navTo('/pages/mine/memberCard/myMemberCardxq')">
				<view class="mycards_two_li_t">
					<image src="/static/images/toux.png" mode="aspectFit" class="mycards_two_li_t_l"></image>
					<view class="mycards_two_li_t_r"><view>会员ID:123123</view><text>次卡：2024.07.27到期</text></view>
				</view>
				<view class="mycards_two_li_t_b">选择</view>
				<view class="mycards_two_li_t_zt">剩余2次</view>
			</view>
		</view>
	</view>
</template>


<script>
import tabbar from '@/components/tabbar.vue'
export default {
	components: {
		tabbar,
	},
	data() {
		return {
			isLogined:true,
			navBg:'',
			zsewmToggle:false,//专属二维码
			cardsLists:[
				{},
				{},
				{},
				{},
				{},
			],
			array_lb: ['卡种类别', '类别1', '类别2', '类别3'],//卡种类别
			index_lb: 0,//卡种类别
			array_md: ['适用门店', '门店1', '门店2', '门店3'],//适用门店
			index_md: 0,//适用门店
		}
	},
	onPageScroll(e) {
		const top = uni.upx2px(100)
		const {
			scrollTop
		} = e
		let percent = scrollTop / top > 1 ? 1 : scrollTop / top
		this.navBg = percent
	},
	onShow() {
		// this.isLogined = uni.getStorageSync('token') ? true : false;
		if(this.isLogined){
			// this.userData();//个人信息
		}
	},
	methods: {
		//卡种类别
		bindPickerChange_lb: function(e) {
			console.log('picker发送选择改变，携带值为', e.detail.value)
			this.index_lb = e.detail.value
		},
		//适用门店
		bindPickerChange_md: function(e) {
			console.log('picker发送选择改变，携带值为', e.detail.value)
			this.index_md = e.detail.value
		},
		navTo(url){
			if(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
			}else{
				uni.navigateTo({
					url:url
				})
			}
		},
	}
}
</script>

<style scoped lang="scss">
	
</style>