package com.yupi.springbootinit.controller;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.config.CosClientConfig;
import com.yupi.springbootinit.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 社交模块文件上传接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/upload")
@Slf4j
@Api(tags = "社交模块文件上传接口")
public class SocialFileController {

    @Resource
    private COSClient cosClient;

    @Resource
    private CosClientConfig cosClientConfig;

    /**
     * 上传用户头像
     */
    @PostMapping("/avatar")
    @ApiOperation(value = "上传用户头像")
    public BaseResponse<String> uploadAvatar(@RequestParam("file") MultipartFile file,
                                           HttpServletRequest request) {
        log.info("接收到头像上传请求，文件名: {}, 大小: {}", file.getOriginalFilename(), file.getSize());

        // 验证文件
        validateImageFile(file, 5 * 1024 * 1024); // 头像限制5MB

        try {
            String fileName = generateFileName("avatar", getFileExtension(file.getOriginalFilename()));
            String fileUrl = uploadToCos(file, fileName);
            
            log.info("头像上传成功，URL: {}", fileUrl);
            return ResultUtils.success(fileUrl);
            
        } catch (Exception e) {
            log.error("头像上传失败", e);
            return ResultUtils.error(ErrorCode.OPERATION_ERROR, "头像上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传帖子图片
     */
    @PostMapping("/post-image")
    @ApiOperation(value = "上传帖子图片")
    public BaseResponse<String> uploadPostImage(@RequestParam("file") MultipartFile file,
                                              HttpServletRequest request) {
        log.info("接收到帖子图片上传请求，文件名: {}, 大小: {}", file.getOriginalFilename(), file.getSize());

        // 验证文件
        validateImageFile(file, 10 * 1024 * 1024); // 帖子图片限制10MB

        try {
            String fileName = generateFileName("posts", getFileExtension(file.getOriginalFilename()));
            String fileUrl = uploadToCos(file, fileName);
            
            log.info("帖子图片上传成功，URL: {}", fileUrl);
            return ResultUtils.success(fileUrl);
            
        } catch (Exception e) {
            log.error("帖子图片上传失败", e);
            return ResultUtils.error(ErrorCode.OPERATION_ERROR, "帖子图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传帖子图片
     */
    @PostMapping("/post-images")
    @ApiOperation(value = "批量上传帖子图片")
    public BaseResponse<List<String>> uploadPostImages(@RequestParam("files") MultipartFile[] files,
                                                     HttpServletRequest request) {
        log.info("接收到批量帖子图片上传请求，文件数量: {}", files.length);

        if (files.length > 9) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "最多只能上传9张图片");
        }

        List<String> fileUrls = new ArrayList<>();
        
        try {
            for (MultipartFile file : files) {
                // 验证每个文件
                validateImageFile(file, 10 * 1024 * 1024);
                
                String fileName = generateFileName("posts", getFileExtension(file.getOriginalFilename()));
                String fileUrl = uploadToCos(file, fileName);
                fileUrls.add(fileUrl);
            }
            
            log.info("批量帖子图片上传成功，数量: {}", fileUrls.size());
            return ResultUtils.success(fileUrls);
            
        } catch (Exception e) {
            log.error("批量帖子图片上传失败", e);
            return ResultUtils.error(ErrorCode.OPERATION_ERROR, "批量图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传聊天图片
     */
    @PostMapping("/chat-image")
    @ApiOperation(value = "上传聊天图片")
    public BaseResponse<String> uploadChatImage(@RequestParam("file") MultipartFile file,
                                              HttpServletRequest request) {
        log.info("接收到聊天图片上传请求，文件名: {}, 大小: {}", file.getOriginalFilename(), file.getSize());

        // 验证文件
        validateImageFile(file, 5 * 1024 * 1024); // 聊天图片限制5MB

        try {
            String fileName = generateFileName("chat", getFileExtension(file.getOriginalFilename()));
            String fileUrl = uploadToCos(file, fileName);
            
            log.info("聊天图片上传成功，URL: {}", fileUrl);
            return ResultUtils.success(fileUrl);
            
        } catch (Exception e) {
            log.error("聊天图片上传失败", e);
            return ResultUtils.error(ErrorCode.OPERATION_ERROR, "聊天图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile file, long maxSize) {
        if (file.isEmpty()) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "文件不能为空");
        }

        if (file.getSize() > maxSize) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, 
                "文件大小不能超过" + (maxSize / 1024 / 1024) + "MB");
        }

        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "文件名不能为空");
        }

        String fileExtension = getFileExtension(originalFilename);
        if (!isValidImageExtension(fileExtension)) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "只支持jpg、jpeg、png、gif、webp格式的图片");
        }
    }

    /**
     * 上传文件到腾讯云COS
     */
    private String uploadToCos(MultipartFile file, String fileName) throws IOException {
        try {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());
            
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    cosClientConfig.getBucket(), 
                    fileName, 
                    file.getInputStream(), 
                    metadata
            );
            
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            log.debug("COS上传结果: {}", putObjectResult.getETag());
            
            return cosClientConfig.getBaseUrl() + "/" + fileName;
            
        } catch (Exception e) {
            log.error("COS上传失败", e);
            throw new RuntimeException("COS上传失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一文件名
     */
    private String generateFileName(String category, String fileExtension) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String datePath = sdf.format(new Date());
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return "social/" + category + "/" + datePath + "/" + uuid + "." + fileExtension;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 验证是否为有效的图片扩展名
     */
    private boolean isValidImageExtension(String extension) {
        String[] validExtensions = {"jpg", "jpeg", "png", "gif", "webp"};
        return Arrays.asList(validExtensions).contains(extension);
    }
}
