<template>
	<view class="editinformation" :style="{ '--qjbutton-color': qjbutton }" v-if="loding">
		
		<view class="edi_ban"><image :src="baseUrl_admin + '/static/images/icon23.jpg'"></image></view>
		<view class="edi_one">
			 <!-- @click="UploadImg" -->
			<image :src="avatar == '' ? '/static/images/toux.png' : imgbaseUrl + avatar" mode="aspectFill" class="edma_one_a"></image>
			<text>点击修改头像</text>
			<button class="btn" open-type="chooseAvatar" @chooseavatar="chooseAvatarsc">获取头像</button>
		</view>
		<!-- <button class="btn" open-type="chooseAvatar" @chooseavatar="chooseAvatarsc">获取头像</button> -->
		<!-- <button class="btn" open-type="getUserInfo" @getuserinfo="getuserinfo">获取头像昵称1</button> -->
		<!-- <input type="nickname" v-model="nickname" /> -->
		<view class="edma_two">
		    <view class="edma_two_li">
		        <view>昵称：</view>
		        <input type="nickname" placeholder="请输入昵称" :disabled="is_store > 0 ? true : false" v-model="nickname" maxlength="8" placeholder-style="color:#999999;font-size:28rpx" />
		    </view>
			<view class="edma_two_li">
			    <view>个人简介：</view>
			    <input type="text" placeholder="请输入个人简介" v-model="rek" placeholder-style="color:#999999;font-size:28rpx" style="width:calc(100% - 146rpx);" />
			</view>
		</view>
		
		<view class="edma_thr">
			<view @click="tjxxTap">提交修改</view>
			<view @click="logoutTap">退出登录</view>
		</view>
		
	</view>
</template>

<script>
import {
	upImg,
	userInfoApi,
	toeditUserApi
} from '@/config/http.achieve.js'

export default {
	data() {
		return {
			loding:false,
			isLogined:false,//是否登录
			isH5:false,//是否是h5
			avatar:'',//头像
			nickname:'',//昵称
			rek:'',
			imgbaseUrl:'',
			baseUrl_admin:'',
			qjbutton:'#131315',
			is_store:0,
		}
	},
	created(){
		
	},
	onLoad(options) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.userData();//个人信息
		this.imgbaseUrl = this.$baseUrl;
		this.baseUrl_admin = this.$baseUrl_admin;
	},
	methods: {
		//获取头像昵称
		getuserinfo(event){
			console.log(event,'获取头像昵称')
		},
		//获取头像
		chooseAvatarsc(event){
		  // console.log(event,'event')
		  var that = this;
		  const tempFilePaths = event.detail.avatarUrl
		  uni.showLoading({
		  	title:'加载中'
		  })
		  upImg(tempFilePaths, 'file',{driver:'cos'}).then(ress => {
		  	console.log('上传图片',ress)
		  	if (ress.code == 1) {
		  		uni.hideLoading();
		  		that.avatar = ress.data.file.url
		  	}
		  })
		  
		},
		//退出登录
		logoutTap(){
			var that = this;
			uni.showModal({
				title:'温馨提示',
				content:'确定要退出登录吗？',
				success: function (rep) {
					if (rep.confirm) {
						uni.removeStorageSync('token');
						uni.removeStorageSync('userid');
						uni.showToast({
							icon:'none',
							title:'退出成功',
							duration: 2000
						});
						setTimeout(function(){
							uni.navigateBack()
						},1000)
					} else if (rep.cancel) {
						console.log('用户点击取消');
					}
				}
			});
		},
		//个人信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userInfoApi({}).then(res => {
				if (res.code == 1) {
					console.log('个人信息',res);
					that.loding = true;
					// res.data.is_store = 0
					that.is_store = res.data.is_store;
					that.avatar = res.data.avatar;
					that.nickname = res.data.nickname == '' ? '微信昵称' : res.data.nickname;
					that.rek = res.data.introduction ? res.data.introduction : '';
					uni.hideLoading();
				}
			})
		},
		// 更换头像
		UploadImg() {
			let that = this
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					const tempFilePaths = res.tempFilePaths[0]
					uni.showLoading({
						title:'加载中'
					})
					upImg(tempFilePaths, 'file', ).then(ress => {
						console.log('上传图片',ress)
						if (ress.code == 1) {
							uni.hideLoading();
							that.avatar = ress.data.file.url
						}
					})
		
				}
			})
		
		},
		//上传头像
		sctxTap(){
			// #ifdef MP-WEIXIN
			// var DeviceType = 'wxapp';
			// #endif
			
			// #ifdef H5
			// var DeviceType = 'mobile';
			// #endif
			var that = this;
			uni.chooseImage({
				count:1, //默认9
				success: (chooseImageRes) => {
					const tempFilePaths = chooseImageRes.tempFilePaths;
					uni.uploadFile({
						//url: 'https://wx.auxphen.com/api/home/<USER>/one', //仅为示例，非真实的接口地址
						url:that.imgbaseUrl + '/api/ajax/upload',
						filePath: tempFilePaths[0],
						header:{
							'server':1,
							'bausertoken': uni.getStorageSync('token'),
						},
						name: 'file',
						formData: {
							'user': 'test'
						},
						success: (uploadFileRes) => {
							console.log(uploadFileRes);
							var res = JSON.parse(uploadFileRes.data)
							if(res.code = 1){
								that.avatar = res.data.fullurl;
								console.log(res,'嘻嘻嘻哈哈123')
							}
						}
					});
				}
			});
		},
		//信息提交
		tjxxTap(){
			if (this.nickname.split(" ").join("").length == 0) {
				uni.showToast({
					icon:'none',
					title: '请输入昵称',
					duration: 2000
				});
				return false;
			}
			uni.showLoading({
				title: '加载中'
			});
			toeditUserApi({
				// userId:uni.getStorageSync('user').userId,
				avatar:this.avatar,
				nickname:this.nickname,
				introduction:this.rek
			}).then(res => {
				if (res.code == 1) {
					uni.hideLoading();
					uni.showToast({
						title:'修改成功',
						duration: 2000
					});
					setTimeout(function(){
						uni.navigateBack({})
					},1000);
				}else{
					
				}
			})
		}
	},
}
</script>

<style lang="less">
.editinformation{
	overflow:hidden;
}
</style>
