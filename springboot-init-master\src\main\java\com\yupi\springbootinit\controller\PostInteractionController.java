package com.yupi.springbootinit.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.exception.BusinessException;
import com.yupi.springbootinit.exception.ThrowUtils;

import com.yupi.springbootinit.model.vo.PostVO;
import com.yupi.springbootinit.service.PostLikeService;
import com.yupi.springbootinit.service.PostFavoriteService;
import com.yupi.springbootinit.service.PostShareService;
import com.yupi.springbootinit.service.UserService;
import com.yupi.springbootinit.service.BaUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

/**
 * 帖子互动接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/interaction")
@Slf4j
@Api(tags = "帖子互动管理")
public class PostInteractionController {

    @Resource
    private PostLikeService postLikeService;

    @Resource
    private PostFavoriteService postFavoriteService;

    @Resource
    private PostShareService postShareService;

    @Resource
    private UserService userService;

    @Resource
    private BaUserService baUserService;

    // ==================== 点赞相关接口 ====================

    /**
     * 点赞帖子
     */
    @PostMapping("/like")
    @ApiOperation(value = "点赞帖子")
    public BaseResponse<Boolean> likePost(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postLikeService.likePost(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 取消点赞帖子
     */
    @PostMapping("/unlike")
    @ApiOperation(value = "取消点赞帖子")
    public BaseResponse<Boolean> unlikePost(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postLikeService.unlikePost(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 检查是否已点赞
     */
    @GetMapping("/like/check")
    @ApiOperation(value = "检查是否已点赞")
    public BaseResponse<Boolean> checkLike(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postLikeService.isLiked(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 获取帖子点赞数
     */
    @GetMapping("/like/count")
    @ApiOperation(value = "获取帖子点赞数")
    public BaseResponse<Integer> getLikeCount(@RequestParam Long postId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        Integer result = postLikeService.getLikeCount(postId);
        return ResultUtils.success(result);
    }

    /**
     * 批量检查点赞状态
     */
    @PostMapping("/like/batch-check")
    @ApiOperation(value = "批量检查点赞状态")
    public BaseResponse<List<Long>> batchCheckLike(@RequestBody List<Long> postIds, @RequestParam Long userId) {
        ThrowUtils.throwIf(postIds == null || postIds.isEmpty(), ErrorCode.PARAMS_ERROR, "帖子ID列表不能为空");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        List<Long> result = postLikeService.getBatchLikeStatus(postIds, userId);
        return ResultUtils.success(result);
    }

    // ==================== 收藏相关接口 ====================

    /**
     * 收藏帖子
     */
    @PostMapping("/favorite")
    @ApiOperation(value = "收藏帖子")
    public BaseResponse<Boolean> favoritePost(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postFavoriteService.favoritePost(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 取消收藏帖子
     */
    @PostMapping("/unfavorite")
    @ApiOperation(value = "取消收藏帖子")
    public BaseResponse<Boolean> unfavoritePost(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postFavoriteService.unfavoritePost(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 检查是否已收藏
     */
    @GetMapping("/favorite/check")
    @ApiOperation(value = "检查是否已收藏")
    public BaseResponse<Boolean> checkFavorite(@RequestParam Long postId, @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postFavoriteService.isFavorited(postId, userId);
        return ResultUtils.success(result);
    }

    /**
     * 获取帖子收藏数
     */
    @GetMapping("/favorite/count")
    @ApiOperation(value = "获取帖子收藏数")
    public BaseResponse<Integer> getFavoriteCount(@RequestParam Long postId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        Integer result = postFavoriteService.getFavoriteCount(postId);
        return ResultUtils.success(result);
    }

    /**
     * 获取用户收藏的帖子列表
     */
    @GetMapping("/favorite/list")
    @ApiOperation(value = "获取用户收藏的帖子列表")
    public BaseResponse<Page<PostVO>> getUserFavorites(@RequestParam(defaultValue = "1") Integer current,
                                                      @RequestParam(defaultValue = "10") Integer pageSize,
                                                      @RequestParam Long userId) {
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Page<PostVO> result = postFavoriteService.getUserFavoritePosts(userId, current, pageSize);
        return ResultUtils.success(result);
    }

    /**
     * 批量检查收藏状态
     */
    @PostMapping("/favorite/batch-check")
    @ApiOperation(value = "批量检查收藏状态")
    public BaseResponse<List<Long>> batchCheckFavorite(@RequestBody List<Long> postIds, @RequestParam Long userId) {
        ThrowUtils.throwIf(postIds == null || postIds.isEmpty(), ErrorCode.PARAMS_ERROR, "帖子ID列表不能为空");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        List<Long> result = postFavoriteService.getBatchFavoriteStatus(postIds, userId);
        return ResultUtils.success(result);
    }

    // ==================== 分享相关接口 ====================

    /**
     * 分享帖子
     */
    @PostMapping("/share")
    @ApiOperation(value = "分享帖子")
    public BaseResponse<Boolean> sharePost(@RequestParam Long postId,
                                          @RequestParam Integer shareType,
                                          @RequestParam(required = false) String platform,
                                          @RequestParam Long userId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR, "帖子ID无效");
        ThrowUtils.throwIf(shareType == null || shareType <= 0, ErrorCode.PARAMS_ERROR, "分享类型无效");
        ThrowUtils.throwIf(userId == null || userId <= 0, ErrorCode.PARAMS_ERROR, "用户ID无效");

        // 验证用户是否存在
        if (baUserService.getById(userId) == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        Boolean result = postShareService.sharePost(postId, userId, shareType, platform);
        return ResultUtils.success(result);
    }

    /**
     * 获取帖子分享数
     */
    @GetMapping("/share/count")
    @ApiOperation(value = "获取帖子分享数")
    public BaseResponse<Integer> getShareCount(@RequestParam Long postId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        Integer result = postShareService.getShareCount(postId);
        return ResultUtils.success(result);
    }

    /**
     * 按类型统计分享数
     */
    @GetMapping("/share/stats")
    @ApiOperation(value = "按类型统计分享数")
    public BaseResponse<Map<Integer, Integer>> getShareStats(@RequestParam Long postId) {
        ThrowUtils.throwIf(postId == null || postId <= 0, ErrorCode.PARAMS_ERROR);
        Map<Integer, Integer> result = postShareService.getShareCountByType(postId);
        return ResultUtils.success(result);
    }
}
