{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCard.vue?6a19", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCard.vue?f6cc", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCard.vue?5e32", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCard.vue?b4ae", "uni-app:///pages/mine/memberCard/myMemberCard.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "navBg", "avatar", "zsewmToggle", "cardsLists", "page", "total_pages", "zanwsj", "status", "loadingText", "loadmoreText", "nomoreText", "onPageScroll", "scrollTop", "e", "onLoad", "onShow", "methods", "xzhykTap", "uni", "userData", "title", "console", "that", "bindPickerChange_lb", "bindPickerChange_md", "storeData", "type", "longitude", "latitude", "limit", "array_md", "cardsData", "size", "store_id", "obj", "onReachBottom", "onPullDownRefresh", "navTo", "icon", "setTimeout", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;;;AAG3D;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2uB,CAAgB,2sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC4E/vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;IAAA,oDACA,0DACA,qDAEA,wEACA,oDACA,gEACA,qDACA,iDAEA;EAEA;EACAC;IACA;IACA,IACAC,YACAC,EADAD;IAEA;IACA;EACA;EACAE;IACA;IACA;EACA;EACAC;IACA;IACA;MACA;IAAA;IAEA;IACA;IACA;IACA;IACA;IACA;EACA;;EACAC;IACA;IACAC;MACAC;MACAA;IACA;IACA;IACAC;MACAD;QACAE;MACA;MACA;MACA;QACAC;QACA;UACAC;UACAA;UACAJ;QACA;MACA;IACA;IACA;IACAK;MACAF;MACA;MACA;MACA;MACA;IACA;;IACA;IACAG;MACAH;MACA;MACA;MACA;MACA;IACA;IACA;IACAI;MACAP;QACAE;MACA;MACA;MACA;QACAM;QACAC;QACAC;QACAC;MACA;QACAR;QACA;UACA;UACA;YACAS;UACA;UACAR;UACAA;UACAJ;QACA;MACA;IACA;IACA;IACAa;MACAb;QACAE;MACA;MACA;MACA;QACAhB;QACA4B;QACAN;QACAO;MACA;QACAZ;QACA;UACA;UACA;YACA;YACAa;YACAA;UACA;UACAZ;UACAA;UACAA;UACA;UACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UACA;YACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAA;UACAJ;UACAA;QACA;MACA;IAEA;IACAiB;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACAnB;UACAoB;UACAlB;QACA;QACAmB;UACArB;YACAsB;UACA;QACA;MACA;QACAtB;UACAsB;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/mine/memberCard/myMemberCard.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/memberCard/myMemberCard.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myMemberCard.vue?vue&type=template&id=56167e78&scoped=true&\"\nvar renderjs\nimport script from \"./myMemberCard.vue?vue&type=script&lang=js&\"\nexport * from \"./myMemberCard.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"56167e78\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/memberCard/myMemberCard.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myMemberCard.vue?vue&type=template&id=56167e78&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myMemberCard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myMemberCard.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"myMemberCard\">\r\n\t\t<view class=\"mucars_one\">\r\n\t\t\t<view class=\"mucars_one_li\">\r\n\t\t\t\t<picker @change=\"bindPickerChange_lb\" :value=\"index_lb\" :range=\"array_lb\">\r\n\t\t\t\t\t<view class=\"uni-input\">{{array_lb[index_lb]}}<text></text></view>\r\n\t\t\t\t</picker> \r\n\t\t\t</view>\r\n\t\t\t<view class=\"mucars_one_li\">\r\n\t\t\t\t<picker @change=\"bindPickerChange_md\" :value=\"index_md\" :range=\"array_md\">\r\n\t\t\t\t\t<view class=\"uni-input\">{{array_md[index_md]}}<text></text></view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"mycards_thr\">\r\n\t\t\t<view class=\"mycards_thr_li\" v-for=\"(item,index) in cardsLists\" :key=\"index\" @click=\"navTo('/pages/mine/memberCard/myMemberCardxq?id=' + item.id)\">\r\n\t\t\t\t<!-- https://file.foxdance.com.cn/storage/default/20250417/3IrxlLQQb7Wc9302dcd60cd7d0cef3b399122418a1926274929a44c.jpg -->\r\n\t\t\t\t<image :src=\"imgbaseUrl + item.image\" class=\"mycards_thr_li_bj\"></image>\r\n\t\t\t\t<view class=\"mycards_thr_li_zt\" v-if=\"!xzhyk\">{{item.status == 0 ? '未激活' : item.status == 1 ? '使用中' : item.status == 2 ? '请假中' : item.status == 3 ? '已耗尽' : item.status == 4 ? '已过期' : item.status == 5 ? '已转让' : item.status == 6 ? '已退款' : item.status == 7 ? '已转卡' : ''}}</view>\r\n\t\t\t\t<view class=\"mycards_thr_li_c\">\r\n\t\t\t\t\t<view class=\"mycards_thr_li_c_l\"><image :src=\"imgbaseUrl + avatar\" mode=\"aspectFill\"></image></view>\r\n\t\t\t\t\t<view class=\"mycards_thr_li_c_r\">\r\n\t\t\t\t\t\t<view class=\"mycards_thr_li_c_r_a\">{{item.contract_name}}</view>\r\n\t\t\t\t\t\t<view class=\"mycards_thr_li_c_r_b\" v-if=\"item.type*1 == 0\">剩余<text>{{item.surplus_frequency}}</text>次</view>\r\n\t\t\t\t\t\t<view class=\"mycards_thr_li_c_r_b\" v-else>{{item.status > 0 ? item.become_time + '到期' : '未激活'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"mycards_thr_li_c_r_f\">\r\n\t\t\t\t\t<view class=\"mycards_thr_li_c_r_f_l\">使用期限:{{item.status == 0 ? '未激活' : item.activation_time + ' - ' + item.become_time}}</view>\r\n\t\t\t\t\t<view class=\"mycards_thr_li_c_r_f_r\" v-if=\"xzhyk\" @click.stop=\"xzhykTap(item)\">选择</view>\r\n\t\t\t\t\t<view class=\"mycards_thr_li_c_r_f_r\" v-else=\"xzhyk\">详情</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"mycards_two\" v-if=\"false\">\r\n\t\t\t<view class=\"mycards_two_li\" v-for=\"(item,index) in cardsLists\" :key=\"index\" @click=\"navTo('/pages/mine/memberCard/myMemberCardxq?id=' + item.id)\">\r\n\t\t\t\t<view class=\"mycards_two_li_t\">\r\n\t\t\t\t\t<image :src=\"imgbaseUrl + avatar\" mode=\"aspectFill\" class=\"mycards_two_li_t_l\"></image>\r\n\t\t\t\t\t<!-- 会员卡类型:0=次卡,1=年卡,2=月卡 -->\r\n\t\t\t\t\t<view class=\"mycards_two_li_t_r\">\r\n\t\t\t\t\t\t<view v-if=\"item.contract_name\">会员名称:{{item.contract_name}}</view>\r\n\t\t\t\t\t\t<view v-else>会员ID:{{item.out_trade_no}}</view>\r\n\t\t\t\t\t\t<text>{{item.type*1 == 0 ? '次卡' : '时长卡'}}：{{item.status > 0 ? item.become_time + '到期　' : '未激活　'}}　<template v-if=\"item.type*1 == 0\">剩余{{item.surplus_frequency}}次</template></text>\r\n\t\t\t\t\t\t<text>到期时间:{{item.status == 0 ? '未激活' : item.activation_time + ' ~ ' + item.become_time}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"mycards_two_li_t_b\" v-if=\"xzhyk\" @click.stop=\"xzhykTap(item)\">选择</view>\r\n\t\t\t\t<view class=\"mycards_two_li_t_b\" v-else=\"xzhyk\">详情</view>\r\n\t\t\t\t<!-- <view class=\"mycards_two_li_t_zt\" v-if=\"item.status == 1 && !xzhyk\">请假中</view> -->\r\n\t\t\t\t<view class=\"mycards_two_li_t_zt\" v-if=\"!xzhyk\">{{item.status == 0 ? '未激活' : item.status == 1 ? '使用中' : item.status == 2 ? '请假中' : item.status == 3 ? '已耗尽' : item.status == 4 ? '已过期' : item.status == 5 ? '已转让' : item.status == 6 ? '已退款' : item.status == 7 ? '已转卡' : ''}}</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- status：状态:0=待激活,1=使用中,2=请假中,3=已耗尽,4=已过期,5=已转让,6=已退款,7=已转卡 -->\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"mycards_two_li_t_zt\" v-if=\"xzhyk\">剩余{{item.leave_frequency}}次</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"gg_loding\" v-if=\"!zanwsj\">\r\n\t\t\t<view class=\"loader-inner ball-clip-rotate\" v-if=\"status == 'loading'\">\r\n\t\t\t\t<view></view>\r\n\t\t\t\t<text>加载中</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"gg_loding_wusj\" v-else>─── 没有更多数据了 ───</view>\r\n\t\t</view>\r\n\t\t<view class=\"gg_zwsj\" v-if=\"zanwsj\">\r\n\t\t\t<view class=\"gg_zwsj_w\">\r\n\t\t\t\t<image src=\"/static/images/wusj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<text>暂无数据</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tmyCardApi,\r\n\tstoreListsApi,\r\n\tuserInfoApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tnavBg:'',\r\n\t\t\tavatar:'',\r\n\t\t\tzsewmToggle:false,//专属二维码\r\n\t\t\tcardsLists:[],\r\n\t\t\tpage:1,//当前页数\r\n\t\t\ttotal_pages: 1,//总页数\r\n\t\t\tzanwsj:false,//是否有数据\r\n\t\t\tstatus: 'loading',//底部loding是否显示\r\n\t\t\tloadingText: '努力加载中',\r\n\t\t\tloadmoreText: '轻轻上拉',\r\n\t\t\tnomoreText: '实在没有了',\r\n\t\t\tisLogined:false,\r\n\t\t\timgbaseUrl:'',//图片地址\r\n\t\t\t\r\n\t\t\tarray_lb: ['卡种类别', '次卡', '时长卡'],//卡种类别\r\n\t\t\tindex_lb: 0,//卡种类别\r\n\t\t\tarray_md: ['适用门店'],//适用门店\r\n\t\t\tarray_md_cunc:[],////适用门店存储\r\n\t\t\tindex_md: 0,//适用门店\r\n\t\t\t\r\n\t\t\txzhyk:false,\r\n\t\t}\r\n\t},\r\n\tonPageScroll(e) {\r\n\t\tconst top = uni.upx2px(100)\r\n\t\tconst {\r\n\t\t\tscrollTop\r\n\t\t} = e\r\n\t\tlet percent = scrollTop / top > 1 ? 1 : scrollTop / top\r\n\t\tthis.navBg = percent\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.avatar = option.avatar;\r\n\t\tthis.xzhyk = option.xzhyk ? true : false;\r\n\t},\r\n\tonShow() {\r\n\t\t// this.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t\tif(this.isLogined){\r\n\t\t\t// this.userData();//个人信息\r\n\t\t}\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.page = 1;\r\n\t\tthis.cardsLists = [];\r\n\t\tthis.cardsData();//我的会员卡\r\n\t\tthis.storeData();//门店列表\r\n\t\tthis.userData();//个人信息\r\n\t},\r\n\tmethods: {\r\n\t\t//选择会员卡\r\n\t\txzhykTap(item){\r\n\t\t\tuni.setStorageSync('selectCards',item)\r\n\t\t\tuni.navigateBack({})\r\n\t\t},\r\n\t\t//个人信息\r\n\t\tuserData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tuserInfoApi({}).then(res => {\r\n\t\t\t\tconsole.log('个人中心',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tthat.avatar = res.data.avatar;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//卡种类别\r\n\t\tbindPickerChange_lb: function(e) {\r\n\t\t\tconsole.log('picker发送选择改变，携带值为', e.detail.value)\r\n\t\t\tthis.index_lb = e.detail.value\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.cardsLists = [];\r\n\t\t\tthis.cardsData();//我的会员卡\r\n\t\t},\r\n\t\t//适用门店\r\n\t\tbindPickerChange_md: function(e) {\r\n\t\t\tconsole.log('picker发送选择改变，携带值为', e.detail.value)\r\n\t\t\tthis.index_md = e.detail.value;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.cardsLists = [];\r\n\t\t\tthis.cardsData();//我的会员卡\r\n\t\t},\r\n\t\t//门店列表\r\n\t\tstoreData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tstoreListsApi({\r\n\t\t\t\ttype:1,\r\n\t\t\t\tlongitude:uni.getStorageSync('postion').longitude,\r\n\t\t\t\tlatitude:uni.getStorageSync('postion').latitude,\r\n\t\t\t\tlimit:9999,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('门店列表',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tvar array_md = ['适用门店']\r\n\t\t\t\t\tfor(var i=0;i<res.data.data.length;i++){\r\n\t\t\t\t\t\tarray_md.push(res.data.data[i].name)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.array_md = array_md;\r\n\t\t\t\t\tthat.array_md_cunc = res.data.data;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//我的会员卡\r\n\t\tcardsData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tmyCardApi({\r\n\t\t\t\tpage:that.page,\r\n\t\t\t\tsize:10,\r\n\t\t\t\ttype:that.index_lb,\r\n\t\t\t\tstore_id:that.index_md == 0 ? 0 : that.array_md_cunc[that.index_md-1].id,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('我的会员卡2',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tvar obj = res.data.data;\r\n\t\t\t\t\tfor(var i=0;i<obj.length;i++){\r\n\t\t\t\t\t\t// console.log(obj[i].activation_time.split('-')[0] + '/' + obj[i].activation_time.split('-')[1] + '/' + obj[i].activation_time.split('-')[2],'sss')\r\n\t\t\t\t\t\tobj[i].activation_time = obj[i].activation_time.split('-')[0] + '/' + obj[i].activation_time.split('-')[1] + '/' + obj[i].activation_time.split('-')[2]\r\n\t\t\t\t\t\tobj[i].become_time = obj[i].become_time.split('-')[0] + '/' + obj[i].become_time.split('-')[1] + '/' + obj[i].become_time.split('-')[2]\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.cardsLists = that.cardsLists.concat(obj);\r\n\t\t\t\t\tthat.zanwsj = that.cardsLists.length ? true : false;\r\n\t\t\t\t\tthat.page++;\r\n\t\t\t\t\t// that.total_pages = Math.ceil(res.total/20);\r\n\t\t\t\t\tthat.total_pages = res.data.last_page;\r\n\t\t\t\t\tif (that.page != 1) {\r\n\t\t\t\t\t\tif (that.total_pages >= that.page) {\r\n\t\t\t\t\t\t\tthat.status = 'loading'\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.cardsLists.length == 0){\r\n\t\t\t\t\t\tthat.zanwsj = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.zanwsj = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(res.data.total*1<=10){\r\n\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\t// console.log('到底了');\r\n\t\t\tif (this.page != 1) {\r\n\t\t\t\tif (this.total_pages >= this.page) {\r\n\t\t\t\t\tthis.cardsData();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t    // console.log('我被下拉了');\r\n\t\t    this.page = 1;\r\n\t\t\tthis.cardsLists = [];\r\n\t\t\tthis.cardsData();//我的会员卡\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tif(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t}else{\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:url\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t\r\n</style>"], "sourceRoot": ""}