import $commentHttp from './comment.http'

// 评论功能API服务
const commentApi = {
  /**
   * 获取评论列表
   * @param {Object} params - 请求参数
   * @param {Long} params.userId - 当前用户ID
   * @param {Long} params.contentId - 内容ID
   * @param {string} params.filter - 排序方式：hot(最热)、new(最新)、my(我的)
   * @param {number} params.current - 页码
   * @param {number} params.pageSize - 每页条数
   * @returns {Promise} 请求Promise对象
   */
  getCommentList(params) {
    return $commentHttp({
      url: '/api/comments',
      method: 'get',
      data: params
    })
  },

  /**
   * 获取评论详情
   * @param {string} commentId - 评论ID
   * @param {Object} params - 请求参数
   * @param {Long} params.userId - 当前用户ID
   * @param {string} params.sort - 排序方式：hot(最热)、new(最新)
   * @param {number} params.current - 页码
   * @param {number} params.pageSize - 每页条数
   * @returns {Promise} 请求Promise对象
   */
  getCommentDetail(commentId, params) {
    return $commentHttp({
      url: `/api/comments/${commentId}`,
      method: 'get',
      data: params
    })
  },

  /**
   * 发表评论
   * @param {Object} data - 请求数据
   * @param {Long} data.userId - 当前用户ID
   * @param {Long} data.contentId - 内容ID
   * @param {string} data.contentType - 内容类型
   * @param {string} data.content - 评论内容
   * @returns {Promise} 请求Promise对象
   */
  postComment(data) {
    return $commentHttp({
      url: '/api/comments',
      method: 'post',
      data
    })
  },

  /**
   * 回复评论
   * @param {string} commentId - 评论ID
   * @param {Object} data - 请求数据
   * @param {Long} data.userId - 当前用户ID
   * @param {string} data.content - 回复内容
   * @param {Long} data.replyToId - 被回复的用户ID，直接回复评论则为null
   * @returns {Promise} 请求Promise对象
   */
  replyComment(commentId, data) {
    return $commentHttp({
      url: `/api/comments/${commentId}/replies`,
      method: 'post',
      data
    })
  },

  /**
   * 点赞/取消点赞评论
   * @param {string} commentId - 评论ID
   * @param {Object} data - 请求数据
   * @param {Long} data.userId - 当前用户ID
   * @param {string} data.action - "like"表示点赞，"unlike"表示取消点赞
   * @returns {Promise} 请求Promise对象
   */
  likeComment(commentId, data) {
    return $commentHttp({
      url: `/api/comments/${commentId}/like`,
      method: 'post',
      data
    })
  },

  /**
   * 点赞/取消点赞回复
   * @param {string} replyId - 回复ID
   * @param {Object} data - 请求数据
   * @param {Long} data.userId - 当前用户ID
   * @param {string} data.action - "like"表示点赞，"unlike"表示取消点赞
   * @returns {Promise} 请求Promise对象
   */
  likeReply(replyId, data) {
    return $commentHttp({
      url: `/api/comments/replies/${replyId}/like`,
      method: 'post',
      data
    })
  },

  /**
   * 删除评论
   * @param {string} commentId - 评论ID
   * @param {Object} data - 请求数据
   * @param {Long} data.userId - 当前用户ID，必须是评论的发布者
   * @returns {Promise} 请求Promise对象
   */
  deleteComment(commentId, data) {
    return $commentHttp({
      url: `/api/comments/${commentId}`,
      method: 'delete',
      data
    })
  },

  /**
   * 删除回复
   * @param {string} replyId - 回复ID
   * @param {Object} data - 请求数据
   * @param {Long} data.userId - 当前用户ID，必须是回复的发布者
   * @returns {Promise} 请求Promise对象
   */
  deleteReply(replyId, data) {
    return $commentHttp({
      url: `/api/comments/replies/${replyId}`,
      method: 'delete',
      data
    })
  }
}

export default commentApi 