<template>
	<view class="feedback" :style="{ '--qjbutton-color': qjbutton }">
		
		<view class="fee_one fee_one_yi">
			<view class="fee_one_t">反馈类型</view>
			<view class="fee_one_b">
				<view class="fee_one_b_li" :class="type == index ? 'fee_one_b_li_ac' : ''" @click="tabTap(index)" v-for="(item,index) in feedLists" :key="index">
					{{item}}
				</view>
			</view>
		</view>
		
		<view class="fee_one fee_two">
			<view class="fee_one_t">反馈内容</view>
			<textarea placeholder="问题描述的越详细，有助于我们更快的解决问题" placeholder-style="color: #999999;" v-model="contents"></textarea>
			<view class="fee_two_tp">
				<view class="fee_two_tp_li" v-for="(item,index) in uploadimgs" :key="index">
					<image :src="imgbaseUrl + item" mode="aspectFill" class="fee_two_tp_li_tp"></image>
					<image src="/static/images/icon49-1.png" class="fee_two_tp_li_gb" @click="uploadImgDel(index)"></image>
				</view>
				<view class="fee_two_tp_li" @click="uploadImgTap">
					<image src="/static/images/icon50.png" class="fee_two_tp_li_tp"></image>
				</view>
			</view>
		</view>
		
		
		<view class="fee_one fee_thr">
			<view class="fee_one_t">请留下您的联系方式</view>
			<input type="text" placeholder="手机号/邮箱/QQ号" placeholder-style="color: #999999;" v-model="contact_mode" />
		</view>
		
		<view class="lea_two_sub" @click="fkSubTap">提交</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	feedbackCateApi,
	feedbackSubApi,
	upImg
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			feedLists:[],
			uploadimgs:[],
			type:-1,
			contents:'',
			contact_mode:'',//联系方式
			imgbaseUrl:'',
			qjbutton:'#131315',
		}
	},
	onShow() {
		
	},
	onLoad() {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.imgbaseUrl = this.$baseUrl;
		this.feedbackCateData();
	},
	methods: {
		//用户反馈
		fkSubTap(){
			if (this.type == -1) {
				uni.showToast({
					icon:'none',
					title: '请选择反馈类型',
					duration: 2000
				});
				return false;
			}
			if (this.contents.split(" ").join("").length == 0) {
				uni.showToast({
					icon:'none',
					title: '请输入反馈内容',
					duration: 2000
				});
				return false;
			}
			if (this.contact_mode.split(" ").join("").length == 0) {
				uni.showToast({
					icon:'none',
					title: '请输入联系方式',
					duration: 2000
				});
				return false;
			}
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			feedbackSubApi({
				type:that.feedLists[that.type],
				problem:that.contents,
				image:that.uploadimgs.join(','),
				contact_mode:that.contact_mode
			}).then(res => {
				console.log('分类',res)
				if (res.code == 1) {
					uni.hideLoading();
					uni.showToast({
						title:'提交成功',
						duration: 2000
					});
					setTimeout(function(){
						uni.navigateBack({})
					},1000);
				}
			})
		},
		//分类
		feedbackCateData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			feedbackCateApi({}).then(res => {
				console.log('分类',res)
				if (res.code == 1) {
					that.feedLists = res.data;
					uni.hideLoading();
				}
			})
		},
		//上传图片
		uploadImgTap() {
			let that = this
			let num = 5 - that.uploadimgs.length
		
			uni.chooseImage({
				count: num, //默认9
				success: function(res) {
					uni.showLoading({
						title: '上传中'
					})
					const tempFilePaths = res.tempFilePaths
					console.log(tempFilePaths);
					// const tempFilePaths = res.tempFiles
					for (let i = 0; i < tempFilePaths.length; i++) {
						upImg(tempFilePaths[i], 'file', ).then(ress => {
							if (ress.code == 1) {
								if (i == tempFilePaths.length - 1) {
		
									that.$toast({
										title: '上传完毕',
										icon: 'success'
									})
								}
								// that.$toast({
								// 	title: '上传成功',
								// 	icon: 'success'
								// })
		
								that.uploadimgs.push(ress.data.file.url)
							}
						})
		
					}
				}
		
			})
		
		},
		uploadImgDel(index, type) {
			this.uploadimgs.splice(index, 1)
		},
		tabTap(index){
			this.type = index;
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.feedback{overflow: hidden;}
page{padding-bottom: 0;}
</style>