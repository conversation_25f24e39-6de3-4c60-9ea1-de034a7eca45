<template>
  <view class="followers-container">
    <!-- 消息列表 -->
    <scroll-view 
      class="message-list"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view v-if="messageList && messageList.length > 0">
        <view 
          v-for="message in messageList" 
          :key="message.id"
          class="message-card"
          @click="openUserProfile(message.userId)"
        >
          <!-- 用户头像 -->
          <u-avatar 
            :src="message.userAvatar" 
            size="50"
            class="user-avatar"
          ></u-avatar>
          
          <!-- 用户信息 -->
          <view class="user-info">
            <view class="user-header">
              <text class="user-name">{{ message.userName }}</text>
              <text class="follow-time">{{ formatTime(message.createTime) }}</text>
            </view>
            <text class="user-desc">{{ message.userDesc || '这个人很懒，什么都没有留下' }}</text>
            
            <!-- 用户标签 -->
            <view v-if="message.userTags && message.userTags.length > 0" class="user-tags">
              <u-tag 
                v-for="tag in message.userTags" 
                :key="tag"
                :text="tag"
                size="mini"
                type="primary"
                mode="light"
                class="tag-item"
              ></u-tag>
            </view>
          </view>
          
          <!-- 关注按钮 -->
          <view class="follow-actions">
            <u-button 
              v-if="!message.isFollowBack"
              type="primary"
              size="mini"
              :custom-style="followBtnStyle"
              @click.stop="followBack(message)"
            >
              回关
            </u-button>
            <u-button 
              v-else
              type="default"
              size="mini"
              :custom-style="followedBtnStyle"
              @click.stop="unfollowUser(message)"
            >
              已关注
            </u-button>
          </view>
          
          <!-- 未读标识 -->
          <view v-if="!message.isRead" class="unread-dot"></view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <u-empty mode="data" text="暂无新粉丝"></u-empty>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && messageList && messageList.length > 0" class="load-more">
        <u-loading mode="flower" size="24"></u-loading>
        <text class="load-text">加载中...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'NewFollowers',
  data() {
    return {
      isRefreshing: false,
      hasMore: true,
      page: 1,
      pageSize: 20,
      followBtnStyle: {
        width: '120rpx',
        height: '60rpx',
        fontSize: '24rpx'
      },
      followedBtnStyle: {
        width: '120rpx',
        height: '60rpx',
        fontSize: '24rpx',
        color: '#999',
        borderColor: '#e4e7ed'
      },
      messageList: [
        {
          id: 1,
          userId: 'user001',
          userName: '舞蹈新星',
          userAvatar: 'https://picsum.photos/100/100?random=701',
          userDesc: '热爱街舞的95后，希望能和大家一起进步！',
          userTags: ['街舞', '新手'],
          createTime: new Date(Date.now() - 1800000),
          isRead: false,
          isFollowBack: false
        },
        {
          id: 2,
          userId: 'user002',
          userName: '芭蕾小公主',
          userAvatar: 'https://picsum.photos/100/100?random=702',
          userDesc: '从小学习芭蕾，现在是专业舞者',
          userTags: ['芭蕾', '专业'],
          createTime: new Date(Date.now() - 3600000),
          isRead: true,
          isFollowBack: true
        },
        {
          id: 3,
          userId: 'user003',
          userName: '现代舞爱好者',
          userAvatar: 'https://picsum.photos/100/100?random=703',
          userDesc: null,
          userTags: ['现代舞'],
          createTime: new Date(Date.now() - 7200000),
          isRead: false,
          isFollowBack: false
        },
        {
          id: 4,
          userId: 'user004',
          userName: '拉丁舞教练',
          userAvatar: 'https://picsum.photos/100/100?random=704',
          userDesc: '拉丁舞教练，有10年教学经验',
          userTags: ['拉丁舞', '教练', '经验丰富'],
          createTime: new Date(Date.now() - 10800000),
          isRead: true,
          isFollowBack: false
        }
      ]
    }
  },
  onLoad() {
    this.loadMessages()
  },
  methods: {
    formatTime(time) {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 60) {
        return `${minutes}分钟前关注了你`
      } else if (hours < 24) {
        return `${hours}小时前关注了你`
      } else {
        return `${days}天前关注了你`
      }
    },

    openUserProfile(userId) {
      // 标记为已读
      const message = this.messageList.find(msg => msg.userId === userId)
      if (message) {
        message.isRead = true
      }
      
      // 跳转到用户主页
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${userId}`
      })
    },

    followBack(message) {
      // 模拟关注操作
      message.isFollowBack = true
      this.$u.toast('关注成功')
      
      // 这里可以调用API进行关注操作
      this.callFollowAPI(message.userId)
    },

    unfollowUser(message) {
      uni.showModal({
        title: '确认取消关注',
        content: `确定要取消关注 ${message.userName} 吗？`,
        success: (res) => {
          if (res.confirm) {
            message.isFollowBack = false
            this.$u.toast('已取消关注')
            
            // 这里可以调用API进行取消关注操作
            this.callUnfollowAPI(message.userId)
          }
        }
      })
    },

    async callFollowAPI(userId) {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        console.log('关注用户:', userId)
      } catch (error) {
        console.error('关注失败:', error)
        this.$u.toast('关注失败，请重试')
      }
    },

    async callUnfollowAPI(userId) {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        console.log('取消关注用户:', userId)
      } catch (error) {
        console.error('取消关注失败:', error)
        this.$u.toast('操作失败，请重试')
      }
    },

    onRefresh() {
      this.isRefreshing = true
      this.page = 1
      this.loadMessages().finally(() => {
        this.isRefreshing = false
      })
    },

    loadMore() {
      if (!this.hasMore) return
      this.page++
      this.loadMessages()
    },

    async loadMessages() {
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        if (this.page >= 3) {
          this.hasMore = false
        }
      } catch (error) {
        console.error('加载消息失败:', error)
        this.$u.toast('加载失败，请重试')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.followers-container {
  height: 100vh;
  background: #f5f5f5;
}

.message-list {
  height: 100%;
  padding: 32rpx 0;
}

.message-card {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  margin: 0 32rpx 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.user-avatar {
  margin-right: 24rpx;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  margin-right: 24rpx;
}

.user-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.follow-time {
  font-size: 24rpx;
  color: #999;
}

.user-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: block;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item {
  margin: 0;
}

.follow-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.unread-dot {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
}

.empty-state {
  display: flex;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.load-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}
</style>
