<template>
  <view class="test-container">
    <view class="header">
      <text class="title">组件测试页面</text>
    </view>
    
    <scroll-view class="content" scroll-y>
      <!-- 测试基础组件 -->
      <view class="test-section">
        <text class="section-title">基础组件测试</text>
        
        <view class="test-item">
          <text class="test-label">头像组件:</text>
          <u-avatar src="https://picsum.photos/100/100?random=1" size="40"></u-avatar>
        </view>
        
        <view class="test-item">
          <text class="test-label">图标组件:</text>
          <u-icon name="heart" color="#ff4757" size="24"></u-icon>
          <u-icon name="chat" color="#2979ff" size="24"></u-icon>
          <u-icon name="share" color="#52c41a" size="24"></u-icon>
        </view>
        
        <view class="test-item">
          <text class="test-label">按钮组件:</text>
          <u-button type="primary" text="主要按钮" size="small"></u-button>
          <u-button type="default" text="默认按钮" size="small"></u-button>
        </view>
        
        <view class="test-item">
          <text class="test-label">加载组件:</text>
          <u-loading mode="circle" size="24"></u-loading>
        </view>
      </view>
      
      <!-- 测试导航功能 -->
      <view class="test-section">
        <text class="section-title">页面导航测试</text>
        
        <view class="nav-buttons">
          <u-button 
            type="primary" 
            text="首页" 
            @click="goPage('/pagesSub/social/home/<USER>')"
          ></u-button>
          
          <u-button 
            type="success" 
            text="发现" 
            @click="goPage('/pagesSub/social/discover/index')"
          ></u-button>
          
          <u-button 
            type="warning" 
            text="发布" 
            @click="goPage('/pagesSub/social/publish/index')"
          ></u-button>
          
          <u-button 
            type="error" 
            text="消息" 
            @click="goPage('/pagesSub/social/message/index')"
          ></u-button>
          
          <u-button 
            type="info" 
            text="我的" 
            @click="goPage('/pagesSub/social/profile/index')"
          ></u-button>
        </view>
      </view>
      
      <!-- 测试状态 -->
      <view class="test-section">
        <text class="section-title">测试状态</text>
        
        <view class="status-list">
          <view class="status-item success">
            <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
            <text class="status-text">uni.promisify.adaptor.js 已修复</text>
          </view>
          
          <view class="status-item success">
            <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
            <text class="status-text">u-loading 组件已替换</text>
          </view>
          
          <view class="status-item success">
            <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
            <text class="status-text">页面路由配置完成</text>
          </view>
          
          <view class="status-item success">
            <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
            <text class="status-text">uview-ui 组件正常</text>
          </view>
        </view>
      </view>
      
      <!-- 返回演示页面 -->
      <view class="test-section">
        <u-button 
          type="primary" 
          size="large"
          text="返回演示页面" 
          @click="goPage('/pagesSub/social/demo/index')"
        ></u-button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'SocialTest',
  methods: {
    goPage(url) {
      uni.navigateTo({
        url: url,
        success: () => {
          console.log('页面跳转成功:', url)
        },
        fail: (err) => {
          console.error('页面跳转失败:', err)
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  background: #2979ff;
  padding: var(--status-bar-height) 16px 16px;
  text-align: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.content {
  padding: 20px 16px;
}

.test-section {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: block;
}

.test-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.test-label {
  font-size: 14px;
  color: #666;
  min-width: 80px;
}

.nav-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
}

.status-item.success {
  background: rgba(82, 196, 26, 0.1);
}

.status-text {
  font-size: 14px;
  color: #333;
}
</style>
