# 店铺API配置指南

## 🔧 **配置概述**

店铺API已配置为使用`vote_baseUrl`作为基础URL，从`config/http.api.js`中获取配置。

## 📁 **文件结构**

```
fox-dance-user-terminal/
├── config/
│   ├── http.api.js          # 基础配置文件
│   └── store.js             # 店铺API接口（已更新）
└── pagesSub/
    └── store/
        └── store-list.vue   # 店铺列表页面（已更新import路径）
```

## ⚙️ **配置详情**

### **1. 基础URL配置**

在`config/http.api.js`中：
```javascript
let apis = {
  baseUrl: 'https://admin.foxdance.com.cn',      // 主系统接口地址
  vote_baseUrl: 'https://vote.foxdance.com.cn',  // 投票系统接口地址（店铺API使用）
  // ... 其他配置
}
```

### **2. 店铺API配置**

在`config/store.js`中：
```javascript
import apis from './http.api.js'

// 获取投票系统的基础URL
const VOTE_BASE_URL = apis.vote_baseUrl || 'https://vote.foxdance.com.cn'

// 使用VOTE_BASE_URL拼接API路径
function storeRequest(options) {
  let fullUrl = `${VOTE_BASE_URL}${url}`  // 拼接完整URL
  // ... 其他请求逻辑
}
```

### **3. 页面引用**

在`pagesSub/store/store-list.vue`中：
```javascript
// 从config目录导入
import { getStoreNames } from '@/config/store'
```

## 🌐 **API端点**

所有店铺API都会使用`vote_baseUrl`作为基础URL：

| API | 完整URL |
|-----|---------|
| 获取店铺名称 | `https://vote.foxdance.com.cn/api/store/names` |
| 获取店铺列表 | `https://vote.foxdance.com.cn/api/store/list` |
| 获取店铺详情 | `https://vote.foxdance.com.cn/api/store/{id}` |

## 🔐 **认证配置**

店铺API支持多种token获取方式：
```javascript
// 优先获取bausertoken，如果没有则获取token
const token = uni.getStorageSync('bausertoken') || uni.getStorageSync('token')
```

请求头配置：
```javascript
const header = {
  'Content-Type': 'application/json',
  'server': 1,
  'bausertoken': token  // 如果token存在
}
```

## 📝 **请求日志**

店铺API包含详细的请求和响应日志：
```javascript
console.log('🏪 店铺API请求:', {
  method,
  url: fullUrl,
  header,
  data
})

console.log('🏪 店铺API响应:', res)
```

## 🧪 **测试验证**

### **1. 配置测试**
```bash
# 在前端项目根目录运行
node test-store-config.js
```

### **2. 实际API测试**
```bash
# 测试后端API（确保后端服务运行在vote_baseUrl对应的服务器上）
curl -X GET "https://vote.foxdance.com.cn/api/store/names"
```

### **3. 前端页面测试**
1. 启动前端项目
2. 访问店铺列表页面：`/pagesSub/store/store-list`
3. 查看浏览器控制台的API请求日志
4. 验证请求URL是否正确拼接

## 🔄 **环境切换**

如需切换到不同环境，只需修改`config/http.api.js`中的`vote_baseUrl`：

```javascript
// 开发环境
vote_baseUrl: 'http://localhost:8101',

// 测试环境  
vote_baseUrl: 'https://test-vote.foxdance.com.cn',

// 生产环境
vote_baseUrl: 'https://vote.foxdance.com.cn',
```

## ⚠️ **注意事项**

1. **URL拼接**: 确保`vote_baseUrl`不以`/`结尾，API路径以`/`开头
2. **Token管理**: 确保用户登录后token正确存储
3. **跨域配置**: 确保后端API支持跨域访问
4. **错误处理**: API包含完整的错误处理和日志记录

## 🎯 **配置验证清单**

- ✅ `config/http.api.js`中`vote_baseUrl`配置正确
- ✅ `config/store.js`正确导入并使用`vote_baseUrl`
- ✅ 页面中import路径更新为`@/config/store`
- ✅ 后端API部署在`vote_baseUrl`对应的服务器上
- ✅ API请求包含正确的认证信息
- ✅ 跨域配置正确

## 📞 **故障排除**

### **常见问题**

1. **API请求失败**
   - 检查`vote_baseUrl`是否正确
   - 确认后端服务是否运行
   - 查看网络请求日志

2. **认证失败**
   - 检查token是否正确存储
   - 确认token格式是否正确
   - 验证后端认证逻辑

3. **跨域问题**
   - 确认后端CORS配置
   - 检查请求头设置
   - 验证域名配置

通过以上配置，店铺API现在会正确使用`vote_baseUrl`进行请求，确保与投票系统的后端服务正确通信。
