<template>
  <view class="detail-container">

    <scroll-view class="content" scroll-y>
      <!-- 帖子内容 -->
      <view class="post-detail">
        <!-- 用户信息 -->
        <view class="user-info">
          <u-avatar :src="postData.userAvatar" size="50" @click="goUserProfile"></u-avatar>
          <view class="user-details">
            <text class="username">{{ postData.username }}</text>
            <text class="time">{{ formatTime(postData.createTime) }}</text>
          </view>
          <FollowButton
            :user="{ id: postData.userId, nickname: postData.username }"
            :followed="postData.isFollowed"
            size="mini"
            @follow="onUserFollow"
            @change="onFollowChange"
          />
        </view>

        <!-- 帖子文字内容 -->
        <view class="post-content">
          <text class="content-text">{{ postData.content }}</text>
        </view>

        <!-- 话题标签 -->
        <view class="topic-tags" v-if="postData.topics && postData.topics.length">
          <text 
            v-for="topic in postData.topics" 
            :key="topic"
            class="topic-tag"
            @click="goTopic(topic)"
          >
            #{{ topic }}
          </text>
        </view>

        <!-- 帖子图片轮播 -->
        <view class="post-images" v-if="postData.images && postData.images.length">
          <swiper
            class="image-swiper"
            :indicator-dots="postData.images.length > 1"
            :autoplay="false"
            :circular="true"
            indicator-color="rgba(255, 255, 255, 0.5)"
            indicator-active-color="#fff"
          >
            <swiper-item
              v-for="(img, index) in postData.images"
              :key="index"
            >
              <image
                :src="img"
                class="swiper-image"
                mode="aspectFill"
                @click="previewImage(index)"
              />
            </swiper-item>
          </swiper>
        </view>

        <!-- 互动数据 -->
        <view class="post-stats">
          <text class="stat-item">{{ postData.likeCount }}人点赞</text>
          <text class="stat-item">{{ postData.commentCount }}条评论</text>
          <text class="stat-item">{{ postData.shareCount }}次分享</text>
        </view>

        <!-- 互动按钮 -->
        <view class="action-bar">
          <view class="action-item" @click="toggleLike">
            <u-icon 
              :name="postData.isLiked ? 'heart-fill' : 'heart'" 
              :color="postData.isLiked ? '#ff4757' : '#666'"
              size="24"
            ></u-icon>
            <text class="action-text">点赞</text>
          </view>
          <view class="action-item" @click="focusComment">
            <u-icon name="chat" color="#666" size="24"></u-icon>
            <text class="action-text">评论</text>
          </view>
          <view class="action-item" @click="sharePost">
            <u-icon name="share" color="#666" size="24"></u-icon>
            <text class="action-text">分享</text>
          </view>
        </view>
      </view>

      <!-- 评论列表 -->
      <view class="comments-section">
        <view class="comments-header">
          <text class="comments-title">评论 {{ commentList.length }}</text>
          <u-sticky bgColor="#fff">
            <u-tabs
              :list="tabList"
              :current="currentTab"
              @change="changeSortType"
              :scrollable="false"
              activeColor="#2979ff"
              inactiveColor="#999"
              fontSize="28"
              lineColor="#2979ff"
              lineWidth="20"
              lineHeight="3"
              height="40"
            ></u-tabs>
          </u-sticky>
        </view>

        <view class="comment-list">
          <view 
            v-for="comment in commentList" 
            :key="comment.id"
            class="comment-item"
          >
            <u-avatar :src="comment.userAvatar" size="36"></u-avatar>
            <view class="comment-content">
              <view class="comment-header">
                <text class="comment-username">{{ comment.username }}</text>
                <text class="comment-time">{{ formatTime(comment.createTime) }}</text>
              </view>
              <text class="comment-text">{{ comment.content }}</text>
              
              <!-- 回复列表 -->
              <view v-if="comment.replies && comment.replies.length" class="replies">
                <view 
                  v-for="reply in comment.replies" 
                  :key="reply.id"
                  class="reply-item"
                >
                  <text class="reply-user">{{ reply.username }}</text>
                  <text class="reply-text">{{ reply.content }}</text>
                </view>
              </view>

              <view class="comment-actions">
                <view class="comment-action" @click="toggleCommentLike(comment)">
                  <u-icon 
                    :name="comment.isLiked ? 'heart-fill' : 'heart'" 
                    :color="comment.isLiked ? '#ff4757' : '#999'"
                    size="16"
                  ></u-icon>
                  <text class="action-count">{{ comment.likeCount || '' }}</text>
                </view>
                <view class="comment-action" @click="replyComment(comment)">
                  <u-icon name="chat" color="#999" size="16"></u-icon>
                  <text class="action-text">回复</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 评论输入框 -->
    <view class="comment-input-bar">
      <view class="input-container">
        <u-avatar :src="currentUser.avatar" size="32"></u-avatar>
        <input 
          ref="commentInput"
          v-model="commentText"
          class="comment-input"
          placeholder="写评论..."
          @focus="onInputFocus"
          @blur="onInputBlur"
        />
        <text 
          class="send-btn"
          :class="{ active: commentText.trim() }"
          @click="sendComment"
        >
          发送
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import FollowButton from '../components/FollowButton.vue'

export default {
  name: 'PostDetail',
  components: {
    FollowButton
  },
  data() {
    return {
      postId: '',
      postData: {},
      commentList: [],
      commentText: '',
      sortType: 'time', // time, hot
      currentTab: 0,
      tabList: [
        { name: '最新' },
        { name: '最热' }
      ],
      currentUser: {
        avatar: 'https://picsum.photos/100/100?random=999',
        nickname: '我的昵称'
      },
      replyingTo: null
    }
  },
  onLoad(options) {
    this.postId = options.id
    this.loadPostDetail()
    this.loadComments()
  },
  methods: {
    loadPostDetail() {
      // 模拟帖子详情数据
      this.postData = {
        id: this.postId,
        username: '时尚博主小美',
        userAvatar: 'https://picsum.photos/100/100?random=100',
        content: '今天分享一套超级喜欢的穿搭！简约而不简单的搭配，既舒适又时尚。这件白色衬衫是我的最爱，搭配牛仔裤和小白鞋，简直是完美的日常look！大家觉得怎么样呢？',
        images: [
          'https://picsum.photos/400/600?random=200',
          'https://picsum.photos/400/600?random=201',
          'https://picsum.photos/400/600?random=202'
        ],
        topics: ['穿搭', '时尚', '日常'],
        location: '北京·三里屯太古里',
        likeCount: 234,
        commentCount: 45,
        shareCount: 12,
        isLiked: false,
        isFollowed: false,
        createTime: new Date(Date.now() - 3600000 * 2)
      }
    },

    loadComments() {
      // 模拟评论数据
      this.commentList = [
        {
          id: 1,
          username: '小仙女',
          userAvatar: 'https://picsum.photos/100/100?random=300',
          content: '好好看啊！请问衬衫是什么牌子的？',
          likeCount: 12,
          isLiked: false,
          createTime: new Date(Date.now() - 1800000),
          replies: [
            {
              id: 11,
              username: '时尚博主小美',
              content: '是ZARA的哦，性价比很高！',
              createTime: new Date(Date.now() - 1200000)
            }
          ]
        },
        {
          id: 2,
          username: '穿搭达人',
          userAvatar: 'https://picsum.photos/100/100?random=301',
          content: '这套搭配真的很棒！简约大方，我也要试试这样搭配',
          likeCount: 8,
          isLiked: true,
          createTime: new Date(Date.now() - 3600000),
          replies: []
        }
      ]
    },

    formatTime(time) {
      const now = new Date()
      const diff = now - new Date(time)
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      return `${days}天前`
    },

    goBack() {
      uni.navigateBack()
    },

    goUserProfile() {
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${this.postData.userId}`
      })
    },

    onUserFollow(data) {
      console.log('关注操作:', data)
      // 这里可以调用API进行关注/取消关注操作
    },

    onFollowChange(data) {
      // 更新本地数据
      this.postData.isFollowed = data.isFollowed
      console.log('关注状态变化:', data)
    },

    toggleLike() {
      this.postData.isLiked = !this.postData.isLiked
      this.postData.likeCount += this.postData.isLiked ? 1 : -1
    },

    toggleCommentLike(comment) {
      comment.isLiked = !comment.isLiked
      comment.likeCount += comment.isLiked ? 1 : -1
    },

    previewImage(index) {
      uni.previewImage({
        urls: this.postData.images,
        current: index
      })
    },

    sharePost() {
      uni.showActionSheet({
        itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
        success: (res) => {
          this.$u.toast('分享成功')
          this.postData.shareCount++
        }
      })
    },

    goTopic(topic) {
      uni.navigateTo({
        url: `/pagesSub/social/topic/detail?name=${topic}`
      })
    },

    changeSortType(...args) {
      console.log('tabs点击参数:', args)
      const index = typeof args[0] === 'number' ? args[0] : (args[0] && args[0].index !== undefined ? args[0].index : 0)
      this.currentTab = index
      this.sortType = index === 0 ? 'time' : 'hot'
      // 重新加载评论
      this.loadComments()
    },

    focusComment() {
      this.$refs.commentInput.focus()
    },

    onInputFocus() {
      // 输入框获得焦点
    },

    onInputBlur() {
      // 输入框失去焦点
    },

    replyComment(comment) {
      this.replyingTo = comment
      this.commentText = `@${comment.username} `
      this.focusComment()
    },

    sendComment() {
      if (!this.commentText.trim()) return
      
      const newComment = {
        id: Date.now(),
        username: this.currentUser.nickname,
        userAvatar: this.currentUser.avatar,
        content: this.commentText,
        likeCount: 0,
        isLiked: false,
        createTime: new Date(),
        replies: []
      }
      
      this.commentList.unshift(newComment)
      this.postData.commentCount++
      this.commentText = ''
      this.replyingTo = null
      
      this.$u.toast('评论成功')
    },

    showMoreActions() {
      uni.showActionSheet({
        itemList: ['举报', '不感兴趣', '屏蔽用户'],
        success: (res) => {
          console.log('更多操作:', res.tapIndex)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 60px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}


.post-detail {
  background: #fff;
  padding: 40rpx 32rpx;
  margin-bottom: 16rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.user-details {
  flex: 1;
  margin-left: 24rpx;
}

.username {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
}

.time {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}

.location {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}

.content-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.topic-tags {
  margin-bottom: 32rpx;
}

.topic-tag {
  color: #2979ff;
  font-size: 28rpx;
  margin-right: 16rpx;
}

.post-images {
  margin-bottom: 32rpx;
}

.image-swiper {
  width: 100%;
  height: 600rpx;
  border-radius: 24rpx;
  overflow: hidden;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.post-stats {
  padding: 24rpx 0;
  border-top: 2rpx solid #f0f0f0;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 24rpx;
}

.stat-item {
  font-size: 26rpx;
  color: #666;
  margin-right: 32rpx;
}

.action-bar {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.comments-section {
  background: #fff;
  padding: 32rpx;
}

.comments-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.comments-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.comment-item {
  display: flex;
  margin-bottom: 32rpx;
}

.comment-content {
  flex: 1;
  margin-left: 24rpx;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.comment-username {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  line-height: 1.4;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.replies {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
}

.reply-item {
  margin-bottom: 8rpx;
}

.reply-user {
  font-size: 26rpx;
  color: #2979ff;
  margin-right: 8rpx;
}

.reply-text {
  font-size: 26rpx;
  color: #333;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.comment-action {
  display: flex;
  align-items: center;
}

.action-count, .action-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}

.comment-input-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}

.input-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 104rpx;
  position: relative;
}

.comment-input {
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  background: #f5f5f5;
  border: none;
  border-radius: 36rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  appearance: none;
  transition: none;
  position: absolute;
  left: 48rpx;
  top: 16rpx;
}

.comment-input:focus {
  background: #f5f5f5;
  border: none;
  outline: none;
  box-shadow: none;
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 32rpx;
  left: 48rpx;
  top: 16rpx;
}

.send-btn {
  width: 80rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ccc;
  white-space: nowrap;
  position: absolute;
  right: 0;
  top: 16rpx;
}

.send-btn.active {
  color: #2979ff;
}
</style>
