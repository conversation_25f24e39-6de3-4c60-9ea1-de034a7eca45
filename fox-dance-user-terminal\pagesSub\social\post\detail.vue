<template>
  <view class="detail-container">

    <scroll-view class="content" scroll-y>
      <!-- 帖子内容 -->
      <view class="post-detail">
        <!-- 用户信息 -->
        <view class="user-info">
          <u-avatar :src="postData.userAvatar" size="50" @click="goUserProfile"></u-avatar>
          <view class="user-details">
            <text class="username">{{ postData.username }}</text>
            <text class="time">{{ formatTime(postData.createTime) }}</text>
          </view>
          <FollowButton
            :user="{ id: postData.userId, nickname: postData.username }"
            :followed="postData.isFollowed"
            size="mini"
            @follow="onUserFollow"
            @change="onFollowChange"
          />
        </view>

        <!-- 帖子文字内容 -->
        <view class="post-content">
          <text class="content-text">{{ postData.content }}</text>
        </view>

        <!-- 话题标签 -->
        <view class="topic-tags" v-if="postData.topics && postData.topics.length">
          <text 
            v-for="topic in postData.topics" 
            :key="topic"
            class="topic-tag"
            @click="goTopic(topic)"
          >
            #{{ topic }}
          </text>
        </view>

        <!-- 帖子图片轮播 -->
        <view class="post-images" v-if="postData.images && postData.images.length">
          <swiper
            class="image-swiper"
            :indicator-dots="postData.images.length > 1"
            :autoplay="false"
            :circular="true"
            indicator-color="rgba(255, 255, 255, 0.5)"
            indicator-active-color="#fff"
          >
            <swiper-item
              v-for="(img, index) in postData.images"
              :key="index"
            >
              <image
                :src="img"
                class="swiper-image"
                mode="aspectFill"
                @click="previewImage(index)"
              />
            </swiper-item>
          </swiper>
        </view>

        <!-- 互动数据 -->
        <view class="post-stats">
          <text class="stat-item">{{ postData.likeCount }}人点赞</text>
          <text class="stat-item">{{ postData.commentCount }}条评论</text>
          <text class="stat-item">{{ postData.shareCount }}次分享</text>
        </view>

        <!-- 互动按钮 -->
        <view class="action-bar">
          <view class="action-item" @click="toggleLike">
            <u-icon 
              :name="postData.isLiked ? 'heart-fill' : 'heart'" 
              :color="postData.isLiked ? '#ff4757' : '#666'"
              size="24"
            ></u-icon>
            <text class="action-text">点赞</text>
          </view>
          <view class="action-item" @click="focusComment">
            <u-icon name="chat" color="#666" size="24"></u-icon>
            <text class="action-text">评论</text>
          </view>
          <view class="action-item" @click="sharePost">
            <u-icon name="share" color="#666" size="24"></u-icon>
            <text class="action-text">分享</text>
          </view>
        </view>
      </view>

      <!-- 评论列表 -->
      <view class="comments-section">
        <view class="comments-header">
          <text class="comments-title">评论 {{ commentList.length }}</text>
          <u-sticky bgColor="#fff">
            <u-tabs
              :list="tabList"
              :current="currentTab"
              @change="changeSortType"
              :scrollable="false"
              activeColor="#2979ff"
              inactiveColor="#999"
              fontSize="28"
              lineColor="#2979ff"
              lineWidth="20"
              lineHeight="3"
              height="40"
            ></u-tabs>
          </u-sticky>
        </view>

        <view class="comment-list">
          <view 
            v-for="comment in commentList" 
            :key="comment.id"
            class="comment-item"
          >
            <u-avatar :src="comment.userAvatar" size="36"></u-avatar>
            <view class="comment-content">
              <view class="comment-header">
                <text class="comment-username">{{ comment.username }}</text>
                <text class="comment-time">{{ formatTime(comment.createTime) }}</text>
              </view>
              <text class="comment-text">{{ comment.content }}</text>
              
              <!-- 回复列表 -->
              <view v-if="comment.replies && comment.replies.length" class="replies">
                <view 
                  v-for="reply in comment.replies" 
                  :key="reply.id"
                  class="reply-item"
                >
                  <text class="reply-user">{{ reply.username }}</text>
                  <text class="reply-text">{{ reply.content }}</text>
                </view>
              </view>

              <view class="comment-actions">
                <view class="comment-action" @click="toggleCommentLike(comment)">
                  <u-icon 
                    :name="comment.isLiked ? 'heart-fill' : 'heart'" 
                    :color="comment.isLiked ? '#ff4757' : '#999'"
                    size="16"
                  ></u-icon>
                  <text class="action-count">{{ comment.likeCount || '' }}</text>
                </view>
                <view class="comment-action" @click="replyComment(comment)">
                  <u-icon name="chat" color="#999" size="16"></u-icon>
                  <text class="action-text">回复</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 评论输入框 -->
    <view class="comment-input-bar">
      <view class="input-container">
        <u-avatar :src="currentUser.avatar" size="32"></u-avatar>
        <input 
          ref="commentInput"
          v-model="commentText"
          class="comment-input"
          placeholder="写评论..."
          @focus="onInputFocus"
          @blur="onInputBlur"
        />
        <text 
          class="send-btn"
          :class="{ active: commentText.trim() }"
          @click="sendComment"
        >
          发送
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import FollowButton from '../components/FollowButton.vue'
import { getPostDetail, getCommentList, createComment } from '@/utils/socialApi.js'

export default {
  name: 'PostDetail',
  components: {
    FollowButton
  },
  data() {
    return {
      postId: '',
      postData: {},
      commentList: [],
      commentText: '',
      sortType: 'time', // time, hot
      currentTab: 0,
      tabList: [
        { name: '最新' },
        { name: '最热' }
      ],
      currentUser: {
        avatar: 'https://picsum.photos/100/100?random=999',
        nickname: '我的昵称'
      },
      replyingTo: null
    }
  },
  onLoad(options) {
    console.log('详情页接收到的参数:', options)

    // 获取帖子ID，支持多种参数名
    this.postId = options.id || options.postId || '7' // 默认使用ID 7进行测试

    console.log('最终使用的帖子ID:', this.postId)

    // 确保postId是字符串类型
    this.postId = String(this.postId)

    this.loadPostDetail()
    this.loadComments()
  },
  methods: {
    async loadPostDetail() {
      try {
        console.log('加载帖子详情，ID:', this.postId)
        console.log('postId类型:', typeof this.postId)

        if (!this.postId) {
          throw new Error('帖子ID为空')
        }

        const result = await getPostDetail(this.postId)
        console.log('帖子详情API返回:', result)

        if (result) {
          const post = result
          this.postData = {
            id: post.id,
            userId: post.userId,
            username: post.nickname || '用户' + post.userId,
            userAvatar: post.avatar || 'https://picsum.photos/100/100?random=' + post.userId,
            content: post.content,
            images: post.images || [],
            topics: post.tags || [],
            location: post.locationName || '',
            likeCount: post.likeCount || 0,
            commentCount: post.commentCount || 0,
            shareCount: post.shareCount || 0,
            isLiked: post.isLiked || false,
            isFollowed: post.isFollowed || false,
            createTime: new Date(post.createTime)
          }
          console.log('帖子数据加载成功:', this.postData)
        } else {
          console.error('API返回数据格式错误:', result)
          throw new Error('获取帖子详情失败 - API返回格式错误')
        }
      } catch (error) {
        console.error('加载帖子详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
        // 使用默认数据作为后备
        this.postData = {
          id: this.postId,
          username: '用户',
          userAvatar: 'https://picsum.photos/100/100?random=100',
          content: '帖子内容加载失败',
          images: [],
          topics: [],
          location: '',
          likeCount: 0,
          commentCount: 0,
          shareCount: 0,
          isLiked: false,
          isFollowed: false,
          createTime: new Date()
        }
      }
    },

    async loadComments() {
      try {
        console.log('加载评论列表，帖子ID:', this.postId)
        const result = await getCommentList({
          userId: 1, // 当前用户ID，实际应该从用户状态获取
          contentId: this.postId,
          filter: this.sortType === 'time' ? 'latest' : 'hot',
          page: 1,
          size: 50
        })
        console.log('评论列表API返回:', result)
        console.log('result.code:', result?.code)
        console.log('result.data:', result?.data)

        if (result && result.code === 0 && result.data) {
          // 处理不同的API返回格式
          let comments = []
          if (result.data.comments) {
            comments = result.data.comments
          } else if (Array.isArray(result.data)) {
            comments = result.data
          }

          this.commentList = comments.map(comment => ({
            id: comment.id,
            username: comment.nickname || '用户' + comment.userId,
            userAvatar: comment.avatar || 'https://picsum.photos/100/100?random=' + comment.userId,
            content: comment.content,
            likeCount: comment.likes || 0,
            isLiked: comment.isLiked || false,
            createTime: new Date(comment.createdAt || comment.createTime),
            replies: comment.replies || []
          }))

          console.log('评论列表加载成功，数量:', this.commentList.length)
        } else {
          console.log('评论API返回格式不正确或无数据')
          this.commentList = []
        }
      } catch (error) {
        console.error('加载评论失败:', error)
        // 使用模拟数据作为后备
        this.commentList = [
          {
            id: 1,
            username: '小仙女',
            userAvatar: 'https://picsum.photos/100/100?random=300',
            content: '好好看啊！请问衬衫是什么牌子的？',
            likeCount: 12,
            isLiked: false,
            createTime: new Date(Date.now() - 1800000),
            replies: [
              {
                id: 11,
                username: '时尚博主小美',
                content: '是ZARA的哦，性价比很高！',
                createTime: new Date(Date.now() - 1200000)
              }
            ]
          },
          {
            id: 2,
            username: '穿搭达人',
            userAvatar: 'https://picsum.photos/100/100?random=301',
            content: '这套搭配真的很棒！简约大方，我也要试试这样搭配',
            likeCount: 8,
            isLiked: true,
            createTime: new Date(Date.now() - 3600000),
            replies: []
          }
        ]
      }
    },

    formatTime(time) {
      const now = new Date()
      const diff = now - new Date(time)
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      return `${days}天前`
    },

    goBack() {
      uni.navigateBack()
    },

    goUserProfile() {
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${this.postData.userId}`
      })
    },

    onUserFollow(data) {
      console.log('关注操作:', data)
      // 这里可以调用API进行关注/取消关注操作
    },

    onFollowChange(data) {
      // 更新本地数据
      this.postData.isFollowed = data.isFollowed
      console.log('关注状态变化:', data)
    },

    toggleLike() {
      this.postData.isLiked = !this.postData.isLiked
      this.postData.likeCount += this.postData.isLiked ? 1 : -1
    },

    toggleCommentLike(comment) {
      comment.isLiked = !comment.isLiked
      comment.likeCount += comment.isLiked ? 1 : -1
    },

    previewImage(index) {
      uni.previewImage({
        urls: this.postData.images,
        current: index
      })
    },

    sharePost() {
      uni.showActionSheet({
        itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
        success: (res) => {
          this.$u.toast('分享成功')
          this.postData.shareCount++
        }
      })
    },

    goTopic(topic) {
      uni.navigateTo({
        url: `/pagesSub/social/topic/detail?name=${topic}`
      })
    },

    changeSortType(...args) {
      console.log('tabs点击参数:', args)
      const index = typeof args[0] === 'number' ? args[0] : (args[0] && args[0].index !== undefined ? args[0].index : 0)
      this.currentTab = index
      this.sortType = index === 0 ? 'time' : 'hot'
      // 重新加载评论
      this.loadComments()
      this.loadComments()
    },

    focusComment() {
      this.$refs.commentInput.focus()
    },

    onInputFocus() {
      // 输入框获得焦点
    },

    onInputBlur() {
      // 输入框失去焦点
    },

    replyComment(comment) {
      this.replyingTo = comment
      this.commentText = `@${comment.username} `
      this.focusComment()
    },

    async sendComment() {
      if (!this.commentText.trim()) {
        uni.showToast({
          title: '请输入评论内容',
          icon: 'none'
        })
        return
      }

      try {
        console.log('发送评论，帖子ID:', this.postId, '内容:', this.commentText)

        // 准备评论数据
        const commentData = {
          contentId: String(this.postId), // 确保是字符串类型
          content: this.commentText.trim(),
          userId: 1, // 当前用户ID，实际应该从用户状态获取
          topicId: null // 帖子评论不需要topicId
        }

        console.log('评论数据:', commentData)

        // 尝试调用API发送评论
        try {
          const result = await createComment(commentData)
          console.log('发送评论API返回:', result)

          if (result && result.code === 0) {
            // API调用成功
            this.handleCommentSuccess()
            return
          }
        } catch (apiError) {
          console.warn('评论API调用失败，使用临时方案:', apiError)
        }

        // API失败时的临时处理：直接添加到本地列表
        console.log('使用临时评论方案')
        const newComment = {
          id: Date.now(),
          username: '当前用户',
          userAvatar: 'https://picsum.photos/100/100?random=999',
          content: this.commentText.trim(),
          likeCount: 0,
          isLiked: false,
          createTime: new Date(),
          replies: []
        }

        // 添加到评论列表顶部
        this.commentList.unshift(newComment)
        this.handleCommentSuccess()

      } catch (error) {
        console.error('发送评论失败:', error)
        uni.showToast({
          title: '评论失败',
          icon: 'none'
        })
      }
    },

    // 处理评论成功的通用逻辑
    handleCommentSuccess() {
      // 清空输入框和回复状态
      this.commentText = ''
      this.replyingTo = null

      // 更新帖子评论数
      this.postData.commentCount++

      // 显示成功提示
      uni.showToast({
        title: '评论成功',
        icon: 'success'
      })

      // 可选：重新加载评论列表以获取最新数据
      // this.loadComments()
    },

    showMoreActions() {
      uni.showActionSheet({
        itemList: ['举报', '不感兴趣', '屏蔽用户'],
        success: (res) => {
          console.log('更多操作:', res.tapIndex)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 60px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}


.post-detail {
  background: #fff;
  padding: 40rpx 32rpx;
  margin-bottom: 16rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.user-details {
  flex: 1;
  margin-left: 24rpx;
}

.username {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
}

.time {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}

.location {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}

.content-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.topic-tags {
  margin-bottom: 32rpx;
}

.topic-tag {
  color: #2979ff;
  font-size: 28rpx;
  margin-right: 16rpx;
}

.post-images {
  margin-bottom: 32rpx;
}

.image-swiper {
  width: 100%;
  height: 600rpx;
  border-radius: 24rpx;
  overflow: hidden;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.post-stats {
  padding: 24rpx 0;
  border-top: 2rpx solid #f0f0f0;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 24rpx;
}

.stat-item {
  font-size: 26rpx;
  color: #666;
  margin-right: 32rpx;
}

.action-bar {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.comments-section {
  background: #fff;
  padding: 32rpx;
}

.comments-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.comments-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.comment-item {
  display: flex;
  margin-bottom: 32rpx;
}

.comment-content {
  flex: 1;
  margin-left: 24rpx;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.comment-username {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  line-height: 1.4;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.replies {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
}

.reply-item {
  margin-bottom: 8rpx;
}

.reply-user {
  font-size: 26rpx;
  color: #2979ff;
  margin-right: 8rpx;
}

.reply-text {
  font-size: 26rpx;
  color: #333;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.comment-action {
  display: flex;
  align-items: center;
}

.action-count, .action-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}

.comment-input-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}

.input-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 104rpx;
  position: relative;
}

.comment-input {
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  background: #f5f5f5;
  border: none;
  border-radius: 36rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  appearance: none;
  transition: none;
  position: absolute;
  left: 48rpx;
  top: 16rpx;
}

.comment-input:focus {
  background: #f5f5f5;
  border: none;
  outline: none;
  box-shadow: none;
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 32rpx;
  left: 48rpx;
  top: 16rpx;
}

.send-btn {
  width: 80rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ccc;
  white-space: nowrap;
  position: absolute;
  right: 0;
  top: 16rpx;
}

.send-btn.active {
  color: #2979ff;
}
</style>
