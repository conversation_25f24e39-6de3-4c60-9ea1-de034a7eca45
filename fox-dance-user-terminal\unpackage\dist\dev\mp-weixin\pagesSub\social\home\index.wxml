<view class="home-container data-v-de45d8c2"><view class="header data-v-de45d8c2"><view class="header-content data-v-de45d8c2"><view class="logo data-v-de45d8c2"><text class="logo-text data-v-de45d8c2">社区</text></view><view class="header-actions data-v-de45d8c2"><u-icon vue-id="ea743d72-1" name="search" size="24" color="#666" data-event-opts="{{[['^click',[['goSearch']]]]}}" bind:click="__e" class="data-v-de45d8c2" bind:__l="__l"></u-icon></view></view></view><view class="topic-tabs-container data-v-de45d8c2"><u-sticky vue-id="ea743d72-2" bgColor="#fff" class="data-v-de45d8c2" bind:__l="__l" vue-slots="{{['default']}}"><u-tabs vue-id="{{('ea743d72-3')+','+('ea743d72-2')}}" list="{{topicList}}" current="{{currentTopicIndex}}" scrollable="{{true}}" activeColor="#2979ff" inactiveColor="#666" fontSize="28" lineColor="#2979ff" lineWidth="40" lineHeight="6" height="80" itemStyle="padding: 0 32rpx;" data-event-opts="{{[['^change',[['selectTopic']]]]}}" bind:change="__e" class="data-v-de45d8c2" bind:__l="__l"></u-tabs></u-sticky></view><scroll-view class="post-list data-v-de45d8c2" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="post-grid data-v-de45d8c2"><block wx:for="{{postList}}" wx:for-item="post" wx:for-index="__i0__" wx:key="id"><post-card class="post-card-item data-v-de45d8c2" vue-id="{{'ea743d72-4-'+__i0__}}" post="{{post}}" data-event-opts="{{[['^click',[['goPostDetail']]],['^userClick',[['goUserProfile']]],['^like',[['onPostLike']]]]}}" bind:click="__e" bind:userClick="__e" bind:like="__e" bind:__l="__l"></post-card></block></view><block wx:if="{{loading}}"><view class="load-more data-v-de45d8c2"><u-loading vue-id="ea743d72-5" mode="circle" size="24" class="data-v-de45d8c2" bind:__l="__l"></u-loading><text class="load-text data-v-de45d8c2">加载中...</text></view></block><block wx:if="{{$root.g0}}"><view class="empty-state data-v-de45d8c2"><u-icon vue-id="ea743d72-6" name="file-text" color="#ccc" size="120rpx" class="data-v-de45d8c2" bind:__l="__l"></u-icon><text class="empty-text data-v-de45d8c2">{{$root.m0}}</text><text class="empty-desc data-v-de45d8c2">{{$root.m1}}</text></view></block></scroll-view></view>