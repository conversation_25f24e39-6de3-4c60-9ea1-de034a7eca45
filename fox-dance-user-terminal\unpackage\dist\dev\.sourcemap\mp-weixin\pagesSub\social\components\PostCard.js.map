{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/components/PostCard.vue?f1c4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/components/PostCard.vue?6e81", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/components/PostCard.vue?b9c3", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/components/PostCard.vue?e9d6", "uni-app:///pagesSub/social/components/PostCard.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/components/PostCard.vue?a265", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/components/PostCard.vue?d920"], "names": ["name", "props", "post", "type", "required", "data", "imageError", "defaultCover", "methods", "formatLikeCount", "onImageError", "onImageLoad", "toggleLike", "goPostDetail", "goUserProfile"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAAuuB,CAAgB,usBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4C3vB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAAk5C,CAAgB,kxCAAG,EAAC,C;;;;;;;;;;;ACAt6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/components/PostCard.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./PostCard.vue?vue&type=template&id=92e3cfc8&scoped=true&\"\nvar renderjs\nimport script from \"./PostCard.vue?vue&type=script&lang=js&\"\nexport * from \"./PostCard.vue?vue&type=script&lang=js&\"\nimport style0 from \"./PostCard.vue?vue&type=style&index=0&id=92e3cfc8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"92e3cfc8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/components/PostCard.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PostCard.vue?vue&type=template&id=92e3cfc8&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatLikeCount(_vm.post.likeCount)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PostCard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PostCard.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"post-card\" @click=\"goPostDetail\">\n    <!-- 封面图片 -->\n    <view class=\"cover-container\">\n      <image\n        :src=\"post.coverImage || (post.images && post.images[0]) || defaultCover\"\n        class=\"cover-image\"\n        mode=\"aspectFill\"\n        @error=\"onImageError\"\n        @load=\"onImageLoad\"\n      />\n      <view v-if=\"imageError\" class=\"image-placeholder\">\n        <u-icon name=\"image\" color=\"#ccc\" size=\"32\"></u-icon>\n        <text class=\"placeholder-text\">图片加载失败</text>\n      </view>\n    </view>\n\n    <!-- 帖子标题 -->\n    <view class=\"post-title\">\n      <text class=\"title-text\">{{ post.title || post.content || '无标题' }}</text>\n    </view>\n\n    <!-- 底部信息 -->\n    <view class=\"post-footer\">\n      <!-- 左侧：用户信息 -->\n      <view class=\"user-info\" @click.stop=\"goUserProfile\">\n        <u-avatar :src=\"post.userAvatar\" size=\"24\"></u-avatar>\n        <text class=\"username\">{{ post.username }}</text>\n      </view>\n\n      <!-- 右侧：点赞数 -->\n      <view class=\"like-info\" @click.stop=\"toggleLike\">\n        <u-icon\n          :name=\"post.isLiked ? 'heart-fill' : 'heart'\"\n          :color=\"post.isLiked ? '#ff4757' : '#999'\"\n          size=\"16\"\n        ></u-icon>\n        <text class=\"like-count\">{{ formatLikeCount(post.likeCount) }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'PostCard',\n  props: {\n    post: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      imageError: false,\n      defaultCover: 'https://picsum.photos/300/200?random=default'\n    }\n  },\n  methods: {\n    formatLikeCount(count) {\n      if (!count || count === 0) return '0'\n      if (count < 1000) return count.toString()\n      if (count < 10000) return (count / 1000).toFixed(1) + 'k'\n      if (count < 1000000) return Math.floor(count / 1000) + 'k'\n      return (count / 1000000).toFixed(1) + 'M'\n    },\n\n    onImageError() {\n      this.imageError = true\n    },\n\n    onImageLoad() {\n      this.imageError = false\n    },\n\n    toggleLike() {\n      this.post.isLiked = !this.post.isLiked\n      this.post.likeCount += this.post.isLiked ? 1 : -1\n      this.$emit('like', this.post)\n    },\n\n    goPostDetail() {\n      this.$emit('click', this.post)\n    },\n\n    goUserProfile() {\n      this.$emit('user-click', this.post)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.post-card {\n  background: #fff;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.post-card:active {\n  transform: scale(0.98);\n  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.12);\n}\n\n.cover-container {\n  position: relative;\n  width: 100%;\n  height: 160px;\n  overflow: hidden;\n}\n\n.cover-image {\n  width: 100%;\n  height: 100%;\n  transition: transform 0.3s ease;\n}\n\n.post-card:hover .cover-image {\n  transform: scale(1.05);\n}\n\n.image-placeholder {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.placeholder-text {\n  font-size: 12px;\n  color: #999;\n  margin-top: 8px;\n}\n\n.post-title {\n  padding: 12px 12px 8px;\n}\n\n.title-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.post-footer {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 12px 12px;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.username {\n  font-size: 12px;\n  color: #666;\n  margin-left: 6px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.like-info {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.like-count {\n  font-size: 12px;\n  color: #999;\n  font-weight: 500;\n}\n\n/* 响应式适配 */\n@media screen and (max-width: 375px) {\n  .cover-container {\n    height: 140px;\n  }\n\n  .title-text {\n    font-size: 13px;\n  }\n\n  .username, .like-count {\n    font-size: 11px;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PostCard.vue?vue&type=style&index=0&id=92e3cfc8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PostCard.vue?vue&type=style&index=0&id=92e3cfc8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752725568696\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}