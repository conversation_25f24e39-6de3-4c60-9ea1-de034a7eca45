{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCardxq.vue?c882", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCardxq.vue?ed94", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCardxq.vue?a54d", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCardxq.vue?0169", "uni-app:///pages/mine/memberCard/myMemberCardxq.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "imgbaseUrl", "isLogined", "myCardInfo", "id", "jhhyToggle", "pageId", "xyCont", "xyToggle", "scrollViewHeight", "isScrollToBottom", "qj<PERSON>ton", "ismr", "onShow", "onLoad", "methods", "ckhtTap", "uni", "title", "url", "success", "filePath", "showMenu", "fail", "icon", "complete", "switch1Change", "console", "setcardData", "duration", "onScroll", "query", "that", "xyGbTap", "tyTap", "myCardxqData", "jhhySubTap"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;;;AAG7D;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChBA;AAAA;AAAA;AAAA;AAA6uB,CAAgB,6sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqEjwB;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;MACAC;MAEAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;MACA;MACAC;QAAAC;MAAA;MACA;MACAD;QACAE;QACAC;UACA;YACA;YACAH;cACAI;cACAC;cAAA;cACAF;cACAG;gBACAN;kBAAAC;kBAAAM;gBAAA;cACA;YACA;UACA;QACA;QACAD;UACAN;YAAAC;YAAAM;UAAA;QACA;QACAC;UACAR;QACA;MACA;IACA;IACAS;MACAC;MACA;MACA;IACA;IACA;IACAC;MACAX;QACAC;MACA;MACA;MACA;QACAd;MACA;QACAuB;QACA;UACAV;UACAA;YACAC;YACAW;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QAEA;QACAC;QACAA;QACAA;UACAJ;UACA;YACA;YACA;YACA;YACA;cACAA;cACAK;YACA;UACA;QACA;MAEA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACAlB;QACAC;MACA;MACA;MACA;QACAd;MACA;QACAuB;QACA;UACAK;UACAA;UACAA;UACAf;QACA;MACA;IACA;IACA;IACAmB;MACAnB;QACAC;MACA;MACA;MACA;QACAd;MACA;QACAuB;QACA;UACAK;UACAA;UACAA;UACAA;UACAf;UACAA;YACAC;YACAW;UACA;QAEA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/mine/memberCard/myMemberCardxq.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/memberCard/myMemberCardxq.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myMemberCardxq.vue?vue&type=template&id=af5e1e1e&scoped=true&\"\nvar renderjs\nimport script from \"./myMemberCardxq.vue?vue&type=script&lang=js&\"\nexport * from \"./myMemberCardxq.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"af5e1e1e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/memberCard/myMemberCardxq.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myMemberCardxq.vue?vue&type=template&id=af5e1e1e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.xyToggle = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.jhhyToggle = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myMemberCardxq.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myMemberCardxq.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"myMemberCard\" v-if=\"myCardInfo.id\"  :style=\"{ '--qjbutton-color': qjbutton }\">\r\n\t\t<view class=\"mymemxqCon\">\r\n\t\t\t<view class=\"mymemxqCon_t\">会员卡详情</view>\r\n\t\t\t<view class=\"mymemxqCon_b\"><view>会员卡ID</view><text>{{myCardInfo.out_trade_no}}</text></view>\r\n\t\t\t<view class=\"mymemxqCon_b\"><view>卡种类别</view><text>{{myCardInfo.type*1 == 0 ? '次卡' : '时长卡'}}</text></view>\r\n\t\t\t<view class=\"mymemxqCon_b\" v-if=\"myCardInfo.type*1 == 0\"><view>剩余次数</view><text>{{myCardInfo.surplus_frequency*1}}次</text></view>\r\n\t\t\t<view class=\"mymemxqCon_b\" v-else><view>剩余时长</view><text>{{myCardInfo.day}}天</text></view>\r\n\t\t\t<view class=\"mymemxqCon_b\"><view>购卡时间</view><text>{{myCardInfo.purchase_card_time}}</text></view>\r\n\t\t\t<view class=\"mymemxqCon_jh\" v-if=\"myCardInfo.status == 0\">\r\n\t\t\t\t<view class=\"mymemxqCon_jh_l\">待激活</view>\r\n\t\t\t\t<!-- <view class=\"mymemxqCon_jh_r\" @click=\"jhhyToggle = true\">去激活<image src=\"/static/images/jt1.png\"></image></view> -->\r\n\t\t\t\t<view class=\"mymemxqCon_jh_r\" @click=\"xyToggle = true\">去激活<image src=\"/static/images/jt1.png\"></image></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"mymemxqCon_b\" v-else><view>激活时间</view><text>{{myCardInfo.activation_time}}</text></view>\r\n\t\t\t<!-- <view class=\"mymemxqCon_b\"><view>到期时间</view><text>{{myCardInfo.become_time}}</text></view> -->\r\n\t\t\t<view class=\"mymemxqCon_b\"><view>适用门店</view></view>\r\n\t\t\t<view class=\"mymemxqCon_c\">\r\n\t\t\t\t<view v-for=\"(item,index) in myCardInfo.storeTable.name\" :key=\"index\">{{item}}</view>\r\n\t\t\t</view> \r\n\t\t\t<view class=\"mymemxqCon_b\" v-if=\"myCardInfo.status == 1\"><view>默认会员卡</view><switch :checked=\"ismr\" @change=\"switch1Change\" style=\"transform:scale(0.7);position: relative;left:14rpx;\" /></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"peode_foo\"  style=\"background: #f6f6f6;\" v-if=\"myCardInfo.contract\"><view  style=\"width:auto;margin:0 70rpx;\" @click=\"ckhtTap(imgbaseUrl + myCardInfo.contract)\">查看合同</view></view>\r\n\t\t\r\n\t\t<!-- 激活会员卡 go -->\r\n\t\t<view class=\"xjTanc hszt\" v-if=\"jhhyToggle\">\r\n\t\t\t<view class=\"xjTanc_n\">\r\n\t\t\t\t<view class=\"xjTanc_a\">温馨提示</view>\r\n\t\t\t\t<view class=\"xjTanc_b\">是否激活该会员卡？</view>\r\n\t\t\t\t<view class=\"xjTanc_c\">\r\n\t\t\t\t\t<view @click=\"jhhyToggle = false\">取消</view>\r\n\t\t\t\t\t<view class=\"bak\" @click=\"jhhySubTap\">确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 激活会员卡 end -->\r\n\t\t\r\n\t\t<view class=\"xytc\" v-if=\"xyToggle\">\r\n\t\t\t<view class=\"xytcCon\">\r\n\t\t\t\t<view class=\"xytcCon_a\">激活该会员卡提示</view>\r\n\t\t\t\t<view class=\"xytcCon_b\">欢迎使用Fox舞蹈小程序!为了更好的保您的个人权益，在使用本产品前，请先阅读并同意以下内容:</view>\r\n\t\t\t\t<!-- <view class=\"xytcCon_c\"><view><rich-text :nodes=\"xyCont\"></rich-text></view></view> -->\r\n\t\t\t\t<view class=\"xytcCon_c\">\r\n\t\t\t\t\t<scroll-view\r\n\t\t\t\t\t    class=\"scroll-view\"\r\n\t\t\t\t\t    :style=\"{ height: scrollViewHeight + 'px' }\"\r\n\t\t\t\t\t    scroll-y\r\n\t\t\t\t\t    @scroll=\"onScroll\"\r\n\t\t\t\t\t\tid=\"myScrollView\"\r\n\t\t\t\t\t    ref=\"scrollViewRef\"\r\n\t\t\t\t\t  >\r\n\t\t\t\t\t    <!-- 这里放置需要滚动的内容 -->\r\n\t\t\t\t\t\t<view><rich-text :nodes=\"xyCont\"></rich-text></view>\r\n\t\t\t\t\t  </scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"xytcCon_b\">如您同意以上内容，请点击同意并继续，开始使用我们的产品和服务!</view>\r\n\t\t\t\t<view class=\"xytcCon_f\">\r\n\t\t\t\t\t<view class=\"ty\" @click=\"tyTap\" :style=\"isScrollToBottom ? '' : 'opacity:.7'\">{{isScrollToBottom ? '同意并继续' : '请仔细阅读下滑查看完毕'}}</view>\r\n\t\t\t\t\t<view class=\"noty\" @click=\"xyGbTap\">不同意</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tmyCardxqApi,\r\n\tjhcardApi,\r\n\tsetcardApi\r\n} from '@/config/http.achieve.js'\r\nimport util from '@/utils/utils.js';\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tisLogined:true,\r\n\t\t\tmyCardInfo:{id:0},\r\n\t\t\tjhhyToggle:false,\r\n\t\t\tpageId:0,\r\n\t\t\t\r\n\t\t\txyCont:'',\r\n\t\t\txyToggle:false,\r\n\t\t\tscrollViewHeight: 300, // 可根据实际情况调整滚动视图的高度\r\n\t\t\tisScrollToBottom: false ,// 标记是否滚动到底部\r\n\t\t\tqjbutton:'#131315',\r\n\t\t\tismr:false\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.imgbaseUrl = this.$baseUrl_ht;\r\n\t\tthis.pageId = option.id\r\n\t\tthis.myCardxqData();//门店详情\r\n\t},\r\n\tmethods: {\r\n\t\t//查看合同\r\n\t\tckhtTap(url){\r\n\t\t\t// console.log(this.myCardInfo.contract.substring(0,5),'sdsss')\r\n\t\t\tvar urls = this.myCardInfo.contract.substring(0,5) == 'https' ? this.myCardInfo.contract : (this.imgbaseUrl + this.myCardInfo.contract);\r\n\t\t\t// console.log(urls)\r\n\t\t\tuni.showLoading({ title: '下载中...' });\r\n\t\t\t// 下载文件到临时路径\r\n\t\t\tuni.downloadFile({\r\n\t\t\t  url: urls,\r\n\t\t\t  success: (res) => {\r\n\t\t\t    if (res.statusCode === 200) {\r\n\t\t\t      const tempPath = res.tempFilePath;\r\n\t\t\t      uni.openDocument({\r\n\t\t\t        filePath: tempPath,\r\n\t\t\t        showMenu: true, // 显示右上角菜单\r\n\t\t\t        success: () => {},\r\n\t\t\t        fail: () => {\r\n\t\t\t          uni.showToast({ title: '无法打开文件', icon: 'none' });\r\n\t\t\t        }\r\n\t\t\t      });\r\n\t\t\t    }\r\n\t\t\t  },\r\n\t\t\t  fail: (err) => {\r\n\t\t\t    uni.showToast({ title: '下载失败', icon: 'none' });\r\n\t\t\t  },\r\n\t\t\t  complete: () => {\r\n\t\t\t    uni.hideLoading();\r\n\t\t\t  }\r\n\t\t\t});\r\n\t\t},\r\n\t\tswitch1Change(e){\r\n\t\t\tconsole.log(e,'默认会员')\r\n\t\t\tthis.ismr = e.detail.value;\r\n\t\t\tthis.setcardData();\r\n\t\t},\r\n\t\t//设置默认会员卡\r\n\t\tsetcardData(id){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tsetcardApi({\r\n\t\t\t\tid:that.pageId\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('设置默认会员卡',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:'设置成功',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tonScroll(e) {\r\n\t\t\tvar that = this;\r\n\t\t  this.$nextTick(() => {\r\n\t\t\t\t\r\n\t\t\t  const query = uni.createSelectorQuery().in(this);\r\n\t\t\t  query.select('.scroll-view').boundingClientRect();\r\n\t\t\t  query.select('.scroll-view').scrollOffset();\r\n\t\t\t  query.exec((res) => {\r\n\t\t\t\t  console.log(res,'res')\r\n\t\t\t\tif (res && res[0] && res[1]) {\r\n\t\t\t\t  const scrollViewHeight = res[0].height;\r\n\t\t\t\t  const scrollTop = res[1].scrollTop;\r\n\t\t\t\t  const scrollHeight = e.detail.scrollHeight;\r\n\t\t\t\t  if (scrollTop + scrollViewHeight >= scrollHeight-20) {\r\n\t\t\t\t\tconsole.log('已经滚动到底部');\r\n\t\t\t\t\tthat.isScrollToBottom = true;\r\n\t\t\t\t  }\r\n\t\t\t\t}\r\n\t\t\t  });\r\n\t\t\t\r\n\t\t  });\r\n\t\t},\r\n\t\t//协议关闭\r\n\t\txyGbTap(){\r\n\t\t\tthis.xyToggle = false;\r\n\t\t\tthis.isScrollToBottom = false;\r\n\t\t},\r\n\t\t//同意弹窗\r\n\t\ttyTap(){\r\n\t\t\tif(!this.isScrollToBottom){\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tthis.jhhySubTap();//激活会员\r\n\t\t},\r\n\t\t//门店详情\r\n\t\tmyCardxqData(id){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tmyCardxqApi({\r\n\t\t\t\tid:that.pageId\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('门店详情',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.ismr = res.data.default == 1 ? true : false\r\n\t\t\t\t\tthat.myCardInfo = res.data;\r\n\t\t\t\t\tthat.xyCont = res.data.agreement;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//激活会员\r\n\t\tjhhySubTap(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tjhcardApi({\r\n\t\t\t\tid:that.pageId\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('激活会员',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.jhhyToggle = false;\r\n\t\t\t\t\tthat.xyToggle = false;\r\n\t\t\t\t\tthat.isScrollToBottom = false;\r\n\t\t\t\t\tthat.myCardxqData();//门店详情\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:'激活成功',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t\r\n</style>"], "sourceRoot": ""}