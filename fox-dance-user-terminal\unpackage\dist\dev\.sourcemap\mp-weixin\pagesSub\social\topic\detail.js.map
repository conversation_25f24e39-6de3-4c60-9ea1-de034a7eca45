{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/topic/detail.vue?8450", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/topic/detail.vue?9e49", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/topic/detail.vue?2d3e", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/topic/detail.vue?099e", "uni-app:///pagesSub/social/topic/detail.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/topic/detail.vue?7603", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/topic/detail.vue?3773"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "PostCard", "data", "topicId", "topicInfo", "posts", "refreshing", "loading", "hasMore", "page", "pageSize", "<PERSON><PERSON><PERSON><PERSON>", "filterTabs", "value", "computed", "selectedFilterIndex", "onLoad", "methods", "goBack", "uni", "showMore", "itemList", "success", "loadTopicInfo", "id", "description", "cover", "postCount", "followCount", "isFollowed", "loadPosts", "setTimeout", "generatePosts", "content", "images", "index", "user", "nickname", "avatar", "likeCount", "commentCount", "shareCount", "isLiked", "createTime", "onRefresh", "loadMore", "selectFilter", "to<PERSON><PERSON><PERSON><PERSON>", "publishPost", "url", "shareTopic", "onPostLike", "post", "onPostComment", "onPostShare", "goPostDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAquB,CAAgB,qsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eC2GzvB;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QAAAb;QAAAc;MAAA,GACA;QAAAd;QAAAc;MAAA,GACA;QAAAd;QAAAc;MAAA;IAEA;EACA;EACAC;IACAC;MAAA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IAEAC;MAAA;MACAD;QACAE;QACAC;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACAC;QACAzB;QACA0B;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEAC;MAAA;MACA;;MAEA;MACAC;QACA;QAEA;UACA;QACA;UACA;QACA;QAEA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MAEA;QACA;QACA;UACAR;UACAS;UACAC,wDACAC,+DACAA,eACA;UACAC;YACAZ;YACAa;YACAC;UACA;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAtC;MACA;MAEA;IACA;IAEAuC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA7B;QACA8B;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACAC;MACA;QACAA;MACA;QACAA;MACA;IACA;IAEAC;MACAlC;QACA8B;MACA;IACA;IAEAK;MACA;IACA;IAEAC;MACApC;QACA8B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnSA;AAAA;AAAA;AAAA;AAAg5C,CAAgB,gxCAAG,EAAC,C;;;;;;;;;;;ACAp6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/topic/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/topic/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=5790f86e&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=5790f86e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5790f86e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/topic/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=5790f86e&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.hasMore && _vm.posts.length > 0\n  var g1 = _vm.posts.length === 0 && !_vm.loading\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"topic-detail-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"nav-bar\">\n        <u-icon name=\"arrow-left\" size=\"20\" color=\"#fff\" @click=\"goBack\"></u-icon>\n        <text class=\"nav-title\">话题详情</text>\n        <u-icon name=\"more-dot-fill\" size=\"20\" color=\"#fff\" @click=\"showMore\"></u-icon>\n      </view>\n    </view>\n\n    <!-- 话题信息 -->\n    <view class=\"topic-header\" :style=\"{ backgroundImage: `url(${topicInfo.cover})` }\">\n      <view class=\"topic-overlay\">\n        <view class=\"topic-content\">\n          <text class=\"topic-name\">#{{ topicInfo.name }}</text>\n          <text class=\"topic-desc\">{{ topicInfo.description }}</text>\n          <view class=\"topic-stats\">\n            <text class=\"stat-item\">{{ topicInfo.postCount }}条帖子</text>\n            <text class=\"stat-item\">{{ topicInfo.followCount }}人关注</text>\n          </view>\n          <view class=\"topic-actions\">\n            <u-button \n              :type=\"topicInfo.isFollowed ? 'default' : 'primary'\"\n              size=\"default\"\n              :text=\"topicInfo.isFollowed ? '已关注' : '关注话题'\"\n              @click=\"toggleFollow\"\n            >{{ topicInfo.isFollowed ? '已关注' : '关注话题' }}</u-button>\n            <u-button \n              type=\"primary\" \n              plain\n              size=\"default\"\n              text=\"发布帖子\"\n              @click=\"publishPost\"\n            >发布帖子</u-button>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 帖子列表 -->\n    <scroll-view \n      class=\"posts-container\" \n      scroll-y \n      @scrolltolower=\"loadMore\"\n      :refresher-enabled=\"true\"\n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\"\n    >\n      <!-- 排序筛选 -->\n      <view class=\"filter-bar\">\n        <u-tabs\n          :list=\"filterTabs\"\n          :current=\"selectedFilterIndex\"\n          @change=\"selectFilter\"\n          :is-scroll=\"false\"\n          active-color=\"#2979ff\"\n          inactive-color=\"#666\"\n          :bar-width=\"40\"\n          :bar-height=\"4\"\n        ></u-tabs>\n      </view>\n\n      <!-- 帖子列表 -->\n      <view class=\"posts-list\">\n        <PostCard \n          v-for=\"post in posts\" \n          :key=\"post.id\"\n          :post=\"post\"\n          @like=\"onPostLike\"\n          @comment=\"onPostComment\"\n          @share=\"onPostShare\"\n          @click=\"goPostDetail\"\n        />\n      </view>\n      \n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"hasMore\">\n        <u-icon name=\"loading\" v-if=\"loading\" size=\"16\" color=\"#999\"></u-icon>\n        <text class=\"load-text\">{{ loading ? '加载中...' : '上拉加载更多' }}</text>\n      </view>\n      \n      <!-- 没有更多数据 -->\n      <view class=\"no-more\" v-if=\"!hasMore && posts.length > 0\">\n        <text class=\"no-more-text\">没有更多帖子了</text>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"posts.length === 0 && !loading\">\n        <u-icon name=\"file-text\" size=\"60\" color=\"#ccc\"></u-icon>\n        <text class=\"empty-text\">暂无帖子</text>\n        <text class=\"empty-desc\">成为第一个发布帖子的人吧</text>\n        <u-button \n          type=\"primary\" \n          size=\"default\"\n          text=\"发布帖子\"\n          @click=\"publishPost\"\n          style=\"margin-top: 20rpx;\"\n        ></u-button>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport PostCard from '../components/PostCard.vue'\n\nexport default {\n  name: 'TopicDetail',\n  components: {\n    PostCard\n  },\n  data() {\n    return {\n      topicId: '',\n      topicInfo: {},\n      posts: [],\n      refreshing: false,\n      loading: false,\n      hasMore: true,\n      page: 1,\n      pageSize: 10,\n      selectedFilter: 'latest',\n      filterTabs: [\n        { name: '最新', value: 'latest' },\n        { name: '最热', value: 'hot' },\n        { name: '精华', value: 'featured' }\n      ]\n    }\n  },\n  computed: {\n    selectedFilterIndex() {\n      return this.filterTabs.findIndex(tab => tab.value === this.selectedFilter)\n    }\n  },\n  onLoad(options) {\n    this.topicId = options.id || '1'\n    this.loadTopicInfo()\n    this.loadPosts()\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack()\n    },\n    \n    showMore() {\n      uni.showActionSheet({\n        itemList: ['举报话题', '分享话题'],\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            this.$u.toast('举报成功')\n          } else if (res.tapIndex === 1) {\n            this.shareTopic()\n          }\n        }\n      })\n    },\n    \n    loadTopicInfo() {\n      // 模拟API请求\n      this.topicInfo = {\n        id: this.topicId,\n        name: '今日穿搭',\n        description: '分享你的穿搭灵感，展示个人风格，发现时尚趋势',\n        cover: 'https://picsum.photos/400/300?random=1',\n        postCount: 1234,\n        followCount: 5678,\n        isFollowed: false\n      }\n    },\n    \n    loadPosts() {\n      this.loading = true\n      \n      // 模拟API请求\n      setTimeout(() => {\n        const newPosts = this.generatePosts(this.page)\n        \n        if (this.page === 1) {\n          this.posts = newPosts\n        } else {\n          this.posts = [...this.posts, ...newPosts]\n        }\n        \n        this.loading = false\n        this.refreshing = false\n        \n        // 模拟没有更多数据\n        if (this.page >= 3) {\n          this.hasMore = false\n        }\n      }, 1000)\n    },\n    \n    generatePosts(page) {\n      const posts = []\n      const startIndex = (page - 1) * this.pageSize\n      \n      for (let i = 0; i < this.pageSize; i++) {\n        const index = startIndex + i\n        const post = {\n          id: index + 1,\n          content: `这是关于${this.topicInfo.name}的第${index + 1}条帖子内容，分享一些有趣的想法和体验...`,\n          images: [\n            `https://picsum.photos/300/400?random=${index * 3 + 1}`,\n            `https://picsum.photos/300/400?random=${index * 3 + 2}`\n          ],\n          user: {\n            id: index + 1,\n            nickname: `用户${index + 1}`,\n            avatar: `https://picsum.photos/100/100?random=${index + 10}`\n          },\n          likeCount: Math.floor(Math.random() * 100) + 10,\n          commentCount: Math.floor(Math.random() * 50) + 5,\n          shareCount: Math.floor(Math.random() * 20) + 1,\n          isLiked: Math.random() > 0.5,\n          createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()\n        }\n        posts.push(post)\n      }\n      \n      return posts\n    },\n    \n    onRefresh() {\n      this.refreshing = true\n      this.page = 1\n      this.hasMore = true\n      this.loadPosts()\n    },\n    \n    loadMore() {\n      if (!this.loading && this.hasMore) {\n        this.page++\n        this.loadPosts()\n      }\n    },\n    \n    selectFilter(index) {\n      this.selectedFilter = this.filterTabs[index].value\n      this.page = 1\n      this.hasMore = true\n      this.loadPosts()\n    },\n    \n    toggleFollow() {\n      this.topicInfo.isFollowed = !this.topicInfo.isFollowed\n      if (this.topicInfo.isFollowed) {\n        this.topicInfo.followCount++\n        this.$u.toast('关注成功')\n      } else {\n        this.topicInfo.followCount--\n        this.$u.toast('取消关注')\n      }\n    },\n    \n    publishPost() {\n      uni.navigateTo({\n        url: `/pagesSub/social/publish/index?topicId=${this.topicId}&topicName=${this.topicInfo.name}`\n      })\n    },\n    \n    shareTopic() {\n      this.$u.toast('分享成功')\n    },\n    \n    onPostLike(post) {\n      post.isLiked = !post.isLiked\n      if (post.isLiked) {\n        post.likeCount++\n      } else {\n        post.likeCount--\n      }\n    },\n    \n    onPostComment(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    },\n    \n    onPostShare(post) {\n      this.$u.toast('分享成功')\n    },\n    \n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.topic-detail-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: transparent;\n  padding: var(--status-bar-height) 32rpx 0;\n}\n\n.nav-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n}\n\n.nav-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #fff;\n}\n\n.topic-header {\n  height: 500rpx;\n  background-size: cover;\n  background-position: center;\n  position: relative;\n}\n\n.topic-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n  padding: 80rpx 32rpx 40rpx;\n}\n\n.topic-content {\n  color: #fff;\n}\n\n.topic-name {\n  font-size: 48rpx;\n  font-weight: 700;\n  margin-bottom: 16rpx;\n  display: block;\n}\n\n.topic-desc {\n  font-size: 28rpx;\n  line-height: 1.5;\n  margin-bottom: 24rpx;\n  opacity: 0.9;\n  display: block;\n}\n\n.topic-stats {\n  display: flex;\n  gap: 32rpx;\n  margin-bottom: 32rpx;\n}\n\n.stat-item {\n  font-size: 24rpx;\n  opacity: 0.8;\n}\n\n.topic-actions {\n  display: flex;\n  gap: 24rpx;\n}\n\n.posts-container {\n  background: #fff;\n  border-radius: 32rpx 32rpx 0 0;\n  min-height: calc(100vh - 500rpx);\n}\n\n.filter-bar {\n  padding: 32rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.posts-list {\n  padding: 0 32rpx;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx 0;\n  gap: 16rpx;\n}\n\n.load-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.no-more {\n  display: flex;\n  justify-content: center;\n  padding: 40rpx 0;\n}\n\n.no-more-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 0;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #999;\n  margin: 32rpx 0 16rpx;\n}\n\n.empty-desc {\n  font-size: 28rpx;\n  color: #ccc;\n  margin-bottom: 40rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=5790f86e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=5790f86e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752818685255\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}