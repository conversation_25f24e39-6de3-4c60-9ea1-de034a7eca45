@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.detail-container.data-v-0722cd1a {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 60px;
}
.header.data-v-0722cd1a {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: 25px;
}
.header-content.data-v-0722cd1a {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}
.title.data-v-0722cd1a {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.post-detail.data-v-0722cd1a {
  background: #fff;
  padding: 40rpx 32rpx;
  margin-bottom: 16rpx;
}
.user-info.data-v-0722cd1a {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.user-details.data-v-0722cd1a {
  flex: 1;
  margin-left: 24rpx;
}
.username.data-v-0722cd1a {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
}
.time.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}
.location.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}
.content-text.data-v-0722cd1a {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}
.topic-tags.data-v-0722cd1a {
  margin-bottom: 32rpx;
}
.topic-tag.data-v-0722cd1a {
  color: #2979ff;
  font-size: 28rpx;
  margin-right: 16rpx;
}
.post-images.data-v-0722cd1a {
  margin-bottom: 32rpx;
}
.image-swiper.data-v-0722cd1a {
  width: 100%;
  height: 600rpx;
  border-radius: 24rpx;
  overflow: hidden;
}
.swiper-image.data-v-0722cd1a {
  width: 100%;
  height: 100%;
}
.post-stats.data-v-0722cd1a {
  padding: 24rpx 0;
  border-top: 2rpx solid #f0f0f0;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 24rpx;
}
.stat-item.data-v-0722cd1a {
  font-size: 26rpx;
  color: #666;
  margin-right: 32rpx;
}
.action-bar.data-v-0722cd1a {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.action-item.data-v-0722cd1a {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.action-text.data-v-0722cd1a {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}
.comments-section.data-v-0722cd1a {
  background: #fff;
  padding: 32rpx;
}
.comments-header.data-v-0722cd1a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}
.comments-title.data-v-0722cd1a {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.comment-item.data-v-0722cd1a {
  display: flex;
  margin-bottom: 32rpx;
}
.comment-content.data-v-0722cd1a {
  flex: 1;
  margin-left: 24rpx;
}
.comment-header.data-v-0722cd1a {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.comment-username.data-v-0722cd1a {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}
.comment-time.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
}
.comment-text.data-v-0722cd1a {
  font-size: 28rpx;
  line-height: 1.4;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}
.replies.data-v-0722cd1a {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
}
.reply-item.data-v-0722cd1a {
  margin-bottom: 8rpx;
}
.reply-user.data-v-0722cd1a {
  font-size: 26rpx;
  color: #2979ff;
  margin-right: 8rpx;
}
.reply-text.data-v-0722cd1a {
  font-size: 26rpx;
  color: #333;
}
.comment-actions.data-v-0722cd1a {
  display: flex;
  align-items: center;
  gap: 32rpx;
}
.comment-action.data-v-0722cd1a {
  display: flex;
  align-items: center;
}
.action-count.data-v-0722cd1a, .action-text.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}
.comment-input-bar.data-v-0722cd1a {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}
.input-container.data-v-0722cd1a {
  display: flex;
  align-items: center;
  width: 100%;
  height: 104rpx;
  position: relative;
}
.comment-input.data-v-0722cd1a {
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  background: #f5f5f5;
  border: none;
  border-radius: 36rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  appearance: none;
  transition: none;
  position: absolute;
  left: 48rpx;
  top: 16rpx;
}
.comment-input.data-v-0722cd1a:focus {
  background: #f5f5f5;
  border: none;
  outline: none;
  box-shadow: none;
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 32rpx;
  left: 48rpx;
  top: 16rpx;
}
.send-btn.data-v-0722cd1a {
  width: 80rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ccc;
  white-space: nowrap;
  position: absolute;
  right: 0;
  top: 16rpx;
}
.send-btn.active.data-v-0722cd1a {
  color: #2979ff;
}
/* 新增样式 - 参考comment.vue的设计 */
.comment-item.data-v-0722cd1a {
  background: rgba(255, 255, 255, 0.95) !important;
  padding: 32rpx !important;
  margin-bottom: 20rpx !important;
  border-radius: 28rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08) !important;
  border: 1rpx solid rgba(255, 255, 255, 0.8) !important;
  transition: -webkit-transform 0.2s ease !important;
  transition: transform 0.2s ease !important;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease !important;
}
.user-avatar.data-v-0722cd1a {
  margin-right: 24rpx;
  flex-shrink: 0;
}
.user-info-row.data-v-0722cd1a {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.user-info.data-v-0722cd1a {
  flex: 1;
  min-width: 0;
}
.user-name.data-v-0722cd1a {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.user-level.data-v-0722cd1a {
  font-size: 20rpx;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
  font-weight: 500;
}
.time.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
  letter-spacing: 0.3rpx;
}
.like-btn.data-v-0722cd1a {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 32rpx;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.1);
}
.like-btn.data-v-0722cd1a:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.15);
}
.like-btn text.data-v-0722cd1a {
  font-size: 26rpx;
  color: #ff6b87;
  margin-left: 10rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}
.text.data-v-0722cd1a {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
  letter-spacing: 0.3rpx;
  word-break: break-word;
}
.expand-btn.data-v-0722cd1a {
  color: #ff6b87;
  font-size: 26rpx;
  margin-top: 12rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 107, 135, 0.1);
  border-radius: 20rpx;
  display: inline-block;
  transition: all 0.3s ease;
}
.expand-btn.data-v-0722cd1a:active {
  background: rgba(255, 107, 135, 0.2);
}
.actions.data-v-0722cd1a {
  display: flex;
  align-items: center;
  gap: 32rpx;
  margin-top: 16rpx;
}
.reply-btn.data-v-0722cd1a {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 28rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);
}
.reply-btn.data-v-0722cd1a:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(255, 107, 135, 0.15);
}
.reply-btn text.data-v-0722cd1a {
  font-size: 26rpx;
  color: #ff6b87;
  margin-left: 10rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}
.more-btn.data-v-0722cd1a {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 107, 135, 0.1);
  transition: all 0.3s ease;
}
.more-btn.data-v-0722cd1a:active {
  background: rgba(255, 107, 135, 0.2);
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.reply-preview.data-v-0722cd1a {
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.05) 0%, rgba(255, 142, 83, 0.05) 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-top: 20rpx;
  border: 1rpx solid rgba(255, 107, 135, 0.1);
}
.reply-item.data-v-0722cd1a {
  font-size: 26rpx;
  margin-bottom: 16rpx;
  line-height: 1.7;
  color: #6a6a6a;
  letter-spacing: 0.3rpx;
}
.reply-item.data-v-0722cd1a:last-child {
  margin-bottom: 0;
}
.reply-nickname.data-v-0722cd1a {
  color: #ff6b87 !important;
  font-weight: 600;
}
.reply-to.data-v-0722cd1a {
  color: #ff8e53 !important;
  font-weight: 500;
}
.reply-content.data-v-0722cd1a {
  color: #666;
}
.view-more.data-v-0722cd1a {
  color: #ff6b87;
  font-size: 26rpx;
  font-weight: 500;
  margin-top: 12rpx;
  padding: 8rpx 0;
}
.view-more.data-v-0722cd1a:active {
  opacity: 0.7;
}
/* 回复状态指示器 */
.reply-indicator.data-v-0722cd1a {
  background: rgba(255, 107, 135, 0.1);
  border-bottom: 1rpx solid rgba(255, 107, 135, 0.15);
  padding: 16rpx 32rpx;
  opacity: 1;
  transition: opacity 0.2s ease;
}
.reply-info.data-v-0722cd1a {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.reply-text.data-v-0722cd1a {
  font-size: 26rpx;
  color: #ff6b87;
  font-weight: 500;
}
.cancel-reply-btn.data-v-0722cd1a {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 107, 135, 0.15);
  border-radius: 50%;
  font-size: 24rpx;
  color: #ff6b87;
  font-weight: bold;
}
.cancel-reply-btn.data-v-0722cd1a:active {
  background: rgba(255, 107, 135, 0.25);
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
/* 更多操作弹窗 */
.action-popup.data-v-0722cd1a {
  padding: 40rpx 0 60rpx;
  background: white;
  border-radius: 30rpx 30rpx 0 0;
}
.action-item.data-v-0722cd1a {
  display: flex;
  align-items: center;
  padding: 32rpx 40rpx;
  font-size: 32rpx;
  color: #333;
}
.action-item.data-v-0722cd1a:active {
  background: #f8f9fa;
}
.action-item.delete.data-v-0722cd1a {
  color: #f56c6c;
}
.action-icon.data-v-0722cd1a {
  margin-right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

