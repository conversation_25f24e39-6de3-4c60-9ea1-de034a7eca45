@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.detail-container.data-v-0722cd1a {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 60px;
}
.header.data-v-0722cd1a {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: 25px;
}
.header-content.data-v-0722cd1a {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}
.title.data-v-0722cd1a {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.post-detail.data-v-0722cd1a {
  background: #fff;
  padding: 40rpx 32rpx;
  margin-bottom: 16rpx;
}
.user-info.data-v-0722cd1a {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.user-details.data-v-0722cd1a {
  flex: 1;
  margin-left: 24rpx;
}
.username.data-v-0722cd1a {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
}
.time.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}
.location.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}
.content-text.data-v-0722cd1a {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}
.topic-tags.data-v-0722cd1a {
  margin-bottom: 32rpx;
}
.topic-tag.data-v-0722cd1a {
  color: #2979ff;
  font-size: 28rpx;
  margin-right: 16rpx;
}
.post-images.data-v-0722cd1a {
  margin-bottom: 32rpx;
}
.image-swiper.data-v-0722cd1a {
  width: 100%;
  height: 600rpx;
  border-radius: 24rpx;
  overflow: hidden;
}
.swiper-image.data-v-0722cd1a {
  width: 100%;
  height: 100%;
}
.post-stats.data-v-0722cd1a {
  padding: 24rpx 0;
  border-top: 2rpx solid #f0f0f0;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 24rpx;
}
.stat-item.data-v-0722cd1a {
  font-size: 26rpx;
  color: #666;
  margin-right: 32rpx;
}
.action-bar.data-v-0722cd1a {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.action-item.data-v-0722cd1a {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.action-text.data-v-0722cd1a {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}
.comments-section.data-v-0722cd1a {
  background: #fff;
  padding: 32rpx;
}
.comments-header.data-v-0722cd1a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}
.comments-title.data-v-0722cd1a {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.comment-item.data-v-0722cd1a {
  display: flex;
  margin-bottom: 32rpx;
}
.comment-content.data-v-0722cd1a {
  flex: 1;
  margin-left: 24rpx;
}
.comment-header.data-v-0722cd1a {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.comment-username.data-v-0722cd1a {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}
.comment-time.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
}
.comment-text.data-v-0722cd1a {
  font-size: 28rpx;
  line-height: 1.4;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}
.replies.data-v-0722cd1a {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
}
.reply-item.data-v-0722cd1a {
  margin-bottom: 8rpx;
}
.reply-user.data-v-0722cd1a {
  font-size: 26rpx;
  color: #2979ff;
  margin-right: 8rpx;
}
.reply-text.data-v-0722cd1a {
  font-size: 26rpx;
  color: #333;
}
.comment-actions.data-v-0722cd1a {
  display: flex;
  align-items: center;
  gap: 32rpx;
}
.comment-action.data-v-0722cd1a {
  display: flex;
  align-items: center;
}
.action-count.data-v-0722cd1a, .action-text.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}
.comment-input-bar.data-v-0722cd1a {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}
.input-container.data-v-0722cd1a {
  display: flex;
  align-items: center;
  width: 100%;
  height: 104rpx;
  position: relative;
}
.comment-input.data-v-0722cd1a {
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  background: #f5f5f5;
  border: none;
  border-radius: 36rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  appearance: none;
  transition: none;
  position: absolute;
  left: 48rpx;
  top: 16rpx;
}
.comment-input.data-v-0722cd1a:focus {
  background: #f5f5f5;
  border: none;
  outline: none;
  box-shadow: none;
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 32rpx;
  left: 48rpx;
  top: 16rpx;
}
.send-btn.data-v-0722cd1a {
  width: 80rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ccc;
  white-space: nowrap;
  position: absolute;
  right: 0;
  top: 16rpx;
}
.send-btn.active.data-v-0722cd1a {
  color: #2979ff;
}

