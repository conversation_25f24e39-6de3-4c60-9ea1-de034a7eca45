@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.detail-container.data-v-0722cd1a {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 60px;
}
.header.data-v-0722cd1a {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: 25px;
}
.header-content.data-v-0722cd1a {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}
.title.data-v-0722cd1a {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.post-detail.data-v-0722cd1a {
  background: #fff;
  padding: 40rpx 32rpx;
  margin-bottom: 16rpx;
}
.user-info.data-v-0722cd1a {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.user-details.data-v-0722cd1a {
  flex: 1;
  margin-left: 24rpx;
}
.username.data-v-0722cd1a {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
}
.time.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}
.location.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}
.content-text.data-v-0722cd1a {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}
.topic-tags.data-v-0722cd1a {
  margin-bottom: 32rpx;
}
.topic-tag.data-v-0722cd1a {
  color: #2979ff;
  font-size: 28rpx;
  margin-right: 16rpx;
}
.post-images.data-v-0722cd1a {
  margin-bottom: 32rpx;
}
.image-swiper.data-v-0722cd1a {
  width: 100%;
  height: 600rpx;
  border-radius: 24rpx;
  overflow: hidden;
}
.swiper-image.data-v-0722cd1a {
  width: 100%;
  height: 100%;
}
.post-stats.data-v-0722cd1a {
  padding: 24rpx 0;
  border-top: 2rpx solid #f0f0f0;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 24rpx;
}
.stat-item.data-v-0722cd1a {
  font-size: 26rpx;
  color: #666;
  margin-right: 32rpx;
}
.action-bar.data-v-0722cd1a {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.action-item.data-v-0722cd1a {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.action-text.data-v-0722cd1a {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}
.comments-section.data-v-0722cd1a {
  background: #fff;
  padding: 32rpx;
}
.comments-header.data-v-0722cd1a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}
.comments-title.data-v-0722cd1a {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.comment-item.data-v-0722cd1a {
  display: flex;
  margin-bottom: 32rpx;
}
.comment-content.data-v-0722cd1a {
  flex: 1;
  margin-left: 24rpx;
}
.comment-header.data-v-0722cd1a {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.comment-username.data-v-0722cd1a {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}
.comment-time.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
}
.comment-text.data-v-0722cd1a {
  font-size: 28rpx;
  line-height: 1.4;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}
.replies.data-v-0722cd1a {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
}
.reply-item.data-v-0722cd1a {
  margin-bottom: 8rpx;
}
.reply-user.data-v-0722cd1a {
  font-size: 26rpx;
  color: #2979ff;
  margin-right: 8rpx;
}
.reply-text.data-v-0722cd1a {
  font-size: 26rpx;
  color: #333;
}
.comment-actions.data-v-0722cd1a {
  display: flex;
  align-items: center;
  gap: 32rpx;
}
.comment-action.data-v-0722cd1a {
  display: flex;
  align-items: center;
}
.action-count.data-v-0722cd1a, .action-text.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}
.comment-input-bar.data-v-0722cd1a {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}
.input-container.data-v-0722cd1a {
  display: flex;
  align-items: center;
  width: 100%;
  height: 104rpx;
  position: relative;
}
.comment-input.data-v-0722cd1a {
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  background: #f5f5f5;
  border: none;
  border-radius: 36rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  appearance: none;
  transition: none;
  position: absolute;
  left: 48rpx;
  top: 16rpx;
}
.comment-input.data-v-0722cd1a:focus {
  background: #f5f5f5;
  border: none;
  outline: none;
  box-shadow: none;
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 32rpx;
  left: 48rpx;
  top: 16rpx;
}
.send-btn.data-v-0722cd1a {
  width: 80rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ccc;
  white-space: nowrap;
  position: absolute;
  right: 0;
  top: 16rpx;
}
.send-btn.active.data-v-0722cd1a {
  color: #2979ff;
}
/* 重新设计的评论样式 - 符合帖子详情页风格 */
.comment-item.data-v-0722cd1a {
  display: flex !important;
  padding: 24rpx 0 !important;
  margin-bottom: 0 !important;
  border-bottom: 1rpx solid #f0f0f0 !important;
  background: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  border: none !important;
  transition: none !important;
}
.comment-item.data-v-0722cd1a:last-child {
  border-bottom: none;
}
.user-avatar.data-v-0722cd1a {
  margin-right: 16rpx;
  flex-shrink: 0;
}
.user-info-row.data-v-0722cd1a {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}
.user-info.data-v-0722cd1a {
  flex: 1;
  min-width: 0;
}
.user-name.data-v-0722cd1a {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}
.user-level.data-v-0722cd1a {
  font-size: 18rpx;
  color: white;
  padding: 2rpx 8rpx;
  border-radius: 12rpx;
  margin-left: 12rpx;
  font-weight: 500;
  background: #2979ff;
}
.time.data-v-0722cd1a {
  font-size: 24rpx;
  color: #999;
}
.like-btn.data-v-0722cd1a {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f8f9fa;
  border: 1rpx solid #e4e7ed;
  transition: all 0.2s ease;
  min-width: 60rpx;
  justify-content: center;
}
.like-btn.data-v-0722cd1a:active {
  background: #e4e7ed;
}
.like-btn text.data-v-0722cd1a {
  font-size: 24rpx;
  color: #666;
  margin-left: 6rpx;
  font-weight: 400;
}
.like-btn.liked.data-v-0722cd1a {
  background: #fff0f0;
  border-color: #ff4757;
}
.like-btn.liked text.data-v-0722cd1a {
  color: #ff4757;
}
.text.data-v-0722cd1a {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16rpx;
  word-break: break-word;
}
.expand-btn.data-v-0722cd1a {
  color: #2979ff;
  font-size: 26rpx;
  margin-top: 8rpx;
  padding: 4rpx 0;
  display: inline-block;
  transition: opacity 0.2s ease;
}
.expand-btn.data-v-0722cd1a:active {
  opacity: 0.7;
}
.actions.data-v-0722cd1a {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-top: 12rpx;
}
.reply-btn.data-v-0722cd1a {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f8f9fa;
  border: 1rpx solid #e4e7ed;
  transition: all 0.2s ease;
}
.reply-btn.data-v-0722cd1a:active {
  background: #e4e7ed;
}
.reply-btn text.data-v-0722cd1a {
  font-size: 24rpx;
  color: #666;
  margin-left: 6rpx;
  font-weight: 400;
}
.more-btn.data-v-0722cd1a {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8f9fa;
  transition: all 0.2s ease;
}
.more-btn.data-v-0722cd1a:active {
  background: #e4e7ed;
}
.reply-preview.data-v-0722cd1a {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-top: 16rpx;
  border: 1rpx solid #f0f0f0;
}
.reply-item.data-v-0722cd1a {
  font-size: 26rpx;
  margin-bottom: 8rpx;
  line-height: 1.5;
  color: #666;
}
.reply-item.data-v-0722cd1a:last-child {
  margin-bottom: 0;
}
.reply-nickname.data-v-0722cd1a {
  color: #2979ff !important;
  font-weight: 500;
}
.reply-to.data-v-0722cd1a {
  color: #666 !important;
  font-weight: 400;
}
.reply-content.data-v-0722cd1a {
  color: #666;
}
.view-more.data-v-0722cd1a {
  color: #2979ff;
  font-size: 26rpx;
  font-weight: 400;
  margin-top: 8rpx;
  padding: 4rpx 0;
}
.view-more.data-v-0722cd1a:active {
  opacity: 0.7;
}
/* 回复状态指示器 - 符合整体风格 */
.reply-indicator.data-v-0722cd1a {
  background: #f8f9fa;
  border-bottom: 1rpx solid #e4e7ed;
  padding: 16rpx 32rpx;
  opacity: 1;
  transition: opacity 0.2s ease;
}
.reply-info.data-v-0722cd1a {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.reply-text.data-v-0722cd1a {
  font-size: 26rpx;
  color: #2979ff;
  font-weight: 400;
}
.cancel-reply-btn.data-v-0722cd1a {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e4e7ed;
  border-radius: 50%;
  font-size: 20rpx;
  color: #666;
  font-weight: normal;
}
.cancel-reply-btn.data-v-0722cd1a:active {
  background: #d3d4d6;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
/* 更多操作弹窗 - 简洁风格 */
.action-popup.data-v-0722cd1a {
  padding: 32rpx 0 40rpx;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
}
.action-item.data-v-0722cd1a {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 400;
}
.action-item.data-v-0722cd1a:active {
  background: #f8f9fa;
}
.action-item.delete.data-v-0722cd1a {
  color: #ff4757;
}
.action-icon.data-v-0722cd1a {
  margin-right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

