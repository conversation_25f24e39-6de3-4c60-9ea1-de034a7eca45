<template>
	<view v-if="loding" :style="{ '--qjbutton-color': qjbutton }">
		<view class="cont">
			<view class="cont_row flex col-top">
				<view class="cont_row_l">
					收货人
				</view>
				<view class="cont_row_r flex-1">
					<input v-model="name" maxlength="11" type="text" placeholder="收货人姓名" />
					<view class="check_sex flex">
						<view class="check_sex_li flex" :class="sex == 1 ? 'check_sex_li_ac' : ''" @click="sex = 1">
							<image v-if="sex==1" src="/static/images/dzxz-11.png" mode="scaleToFill"></image>
							<image v-else src="/static/images/dzxz.png" mode="scaleToFill"></image>
							男士
						</view>
						<view class="check_sex_li flex" :class="sex == 2 ? 'check_sex_li_ac' : ''" @click="sex = 2">
							<image v-if="sex==2" src="/static/images/dzxz-11.png" mode="scaleToFill"></image>
							<image v-else src="/static/images/dzxz.png" mode="scaleToFill"></image>
							女士
						</view>
					</view>
				</view>
			</view>

			<view class="cont_row flex col-top" @click="choose">
				<view class="cont_row_l">
					选择地址
				</view>
				<view class="cont_row_r flex-1 flex row-between">
					<view class="select line-1" :style="{color:address?'#333':'#999'}" style="width: 450rpx;">
						{{address?address:'选择详细地址'}}
					</view>
					<image src="/static/images/index_help_more.png" mode="scaleToFill" class="select_img"></image>
				</view>
			</view>
			<view class="cont_row flex ">
				<view class="cont_row_l">
					详细地址
				</view>
				<view class="cont_row_r flex-1 flex row-between">
					<textarea v-model="address_detail" placeholder="请输入省市区县、乡镇" auto-height placeholder-style="color:#999999;"></textarea>
				</view>
			</view>
			<view class="cont_row flex ">
				<view class="cont_row_l">
					手机号
				</view>
				<view class="cont_row_r flex-1 flex row-between">
					<input v-model="mobile" maxlength="11" type="number" placeholder="收货人手机号" placeholder-class="color9" />
				</view>
			</view>

		</view>
		<view class="default flex row-between">
			<view class="default_t ">
				<view class="">
					设置为默认地址
				</view>
				<view class="default_d">
					启动时将优先定位在默认地址，避免选错
				</view>

			</view>
			<view class="">
				<u-switch :active-color="qjbutton" size="40" v-model="switchVal" @change="change"></u-switch>
			</view>
		</view>

		<!-- <view class="foot">
			<view v-if="!Edit_id" class="add_addr btn" style="margin: 0 auto;" @click="submit()">
				添加地址
			</view>
			<view class="edit_use flex row-between" v-if="Edit_id">
				<view class="btn" @click="delAddress">
					删除
				</view>
				<view class="btn" @click="submit">
					保存
				</view>
			</view>
		</view> -->
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		<view class="add_foo" v-if="!Edit_id"><view class="" @click="submit">添加地址</view></view>
		<view class="add_foo add_fooEdit" v-if="Edit_id">
			<view @click="delAddress">
				删除
			</view>
			<view @click="submit">
				保存
			</view>
		</view>
		
		<!-- 弹窗 -->
		<u-popup v-model="showPopup" mode="center" border-radius="20">
			<view class="prompt">
				<view class="prompt_t">
					<view class="prompt_t_img">
						<image src="/static/images/popup-icon.png" mode="scaleToFill"></image> 
					</view>
					<view class="prompt_t_text">
						提示
					</view>
				</view>
				<view class="prompt_c">
					确定退出当前删除？
				</view>
				<view class="prompt_d">
					<view class="prompt_d_l" @click="showPopup=  false">
						取消
					</view>
					<view class="prompt_d_r" @click="confirmDel">
						确定
					</view>
				</view>
			</view>
		</u-popup>
		
	</view>
</template>

<script>
	import{
		addrAdd,
		addrDel,
		addrEdit
	} from '@/config/http.achieve.js'
	export default {
		data() {
			return {
				switchVal: false,
				showPopup:false,
				name:'',
				sex:1,
				address:'',
				address_detail:'',
				lat:'',
				lng:'',
				mobile:'',
				Edit_id:'',
				editType:0,
				loding:false,
				qjbutton:'#131315',
			}
		},
		onLoad(opt) {
			this.qjbutton = uni.getStorageSync('storeInfo').button
			uni.setNavigationBarTitle({
				title:opt.type == 0 ? '添加地址' : '编辑地址'
			})
			var that = this;
			console.log(opt,'opt')
			this.editType = opt.type
			if(opt.data){
				if(opt.data == 'undefined'){
					this.loding = true;
					return false;
				}
				let item = JSON.parse(opt.data)
				this.Edit_id = item.id
				this.name = item.name
				this.sex =item.gender
				this.address = item.area
				this.address_detail = item.detail
				this.lat = item.lat
				this.lng = item.lng
				this.mobile = item.phone
				this.switchVal= item.is_default ==1?true:false;
				uni.showLoading({
					title:'加载中'
				})
				setTimeout(function(){
					uni.hideLoading();
					that.loding = true;
				},500)
			}
		},
		methods: {
			choose() {
				let that = this
				uni.chooseLocation({
					success: function(res) {
						that.address_detail = res.address
						that.address = res.name
						that.lat = res.latitude
						that.lng = res.longitude
						console.log('位置名称：' + res.name);
						console.log('详细地址：' + res.address);
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude);
					}
				});
			},
			change(e) {
				console.log('change', );
				this.switchVal = e
			},
			
			submit(){
				if(this.name==''){
					this.$toast({title:'请输入收货人姓名'})
					return
				}
				if(this.address==''){
					this.$toast({title:'请选择地址'})
					return
				}
				if(this.address_detail==''){
					this.$toast({title:'请输入详细地址'})
					return
				}
				var reg = /^1[3456789]\d{9}$/
				if(this.mobile==''){
					this.$toast({title:'请输入手机号'})
					return
				}
				if (!reg.test(this.mobile)) {
				  uni.showToast({
					  icon:'none',
					  title: '请输入正确的手机号码',
					  duration: 2000
				  });
				  return false;
				}
				
				uni.showLoading({
					title:'加载中'
				})
				var data = {
					name:this.name,
					phone:this.mobile,
					gender:this.sex,
					area:this.address,
					detail:this.address_detail,
					//lat:this.lat,
					//lng:this.lng,
					is_default:String(this.switchVal==false?'0':'1'),
					addr_id:this.editType == 0 ? 0 : this.Edit_id
				}
				if(this.editType == 0){
					delete data.addr_id
				}
				addrAdd(data).then(res=>{
					if(res.code == 1){
						uni.hideLoading();
						this.$toast({title:this.editType == 0 ? '添加成功' : '编辑成功'})
						setTimeout(()=>{
							uni.navigateBack()
						},1500)
					}
				})
			},
			//删除收货地址
			delAddress(id){
				var that = this;
				
				uni.showModal({
					title: '提示',
					content: '确定要删除该地址吗？',
					success: function (res) {
						if (res.confirm) {
							
							uni.showLoading({
								title: '加载中'
							}); 
							addrDel({
								id: that.Edit_id
							}).then(res => {
								if (res.code == 1) {
									uni.hideLoading();
									if (uni.getStorageSync('diancan')) {
										if(uni.getStorageSync('diancan').addressId == this.nowId){
											uni.removeStorageSync('diancan')
										}
									}
									that.$toast({title:"删除成功"})
									setTimeout(()=>{
										uni.navigateBack()
									},1000)
								}
							})
							
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
				
			},
			confirmDel(){
				addrDel({
					addressId:this.Edit_id
				}).then(res=>{
					if(res.code == 1){
						this.$toast({title:"删除成功"})
						setTimeout(()=>{
							uni.navigateBack()
						},500)
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.cont {
		margin: 22rpx auto 0;
		width: 698rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		padding: 26rpx 26rpx 0;

		.cont_row {
			padding-top: 26rpx;
			border-bottom: 2rpx solid rgba(167, 167, 167, 0.2);
			padding-bottom: 26rpx;

			&:last-child {
				border-bottom: none;
			}

			&:nth-child(1) {
				padding-top: 0;
			}

			.cont_row_l {
				width: 144rpx;
				font-size: 26rpx;
				color: #333333;
				line-height: 30rpx;

			}

			.cont_row_r {

				input {
					width: 100%;
					height: 100%;
					font-size: 26rpx;
					color: #333;
					line-height: 30rpx;
				}

				.color9 {
					color: #999;
				}

				textarea {
					width: 100%;
					font-size: 26rpx;
					color: #333;
					line-height: 30rpx;
				}

				.select {
					font-size: 26rpx;
					color: #999999;
					line-height: 30rpx;
				}

				.select_img {
					width:32rpx;
					height: 32rpx;
				}

				.check_sex {
					margin-top: 26rpx;

					.check_sex_li {
						image {
							width: 40rpx;
							height: 40rpx;
							margin-right: 8rpx;
						}

						font-size: 26rpx;
						color: #999;
						line-height: 30rpx;
						margin-right: 26rpx;
					}
				}
			}
		}
	}

	.default {
		margin: 30rpx auto 0;
		width: 698rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		padding: 26rpx;

		.default_t {
			view {
				&:nth-child(1) {
					font-size: 26rpx;
					color: #333333;
					line-height: 30rpx;
				}


			}

			.default_d {
				margin-top: 10rpx;
				font-size: 26rpx;
				color: #999999;
				line-height: 30rpx;
			}
		}

		// .default_d {
		// 	margin-top: 8rpx;
		// 	font-family: SourceHanSansCN;
		// 	font-size: 24rpx;
		// 	color: #A3A3A3;
		// 	line-height: 28rpx;
		// }
	}

	.foot {
		position: fixed;
		width: 100%;
		bottom: 68rpx;

		.btn {

			width: 608rpx;
			height: 90rpx;
			border-radius: 94rpx 94rpx 94rpx 94rpx;
			color: #333333;
			font-size: 32rpx;
		}

		.edit_use {
			padding: 0 26rpx;

			.btn {
				&:nth-child(1) {
					width: 336rpx;
					height: 88rpx;
					border-radius: 88rpx 88rpx 88rpx 88rpx;
					border: 2rpx solid #09C867;
					background: transparent;
					font-size: 26rpx;
					color: #09C867;
				}

				&:nth-child(2) {
					width: 336rpx;
					height: 88rpx;
					border-radius: 88rpx 88rpx 88rpx 88rpx;
					font-size: 26rpx;
					color: #fff;
				}
			}
		}
	}
	.qu_box{
		width: 480rpx;
		height: 314rpx;
		padding: 50rpx;
		.qu_box_title{
			text-align: center;
			font-weight: 500;
			font-size: 32rpx;
			color: #333333;
			line-height: 39rpx;
		}
		.qu_box_cont{
			margin-top: 26rpx;
			font-size: 26rpx;
			color: #333333;
			line-height: 39rpx;
			text-align: center;
		}
		.qu_box_use{
			padding: 0 26rpx;
			margin-top: 50rpx;
			.qu_box_use_li{
				&:nth-child(1){
					background: #CBF1DE;
				}
				width: 144rpx;
				height: 60rpx;
				background: #09C867;
				border-radius: 88rpx 88rpx 88rpx 88rpx;
				font-size: 26rpx;
				color: #FFFFFF;
				text-align: center;
				line-height: 60rpx;
			}
			
		}
	}
	
	.check_sex_li_ac{
		color:#333!important;
		font-weight: bold;
	}
</style>