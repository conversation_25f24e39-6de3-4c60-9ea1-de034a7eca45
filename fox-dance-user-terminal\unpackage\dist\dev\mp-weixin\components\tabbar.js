(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["components/tabbar"],{

/***/ 891:
/*!************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tabbar_vue_vue_type_template_id_852a8b4e___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tabbar.vue?vue&type=template&id=852a8b4e& */ 892);
/* harmony import */ var _tabbar_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tabbar.vue?vue&type=script&lang=js& */ 894);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _tabbar_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _tabbar_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _tabbar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tabbar.vue?vue&type=style&index=0&lang=scss& */ 896);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 73);

var renderjs





/* normalize component */

var component = Object(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _tabbar_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _tabbar_vue_vue_type_template_id_852a8b4e___WEBPACK_IMPORTED_MODULE_0__["render"],
  _tabbar_vue_vue_type_template_id_852a8b4e___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _tabbar_vue_vue_type_template_id_852a8b4e___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "components/tabbar.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 892:
/*!*******************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?vue&type=template&id=852a8b4e& ***!
  \*******************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_template_id_852a8b4e___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabbar.vue?vue&type=template&id=852a8b4e& */ 893);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_template_id_852a8b4e___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_template_id_852a8b4e___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_template_id_852a8b4e___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_template_id_852a8b4e___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 893:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?vue&type=template&id=852a8b4e& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uIcon: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-icon/u-icon */ "components/uview-ui/components/u-icon/u-icon").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-icon/u-icon.vue */ 870))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.showBox
    ? _vm.__map(_vm.products, function (item, index) {
        var $orig = _vm.__get_orig(item)
        var g0 = Math.abs(_vm.currentPage - index)
        return {
          $orig: $orig,
          g0: g0,
        }
      })
    : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 894:
/*!*************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabbar.vue?vue&type=script&lang=js& */ 895);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 895:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, wx) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var PreviewCard = function PreviewCard() {
  __webpack_require__.e(/*! require.ensure | components/PreviewCard */ "components/PreviewCard").then((function () {
    return resolve(__webpack_require__(/*! ./PreviewCard.vue */ 1151));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  name: 'tabbar',
  components: {
    PreviewCard: PreviewCard
  },
  props: {
    current: {
      type: [Number],
      default: 0
    }
  },
  data: function data() {
    return {
      list: [{
        iconPath: "/static/tabbar/tab_home.png",
        selectedIconPath: "/static/tabbar/tab_home_x.png",
        icon: 'home',
        text: '首页',
        count: 0,
        isDot: false,
        customIcon: false,
        pagePath: "/pages/index/index"
      }, {
        iconPath: "/static/tabbar/tab_buy.png",
        selectedIconPath: "/static/tabbar/tab_buy_x.png",
        icon: 'bag',
        text: '购买',
        count: 0,
        isDot: false,
        customIcon: false,
        pagePath: "/pages/buy/buy"
      }, {
        iconPath: "/static/tabbar/tab_fox1.png",
        selectedIconPath: "/static/tabbar/tab_fox1.png",
        text: '作品',
        count: 0,
        isDot: false,
        customIcon: false,
        pagePath: "/pages/index/index"
      }, {
        iconPath: "/static/tabbar/tab_schedule.png",
        selectedIconPath: "/static/tabbar/tab_schedule_x.png",
        icon: 'tags',
        text: '约课',
        count: 0,
        isDot: false,
        customIcon: false,
        pagePath: "/pages/Schedule/Schedule"
      }, {
        iconPath: "/static/tabbar/tab_mine.png",
        selectedIconPath: "/static/tabbar/tab_mine_x.png",
        icon: 'account',
        text: '我的',
        count: 0,
        isDot: false,
        customIcon: false,
        pagePath: "/pages/mine/mine"
      }],
      bottomHeight2: 4,
      bottomHeight: 10,
      ecology: '',
      noIndex: 0,
      showBox: false,
      showFixed: false,
      selColor: uni.getStorageSync('storeInfo') ? uni.getStorageSync('storeInfo').button : '#131315',
      // 卡片相关数据
      currentPage: 0,
      // 当前页面索引
      showBoundaryHint: false,
      totalCards: 2,
      // 总卡片数量

      // 商品数据
      products: [
      //https://dance-**********.cos.ap-guangzhou.myqcloud.com/static/images/vote-card-image.jpg
      {
        id: 1,
        name: '新店投票',
        tag: 'Fox - New store voting',
        image: 'https://dance-**********.cos.ap-guangzhou.myqcloud.com/static/images/vote-card-image.jpg',
        targetPage: '/pagesSub/switch/vote'
      }, {
        id: 2,
        name: '话题广场',
        tag: 'Fox - Topic square',
        image: 'https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg',
        targetPage: '/pagesSub/switch/topic-list'
      }, {
        id: 3,
        name: '帖子广场',
        tag: 'Fox - Topic square',
        image: 'https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg',
        targetPage: '/pagesSub/social/main/index'
      }],
      // 页面滚动锁定标志
      pageScrollLocked: false,
      // TabBar显示状态
      isTabBarHidden: false,
      // TabBar返回动画标记
      isTabBarReturning: false,
      // 滚动状态
      scrollLeft: 0,
      cardWidth: 0,
      windowWidth: 0,
      // 触摸状态
      touchStartX: 0,
      touchStartY: 0,
      touchStartTime: 0,
      touchMoved: false,
      isSwiping: false,
      swipeThreshold: 10,
      // 滑动阈值
      cardTouchTimer: null,
      // 添加方向判断相关变量
      touchDirection: '',
      // 'horizontal' 或 'vertical'
      directionLocked: false,
      minSwipeDistance: 5,
      // 最小判定为滑动的距离
      directionThreshold: 0.5,
      // 方向判定阈值，值越小越容易判定为水平

      // 调整更严格的角度判断参数
      verticalAngleThreshold: 5,
      // 垂直方向的角度阈值(度)，小于这个角度才会被视为垂直
      horizontalAngleThreshold: 85,
      // 水平方向的角度阈值(度)，小于这个角度视为水平
      minVerticalDistance: 20,
      // 最小垂直滑动距离，小于此距离不触发下拉

      // 副卡相关状态
      rightCardOffsetX: 0,
      leftCardOffsetX: 0,
      rightCardScale: 0.85,
      leftCardScale: 0.85,
      rightCardOpacity: 0.7,
      leftCardOpacity: 0.7,
      // 添加卡片下拉状态
      isCardPulling: false,
      pullDistance: 0,
      // 添加关闭状态
      isClosing: false,
      // 添加卡片退场状态
      isCardExiting: false,
      // 卡片提示文本
      cardTipText: "随便点一下，可能会发现新大陆"
    };
  },
  computed: {
    // 卡片容器样式
    cardContainerStyle: function cardContainerStyle() {
      if (!this.showBox) return {};
      return {
        transform: "translateY(-20px)",
        transition: 'transform 450ms cubic-bezier(0.4, 0, 0.2, 1)'
      };
    }
  },
  created: function created() {
    var _this = this;
    var that = this;
    uni.getSystemInfo({
      success: function success(res) {
        if (res.safeAreaInsets.bottom) {
          that.bottomHeight = res.safeAreaInsets.bottom;
          that.bottomHeight2 = res.safeAreaInsets.bottom - 7;
        }
        // 获取屏幕宽度用于卡片定位
        that.windowWidth = res.windowWidth;
        that.cardWidth = res.windowWidth * 0.8; // 卡片宽度为窗口的80%
      }
    });

    // 全局触摸事件处理
    uni.$on('touchmove', function (e) {
      if (_this.showBox || _this.pageScrollLocked) {
        // 如果卡片显示或页面被锁定，阻止所有触摸移动
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
        // 如果页面被锁定，则滚动到顶部
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0
        });
        return false;
      }
    });

    // 在微信小程序环境中，需要将方法暴露给页面实例
    if (typeof this.$scope !== 'undefined' && this.$scope) {
      this.$scope.preventCardClick = this.preventCardClick;
      this.$scope.handleContainerClick = this.handleContainerClick;
      this.$scope.swiperBackgroundClick = this.swiperBackgroundClick;
    }
  },
  mounted: function mounted() {
    var _this2 = this;
    // 在组件挂载后，确保初始卡片居中显示
    this.$nextTick(function () {
      setTimeout(function () {
        if (_this2.showBox) {
          _this2.scrollToCard(_this2.currentPage);
        }
      }, 300);
    });

    // 在微信小程序环境中，确保方法已注册到页面实例
    if (typeof this.$scope !== 'undefined' && this.$scope) {
      this.$scope.preventCardClick = this.preventCardClick;
      this.$scope.handleContainerClick = this.handleContainerClick;
      this.$scope.swiperBackgroundClick = this.swiperBackgroundClick;
    }

    // 获取卡片提示文本
    this.getCardTip();
  },
  beforeDestroy: function beforeDestroy() {
    // 移除全局触摸事件监听
    uni.$off('touchmove');
  },
  methods: {
    // 轮播图切换事件
    onSwiperChange: function onSwiperChange(e) {
      this.currentPage = e.detail.current;
    },
    // 切换完门店执行切换按钮颜色
    setColor: function setColor(ecology) {
      try {
        this.ecology = ecology ? ecology : '敬请期待~';
        var storeInfo = uni.getStorageSync('storeInfo');
        this.selColor = storeInfo && storeInfo.button ? storeInfo.button : '#131315';
      } catch (error) {
        console.warn('setColor error:', error);
        this.ecology = '敬请期待~';
        this.selColor = '#131315';
      }
    },
    tabbarChange: function tabbarChange(item, index) {
      var _this3 = this;
      this.noIndex = index;
      if (index == 2) {
        // 如果当前是约课页面(current === 3)或购买页面(current === 1)，禁止点击中间Tab进入卡片预览
        if (this.current === 3 || this.current === 1) {
          // 在约课页面或购买页面，禁止打开卡片预览
          var pageName = this.current === 3 ? '约课' : '购买';
          uni.showToast({
            title: "".concat(pageName, "\u9875\u9762\u4E0D\u53EF\u7528"),
            icon: 'none',
            duration: 2000
          });
          return;
        }
        this.noIndex = index;
        // 入场阶段：TabBar和卡片同步动画
        this.showBox = true;
        this.showFixed = true;
        this.isTabBarReturning = false;
        // 阻止页面滚动
        this.preventScroll(true);

        // 尝试使用微信小程序API控制弹性效果
        try {
          wx.setPageStyle({
            style: {
              overflow: 'hidden'
            }
          });
        } catch (e) {
          // 忽略错误
        }

        // 修改：保留图标可见
        setTimeout(function () {
          _this3.isTabBarHidden = false;
        }, 400);

        // 获取卡片提示文本
        this.getCardTip();
        return;
      } else {
        uni.switchTab({
          url: item.pagePath
        });
      }
    },
    // 改进防止滚动的方法
    preventScroll: function preventScroll(prevent) {
      // 通过页面状态控制滚动
      this.pageScrollLocked = prevent;

      // 使用全局变量控制滚动状态
      getApp().globalData = getApp().globalData || {};
      getApp().globalData.pageScrollLocked = prevent;

      // 尝试使用微信小程序原生API控制滚动
      try {
        wx.setPageStyle({
          style: {
            overflow: prevent ? 'hidden' : 'auto'
          }
        });
      } catch (e) {
        // 忽略错误
      }
    },
    // 更强的触摸阻止函数
    preventTouchMove: function preventTouchMove(e) {
      // 如果已经确定了滑动方向
      if (this.directionDetermined) {
        // 如果是水平滑动，不阻止默认行为
        if (this.initialSwipeDirection === 'horizontal') {
          return true;
        }
      }
      if (this.pageScrollLocked) {
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
        return false;
      }
    },
    // 添加卡片下拉处理方法
    handleCardPulling: function handleCardPulling(data) {
      // 只有当明确判定为垂直方向滑动时才触发下拉效果
      if (this.touchDirection === 'vertical') {
        this.isCardPulling = true;
        this.pullDistance = data.distance;
      }
    },
    // 添加触摸开始事件处理
    onCardTouchStart: function onCardTouchStart(e) {
      var touch = e.touches[0];
      this.touchStartX = touch.clientX;
      this.touchStartY = touch.clientY;
      this.touchStartTime = Date.now();
      this.touchMoved = false;
      this.directionLocked = false;
      this.touchDirection = '';
      this.sampledTouches = []; // 重置采样点数组

      // 重置滑动判断相关变量
      this.initialSwipeDirection = '';
      this.directionDetermined = false;
      this.lastDeltaX = 0;
      this.lastDeltaY = 0;
      this.accumulatedHorizontalDistance = 0;
      this.accumulatedVerticalDistance = 0;
      this.lastTouchTime = Date.now();

      // 添加首个采样点
      this.sampledTouches.push({
        x: touch.clientX,
        y: touch.clientY,
        time: Date.now()
      });
    },
    // 添加触摸移动事件处理
    onCardTouchMove: function onCardTouchMove(e) {
      // 防止页面滚动
      if (this.pageScrollLocked) {
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
      }

      // 实现事件节流，避免过于频繁处理造成卡顿
      var now = Date.now();
      if (now - this.lastTouchTime < this.touchThrottleDelay) {
        return; // 如果距离上次处理时间太短，则跳过本次处理
      }

      this.lastTouchTime = now;
      var touch = e.touches[0];
      var currentX = touch.clientX;
      var currentY = touch.clientY;
      var deltaX = currentX - this.touchStartX;
      var deltaY = currentY - this.touchStartY;

      // 计算相对于上次位移的差值
      var diffX = Math.abs(deltaX) - Math.abs(this.lastDeltaX);
      var diffY = Math.abs(deltaY) - Math.abs(this.lastDeltaY);

      // 更新累计位移
      if (diffX > 0) this.accumulatedHorizontalDistance += diffX;
      if (diffY > 0) this.accumulatedVerticalDistance += diffY;

      // 更新上次位移记录
      this.lastDeltaX = Math.abs(deltaX);
      this.lastDeltaY = Math.abs(deltaY);

      // 仅当滑动距离超过阈值时才进行方向判断
      var totalDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      if (!this.directionDetermined && totalDistance >= this.swipeStartDistance) {
        // 计算水平与垂直方向的比值
        var horizontalRatio = Math.abs(deltaX) / (Math.abs(deltaY) || 0.1); // 避免除以0

        // 判断初始滑动方向
        if (horizontalRatio >= this.horizontalDirectionThreshold) {
          // 水平方向优先，判定为水平滑动
          this.initialSwipeDirection = 'horizontal';
          this.touchDirection = 'horizontal';
          this.directionLocked = true;
        } else if (horizontalRatio <= 1 / this.horizontalDirectionThreshold) {
          // 垂直方向优先，判定为垂直滑动
          this.initialSwipeDirection = 'vertical';
          this.touchDirection = 'vertical';
          this.directionLocked = true;
        } else {
          // 优先考虑累计方向
          if (this.accumulatedHorizontalDistance > this.accumulatedVerticalDistance) {
            this.initialSwipeDirection = 'horizontal';
            this.touchDirection = 'horizontal';
            this.directionLocked = true;
          }
        }
        this.directionDetermined = true;
      }

      // 如果已经确定方向是水平，继续处理水平滑动逻辑
      if (this.touchDirection === 'horizontal') {
        // 清除任何可能的下拉状态
        if (this.isCardPulling) {
          this.isCardPulling = false;
          this.pullDistance = 0;
        }
      }

      // 如果确定方向是垂直，处理垂直滑动逻辑
      if (this.touchDirection === 'vertical' && deltaY > 0) {
        this.isCardPulling = true;
        this.pullDistance = deltaY;
      }
      this.touchMoved = true;

      // 添加当前触摸点到采样数组，用于更精确的速度计算
      this.sampledTouches.push({
        x: currentX,
        y: currentY,
        time: now
      });

      // 控制采样点数量
      if (this.sampledTouches.length > 5) {
        this.sampledTouches.shift();
      }
    },
    // 添加触摸结束事件处理
    onCardTouchEnd: function onCardTouchEnd(e) {
      if (!this.touchMoved) return;

      // 计算触摸持续时间
      var touchDuration = Date.now() - this.touchStartTime;

      // 计算最终速度
      var velocityX = 0;
      var velocityY = 0;
      if (this.sampledTouches.length >= 2) {
        var newest = this.sampledTouches[this.sampledTouches.length - 1];
        var oldest = this.sampledTouches[0];
        var timeSpan = newest.time - oldest.time;
        if (timeSpan > 0) {
          velocityX = (newest.x - oldest.x) / timeSpan; // 每毫秒移动的像素数
          velocityY = (newest.y - oldest.y) / timeSpan;
        }
      }

      // 如果是快速水平滑动，可以添加翻页效果
      var isQuickHorizontalSwipe = Math.abs(velocityX) > 0.5 && Math.abs(velocityX) > Math.abs(velocityY) * 1.5;
      if (isQuickHorizontalSwipe) {
        // 根据滑动方向和速度决定切换到下一页或上一页
        var direction = velocityX < 0 ? 1 : -1; // 负值表示向左滑，正值表示向右滑
        var targetPage = Math.max(0, Math.min(this.products.length - 1, this.currentPage + direction));
        if (targetPage !== this.currentPage) {
          this.currentPage = targetPage;
        }
      }

      // 重置下拉状态
      if (this.isCardPulling) {
        this.isCardPulling = false;
        this.pullDistance = 0;
      }

      // 重置方向判断状态
      this.directionLocked = false;
      this.touchDirection = '';
      this.touchMoved = false;
      this.sampledTouches = [];
      this.directionDetermined = false;
      this.initialSwipeDirection = '';
    },
    // 专门处理home图标点击的方法
    onHomeClick: function onHomeClick() {
      var _this4 = this;
      console.log('点击背景关闭预览');

      // 先触发卡片退场动画
      this.isCardExiting = true;

      // 等待动画结束后再隐藏
      setTimeout(function () {
        // 立即恢复页面滚动
        _this4.preventScroll(false);

        // 尝试使用微信小程序API恢复滚动效果
        try {
          wx.setPageStyle({
            style: {
              overflow: 'auto'
            }
          });
        } catch (e) {
          // 忽略错误
        }

        // 关闭所有遮罩和卡片
        _this4.showBox = false;
        _this4.showFixed = false;
        _this4.isCardExiting = false;

        // 重置TabBar状态
        _this4.isTabBarReturning = true;
        _this4.isTabBarHidden = false;

        // 重置其他状态
        _this4.currentPage = 0;
        _this4.noIndex = _this4.current;

        // 稍后重置TabBar动画状态
        setTimeout(function () {
          _this4.isTabBarReturning = false;
        }, 400);
      }, 300); // 等待300ms让退场动画完成
    },
    // 添加点击指示点切换页面的方法
    handleDotTap: function handleDotTap(index) {
      this.currentPage = index;
    },
    // 添加卡片容器点击处理方法
    handleContainerClick: function handleContainerClick() {
      console.log('容器点击关闭预览');
      this.onHomeClick();
    },
    // 专门给微信小程序使用的阻止冒泡方法
    preventCardClick: function preventCardClick() {
      console.log('卡片点击，阻止冒泡');
      // 仅阻止事件冒泡，不关闭预览
      return false;
    },
    // 添加swiper背景点击处理方法 - 微信小程序原生方法
    swiperBackgroundClick: function swiperBackgroundClick() {
      console.log('swiper背景点击，关闭预览');
      uni.showToast({
        title: '点击了背景区域',
        icon: 'none'
      });
      this.onHomeClick();
    },
    // 添加关闭预览的方法
    closePreview: function closePreview() {
      this.onHomeClick();
    },
    // 获取基础URL
    getBaseUrl: function getBaseUrl() {
      return 'https://vote.foxdance.com.cn'; // 微信小程序环境使用外部域名

      // 非小程序环境使用本地开发地址
      return 'https://vote.foxdance.com.cn';
    },
    // 获取卡片提示文本
    getCardTip: function getCardTip() {
      var _this5 = this;
      var BASE_URL = this.getBaseUrl();
      uni.request({
        url: "".concat(BASE_URL, "/api/vote-info/1/card-tip"),
        method: 'GET',
        success: function success(res) {
          console.log('获取卡片提示成功:', res);
          if (res.data && res.data.code === 0 && res.data.data) {
            // 更新卡片提示文本
            _this5.cardTipText = res.data.data;
          }
        },
        fail: function fail(err) {
          console.error('获取卡片提示失败:', err);
        }
      });
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"]))

/***/ }),

/***/ 896:
/*!**********************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?vue&type=style&index=0&lang=scss& ***!
  \**********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabbar.vue?vue&type=style&index=0&lang=scss& */ 897);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_tabbar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 897:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/components/tabbar.vue?vue&type=style&index=0&lang=scss& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/tabbar.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/tabbar-create-component',
    {
        'components/tabbar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(891))
        })
    },
    [['components/tabbar-create-component']]
]);
