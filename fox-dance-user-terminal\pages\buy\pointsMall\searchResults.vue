<template>
	<view class="searchResults" :style="{ '--qjbutton-color': qjbutton }">

		<view class="les_search">
			<view class="les_search_l">
				<image src="/static/images/search.png"></image><input type="text" placeholder-style="color:#999999" placeholder="请搜索你想要的商品" v-model="keywords" confirm-type="search" @confirm="searchTap(keywords)" />
			</view>
			<view class="les_search_r" @click="searchTap(keywords)">搜索</view>
		</view>

		<view class="seajg_con">
			<view class="seajg_con_li" v-for="(item,index) in mallLists" :key="index" @click="navTo('/pages/buy/pointsMall/productDetails?id=' + item.id,'1')">
				<image :src="imgbaseUrl + item.image" mode="aspectFill"></image>
				<view class="seajg_con_li_a">{{item.name}}</view>
				<view class="seajg_con_li_b">
					<view>￥{{item.redeem_points}}</view><text @click.stop="dhTap(item)">购买</text>
				</view>
			</view>
			
		</view>
		<view class="gg_loding" v-if="!zanwsj">
			<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
				<view></view>
				<text>加载中</text>
			</view>
			<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
		</view>
		<view class="gg_zwsj" v-if="zanwsj">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无数据</text>
			</view>
		</view>

		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->

	</view>
</template>


<script>
	import {
		userInfoApi,
		mallListsApi,
	} from '@/config/http.achieve.js'
	export default {
		data() {
			return {
				isLogined: true,
				shopCateIndex: 0,
				keywords:'',
				shopCate: [],
				mallLists: [],
				page: 1, //当前页数
				total_pages: 1, //总页数
				zanwsj: false, //是否有数据
				status: 'loading', //底部loding是否显示
				loadingText: '努力加载中',
				loadmoreText: '轻轻上拉',
				nomoreText: '实在没有了',
				scrollTop: 0,
				score: 0,
				imgbaseUrl: '',
				qjbutton:'#131315',
			}
		},
		onShow() {
			this.isLogined = uni.getStorageSync('token') ? true : false;
			if (this.isLogined) {
				this.userData(); //个人信息
			} else {
				this.loding = true;
			}
			this.imgbaseUrl = this.$baseUrl;
			this.mallLists = [];
			this.page = 1;
			this.mallData(); //积分商城
		},
		onLoad(option) {
			this.qjbutton = uni.getStorageSync('storeInfo').button
			this.keywords = option.keywords;
			uni.setNavigationBarTitle({
				title: this.keywords
			})
		},
		methods: {
			searchTap() {
				this.mallLists = [];
				this.page = 1;
				this.mallData(); //积分商城
			},
			//个人信息
			userData() {
				/*uni.showLoading({
					title: '加载中'
				});*/
				let that = this;
				userInfoApi({}).then(res => {
					console.log('个人中心', res)
					if (res.code == 1) {
						that.score = res.data.score;
						// uni.hideLoading();
					}
				})
			},
			//积分商城
			mallData() {
				uni.showLoading({
					title: '加载中'
				});
				let that = this;
				mallListsApi({
					name: that.keywords,
					page: that.page,
					size: 10,
				}).then(res => {
					console.log('积分商城', res)
					if (res.code == 1) {
						var obj = res.data.data;
						that.score = res.data.score
						that.mallLists = that.mallLists.concat(obj);
						that.zanwsj = that.mallLists.length ? true : false;
						that.page++;
						// that.total_pages = Math.ceil(res.total/20);
						that.total_pages = res.data.last_page;
						if (that.page != 1) {
							if (that.total_pages >= that.page) {
								that.status = 'loading'
							} else {
								that.status = 'nomore'
							}
						}
						if (that.mallLists.length == 0) {
							that.zanwsj = true;
						} else {
							that.zanwsj = false;
						}
						if (res.data.total * 1 <= 10) {
							that.status = 'nomore'
						}
						that.loding = true;
						uni.hideLoading();
						uni.stopPullDownRefresh();
					}
				})

			},
			onReachBottom() {
				console.log('到底了');
				if (this.page != 1) {
					if (this.total_pages >= this.page) {
						this.mallData();
					}
				}
			},
			onPullDownRefresh: function() {
				// console.log('我被下拉了');
				this.page = 1;
				this.mallLists = [];
				this.mallData(); //积分商城
			},
			//兑换
			dhTap(item) {
				if (this.isLogined) {
					if (this.score < item.redeem_points * 1) {
						uni.showToast({
							icon: 'none',
							title: '积分不足',
							duration: 2000
						});
						return false;
					}
					var productxq = JSON.stringify(item)
					uni.navigateTo({
						url: '/pages/buy/pointsMall/confirmOrder?productxq=' + productxq
					})
				} else {
					uni.showToast({
						icon: 'none',
						title: '请先登录',
						duration: 2000
					});
					setTimeout(function() {
						uni.navigateTo({
							url: '/pages/login/login'
						})
					}, 1000)
				}
			},
			navTo(url, ismd) {
				if (ismd) {
					uni.navigateTo({
						url: url
					});
					return false;
				}
				var that = this;
				if (uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync(
						'token')) {
					uni.showToast({
						icon: 'none',
						title: '请先登录'
					});
					setTimeout(function() {
						uni.navigateTo({
							url: '/pages/login/login'
						})
					}, 1000)
				} else {
					uni.navigateTo({
						url: url
					})
				}
			},

		}
	}
</script>

<style lang="scss">
	.searchResults {
		overflow: hidden;
	}

	page {
		padding-bottom: 0;
	}
</style>