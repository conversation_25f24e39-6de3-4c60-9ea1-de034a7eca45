<template>
	<view class="prizedraw" v-if="loding">

		<u-navbar title="抽奖" :border-bottom="false" :back-icon-color="navBg == 1 ? '#fff' : '#fff'"
			:title-color="navBg == 1 ? '#fff' : '#fff'" :background="navBg == 1 ? { background: '#060505' } : ''">
		</u-navbar>
		 
		<view class="pri_one" :style="'margin-top:-'+navHeight+'px'">
			<image :src="imgbaseUrlOss + '/userreport/icon63.png'" class="pri_one_bj">
				<view class="pri_one_a" :style="'margin-top:'+(navHeight+10)+'px'" v-if="noticeText != ''">
					<uni-notice-bar scrollable single :text="noticeText" background-color="transparent" color="#fff"
						:single="true" speed="50"> </uni-notice-bar>
				</view>
				<view :style="'margin-top:'+(navHeight+10)+'px;width: 100%;overflow: hidden;height: 54rpx;'"
					v-if="noticeText == ''"></view>
				<view class="pri_one_b">
					<image :src="imgbaseUrlOss + '/userreport/icon64.png'" />
				</view>
				<view class="pri_one_c" @click="navTo('/pages/prizedraw/winningrecord')">
					<image :src="imgbaseUrlOss + '/userreport/icon66.png'" />
				</view>
		</view>

		<view class="pri_two">
			<view class="pri_two_t">
				<image src="/static/images/icon67.png"></image>奖池预览<image src="/static/images/icon67.png"></image>
			</view>
			<view class="pri_two_b">
				<view class="pri_two_b_li" v-for="(item,index) in jpLists" :key="index" v-if="item.type*1 != 4">
					<image src="/static/images/icon71.png" class="pri_two_b_li_bj"></image>
					<view class="pri_two_b_li_t">
						<image :src="imgbaseUrl + item.image" mode="aspectFill"></image>
					</view>
					<view class="pri_two_b_li_b">{{item.namezs}}</view>
					<view class="pri_two_b_li_num">{{item.remaining_num + '/' + item.frequency}}</view>
				</view>
			</view>
		</view>

		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->

		<view class="pri_foo">
			<image :src="imgbaseUrlOss + '/userreport/icon65.png'" class="pri_foo_bj" />
			<view class="pri_foo_t"><text></text>
				<view>剩余抽奖次数：{{cjNum}}次</view><text></text>
			</view>
			<view class="pri_foo_b">
				<view @click="cjStartTap(1)"></view>
				<view @click="cjStartTap(10)"></view>
			</view>
		</view>





		<!-- 抽奖中弹窗 go -->
		<view class="cjtcTc cjloding" v-if="cjzLoding">
			<view class="cjtcTc_n" @click.stop>
				<image src="/static/images/icon68.png" class="cjtcTc_sltitle"></image>
				<view class="cjtcslTc_a">
					<!-- <open-one-box ref="oneBox" :list="box_skins" :result="cjId" @finsh="finsh"> -->
					<open-one-box ref="oneBox" :list="jpLists" :result="cjId" @finsh="finsh">
						<!-- 可以使用插槽自定内容 -->
						<!-- <template v-slot="{ item }">
							<image :src="item.img" style="width: 100rpx;height: 100rpx;"></image>
						</template> -->
					</open-one-box>
					<view class="cjloding_xian"></view>
					<view class="boxshadow_l"></view>
					<view class="boxshadow_r"></view>
					<!-- <view style="display: flex;align-items: center;justify-content: center;margin-top: 30rpx;">
						<button @click="startOne" style="width: 200rpx;background: #278891;">开始--{{cjId}}</button>
					</view> -->
				</view>
				<view class="cjtcslTc_f">
					<view @click="tgdhTap">
						<image :src="istgdhToggle ? '/static/images/cjxz-1.png' : '/static/images/cjxz.png'"></image>
						跳过动画
						<text v-if="cjType == 10" style="font-size:26rpx;">（第{{cj_moreNum+1}}轮）</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 抽奖中弹窗 end -->


		<!-- 单抽奖品弹窗 go -->
		<view class="cjtcTc" v-if="dcToggle" @click="dcToggle = false">
			<view class="cjtcTc_n" @click.stop>
				<image :src="zjjgLists[0].type*1 == 4 ? '/static/images/icon86.png' : '/static/images/icon69.png'"
					class="cjtcTc_title" :style="zjjgLists[0].type*1 == 4 ? 'width:398rpx;height:162rpx;' : ''"></image>
				<view class="cjtcTc_a" style="height:476rpx;">
					<view class="pri_two_b_li animate__animated animate__bounceIn" style="float:initial;margin: auto;">
						<image src="/static/images/icon71.png" class="pri_two_b_li_bj"></image>
					
						
						
						<template v-if="zjjgLists[0].type*1 == 4">
							<view class="pri_two_b_li_cards" style="background:none;">
								<image src="/static/images/icon87.png" class="pri_two_b_li_hb_bj"
									style="width: 100%;height:100%;" mode="aspectFill"></image>
							</view>
							<view class="pri_two_b_li_b_hb">谢谢参与</view>
						</template>
						
						<template v-else>
							<view class="pri_two_b_li_t">
								<image :src="imgbaseUrl + zjjgLists[0].image" mode="aspectFill"></image>
							</view>
							<view class="pri_two_b_li_b">{{zjjgLists[0].name}}</view>
						</template>

					</view>
				</view>
				<view class="cjtcTc_f" @click="cjStartTap(1)">再抽一次</view>
				<!-- <view class="cjtcslTc_f">
					<view @click="tgdhTap">
						<image :src="istgdhToggle ? '/static/images/cjxz-1.png' : '/static/images/cjxz.png'"></image>
						跳过动画
					</view>
				</view> -->
			</view>
		</view>
		<!-- 单抽奖品弹窗 end -->

		<!-- 10连抽奖品弹窗 go -->
		<view class="cjtcTc" v-if="slcToggle" @click="slcToggle = false">
			<view class="cjtcTc_n" @click="slcToggle = false">
				<image src="/static/images/icon69.png" class="cjtcTc_title"></image>
				<view class="cjtcTc_b" @click.stop>
					<view class="pri_two_b_li  animate__animated animate__bounceIn" v-for="(item,index) in zjjgLists"
						:key="index">
						<image src="/static/images/icon71.png" class="pri_two_b_li_bj"></image>

						<template v-if="item.type*1 == 4">
							<view class="pri_two_b_li_cards" style="background:none;">
								<image src="/static/images/icon87.png" class="pri_two_b_li_hb_bj"
									style="width: 100%;height:100%;" mode="aspectFill"></image>
							</view>
							<view class="pri_two_b_li_b_hb">谢谢参与</view>
						</template>
						
						<template v-else>
							<view class="pri_two_b_li_t">
								<image :src="imgbaseUrl + item.image" mode="aspectFill"></image>
							</view>
							<view class="pri_two_b_li_b">{{item.name}}</view>
						</template>

					</view>
				</view>
				<view class="cjtcTc_f" @click.stop="cjStartTap(10)">再抽一次</view>
				<!-- <view class="cjtcslTc_f">
					<view @click.stop="tgdhTap">
						<image :src="istgdhToggle ? '/static/images/cjxz-1.png' : '/static/images/cjxz.png'"></image>
						跳过动画
					</view>
				</view> -->
			</view>
		</view>
		<!-- 10连抽奖品弹窗 end -->


	</view>
</template>

<script>
	import {
		prizedrawApi,
		drawSubApi
	} from '@/config/http.achieve.js'
	// 获取系统状态栏的高度
	let systemInfo = uni.getSystemInfoSync();
	export default {
		data() {
			return {
				imgbaseUrlOss: '',
				imgbaseUrl: '', //图片地址
				loding: false,
				height: 0,
				noticeText: '',
				box_skins: [],
				navBg: '',
				navHeight: 0,
				titleStyle: {
					fontWeight: 'bold',
					fontSize: '34rpx',
					color: '#fff',
				},
				titleStyle_on: {
					fontWeight: 'bold',
					fontSize: '34rpx',
					color: '#333',
				},
				dcToggle: false, //单抽抽奖结果弹窗是否显示
				slcToggle: false, //10连抽奖结果弹窗是否显示
				istgdhToggle: false, //是否跳过动画
				cjzLoding: false, //抽奖动画是否开启
				cjType: 1, //抽奖类型 1/10
				jpLists: [], //奖池预览
				cjNum: 0, //抽奖次数
				zjjgLists: [
					/*{
						"id": 2,
						"type": "2",
						"price": "2.00",
						"goods": null,
						"memberCard": null
					},
					{"id":3,"type":"3","price":"0.00","goods":null,"memberCard":{"name":"日常卡-次卡","type":0}},
					{
						"id": 1,
						"type": "1",
						"price": "0.00",
						"goods": {
							"name": "印尼进口营多捞面速食泡面夜宵食品网红拉面拌面方便面整箱",
							"image": "/storage/default/20241023/O1CN01v2BipQ1Pk522c5aa07389ab876c7f9c9d4fd52c8fc26d9a76.png"
						},
						"memberCard": null
					},
					{type:4},*/
				], //中奖结果列表
				cjId: 0, //中奖结果id
				countdown: 9,
				timer: null,
				cj_moreNum: 0, //多轮抽奖，当前是第几轮
				cjdh_loding: true,
				cjKey: {},
				jinzLd:true,//抽奖禁止连点
			}
		},
		onLoad() {
			this.navHeight = (uni.getSystemInfoSync().statusBarHeight + 44);
			this.imgbaseUrlOss = this.$baseUrlOss;
			this.imgbaseUrl = this.$baseUrl;
			this.prizedrawData(); //奖池列表
		},
		onUnload: function() {
			console.log('onHide', 'onHide');
			clearInterval(this.timer);
		},
		methods: {
			//商品跳转详情
			goodsSpTo(item) {
				if (item.type * 1 == 1) {
					uni.navigateTo({
						url: '/pages/prizedraw/winningrecordxq?id=' + item.goods_id
					})
				}
			},
			//奖池列表
			prizedrawData() {
				uni.showLoading({
					title: '加载中'
				});

				let that = this;
				prizedrawApi({}).then(res => {
					console.log('奖池列表', res)
					if (res.code == 1) {
						that.loding = true;
						if (res.data.prize.length > 0) {
							// noticeText: '· 182****3311抽中了POP MART泡泡玛特1　　　· 182****3313抽中了POP MART泡泡玛特2　　　· 182****3314抽中了POP MART泡泡玛特3　　',
							var noticeArr = []; //类型:1=商品,2=红包,3=会员卡
							for (var i = 0; i < res.data.prize.length; i++) {
								/*var name = res.data.prize[i].type * 1 == 1 ? (res.data.prize[i].goods ? res.data
										.prize[i].goods.name : 'null') : res.data.prize[i].type * 1 == 2 ? res.data
									.prize[i].price * 1 + '元现金红包' : res.data.prize[i].type * 1 == 3 ? res.data
									.prize[i].memberCard.name : ''*/
								var name = res.data.prize[i].name == '' ? (res.data.prize[i].type * 1 == 1 ? (res.data.prize[i].goods ? res.data
										.prize[i].goods.name : 'null') : res.data.prize[i].type * 1 == 2 ? res.data
									.prize[i].price * 1 + '元现金红包' : res.data.prize[i].type * 1 == 3 ? res.data
									.prize[i].memberCard.name : '') : res.data.prize[i].name
								noticeArr.push('　· ' + res.data.prize[i].user.mobile + ' 抽中了' + name + ' ' + res
									.data.prize[i].create_time + '　　　')
							}
							that.noticeText = noticeArr.join(' ');
							// console.log(noticeArr,'noticeArr')
						}
						for(var j=0;j<res.data.jackpot.length;j++){
							if(res.data.jackpot[j].name.length > 11){
								res.data.jackpot[j].namezs = res.data.jackpot[j].name ? res.data.jackpot[j].name.substring(0,11) + '...' : ''
							}else{
								res.data.jackpot[j].namezs = res.data.jackpot[j].name
							}
						}
						
						that.jpLists = res.data.jackpot;
						that.cjNum = res.data.luck_draw_frequency * 1
						uni.hideLoading();
					}
				})
			},
			//开始抽奖
			cjStartTap(type) {
				var that = this;
				if (this.cjNum * 1 <= 0) {
					uni.showToast({
						icon: 'none',
						duration: 2000,
						title: "抽奖次数不足"
					})
					return false;
				}

				this.dcToggle = false; //关闭单次抽奖结果弹窗
				this.slcToggle = false; //关闭10连抽抽奖结果弹窗
				// this.istgdhToggle = false; //跳过动画
				this.cjType = type;

				//请求接口拿到抽奖结果 go
				uni.showLoading({
					title: '加载中'
				});
				drawSubApi({
					type: type == 1 ? 1 : 10
				}).then(res => {
					// console.log('抽奖结果',res)
					if (res.code == 1) {
						uni.hideLoading();
						that.prizedrawData();
						that.cjId = res.data[0].id;
						that.zjjgLists = res.data;

						//执行抽奖动画
						this.cjzLoding = true; //开启抽奖弹窗动画
						setTimeout(function() {
							that.startOne(); //开始抽奖
						}, 500);

					}
				})
			},
			//跳过动画
			tgdhTap() {
				var that = this;
				/**/
				if (this.cjType == 1) {
					//单次抽奖跳过

					this.countdown = 9;
					clearInterval(this.timer);

					this.cjzLoding = false; //关闭抽奖弹窗动画
					// this.istgdhToggle = true;
					this.cjType == 1 ? this.dcToggle = true : this.slcToggle = true;
					// this.istgdhToggle = !this.istgdhToggle;

				} else {
					if(!that.jinzLd){
					  uni.showToast({
						icon:'none',
						title:'您点击的太快了~',
						duration: 2000
					  });
					  return false;
					}
					that.jinzLd = false;
					
					//10连抽奖
					this.countdown = 9;
					clearInterval(this.timer);
					this.cjzLoding = false;
					this.cj_moreNum++
					this.$nextTick(function() {
						that.cjzLoding = true;
						if (this.cj_moreNum == that.zjjgLists.length) {
							//轮数到了截止抽奖
							this.countdown = 9;
							clearInterval(this.timer);
							this.cjzLoding = false; //关闭抽奖弹窗动画
							this.slcToggle = true;
							this.cj_moreNum = 0;
							return false;
						}
					})
					/*this.cjdh_loding = false;
					this.$nextTick(function() {
						that.cjdh_loding = true;
					})*/
					if (this.cj_moreNum == that.zjjgLists.length) {
						return false;
					}
					that.cjId = that.zjjgLists[that.cj_moreNum * 1].id;
					that.cjKey = that.zjjgLists[that.cj_moreNum * 1];
					setTimeout(function() {
						that.startOne(); //开始抽奖
					}, 500);
					setTimeout(function(){
						that.jinzLd = true
					},1500)
				}


			},
			onPageScroll(e) {
				const top = uni.upx2px(100)
				const {
					scrollTop
				} = e
				let percent = scrollTop / top > 1 ? 1 : scrollTop / top
				this.navBg = percent;

				uni.setNavigationBarColor({
					frontColor: this.navBg == 1 ? '#000000' : '#ffffff',
					backgroundColor: this.navBg == 1 ? '#ffffff' : '#000000' // 可根据需要修改背景颜色
				});

			},
			// 转换字符数值为真正的数值
			navbarHeight() {
				// #ifdef  H5
				return this.height ? this.height : 44;
				// #endif
				// #ifdef APP-PLUS || MP
				// 小程序特别处理，让导航栏高度 = 胶囊高度 + 两倍胶囊顶部与状态栏底部的距离之差(相当于同时获得了导航栏底部与胶囊底部的距离)
				// 此方法有缺陷，暂不用(会导致少了几个px)，采用直接固定值的方式
				// return menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;//导航高度
				let height = systemInfo.platform == 'ios' || systemInfo.platform == 'devtools' ? 44 : 48;
				return this.height ? this.height : height;
				// #endif
			},
			startOne() {
				var that = this;
				this.timer = setInterval(() => {
					// 每秒减少 1 秒
					this.countdown--;
					// 当倒计时结束时
					// console.log('this.countdown',this.countdown)
					if (this.countdown === 0) {
						// 清除定时器
						clearInterval(this.timer);
						that.countdown = 9;
						console.log('倒计时结束');

						if (that.cjType == 1) {
							//单抽
							that.cjzLoding = false; //关闭抽奖弹窗动画
							that.dcToggle = true
						}

						if (that.cjType == 10) {
							//10连抽
							that.cjzLoding = false; //关闭抽奖弹窗动画
							if (that.cj_moreNum == that.zjjgLists.length) {
								//轮数到了截止抽奖
								that.countdown = 9;
								clearInterval(that.timer);
								that.cjzLoding = false; //关闭抽奖弹窗动画
								that.slcToggle = true;
								that.cj_moreNum = 1;
								return false;
							} else {
								that.tgdhTap(); //继续抽奖
							}
						}
					}
				}, 1000);
				this.$refs.oneBox.start()
			},
			finsh(e) {
				/*var that = this;
				setTimeout(function(){
					that.cjzLoding = false;//关闭抽奖弹窗动画
					that.cjType == 1 ? that.dcToggle = true : that.slcToggle = true;
				},600)*/
			},
			navTo(url) {
				uni.navigateTo({
					url: url
				})
			}
		}
	}
</script>

<style>
	page {
		background: #060505;
	}
</style>