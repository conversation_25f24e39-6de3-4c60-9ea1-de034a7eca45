use admin_foxdance_c;

-- 用户表
create table if not exists auth_check_root
(
    id           bigint auto_increment comment 'id' primary key,
    userAccount  varchar(256)                           not null comment '账号',
    userPassword varchar(512)                           not null comment '密码',
    unionId      varchar(256)                           null comment '微信开放平台id',
    mpOpenId     varchar(256)                           null comment '公众号openId',
    userName     varchar(256)                           null comment '用户昵称',
    userAvatar   varchar(1024)                          null comment '用户头像',
    userProfile  varchar(512)                           null comment '用户简介',
    userRole     varchar(256) default 'user'            not null comment '用户角色：user/admin/ban',
    createTime   datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updateTime   datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    isDelete     tinyint      default 0                 not null comment '是否删除',
    index idx_unionId (unionId)
) comment '用户' collate = utf8mb4_unicode_ci;
-- vote_info title info tip
create table if not exists vote_info
(
    id           bigint auto_increment comment 'id' primary key,
    title        varchar(256)                           not null comment '标题',
    info        varchar(512)                           not null comment '信息',
    tips        varchar(256)                           null comment '提示',
    card_tip    varchar(256)                           null comment '卡片提示',
    createTime   datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updateTime   datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    isDelete     tinyint      default 0                 not null comment '是否删除'
) comment '投票信息表' collate = utf8mb4_unicode_ci;
-- 给vote_info表添加card_tip字段
ALTER TABLE vote_info ADD COLUMN card_tip varchar(256) COMMENT '投票提示';

-- 创建表
CREATE TABLE metro_lines (
                             id INT AUTO_INCREMENT PRIMARY KEY,
                             line_name VARCHAR(100) NOT NULL UNIQUE COMMENT '地铁线路名称（唯一）',
                             stations JSON NOT NULL COMMENT '该线路的所有站点（JSON数组）',
                             vote_counts JSON NOT NULL COMMENT '各站点的投票数（JSON对象）',
                             created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 增加"更新时间"字段和"逻辑删除"字段
    ALTER TABLE metro_lines ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
    ALTER TABLE metro_lines ADD COLUMN is_deleted TINYINT(1) DEFAULT 0 COMMENT '逻辑删除（0：未删除，1：已删除）';

-- 更改字段"created_at"为create_time
    ALTER TABLE metro_lines CHANGE COLUMN created_at create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
-- 更改字段"updated_at"为update_time
    ALTER TABLE metro_lines CHANGE COLUMN updated_at update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
-- 更改字段"is_deleted"为is_delete
    ALTER TABLE metro_lines CHANGE COLUMN is_deleted is_delete TINYINT(1) DEFAULT 0 COMMENT '逻辑删除（0：未删除，1：已删除）';
-- 初始化表数据

-- 1号线：全部
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('1号线',
     '["广州东站", "体育中心", "体育西路", "杨箕", "东山口", "烈士陵园", "农讲所", "公园前", "西门口", "陈家祠", "长寿路", "黄沙", "芳村", "花地湾", "坑口", "西朗"]',
     '{"广州东站": 0, "体育中心": 0, "体育西路": 0, "杨箕": 0, "东山口": 0, "烈士陵园": 0, "农讲所": 0, "公园前": 0, "西门口": 0, "陈家祠": 0, "长寿路": 0, "黄沙": 0, "芳村": 0, "花地湾": 0, "坑口": 0, "西朗": 0}');

-- 2号线：嘉禾望岗 ~ 洛溪
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('2号线',
     '["嘉禾望岗", "黄边", "江夏", "萧岗", "白云文化广场", "白云公园", "飞翔公园", "三元里", "广州火车站", "越秀公园", "纪念堂", "公园前", "海珠广场", "市二宫", "江南西", "昌岗", "江泰路", "东晓南", "南洲", "洛溪"]',
     '{"嘉禾望岗": 0, "黄边": 0, "江夏": 0, "萧岗": 0, "白云文化广场": 0, "白云公园": 0, "飞翔公园": 0, "三元里": 0, "广州火车站": 0, "越秀公园": 0, "纪念堂": 0, "公园前": 0, "海珠广场": 0, "市二宫": 0, "江南西": 0, "昌岗": 0, "江泰路": 0, "东晓南": 0, "南洲": 0, "洛溪": 0}');

-- 3号线：嘉禾望岗 ~ 市桥
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('3号线',
     '["嘉禾望岗", "白云大道北", "永泰", "同和", "京溪南方医院", "梅花园", "燕塘", "广州东站", "林和西", "体育西路", "珠江新城", "广州塔", "客村", "大塘", "沥滘", "厦滘", "大石", "汉溪长隆", "市桥"]',
     '{"嘉禾望岗": 0, "白云大道北": 0, "永泰": 0, "同和": 0, "京溪南方医院": 0, "梅花园": 0, "燕塘": 0, "广州东站": 0, "林和西": 0, "体育西路": 0, "珠江新城": 0, "广州塔": 0, "客村": 0, "大塘": 0, "沥滘": 0, "厦滘": 0, "大石": 0, "汉溪长隆": 0, "市桥": 0}');

-- 5号线：中山八 ~ 科韵路
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('5号线',
     '["中山八", "西场", "西村", "广州火车站", "小北", "淘金", "区庄", "动物园", "杨箕", "五羊邨", "珠江新城", "猎德", "潭村", "员村", "科韵路"]',
     '{"中山八": 0, "西场": 0, "西村": 0, "广州火车站": 0, "小北": 0, "淘金": 0, "区庄": 0, "动物园": 0, "杨箕": 0, "五羊邨": 0, "珠江新城": 0, "猎德": 0, "潭村": 0, "员村": 0, "科韵路": 0}');

-- 6号线：如意坊 ~ 长湴
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('6号线',
     '["如意坊", "黄沙", "文化公园", "一德路", "北京路", "团一大广场", "东湖", "东山口", "区庄", "黄花岗", "沙河顶", "天平架", "燕塘", "天河客运站", "长湴"]',
     '{"如意坊": 0, "黄沙": 0, "文化公园": 0, "一德路": 0, "北京路": 0, "团一大广场": 0, "东湖": 0, "东山口": 0, "区庄": 0, "黄花岗": 0, "沙河顶": 0, "天平架": 0, "燕塘": 0, "天河客运站": 0, "长湴": 0}');

-- 8号线：上步 ~ 琶洲
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('8号线',
     '["上步", "同德", "聚龙", "石潭", "小坪", "石井", "亭岗", "滘心", "同德", "鹅掌坦", "西村", "彩虹桥", "陈家祠", "华林寺", "文化公园", "同福西", "凤凰新村", "沙园", "宝岗大道", "昌岗", "晓港", "中大", "鹭江", "客村", "赤岗", "磨碟沙", "新港东", "琶洲"]',
     '{"上步": 0, "同德": 0, "聚龙": 0, "石潭": 0, "小坪": 0, "石井": 0, "亭岗": 0, "滘心": 0, "同德": 0, "鹅掌坦": 0, "西村": 0, "彩虹桥": 0, "陈家祠": 0, "华林寺": 0, "文化公园": 0, "同福西": 0, "凤凰新村": 0, "沙园": 0, "宝岗大道": 0, "昌岗": 0, "晓港": 0, "中大": 0, "鹭江": 0, "客村": 0, "赤岗": 0, "磨碟沙": 0, "新港东": 0, "琶洲": 0}');

-- 11号线：全部
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('11号线',
     '["广州火车站", "流花路", "梓元岗", "中医药大学", "广园新村", "大金钟", "云台花园", "沙河", "广州东站", "龙口西", "华师", "天河公园", "员村", "琶洲", "赤沙", "龙潭", "大塘", "石榴岗", "赤沙滘", "琶洲西区", "员村", "天河公园"]',
     '{"广州火车站": 0, "流花路": 0, "梓元岗": 0, "中医药大学": 0, "广园新村": 0, "大金钟": 0, "云台花园": 0, "沙河": 0, "广州东站": 0, "龙口西": 0, "华师": 0, "天河公园": 0, "员村": 0, "琶洲": 0, "赤沙": 0, "龙潭": 0, "大塘": 0, "石榴岗": 0, "赤沙滘": 0, "琶洲西区": 0, "员村": 0, "天河公园": 0}');

-- 18号线：冼村 ~ 龙溪
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('18号线',
     '["冼村", "磨碟沙", "龙潭", "海傍", "番禺广场", "横沥", "万顷沙", "龙溪"]',
     '{"冼村": 0, "磨碟沙": 0, "龙潭": 0, "海傍": 0, "番禺广场": 0, "横沥": 0, "万顷沙": 0, "龙溪": 0}');

-- HTT：猎德大桥南 ~ 琶洲大桥南
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('HTT线',
     '["猎德大桥南", "磨碟沙", "琶洲", "琶洲大桥南"]',
     '{"猎德大桥南": 0, "磨碟沙": 0, "琶洲": 0, "琶洲大桥南": 0}');

-- APM线：全部
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('APM线',
     '["林和西", "体育中心南", "天河南", "黄埔大道", "妇儿中心", "花城大道", "大剧院", "海心沙", "广州塔"]',
     '{"林和西": 0, "体育中心南": 0, "天河南": 0, "黄埔大道": 0, "妇儿中心": 0, "花城大道": 0, "大剧院": 0, "海心沙": 0, "广州塔": 0}');
-- 更改tablename为metro_lines
ALTER TABLE metro_lines RENAME TO metro_lines;
-- 查找ba_user表nickname为o的
SELECT * FROM ba_user WHERE mobile = '18138116848';

INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('全部线路',
     '["广州东站", "体育中心", "体育西路", "杨箕", "东山口", "烈士陵园", "农讲所", "公园前", "西门口", "陈家祠", "长寿路", "黄沙", "芳村", "花地湾", "坑口", "西朗", "嘉禾望岗", "黄边", "江夏", "萧岗", "白云文化广场", "白云公园", "飞翔公园", "三元里", "广州火车站", "越秀公园", "纪念堂", "海珠广场", "市二宫", "江南西", "昌岗", "江泰路", "东晓南", "南洲", "洛溪", "白云大道北", "永泰", "同和", "京溪南方医院", "梅花园", "燕塘", "林和西", "珠江新城", "广州塔", "客村", "大塘", "沥滘", "厦滘", "大石", "汉溪长隆", "市桥", "中山八", "西场", "西村", "小北", "淘金", "区庄", "动物园", "五羊邨", "猎德", "潭村", "员村", "科韵路", "如意坊", "文化公园", "一德路", "北京路", "团一大广场", "东湖", "黄花岗", "沙河顶", "天平架", "天河客运站", "长湴", "上步", "同德", "聚龙", "石潭", "小坪", "石井", "亭岗", "滘心", "鹅掌坦", "彩虹桥", "华林寺", "文化公园", "同福西", "凤凰新村", "沙园", "宝岗大道", "晓港", "中大", "鹭江", "磨碟沙", "新港东", "琶洲", "广州火车站", "流花路", "梓元岗", "中医药大学", "广园新村", "大金钟", "云台花园", "沙河", "龙口西", "华师", "天河公园", "赤沙", "龙潭", "石榴岗", "赤沙滘", "琶洲西区", "天河公园", "冼村", "海傍", "番禺广场", "横沥", "万顷沙", "龙溪", "猎德大桥南", "琶洲大桥南", "林和西", "体育中心南", "天河南", "黄埔大道", "妇儿中心", "花城大道", "大剧院", "海心沙"]',
     '{"广州东站": 0, "体育中心": 0, "体育西路": 0, "杨箕": 0, "东山口": 0, "烈士陵园": 0, "农讲所": 0, "公园前": 0, "西门口": 0, "陈家祠": 0, "长寿路": 0, "黄沙": 0, "芳村": 0, "花地湾": 0, "坑口": 0, "西朗": 0, "嘉禾望岗": 0, "黄边": 0, "江夏": 0, "萧岗": 0, "白云文化广场": 0, "白云公园": 0, "飞翔公园": 0, "三元里": 0, "广州火车站": 0, "越秀公园": 0, "纪念堂": 0, "海珠广场": 0, "市二宫": 0, "江南西": 0, "昌岗": 0, "江泰路": 0, "东晓南": 0, "南洲": 0, "洛溪": 0, "白云大道北": 0, "永泰": 0, "同和": 0, "京溪南方医院": 0, "梅花园": 0, "燕塘": 0, "林和西": 0, "珠江新城": 0, "广州塔": 0, "客村": 0, "大塘": 0, "沥滘": 0, "厦滘": 0, "大石": 0, "汉溪长隆": 0, "市桥": 0, "中山八": 0, "西场": 0, "西村": 0, "小北": 0, "淘金": 0, "区庄": 0, "动物园": 0, "五羊邨": 0, "猎德": 0, "潭村": 0, "员村": 0, "科韵路": 0, "如意坊": 0, "文化公园": 0, "一德路": 0, "北京路": 0, "团一大广场": 0, "东湖": 0, "黄花岗": 0, "沙河顶": 0, "天平架": 0, "天河客运站": 0, "长湴": 0, "上步": 0, "同德": 0, "聚龙": 0, "石潭": 0, "小坪": 0, "石井": 0, "亭岗": 0, "滘心": 0, "鹅掌坦": 0, "彩虹桥": 0, "华林寺": 0, "同福西": 0, "凤凰新村": 0, "沙园": 0, "宝岗大道": 0, "晓港": 0, "中大": 0, "鹭江": 0, "磨碟沙": 0, "新港东": 0, "琶洲": 0, "广州火车站": 0, "流花路": 0, "梓元岗": 0, "中医药大学": 0, "广园新村": 0, "大金钟": 0, "云台花园": 0, "沙河": 0, "龙口西": 0, "华师": 0, "天河公园": 0, "赤沙": 0, "龙潭": 0, "石榴岗": 0, "赤沙滘": 0, "琶洲西区": 0, "天河公园": 0, "冼村": 0, "海傍": 0, "番禺广场": 0, "横沥": 0, "万顷沙": 0, "龙溪": 0, "猎德大桥南": 0, "琶洲大桥南": 0, "林和西": 0, "体育中心南": 0, "天河南": 0, "黄埔大道": 0, "妇儿中心": 0, "花城大道": 0, "大剧院": 0, "海心沙": 0, "广州塔": 0}');

-- 已有线路的其他站点


-- 剩余的线路
-- 4号线：南沙客运港 ~ 黄村
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('4号线',
     '["南沙客运港", "南横", "塘坑", "大涌", "广隆", "飞沙角", "金洲", "蕉门", "黄阁", "黄阁汽车城", "庆盛", "东涌", "官桥", "石碁", "海傍", "低涌", "新造", "大学城南", "大学城北", "官洲", "万胜围", "车陂南", "车陂", "黄村"]',
     '{"南沙客运港": 0, "南横": 0, "塘坑": 0, "大涌": 0, "广隆": 0, "飞沙角": 0, "金洲": 0, "蕉门": 0, "黄阁": 0, "黄阁汽车城": 0, "庆盛": 0, "东涌": 0, "官桥": 0, "石碁": 0, "海傍": 0, "低涌": 0, "新造": 0, "大学城南": 0, "大学城北": 0, "官洲": 0, "万胜围": 0, "车陂南": 0, "车陂": 0, "黄村": 0}');

-- 7号线：美的大道 ~ 燕山
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('7号线',
     '["美的大道", "陈村", "水藤", "大学城西", "大学城南", "板桥", "员岗", "沙湾", "横沙", "鱼珠", "大沙地", "大沙东", "水西", "长湴", "南村万博", "汉溪长隆", "钟村", "谢村", "石壁", "广州南站", "燕塘新站", "燕岗", "燕山"]',
     '{"美的大道": 0, "陈村": 0, "水藤": 0, "大学城西": 0, "大学城南": 0, "板桥": 0, "员岗": 0, "沙湾": 0, "横沙": 0, "鱼珠": 0, "大沙地": 0, "大沙东": 0, "水西": 0, "长湴": 0, "南村万博": 0, "汉溪长隆": 0, "钟村": 0, "谢村": 0, "石壁": 0, "广州南站": 0, "燕塘新站": 0, "燕岗": 0, "燕山": 0}');

-- 9号线：飞鹅岭 ~ 高增
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('9号线',
     '["飞鹅岭", "花都广场", "花果山公园", "花都汽车城", "广州北站", "花城路", "花都火车站", "马鞍山公园", "白鹭湖", "九龙湖", "清布", "清澜", "清塘", "清河东", "清湖", "高增"]',
     '{"飞鹅岭": 0, "花都广场": 0, "花果山公园": 0, "花都汽车城": 0, "广州北站": 0, "花城路": 0, "花都火车站": 0, "马鞍山公园": 0, "白鹭湖": 0, "九龙湖": 0, "清布": 0, "清澜": 0, "清塘": 0, "清河东": 0, "清湖": 0, "高增": 0}');

-- 10号线：西朗 ~ 天河智慧城
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('10号线',
     '["西朗", "鹤洞", "穗丰家居", "集贤", "岗贝", "沙坑", "坦尾", "瓦窑", "中山八", "如意坊", "沙河顶", "程介村", "东风", "广州东站", "燕岭", "沙河", "天平架", "天河智慧城"]',
     '{"西朗": 0, "鹤洞": 0, "穗丰家居": 0, "集贤": 0, "岗贝": 0, "沙坑": 0, "坦尾": 0, "瓦窑": 0, "中山八": 0, "如意坊": 0, "沙河顶": 0, "程介村": 0, "东风": 0, "广州东站": 0, "燕岭": 0, "沙河": 0, "天平架": 0, "天河智慧城": 0}');

-- 13号线：鱼珠 ~ 新沙
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('13号线',
     '["鱼珠", "裕丰围", "双岗", "南海神庙", "夏园", "南岗", "沙村", "白江", "新塘", "官湖", "新沙"]',
     '{"鱼珠": 0, "裕丰围": 0, "双岗": 0, "南海神庙": 0, "夏园": 0, "南岗": 0, "沙村": 0, "白江": 0, "新塘": 0, "官湖": 0, "新沙": 0}');

-- 14号线：嘉禾望岗 ~ 东风
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('14号线',
     '["嘉禾望岗", "白云东平", "夏良", "太和", "竹料", "钟落潭", "马沥", "新和", "太平", "神岗", "赤草", "从化客运站", "东风"]',
     '{"嘉禾望岗": 0, "白云东平": 0, "夏良": 0, "太和": 0, "竹料": 0, "钟落潭": 0, "马沥": 0, "新和": 0, "太平": 0, "神岗": 0, "赤草": 0, "从化客运站": 0, "东风": 0}');

-- 21号线：天河公园 ~ 增城广场
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('21号线',
     '["天河公园", "棠东", "黄村", "大观南路", "天河智慧城", "神舟路", "科学城", "苏元", "水西", "长平", "金坑", "镇龙西", "镇龙", "中新", "坑贝", "凤岗", "朱村", "山田", "钟岗", "增城广场"]',
     '{"天河公园": 0, "棠东": 0, "黄村": 0, "大观南路": 0, "天河智慧城": 0, "神舟路": 0, "科学城": 0, "苏元": 0, "水西": 0, "长平": 0, "金坑": 0, "镇龙西": 0, "镇龙": 0, "中新": 0, "坑贝": 0, "凤岗": 0, "朱村": 0, "山田": 0, "钟岗": 0, "增城广场": 0}');

-- 22号线：番禺广场 ~ 陈头岗
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('22号线',
     '["番禺广场", "市广路", "广州南站", "陈头岗"]',
     '{"番禺广场": 0, "市广路": 0, "广州南站": 0, "陈头岗": 0}');

-- 广佛线：沥滘 ~ 魁奇路
INSERT INTO metro_lines (line_name, stations, vote_counts) VALUES
    ('广佛线',
     '["沥滘", "南洲", "石溪", "燕岗", "沙园", "沙涌", "横沙", "菊树", "龙溪", "金融高新区", "千灯湖", "礌岗", "南桂路", "桂城", "朝安", "普君北路", "祖庙", "同济路", "季华园", "魁奇路", "澜石", "世纪莲", "东平", "新城东"]',
     '{"沥滘": 0, "南洲": 0, "石溪": 0, "燕岗": 0, "沙园": 0, "沙涌": 0, "横沙": 0, "菊树": 0, "龙溪": 0, "金融高新区": 0, "千灯湖": 0, "礌岗": 0, "南桂路": 0, "桂城": 0, "朝安": 0, "普君北路": 0, "祖庙": 0, "同济路": 0, "季华园": 0, "魁奇路": 0, "澜石": 0, "世纪莲": 0, "东平": 0, "新城东": 0}');

-- 更新1号线：添加缺失的站点
UPDATE metro_lines 
SET stations = JSON_ARRAY("广州东站", "体育中心", "体育西路", "杨箕", "东山口", "烈士陵园", "农讲所", "公园前", "西门口", "陈家祠", "长寿路", "黄沙", "芳村", "花地湾", "坑口", "西朗", "番禺广场"),
    vote_counts = JSON_MERGE_PATCH(
        vote_counts,
        JSON_OBJECT("番禺广场", 0)
    )
WHERE line_name = '1号线';

{"坑口": 13, "杨箕": 5, "芳村": 16, "西朗": 41, "黄沙": 6, "东山口": 5, "公园前": 1, "农讲所": 0, "花地湾": 1, "西门口": 1, "长寿路": 1, "陈家祠": 47, "体育中心": 3, "体育西路": 7, "广州东站": 0, "烈士陵园": 6}

-- 更新3号线北延段：添加缺失的站点
UPDATE metro_lines 
SET stations = JSON_ARRAY("番禺广场", "石壁", "汉溪长隆", "大石", "厦滘", "沥滘", "大塘", "客村", "广州塔", "珠江新城", "体育西路", "林和西", "广州东站", "燕塘", "梅花园", "京溪南方医院", "同和", "永泰", "白云大道北", "嘉禾望岗", "白云机场", "白云机场2号航站楼", "人和", "龙归", "嘉禾", "白云文化广场", "永泰", "同和", "京溪南方医院", "梅花园", "燕塘", "广州东站"),
    vote_counts = JSON_MERGE_PATCH(
        vote_counts,
        JSON_OBJECT(
            "白云机场", 0, 
            "白云机场2号航站楼", 0, 
            "人和", 0, 
            "龙归", 0, 
            "嘉禾", 0
        )
    )
WHERE line_name = '3号线';

-- 更新5号线：添加缺失的站点
UPDATE metro_lines 
SET stations = JSON_ARRAY("滘口", "坦尾", "中山八", "西场", "西村", "广州火车站", "小北", "淘金", "区庄", "动物园", "杨箕", "五羊邨", "珠江新城", "猎德", "潭村", "员村", "科韵路", "车陂南", "东圃", "三溪", "金峰", "大沙东", "大沙地", "大沙", "沙村", "沙贝", "文冲"),
    vote_counts = JSON_MERGE_PATCH(
        vote_counts,
        JSON_OBJECT(
            "滘口", 0, "坦尾", 0, "车陂南", 0, "东圃", 0, "三溪", 0, "金峰", 0, "大沙东", 0, "大沙地", 0, "大沙", 0, "沙村", 0, "沙贝", 0, "文冲", 0
        )
    )
WHERE line_name = '5号线';

-- 更新8号线北延段：添加缺失的站点
UPDATE metro_lines 
SET stations = JSON_ARRAY("凤凰新村", "沙园", "宝岗大道", "昌岗", "晓港", "中大", "鹭江", "客村", "赤岗", "磨碟沙", "新港东", "琶洲", "万胜围", "昌岗", "宝岗大道", "沙园", "凤凰新村", "同福西", "文化公园", "华林寺", "陈家祠", "彩虹桥", "西村", "鹅掌坦", "同德", "滘心", "亭岗", "石井", "小坪", "石潭", "聚龙", "万顷沙", "滘口", "竹料", "镇龙北", "镇龙", "增城广场"),
    vote_counts = JSON_MERGE_PATCH(
        vote_counts,
        JSON_OBJECT(
            "万胜围", 0, "镇龙北", 0, "镇龙", 0, "增城广场", 0, "竹料", 0, "滘口", 0
        )
    )
WHERE line_name = '8号线';

-- 更新11号线：添加缺失的站点
UPDATE metro_lines 
SET stations = JSON_ARRAY("广州南站", "石壁", "谢村", "钟村", "汉溪长隆", "市桥", "大石", "厦滘", "沥滘", "大塘", "客村", "赤岗", "信息港", "员村", "天河公园", "棠东", "黄村", "万胜围", "珠江新城", "体育西路", "林和西", "广州东站"),
    vote_counts = JSON_MERGE_PATCH(
        vote_counts,
        JSON_OBJECT(
            "广州南站", 0, "石壁", 0, "谢村", 0, "钟村", 0, "市桥", 0, "信息港", 0, "棠东", 0, "万胜围", 0
        )
    )
WHERE line_name = '11号线';

-- 更新13号线二期：添加缺失的站点
UPDATE metro_lines 
SET stations = JSON_ARRAY("鱼珠", "裕丰围", "双岗", "南海神庙", "夏园", "南岗", "沙村", "白江", "新塘", "官湖", "新沙", "望亭", "茭塘", "石碁", "海傍", "低涌", "东涌", "庆盛", "莲花", "番禺广场"),
    vote_counts = JSON_MERGE_PATCH(
        vote_counts,
        JSON_OBJECT(
            "望亭", 0, "茭塘", 0, "石碁", 0, "海傍", 0, "低涌", 0, "东涌", 0, "庆盛", 0, "莲花", 0, "番禺广场", 0
        )
    )
WHERE line_name = '13号线';

-- 更新14号线二期：添加缺失的站点
UPDATE metro_lines 
SET stations = JSON_ARRAY("嘉禾望岗", "白云东平", "夏良", "太和", "竹料", "钟落潭", "马沥", "新和", "太平", "神岗", "赤草", "从化客运站", "东风", "温泉", "碧水湾", "新民", "由丘", "鳌头", "朱村", "山田", "钟岗", "增城广场"),
    vote_counts = JSON_MERGE_PATCH(
        vote_counts,
        JSON_OBJECT(
            "温泉", 0, "碧水湾", 0, "新民", 0, "由丘", 0, "鳌头", 0, "朱村", 0, "山田", 0, "钟岗", 0, "增城广场", 0
        )
    )
WHERE line_name = '14号线';

-- 更新18号线二期：添加缺失的站点
UPDATE metro_lines 
SET stations = JSON_ARRAY("冼村", "磨碟沙", "龙潭", "海傍", "番禺广场", "横沥", "万顷沙", "龙溪", "官桥", "新造", "石碁", "会江", "南村万博", "番禺广场"),
    vote_counts = JSON_MERGE_PATCH(
        vote_counts,
        JSON_OBJECT(
            "官桥", 0, "新造", 0, "石碁", 0, "会江", 0, "南村万博", 0, "番禺广场", 0
        )
    )
WHERE line_name = '18号线';

-- 更新广州地铁6号线站点数据
UPDATE metro_lines
SET stations = JSON_ARRAY(
        "浔峰岗", "横沙", "沙贝", "河沙", "坦尾", "如意坊", "黄沙", "文化公园",
        "一德路", "海珠广场", "北京路", "团一大广场", "东湖", "东山口", "区庄",
        "黄花岗", "沙河顶", "沙河", "天平架", "燕塘", "天河客运站", "长湴",
        "植物园", "龙洞", "柯木塱", "高塘石", "黄陂", "金峰", "暹岗", "苏元", "萝岗", "香雪"
               ),
    vote_counts = JSON_MERGE_PATCH(
            vote_counts,
            JSON_OBJECT(
                    "浔峰岗", 0,
                    "横沙", 0,
                    "沙贝", 0,
                    "河沙", 0,
                    "坦尾", 0,
                    "海珠广场", 0,
                    "沙河", 0,
                    "植物园", 0,
                    "龙洞", 0,
                    "柯木塱", 0,
                    "高塘石", 0,
                    "黄陂", 0,
                    "金峰", 0,
                    "暹岗", 0,
                    "苏元", 0,
                    "萝岗", 0,
                    "香雪", 0
            )
                  )
WHERE line_name = '6号线';
