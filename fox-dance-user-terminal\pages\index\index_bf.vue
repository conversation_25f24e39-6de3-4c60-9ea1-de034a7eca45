<template>
	<view class="index">
		<u-navbar :is-back="false" title="FOX舞蹈" :background="{ background: 'rgba(26,26, 26,' + navBg + ')' }"
			:border-bottom="false" :title-color="navBg==1?'#fff':'#fff' " title-size="32">

		</u-navbar>

		<view class="use">
			<view class="share">
				<image src="/static/images/index_share.png" mode=""></image>
				<button open-type="share"></button>
			</view>
			<view class="concat" @click="showConcat = false">
				<image src="/static/images/index_concat_kf.png" mode="scaleToFill"></image>
				<button open-type="contact"></button>
			</view>
		</view>
		<view class="userInfo">
			<view class="userInfo_t flex row-between">
				<view class="userInfo_t_l">
					<view class="userInfo_t_l_t">
						Hello
					</view>
					<view class="userInfo_t_l_d">
						{{isLogined ? (userInfo.nickname == '' ? '微信用户' : userInfo.nickname) : '请先登录'}}</view>
				</view>
				<view class="userInfo_t_r">
					<image src="/static/images/toux.png" mode="aspectFill" v-if="!isLogined"></image>
					<image :src="userInfo.avatar == '' ? '/static/images/toux.png' : imgbaseUrl + userInfo.avatar"
						mode="aspectFill" class="min_one_l" v-if="isLogined"></image>
				</view>
			</view>
			<view class="userInfo_count flex row-between">
				<view class="userInfo_count_li">
					<view class="userInfo_count_li_num flex col-baseline">
						{{isLogined ? userInfo.frequency : '0'}}<text>次</text></view>
					<view class="userInfo_count_li_text">我的次数</view>
				</view>
				<view class="userInfo_count_li">
					<view class="userInfo_count_li_num">{{isLogined ? userInfo.score*1 : 0}}</view>
					<view class="userInfo_count_li_text">我的积分</view>
				</view>
			</view>
			<view class="userInfo_shop flex row-between">
				<view class="userInfo_shop_l">
					<view class="userInfo_shop_l_t"
						@click="navTo('/pages/index/storesDetail?id=' + userInfo.store.id,1)">
						{{userInfo.store.name}}
						<image src="/static/images/index_shop_more.png" mode="scaleToFill"></image>
					</view>
					<view class="userInfo_shop_l_d">{{userInfo.store.address}}</view>
				</view>
				<view class="userInfo_shop_r">
					<view class="btn" @click="navTo('/pages/index/switchStores')">
						切换门店
					</view>
				</view>
			</view>
		</view>
		<view class="notice flex">
			<view class="notice_l">
				<image src="/static/images/index_notice.png" mode="scaleToFill"></image>
			</view>
			<view class="notice_r flex-1">
				<uni-notice-bar scrollable single :text="userInfo.notice" background-color="transparent" color="#333"
					:single="true"> </uni-notice-bar>
				<!-- <u-notice-bar mode="horizontal" :list="notice_text"></u-notice-bar> -->
			</view>
		</view>

		<view class="nav flex row-between">
			<view class="nav_l flex row-center flex-col col-center" @click="navTo('/pages/index/foxDetail',1)">
				<view class="nav_l_t">
					<view class="nav_l_text">
						FOX介绍
					</view>
					<view class="title_bottom">

					</view>
				</view>
				<view class="nav_l_d">
					<image src="/static/images/index_fox_js.png" mode="scaleToFill"></image>
				</view>
			</view>
			<view class="nav_r">
				<view class="nav_r_t flex row-center" style="margin-top: 6rpx;"
					@click="navTo('/pages/index/storesDetail?id=' + userInfo.store.id,1)">
					<view class="nav_r_t_text">
						<view class="text">
							门店介绍
						</view>
						<view class="title_bottom">

						</view>
					</view>
					<view class="nav_r_t_img">
						<image src="/static/images/index_fox_mdjs.png" mode="scaleToFill"></image>
					</view>
				</view>
				<view class="nav_r_t flex row-center" style="margin-top: 26rpx;"
					@click="navTo('/pages/index/teacherDetail',1)">
					<view class="nav_r_t_text">
						<view class="text">
							老师介绍
						</view>
						<view class="title_bottom">

						</view>
					</view>
					<view class="nav_r_t_img">
						<image src="/static/images/index_fox_lsjs.png" mode="aspectFill"></image>
					</view>
				</view>
			</view>
		</view>
		<view class="poster">

			<!-- <image src="/static/images/index1111.png" v-for="(item,index) in 3" :key="index"></image> -->
			<image :src="imgbaseUrl + item" mode="widthFix" v-for="(item,index) in userInfo.poster" :key="index"
				@click="openImg(index,userInfo.poster)"></image>
		</view>
		<view class="" style="height: 300rpx;">

		</view>

		<u-popup v-model="showConcat" border-radius="20" mode="center">
			<view class="concat_box">
				<view class="concat_box_title flex col-top row-between">
					<view class="">
						选择门店
					</view>
					<image src="/static/images/popup_close.png" mode="scaleToFill" @click="showConcat = false"></image>
				</view>
				<view class="concat_box_list">
					<view class="concat_box_li flex row-between" v-for="(item,index) in storesLists" :key="index">
						<view class="concat_box_li_l flex">
							<view class="concat_box_li_l_img">
								<image :src="imgbaseUrl + item.image" mode="scaleToFill"></image>
							</view>
							<view class="concat_box_li_l_name">
								<view class="text">{{item.name}}
									<view class="title_bottom"></view>
								</view>

							</view>
						</view>
						<view class="concat_box_li_r btn" @click="navTo('/pages/index/service?id=' + item.id)">联系客服
						</view>
					</view>
					<!-- <view class="concat_box_li flex row-between" v-for="(item,index) in storesLists" :key="index">
						<view class="concat_box_li_l flex">
							<view class="concat_box_li_l_img">
								<image :src="imgbaseUrl + item.image" mode="scaleToFill"></image>
							</view>
							<view class="concat_box_li_l_name">
								<view class="text">{{item.name}}</view>
								<view class="title_bottom"></view>
							</view>
						</view>
						<view class="concat_box_li_r btn" @click="navTo('/pages/index/service',1)">
							联系客服
						</view>
					</view>
					<view class="concat_box_li flex row-between" v-for="(item,index) in storesLists" :key="index">
						<view class="concat_box_li_l flex">
							<view class="concat_box_li_l_img">
								<image :src="imgbaseUrl + item.image" mode="scaleToFill"></image>
							</view>
							<view class="concat_box_li_l_name">
								<view class="text">{{item.name}}</view>
								<view class="title_bottom"></view>
							</view>
						</view>
						<view class="concat_box_li_r btn" @click="navTo('/pages/index/service',1)">
							联系客服
						</view>
					</view> -->
				</view>
			</view>
		</u-popup>

		<tabbar ref="tabbar" :current="0"></tabbar>

		<view class="ind_one">

		</view>
	</view>
</template>


<script>
	import {
		userInfoApi,
		homeDataApi,
		storeListsApi
	} from '@/config/http.achieve.js'
	import {
		authIsPass
	} from '@/utils/auth.js'
	import tabbar from '@/components/tabbar.vue'
	export default {
		components: {
			tabbar,
		},
		data() {
			return {
				isLogined: true,
				navBg: '',
				notice_text: [],
				showConcat: false, //联系客服弹窗
				imgbaseUrl: '', //图片地址
				userInfo: {
					avatar: "",
					frequency: 0,
					nickname: "",
					notice: '',
					score: 0,
					poster: [],
					store: {
						address: '',
						name: '',
						id: 0
					}
				},
				storesLists: [],
			}
		},
		onShow() {
			// uni.setStorageSync('token','1')
			this.imgbaseUrl = this.$baseUrl;
			this.isLogined = uni.getStorageSync('token') ? true : false;
			if (this.isLogined) {
				// this.userData();//个人信息
			}
			if (uni.getStorageSync('postion')) {
				this.homeData(); //首页数据
			}
			uni.hideTabBar()
		},
		onPageScroll(e) {
			const top = uni.upx2px(100)
			const {
				scrollTop
			} = e
			let percent = scrollTop / top > 1 ? 1 : scrollTop / top
			this.navBg = percent
		},
		onLoad(options) {
			if (!uni.getStorageSync('postion')) {
				//this.getPosition()
			} else {
				// this.areaInfo = uni.getStorageSync('postion')
			}
			this.getPosition()

			this.storeData(); //门店列表
			uni.hideTabBar();


			if (options.pid) {
				uni.setStorageSync('pid', options.pid)
				console.log('options进去了？userid', uni.getStorageSync('pid'))
			}
			/*if(options.q){
				const scene = decodeURIComponent(options.q);
				uni.setStorageSync('pid',scene.split('=')[1])
			}*/
		},
		methods: {
			//门店列表
			storeData() {
				let that = this;
				storeListsApi({
					type: 1,
					limit: 9999,
				}).then(res => {
					console.log('门店列表', res)
					if (res.code == 1) {
						uni.hideLoading();
						that.storesLists = res.data.data;
					}
				})

			},
			//打开图片
			openImg(idx, imgs) {
				let arr = []
				for (let i = 0; i < imgs.length; i++) {
					arr.push(this.imgbaseUrl + imgs[i])
				}
				console.log(idx, imgs);
				uni.previewImage({
					current: idx,
					urls: arr
				})
			},
			//首页数据
			homeData() {
				uni.showLoading({
					title: '加载中'
				});
				let that = this;
				homeDataApi({
					longitude: uni.getStorageSync('postion').longitude,
					latitude: uni.getStorageSync('postion').latitude,
				}).then(res => {
					if (res.code == 1) {
						console.log('首页', res);

						// uni.setStorageSync('storeInfo',res.data.store);
						if (uni.getStorageSync('storeInfo')) {
							that.userInfo = res.data;
							this.userInfo.store = uni.getStorageSync('storeInfo');
							uni.setStorageSync('server_token', res.data.server_token)
						} else {
							uni.setStorageSync('storeInfo', res.data.store);
							that.userInfo = res.data;
						}

						uni.hideLoading();
					}
				})
			},
			//获取自身位置
			async getPosition() {

				let that = this
				const flag = await authIsPass('scope.userLocation')
				if (this.IsOpenMap == false) {
					return
				}
				console.log(flag);
				if (!flag) {
					this.IsOpenMap = false
					uni.authorize({
						scope: 'scope.userLocation',
						fail: (res) => {
							uni.showModal({
								title: '使用该功能必须允许位置服务，是否重新授权？',
								showCancel: false,
								success: ({
									confirm
								}) => {
									if (confirm) {
										uni.openSetting({
											success() {
												uni.getLocation({
													type: 'wgs84',
													success: function(res) {
														console.log('定位1',
															res)
														let data = {
															latitude: res
																.latitude,
															longitude: res
																.longitude
														}
														uni.setStorageSync(
															'postion',
															data)
														that.homeData();
													},
													fail: function(err) {

													}
												});
												console.log('开启权限成功')
											},
											fail() {
												console.log('开启权限失败')
											},
										})
									}
								},
							})
						},
						success: () => {
							uni.getLocation({
								type: 'wgs84',
								success: function(res) {
									console.log('定位2', res)
									let data = {
										latitude: res.latitude,
										longitude: res.longitude
									}
									uni.setStorageSync('postion', data)
									that.homeData();
								},
								fail: function(err) {

								}
							});
						},
					})
				} else {
					uni.getLocation({
						type: 'wgs84',
						success: function(res) {
							console.log('定位3', res)
							let data = {
								latitude: res.latitude,
								longitude: res.longitude
							}
							uni.setStorageSync('postion', data)
							that.homeData();
						},
						fail: function(err) {}
					});
				}
			},
			navTo(url, ismd) {
				if (ismd) {
					uni.navigateTo({
						url: url
					});
					return false;
				}
				var that = this;
				if (uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync(
						'token')) {
					uni.showToast({
						icon: 'none',
						title: '请先登录'
					});
					setTimeout(function() {
						uni.navigateTo({
							url: '/pages/login/login'
						})
					}, 1000)
				} else {
					uni.navigateTo({
						url: url
					})
				}
			},
		},
		// 分享到微信好友
		onShareAppMessage() {
			var that = this;
			return {
				title: 'FOX舞蹈，快来和我一起体验吧，超多福利等你来拿!',
				path: '/pages/index/index',
				// imageUrl:that.bannerLists[0].images,
			}
		}
	}
</script>

<style lang="scss">
	page {
		padding-bottom: 0;
		background: #FADAFF;
	}

	.index {
		// background-image: url(/static/login/login_top_bgi.png);
		background-image: url(https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/userreport/login_top_bgi.png);

		background-size: 100% auto;
		background-repeat: no-repeat;
		min-height: 100vh;
	}

	.use {
		position: fixed;
		right: 14rpx;
		bottom: 30vh;
		z-index: 11;

		.share {
			position: relative;

			button {
				display: block;
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				opacity: 0;
			}
		}

		.concat {
			position: relative;

			button {
				display: block;
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				opacity: 0;
			}
		}

		image {
			width: 106rpx;
			height: 106rpx;

			&:nth-child(1) {
				margin-bottom: 34rpx;
			}
		}
	}

	.userInfo {
		margin: 494rpx auto 0;
		padding: 42rpx 32rpx 32rpx;
		width: 700rpx;
		background: #FFFFFF;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		position: relative;

		.userInfo_t {
			.userInfo_t_l {
				padding-left: 20rpx;

				.userInfo_t_l_t {
					font-family: Maoken Glitch Sans;
					font-size: 26rpx;
					color: #333333;
					line-height: 30rpx;
				}

				.userInfo_t_l_d {
					margin-top: 12rpx;
					font-weight: Medium;
					font-size: 28rpx;
					color: #333333;
					line-height: 33rpx;
				}
			}

			.userInfo_t_r {
				position: absolute;
				right: 52rpx;
				top: -44rpx;

				image {
					width: 140rpx;
					height: 140rpx;
					border-radius: 50%;
					border: 4rpx solid #FFFFFF;
					background-color: pink;
				}
			}
		}

		.userInfo_count {
			margin-top: 26rpx;
			padding-bottom: 24rpx;
			border-bottom: 2rpx solid rgba(160, 160, 160, 0.2);

			.userInfo_count_li {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.userInfo_count_li_num {
					font-weight: 500;
					font-size: 40rpx;
					color: #333333;
					line-height: 47rpx;

					text {
						font-size: 26rpx;
						line-height: 33rpx;
					}
				}

				.userInfo_count_li_text {
					margin-top: 26rpx;
					font-size: 26rpx;
					color: #333333;
					line-height: 30rpx;
				}
			}
		}

		.userInfo_shop {
			padding-top: 32rpx;

			.userInfo_shop_l {
				.userInfo_shop_l_t {
					font-weight: 500;
					font-size: 26rpx;
					color: #333333;
					line-height: 30rpx;
					display: flex;
					align-items: center;

					image {
						// margin-left: 24rpx;
						width: 10.46rpx;
						height: 16rpx;
					}
				}

				.userInfo_shop_l_d {
					margin-top: 12rpx;
					font-size: 26rpx;
					color: #333333;
					line-height: 30rpx;
				}
			}

			.userInfo_shop_r {

				.btn {
					width: 144rpx;
					font-size: 26rpx;
					height: 56rpx;
					background: #945048;
					border-radius: 124rpx 124rpx 124rpx 124rpx;
				}
			}
		}
	}

	.notice {
		margin: 24rpx auto 0;
		width: 670rpx;
		height: 80rpx;
		background: #fff;
		padding: 0 26rpx;
		height: 72rpx;
		background: #FFFFFF;
		border-radius: 90rpx 90rpx 90rpx 90rpx;

		.notice_l {
			image {
				width: 32rpx;
				height: 32rpx;
			}
		}

		.notice_r {
			font-size: 26rpx;
			color: #333333;
			line-height: 30rpx;

			/deep/ .uni-noticebar {
				margin-bottom: 0 !important;
			}

		}
	}

	.nav {
		padding: 26rpx 22rpx 0 26rpx;

		.title_bottom {
			position: absolute;
			left: 0;
			bottom: 6rpx;
			width: 126rpx;
			height: 10rpx;
			background: linear-gradient(90deg, #945048 28%, rgba(255, 255, 255, 0) 100%);
		}

		.nav_l {
			width: 362rpx;
			height: 308rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;

			.nav_l_t {
				position: relative;

				.nav_l_text {
					position: relative;
					z-index: 11;
					font-weight: 600;
					font-size: 32rpx;
					color: #333333;
					line-height: 38rpx;

				}
			}

			.nav_l_d {
				image {
					width: 202rpx;
					height: 202rpx;
					margin: 30rpx auto 0;
				}
			}
		}

		.nav_r {
			width: 314rpx;
			height: 308rpx;

			.nav_r_t {
				width: 314rpx;
				height: 136rpx;
				background: #FFFFFF;
				border-radius: 20rpx 20rpx 20rpx 20rpx;

				.nav_r_t_text {
					position: relative;

					.text {
						position: relative;
						z-index: 11;
						font-family: Maoken Glitch Sans;
						font-weight: 600;
						font-size: 32rpx;
						color: #333333;
						line-height: 38rpx;
					}
				}

				.nav_r_t_img {
					image {
						width: 108rpx;
						height: 108rpx;
						margin-left: 26rpx;
					}
				}
			}
		}
	}

	.poster {
		image {
			width: 698rpx;
			height: 238rpx;
			margin: 20rpx auto 0;

			&:nth-child(1) {
				margin-top: 28rpx;
			}
		}
	}

	.concat_box {
		padding: 26rpx 66rpx;
		width: 662rpx;
		height: 770rpx;
		height: auto;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		overflow: auto;

		.concat_box_title {
			padding-bottom: 26rpx;
			border-bottom: 2rpx solid rgba(148, 80, 72, 0.2);

			view {
				font-size: 32rpx;
				color: #333333;
				line-height: 38rpx;
			}

			image {
				width: 40rpx;
				height: 40rpx;

			}
		}

		.concat_box_list {
			-height: calc(100% - 100rpx);
			-overflow: auto;

			.concat_box_li {
				padding: 14rpx 30rpx;
				padding: 14rpx 0;
				margin: 26rpx 0 0;

				.concat_box_li_l {
					.concat_box_li_l_img {
						image {
							width: 108rpx;
							height: 108rpx;
						}
					}

					.concat_box_li_l_name {
						width: 200rpx;
						position: relative;
						margin-left: 40rpx;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
						overflow: hidden;
						margin-right: 20rpx;

						.text {
							position: relative;
							z-index: 11;
							color: #333333;
							font-family: Maoken Glitch Sans;
							font-size: 26rpx;
							line-height: 30rpx;
							float: left;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
							overflow: hidden;
						}

						.title_bottom {
							position: absolute;
							left: 0;
							bottom: 6rpx;
							-width: 126rpx;
							height: 10rpx;
							background: linear-gradient(90deg, #945048 28%, rgba(255, 255, 255, 0) 100%);
							float: left;
							width: 100%;
							z-index: -1;
						}
					}
				}

				.concat_box_li_r {
					width: 152rpx;

					height: 56rpx;
					background: #945048;
					border-radius: 92rpx 92rpx 92rpx 92rpx;
					font-size: 26rpx;
				}
			}
		}
	}
</style>