package com.yupi.springbootinit.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.exception.ThrowUtils;
import com.yupi.springbootinit.mapper.TagMapper;
import com.yupi.springbootinit.model.entity.Tag;
import com.yupi.springbootinit.service.TagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 标签服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
@Slf4j
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {

    @Resource
    private TagMapper tagMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTag(String name, String description, String color) {
        ThrowUtils.throwIf(name == null || name.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "标签名称不能为空");

        // 检查标签是否已存在
        Tag existTag = tagMapper.selectByName(name.trim());
        if (existTag != null) {
            return existTag.getId();
        }

        // 创建新标签
        Tag tag = new Tag();
        tag.setName(name.trim());
        tag.setDescription(description);
        tag.setColor(color);
        tag.setUseCount(0);
        tag.setIsHot(0);
        tag.setIsDelete(0);

        boolean result = this.save(tag);
        return result ? tag.getId() : null;
    }

    @Override
    public Tag getOrCreateTag(String name) {
        ThrowUtils.throwIf(name == null || name.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "标签名称不能为空");

        Tag tag = tagMapper.selectByName(name.trim());
        if (tag != null) {
            return tag;
        }

        // 创建新标签
        Long tagId = createTag(name.trim(), null, null);
        return this.getById(tagId);
    }

    @Override
    public List<Tag> getOrCreateTags(List<String> names) {
        ThrowUtils.throwIf(names == null || names.isEmpty(), ErrorCode.PARAMS_ERROR, "标签名称列表不能为空");

        List<Tag> result = new ArrayList<>();
        for (String name : names) {
            if (name != null && !name.trim().isEmpty()) {
                Tag tag = getOrCreateTag(name.trim());
                if (tag != null) {
                    result.add(tag);
                }
            }
        }
        return result;
    }

    @Override
    public List<Tag> getHotTags(Integer limit) {
        return tagMapper.selectHotTags(new Page<>(), limit).getRecords();
    }

    @Override
    public List<Tag> getTopUsedTags(Integer limit) {
        return tagMapper.selectTopUsedTags(limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean incrementUseCount(Long tagId, Integer increment) {
        ThrowUtils.throwIf(tagId == null || increment == null, ErrorCode.PARAMS_ERROR);

        int result = tagMapper.incrementUseCount(tagId, increment);
        return result > 0;
    }

    @Override
    public Page<Tag> searchTags(String keyword, Integer current, Integer pageSize) {
        Page<Tag> page = new Page<>(current, pageSize);
        return tagMapper.searchTags(page, keyword);
    }

    @Override
    public List<Tag> getRecommendTags(Long userId, Integer limit) {
        return tagMapper.selectRecommendTags(userId, limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateHotStatus(Long tagId, Boolean isHot) {
        ThrowUtils.throwIf(tagId == null || isHot == null, ErrorCode.PARAMS_ERROR);

        Tag tag = this.getById(tagId);
        if (tag == null) {
            return false;
        }

        tag.setIsHot(isHot ? 1 : 0);
        return this.updateById(tag);
    }

    // ==================== 社交功能相关方法实现 ====================

    @Override
    public Object getTagDetail(Long tagId, Long currentUserId) {
        try {
            Tag tag = this.getById(tagId);
            if (tag == null) {
                return null;
            }

            // 构建话题详情对象
            return new Object() {
                public final Long tagId = tag.getId();
                public final String tagName = tag.getName();
                public final String description = tag.getDescription();
                public final String color = tag.getColor();
                public final Integer useCount = tag.getUseCount();
                public final Boolean isHot = tag.getIsHot() == 1;
                public final Boolean isFollowed = false; // TODO: 查询用户是否关注该话题
                public final Integer postCount = tag.getUseCount(); // 使用useCount作为帖子数
                public final Integer followCount = 0; // TODO: 查询话题关注数
                public final String createTime = tag.getCreateTime().toString();
            };

        } catch (Exception e) {
            log.error("获取话题详情异常 - tagId: {}, error: {}", tagId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<Object> getTagPosts(Long tagId, Integer current, Integer size, String sortBy) {
        try {
            // TODO: 实际应该查询post_tags表关联的帖子
            // 这里返回模拟数据
            List<Object> posts = new ArrayList<>();

            // 模拟帖子数据
            for (int i = 0; i < Math.min(size, 5); i++) {
                final int index = i;
                posts.add(new Object() {
                    public final Long id = (long) (tagId * 100 + index);
                    public final String content = "这是话题 " + tagId + " 下的帖子 " + index;
                    public final String coverImage = "https://picsum.photos/400/400?random=" + (tagId * 100 + index);
                    public final String username = "用户" + index;
                    public final String userAvatar = "https://picsum.photos/100/100?random=" + index;
                    public final Integer likeCount = (int) (Math.random() * 1000);
                    public final Integer commentCount = (int) (Math.random() * 100);
                    public final String createTime = "2025-07-17 10:0" + index + ":00";
                });
            }

            return posts;

        } catch (Exception e) {
            log.error("获取话题帖子异常 - tagId: {}, error: {}", tagId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean followTag(Long tagId, Long userId) {
        try {
            // TODO: 实际应该插入到tag_follows表
            log.info("用户关注话题 - tagId: {}, userId: {}", tagId, userId);
            return true;

        } catch (Exception e) {
            log.error("关注话题异常 - tagId: {}, userId: {}, error: {}", tagId, userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean unfollowTag(Long tagId, Long userId) {
        try {
            // TODO: 实际应该从tag_follows表删除
            log.info("用户取消关注话题 - tagId: {}, userId: {}", tagId, userId);
            return true;

        } catch (Exception e) {
            log.error("取消关注话题异常 - tagId: {}, userId: {}, error: {}", tagId, userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<Object> getFollowingTags(Long userId, Integer current, Integer size) {
        try {
            // TODO: 实际应该查询tag_follows表
            // 这里返回模拟数据
            List<Object> followingTags = new ArrayList<>();

            List<Tag> hotTags = getHotTags(size);
            for (Tag tag : hotTags) {
                followingTags.add(new Object() {
                    public final Long tagId = tag.getId();
                    public final String tagName = tag.getName();
                    public final String description = tag.getDescription();
                    public final String color = tag.getColor();
                    public final Integer postCount = tag.getUseCount();
                    public final Integer followCount = (int) (Math.random() * 1000);
                    public final Boolean isFollowed = true;
                });
            }

            return followingTags;

        } catch (Exception e) {
            log.error("获取关注话题异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
