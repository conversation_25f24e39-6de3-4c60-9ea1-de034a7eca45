{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/order/order.vue?4a47", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/order/order.vue?7c5b", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/order/order.vue?fd27", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/order/order.vue?010a", "uni-app:///pages/mine/order/order.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "type", "orderLists", "page", "total_pages", "zanwsj", "status", "loadingText", "loadmoreText", "nomoreText", "imgbaseUrl", "qj<PERSON>ton", "onLoad", "onShow", "methods", "navTap", "orderData", "uni", "title", "size", "console", "that", "onReachBottom", "onPullDownRefresh", "confirmSubTap", "content", "success", "id", "icon", "duration", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;;;AAGpD;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACqDxvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;;EACAC;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACAC;QACAC;MACA;MACA;MACA;QACAf;QACAgB;QACAlB;MACA;QACAmB;QACA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACA;UACAC;UACAA;UACAA;UACA;UACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UACA;YACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAA;UACAJ;UACAA;QACA;MACA;IAEA;IACAK;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACAH;MACA;MACA;MACA;IACA;IACA;IACAI;MACA;MAEAP;QACAC;QACAO;QACAC;UACA;YAEAT;cACAC;YACA;YACA;cACAS;YACA;cACAP;cACA;gBACAH;gBACAI;gBACAA;gBACAA;gBACAJ;kBACAW;kBACAV;kBACAW;gBACA;cAEA;YACA;UAEA;YACAT;UACA;QACA;MACA;IACA;IACAU;MACAb;QACAc;MACA;IACA;EAEA;AACA;AAAA,2B", "file": "pages/mine/order/order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/order/order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order.vue?vue&type=template&id=8985ecec&scoped=true&\"\nvar renderjs\nimport script from \"./order.vue?vue&type=script&lang=js&\"\nexport * from \"./order.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8985ecec\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/order/order.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=template&id=8985ecec&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"order\" :style=\"{ '--qjbutton-color': qjbutton }\">\r\n\t\t<view class=\"ord_nav\">\r\n\t\t\t<view class=\"ord_nav_li\" :class=\"type == 0 ? 'ord_nav_li_ac' : ''\" @click=\"navTap(0)\"><view><text>全部</text><text></text></view></view>\r\n\t\t\t<view class=\"ord_nav_li\" :class=\"type == 1 ? 'ord_nav_li_ac' : ''\" @click=\"navTap(1)\"><view><text>待收货</text><text></text></view></view>\r\n\t\t\t<view class=\"ord_nav_li\" :class=\"type == 2 ? 'ord_nav_li_ac' : ''\" @click=\"navTap(2)\"><view><text>已完成</text><text></text></view></view>\r\n\t\t</view> \r\n\t\t<view class=\"ord_con\">\r\n\t\t\t<view class=\"ord_con_li\" v-for=\"(item,index) in orderLists\" :key=\"index\">\r\n\t\t\t\t<view class=\"ord_con_li_a\"><view>订单号:{{item.order_no}}</view><text>{{item.status == 1 ? '待发货' : item.status == 2 ? '待收货' : item.status == 3 ? '已完成' : '待发货 '}}</text></view>\r\n\t\t\t\t<view class=\"ord_con_li_b\">\r\n\t\t\t\t\t<view class=\"ord_con_li_b_li\">\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.image\" mode=\"scaleToFill\" class=\"ord_con_li_b_li_l\"></image>\r\n\t\t\t\t\t\t<view class=\"ord_con_li_b_li_r\">\r\n\t\t\t\t\t\t\t<view class=\"ord_con_li_b_li_r_a\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"ord_con_li_b_li_r_bb\">规格：{{item.sku_name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"ord_con_li_b_li_r_cc\"><view>￥{{item.redeem_points*1}}</view><text>×{{item.num}}</text></view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"ord_con_li_b_li_r_b\"><text>￥12.99</text>/份</view> -->\r\n\t\t\t\t\t\t\t<!-- <view class=\"ord_con_li_b_li_r_b\">￥<text>{{item.redeem_points*1}}</text></view> -->\r\n\t\t\t\t\t\t\t<!-- <view class=\"ord_con_li_b_li_r_c\">已选：420g</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"ord_con_li_c\" :style=\"item.status == 2 ? '' : 'padding-bottom:0'\">\r\n\t\t\t\t\t<text>共1项</text>\r\n\t\t\t\t\t<view>合计：￥{{item.total_price*1}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- //状态:1=待发货,2=待收货,3=已完成 -->\r\n\t\t\t\t<view class=\"ord_con_li_d\" v-if=\"item.status == 2\">\r\n\t\t\t\t\t<view @click=\"navTo('/pages/mine/order/logistics?id=' + item.id + '&name=' + item.name + '&images=' + item.image)\" v-if=\"item.status == 2\">查看物流</view>\r\n\t\t\t\t\t<view class=\"back\" v-if=\"item.status == 2\" @click.stop=\"confirmSubTap(item.id)\">确认收货</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"gg_loding\" v-if=\"!zanwsj\">\r\n\t\t\t\t<view class=\"loader-inner ball-clip-rotate\" v-if=\"status == 'loading'\">\r\n\t\t\t\t\t<view></view>\r\n\t\t\t\t\t<text>加载中</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"gg_loding_wusj\" v-else>─── 没有更多数据了 ───</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"gg_zwsj\" v-if=\"zanwsj\">\r\n\t\t\t\t<view class=\"gg_zwsj_w\">\r\n\t\t\t\t\t<image src=\"/static/images/wusj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t<text>暂无数据</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tmyOrderApi,\r\n\tconfirmOrderApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\ttype:0,\r\n\t\t\torderLists:[],//积分明细\r\n\t\t\tpage:1,//当前页数\r\n\t\t\ttotal_pages: 1,//总页数\r\n\t\t\tzanwsj:false,//是否有数据\r\n\t\t\tstatus: 'loading',//底部loding是否显示\r\n\t\t\tloadingText: '努力加载中',\r\n\t\t\tloadmoreText: '轻轻上拉',\r\n\t\t\tnomoreText: '实在没有了',\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t},\r\n\tonShow() {\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.page = 1;\r\n\t\tthis.orderLists = [];\r\n\t\tthis.orderData()//订单列表\r\n\t},\r\n\tmethods: {\r\n\t\tnavTap(index){\r\n\t\t\tthis.type = index;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.orderLists = [];\r\n\t\t\tthis.orderData();\r\n\t\t},\r\n\t\t//订单列表\r\n\t\torderData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tmyOrderApi({\r\n\t\t\t\tpage:that.page,\r\n\t\t\t\tsize:10,\r\n\t\t\t\ttype:that.type,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('订单列表',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t/*res.data.data = [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tname:'泡玛特POPMART拉布布Labubu马卡龙坐坐派对搪胶脸盲盒 前方高能3.0三代 ',\r\n\t\t\t\t\t\t\torder_no:'123456',\r\n\t\t\t\t\t\t\tstatus:2,\r\n\t\t\t\t\t\t\timage:'/storage/default/20250405/画板1拷贝46f915eeeece5349b4921bffacb4dc2adb16ed84.png',\r\n\t\t\t\t\t\t\tredeem_points:20,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tname:'泡玛特POPMART拉布布Labubu马卡龙坐坐派对搪胶脸盲盒 前方高能3.0三代 ',\r\n\t\t\t\t\t\t\torder_no:'123456',\r\n\t\t\t\t\t\t\tstatus:1,\r\n\t\t\t\t\t\t\timage:'/storage/default/20250405/画板1拷贝46f915eeeece5349b4921bffacb4dc2adb16ed84.png',\r\n\t\t\t\t\t\t\tredeem_points:20,\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t]\r\n\t\t\t\t\tres.data.last_page = 1*/\r\n\t\t\t\t\tvar obj = res.data.data;\r\n\t\t\t\t\tthat.orderLists = that.orderLists.concat(obj);\r\n\t\t\t\t\tthat.zanwsj = that.orderLists.length ? true : false;\r\n\t\t\t\t\tthat.page++;\r\n\t\t\t\t\t// that.total_pages = Math.ceil(res.total/20);\r\n\t\t\t\t\tthat.total_pages = res.data.last_page;\r\n\t\t\t\t\tif (that.page != 1) {\r\n\t\t\t\t\t\tif (that.total_pages >= that.page) {\r\n\t\t\t\t\t\t\tthat.status = 'loading'\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.orderLists.length == 0){\r\n\t\t\t\t\t\tthat.zanwsj = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.zanwsj = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(res.data.total*1<=10){\r\n\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\t//console.log('到底了');\r\n\t\t\tif (this.page != 1) {\r\n\t\t\t\tif (this.total_pages >= this.page) {\r\n\t\t\t\t\tthis.orderData();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t    console.log('我被下拉了');\r\n\t\t    this.page = 1;\r\n\t\t\tthis.orderLists = [];\r\n\t\t\tthis.orderData();//订单列表\r\n\t\t},\r\n\t\t//确认收货\r\n\t\tconfirmSubTap(id) {\r\n\t\t\tvar that = this;\r\n\t\t\t\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确认要收货吗？',\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tconfirmOrderApi({\r\n\t\t\t\t\t\t\tid: id\r\n\t\t\t\t\t\t}).then(rep => {\r\n\t\t\t\t\t\t\tconsole.log('确认收货', rep)\r\n\t\t\t\t\t\t\tif (rep.code == 1) {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\t\t\tthat.orderLists = [];\r\n\t\t\t\t\t\t\t\tthat.orderData();//订单列表\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\ttitle: '收货成功',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\r\n\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t\r\n</style>"], "sourceRoot": ""}