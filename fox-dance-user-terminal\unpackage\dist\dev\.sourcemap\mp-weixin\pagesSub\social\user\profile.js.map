{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?74b7", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?1ee4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?dd63", "uni-app:///pagesSub/social/user/profile.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?3258", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?38ad"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "PostCard", "data", "userId", "userInfo", "nickname", "avatar", "bio", "postCount", "followingCount", "followersCount", "likeCount", "isFollowed", "userPosts", "onLoad", "methods", "loadUserInfo", "loadUserPosts", "posts", "id", "title", "username", "userAvatar", "content", "coverImage", "commentCount", "isLiked", "createTime", "to<PERSON><PERSON><PERSON><PERSON>", "uni", "icon", "onPostLike", "console", "goPostDetail", "url", "goUserProfile", "goBack", "showMoreActions", "itemList", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAAsuB,CAAgB,ssBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCiF1vB;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAD;QACAE;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IAAA,CACA;IAEAC;MACA;MACA;MACA;QACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAb;UACAc;UACAC;UACAC;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACAC;UACAT;UACAU;QACA;MACA;QACA;QACAD;UACAT;UACAU;QACA;MACA;IACA;IAEAC;MACAC;IACA;IAEAC;MACAJ;QACAK;MACA;IACA;IAEAC;MACA;MACA;MAEAN;QACAK;MACA;IACA;IAEAE;MACAP;IACA;IAEAQ;MACAR;QACAS;QACAC;UACAP;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvLA;AAAA;AAAA;AAAA;AAAi5C,CAAgB,ixCAAG,EAAC,C;;;;;;;;;;;ACAr6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/user/profile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/user/profile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./profile.vue?vue&type=template&id=9e24a6bc&scoped=true&\"\nvar renderjs\nimport script from \"./profile.vue?vue&type=script&lang=js&\"\nexport * from \"./profile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./profile.vue?vue&type=style&index=0&id=9e24a6bc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9e24a6bc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/user/profile.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=template&id=9e24a6bc&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.userPosts.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"user-profile-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <u-icon name=\"arrow-left\" size=\"24\" color=\"#333\" @click=\"goBack\"></u-icon>\n        <text class=\"title\">用户主页</text>\n        <u-icon name=\"more-dot-fill\" size=\"24\" color=\"#333\" @click=\"showMoreActions\"></u-icon>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 用户信息 -->\n      <view class=\"user-section\">\n        <view class=\"user-info\">\n          <u-avatar :src=\"userInfo.avatar\" size=\"80\"></u-avatar>\n          <view class=\"user-details\">\n            <text class=\"nickname\">{{ userInfo.nickname }}</text>\n            <text class=\"user-id\">ID: {{ userInfo.userId }}</text>\n            <text class=\"bio\">{{ userInfo.bio || '这个人很懒，什么都没有留下...' }}</text>\n          </view>\n          <u-button \n            :type=\"userInfo.isFollowed ? 'default' : 'primary'\" \n            size=\"small\" \n            :text=\"userInfo.isFollowed ? '已关注' : '关注'\"\n            @click=\"toggleFollow\"\n          ></u-button>\n        </view>\n\n        <!-- 数据统计 -->\n        <view class=\"stats-section\">\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userInfo.postCount }}</text>\n            <text class=\"stat-label\">帖子</text>\n          </view>\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userInfo.followingCount }}</text>\n            <text class=\"stat-label\">关注</text>\n          </view>\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userInfo.followersCount }}</text>\n            <text class=\"stat-label\">粉丝</text>\n          </view>\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userInfo.likeCount }}</text>\n            <text class=\"stat-label\">获赞</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 用户帖子 -->\n      <view class=\"posts-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">TA的帖子</text>\n        </view>\n        \n        <view class=\"post-grid\">\n          <PostCard\n            v-for=\"post in userPosts\"\n            :key=\"post.id\"\n            :post=\"post\"\n            class=\"post-card-item\"\n            @click=\"goPostDetail\"\n            @user-click=\"goUserProfile\"\n            @like=\"onPostLike\"\n          />\n        </view>\n\n        <!-- 空状态 -->\n        <view v-if=\"!userPosts.length\" class=\"empty-state\">\n          <u-icon name=\"file-text\" color=\"#ccc\" size=\"60\"></u-icon>\n          <text class=\"empty-text\">暂无帖子</text>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport PostCard from '../components/PostCard.vue'\n\nexport default {\n  name: 'UserProfile',\n  components: {\n    PostCard\n  },\n  data() {\n    return {\n      userId: '',\n      userInfo: {\n        userId: '123456',\n        nickname: '用户昵称',\n        avatar: 'https://picsum.photos/100/100?random=999',\n        bio: '分享生活的美好瞬间 ✨',\n        postCount: 28,\n        followingCount: 156,\n        followersCount: 324,\n        likeCount: 1248,\n        isFollowed: false\n      },\n      userPosts: []\n    }\n  },\n  onLoad(options) {\n    this.userId = options.id\n    this.loadUserInfo()\n    this.loadUserPosts()\n  },\n  methods: {\n    loadUserInfo() {\n      // 模拟加载用户信息\n      // 这里应该调用API获取用户数据\n    },\n\n    loadUserPosts() {\n      // 模拟用户帖子数据\n      const posts = []\n      for (let i = 0; i < 6; i++) {\n        posts.push({\n          id: Date.now() + i,\n          title: `用户帖子 ${i + 1}`,\n          username: this.userInfo.nickname,\n          userAvatar: this.userInfo.avatar,\n          content: `这是用户发布的第${i + 1}条帖子`,\n          coverImage: `https://picsum.photos/300/400?random=${Date.now() + i}`,\n          likeCount: Math.floor(Math.random() * 500),\n          commentCount: Math.floor(Math.random() * 50),\n          isLiked: Math.random() > 0.7,\n          createTime: new Date(Date.now() - Math.random() * 86400000 * 30)\n        })\n      }\n      this.userPosts = posts\n    },\n\n    toggleFollow() {\n      this.userInfo.isFollowed = !this.userInfo.isFollowed\n      if (this.userInfo.isFollowed) {\n        this.userInfo.followersCount++\n        uni.showToast({\n          title: '关注成功',\n          icon: 'success'\n        })\n      } else {\n        this.userInfo.followersCount--\n        uni.showToast({\n          title: '取消关注',\n          icon: 'none'\n        })\n      }\n    },\n\n    onPostLike(post) {\n      console.log('点赞帖子:', post.id)\n    },\n\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    },\n\n    goUserProfile(post) {\n      // 如果是当前用户，不需要跳转\n      if (post.userId === this.userId) return\n      \n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${post.userId || post.id}`\n      })\n    },\n\n    goBack() {\n      uni.navigateBack()\n    },\n\n    showMoreActions() {\n      uni.showActionSheet({\n        itemList: ['举报用户', '拉黑用户'],\n        success: (res) => {\n          console.log('更多操作:', res.tapIndex)\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.user-profile-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 1px solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n}\n\n.title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.content {\n  margin-top: calc(44px + var(--status-bar-height));\n  padding: 16px;\n  width: auto;\n}\n\n.user-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.user-info {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 20px;\n}\n\n.user-details {\n  flex: 1;\n  margin-left: 16px;\n  margin-right: 12px;\n}\n\n.nickname {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.user-id {\n  font-size: 12px;\n  color: #999;\n  display: block;\n  margin-bottom: 8px;\n}\n\n.bio {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n  display: block;\n}\n\n.stats-section {\n  display: flex;\n  justify-content: space-around;\n  padding-top: 16px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-number {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #999;\n}\n\n.posts-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.section-header {\n  margin-bottom: 16px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.post-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.post-card-item {\n  width: calc(50% - 4px);\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 20px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n  margin-top: 12px;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&id=9e24a6bc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&id=9e24a6bc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752736466637\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}