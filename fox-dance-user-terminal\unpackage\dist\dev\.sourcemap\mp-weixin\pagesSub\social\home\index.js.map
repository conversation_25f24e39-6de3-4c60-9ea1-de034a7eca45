{"version": 3, "sources": [null, "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "uni-app:///pagesSub/social/home/<USER>", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "uni-app:///main.js"], "names": ["name", "components", "PostCard", "data", "postList", "loading", "refreshing", "page", "pageSize", "currentTopic", "currentTopicIndex", "topicList", "id", "hasMore", "onLoad", "methods", "loadHotTags", "hotTags", "allOption", "console", "loadPosts", "refresh", "params", "current", "size", "sortField", "sortOrder", "result", "posts", "title", "username", "userAvatar", "content", "coverImage", "images", "topics", "topicId", "likeCount", "commentCount", "isLiked", "createTime", "mockPosts", "generateMockPosts", "life", "titles", "food", "travel", "photography", "fitness", "fashion", "music", "movie", "reading", "availableTopics", "onRefresh", "loadMore", "formatTime", "onPostLike", "post", "index", "uni", "icon", "goPostDetail", "url", "goUserProfile", "goSearch", "selectTopic", "dateString", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACyExvB;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,YACA;QAAAX;QAAAY;MAAA,GACA;QAAAZ;QAAAY;MAAA,GACA;QAAAZ;QAAAY;MAAA,GACA;QAAAZ;QAAAY;MAAA,GACA;QAAAZ;QAAAY;MAAA,GACA;QAAAZ;QAAAY;MAAA,GACA;QAAAZ;QAAAY;MAAA,GACA;QAAAZ;QAAAY;MAAA,GACA;QAAAZ;QAAAY;MAAA,GACA;QAAAZ;QAAAY;MAAA,EACA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACAC;kBACA;oBAAA;sBACAlB;sBACAY;oBACA;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAO;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAEAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA;kBACAJ;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAK;gBAEA;kBACAC;oBAAA;sBACAhB;sBACAiB;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBAAA;kBAEA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;;kBAEA;kBACA;gBACA;kBACA;kBACAC;kBACA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAtB;gBACA;gBACAsB;gBACA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;MACA;QACAC;UAAAC;UAAA5C;QAAA;QACA6C;UAAAD;UAAA5C;QAAA;QACA8C;UAAAF;UAAA5C;QAAA;QACA+C;UAAAH;UAAA5C;QAAA;QACAgD;UAAAJ;UAAA5C;QAAA;QACAiD;UAAAL;UAAA5C;QAAA;QACAkD;UAAAN;UAAA5C;QAAA;QACAmD;UAAAP;UAAA5C;QAAA;QACAoD;UAAAR;UAAA5C;QAAA;MACA;;MAEA;MACA;MACA;QACAqD;MACA;MAEA;QACA;QACA;QACA;QACA;QAEAzB;UACAhB;UACAiB;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACA;IACA;IAEAc;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAA;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBACAA;gBACAA;cAAA;gBAGA;gBACAC;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAxC;gBACAyC;kBACA/B;kBACAgC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACAF;QACAG;MACA;IACA;IAEAC;MACAJ;QACAG;MACA;IACA;IAEAE;MACAL;QACAG;MACA;IACA;IAEAG;MACA;MAEA;MACA;MACA;MACA;;MAEA;MACA;IACA;EAAA,6EAGAC;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA;EACA,oFAEA;IAAA;MAAA;IACA;MAAA;IAAA;IACA;EACA,oFAEA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACzWA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pagesSub/social/home/<USER>", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=de45d8c2&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"de45d8c2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/home/<USER>\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=de45d8c2&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uSticky: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-sticky/u-sticky\" */ \"@/components/uview-ui/components/u-sticky/u-sticky.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.postList.length && !_vm.loading\n  var m0 = g0 ? _vm.getEmptyText() : null\n  var m1 = g0 ? _vm.getEmptyDesc() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"home-container\">\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <view class=\"logo\">\n          <text class=\"logo-text\">社区</text>\n        </view>\n        <view class=\"header-actions\">\n          <u-icon name=\"search\" size=\"24\" color=\"#666\" @click=\"goSearch\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 话题标签栏 -->\n    <view class=\"topic-tabs-container\">\n      <u-sticky bgColor=\"#fff\">\n        <u-tabs\n          :list=\"topicList\"\n          :current=\"currentTopicIndex\"\n          @change=\"selectTopic\"\n          :scrollable=\"true\"\n          activeColor=\"#2979ff\"\n          inactiveColor=\"#666\"\n          fontSize=\"28\"\n          lineColor=\"#2979ff\"\n          lineWidth=\"40\"\n          lineHeight=\"6\"\n          height=\"80\"\n          itemStyle=\"padding: 0 32rpx;\"\n        ></u-tabs>\n      </u-sticky>\n    </view>\n\n    <!-- 帖子网格列表 -->\n    <scroll-view\n      class=\"post-list\"\n      scroll-y\n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <view class=\"post-grid\">\n        <PostCard\n          v-for=\"post in postList\"\n          :key=\"post.id\"\n          :post=\"post\"\n          class=\"post-card-item\"\n          @click=\"goPostDetail\"\n          @user-click=\"goUserProfile\"\n          @like=\"onPostLike\"\n        />\n      </view>\n\n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"loading\">\n        <u-loading mode=\"circle\" size=\"24\"></u-loading>\n        <text class=\"load-text\">加载中...</text>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"!postList.length && !loading\" class=\"empty-state\">\n        <u-icon name=\"file-text\" color=\"#ccc\" size=\"120rpx\"></u-icon>\n        <text class=\"empty-text\">{{ getEmptyText() }}</text>\n        <text class=\"empty-desc\">{{ getEmptyDesc() }}</text>\n      </view>\n    </scroll-view>\n\n  </view>\n</template>\n\n<script>\nimport PostCard from '../components/PostCard.vue'\nimport { getPostList, getHotTags, likePost, unlikePost } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'SocialHome',\n  components: {\n    PostCard\n  },\n  data() {\n    return {\n      postList: [],\n      loading: false,\n      refreshing: false,\n      page: 1,\n      pageSize: 10,\n      currentTopic: 'all',\n      currentTopicIndex: 0,\n      topicList: [\n        { name: '全部', id: 'all' },\n        { name: '街舞', id: 'street-dance' },\n        { name: '现代舞', id: 'modern-dance' },\n        { name: '芭蕾', id: 'ballet' },\n        { name: '拉丁舞', id: 'latin-dance' },\n        { name: '爵士舞', id: 'jazz-dance' },\n        { name: '民族舞', id: 'folk-dance' },\n        { name: '古典舞', id: 'classical-dance' },\n        { name: '舞蹈教学', id: 'dance-teaching' },\n        { name: '舞蹈比赛', id: 'dance-competition' }\n      ],\n      hasMore: true\n    }\n  },\n  onLoad() {\n    this.loadHotTags()\n    this.loadPosts()\n  },\n  methods: {\n    // 加载热门话题\n    async loadHotTags() {\n      try {\n        const hotTags = await getHotTags(10)\n        if (hotTags && hotTags.length > 0) {\n          // 保留\"全部\"选项，添加热门话题\n          const allOption = this.topicList[0]\n          this.topicList = [allOption, ...hotTags.map(tag => ({\n            name: tag.tagName || tag.name,\n            id: tag.tagId || tag.id\n          }))]\n        }\n      } catch (error) {\n        console.error('加载热门话题失败:', error)\n        // 使用默认话题列表\n      }\n    },\n\n    async loadPosts(refresh = false) {\n      if (this.loading) return\n\n      this.loading = true\n      try {\n        const params = {\n          current: refresh ? 1 : this.page,\n          size: this.pageSize,\n          sortField: 'createTime',\n          sortOrder: 'desc'\n        }\n\n        // 如果选择了特定话题，添加话题筛选\n        if (this.currentTopic !== 'all') {\n          params.tagId = this.currentTopic\n        }\n\n        const result = await getPostList(params)\n\n        if (result && result.records) {\n          const posts = result.records.map(post => ({\n            id: post.id,\n            title: post.title || '',\n            username: post.nickname || '用户' + post.userId,\n            userAvatar: post.avatar || 'https://picsum.photos/100/100?random=' + post.userId,\n            content: post.content,\n            coverImage: (post.images && post.images[0]) || 'https://picsum.photos/300/400?random=' + post.id,\n            images: post.images || [],\n            topics: post.tags || [],\n            topicId: this.currentTopic,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            isLiked: post.isLiked || false,\n            createTime: new Date(post.createTime)\n          }))\n\n          if (refresh) {\n            this.postList = posts\n            this.page = 2\n          } else {\n            this.postList = [...this.postList, ...posts]\n            this.page++\n          }\n\n          // 检查是否还有更多数据\n          this.hasMore = result.records.length >= this.pageSize\n        } else {\n          // 如果API调用失败，使用模拟数据\n          const mockPosts = this.generateMockPosts()\n          if (refresh) {\n            this.postList = mockPosts\n            this.page = 2\n          } else {\n            this.postList = [...this.postList, ...mockPosts]\n            this.page++\n          }\n        }\n\n      } catch (error) {\n        console.error('加载帖子失败:', error)\n        // 使用模拟数据作为后备\n        const mockPosts = this.generateMockPosts()\n        if (refresh) {\n          this.postList = mockPosts\n          this.page = 2\n        } else {\n          this.postList = [...this.postList, ...mockPosts]\n          this.page++\n        }\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    generateMockPosts() {\n      const posts = []\n      const topicData = {\n        life: { titles: ['今日穿搭分享', '生活小确幸', '宠物日常', '家居装饰'], name: '生活' },\n        food: { titles: ['美食探店记录', '美妆教程', '手工制作'], name: '美食' },\n        travel: { titles: ['旅行日记', '风景随拍'], name: '旅行' },\n        photography: { titles: ['摄影作品', '风景随拍'], name: '摄影' },\n        fitness: { titles: ['健身打卡'], name: '健身' },\n        fashion: { titles: ['今日穿搭分享', '美妆教程'], name: '时尚' },\n        music: { titles: ['音乐推荐'], name: '音乐' },\n        movie: { titles: ['电影观后感'], name: '电影' },\n        reading: { titles: ['读书笔记', '学习心得'], name: '读书' }\n      }\n\n      // 如果选择了特定话题，只生成该话题的帖子\n      let availableTopics = Object.keys(topicData)\n      if (this.currentTopic !== 'all') {\n        availableTopics = [this.currentTopic]\n      }\n\n      for (let i = 0; i < this.pageSize; i++) {\n        const randomTopicKey = availableTopics[Math.floor(Math.random() * availableTopics.length)]\n        const topicInfo = topicData[randomTopicKey]\n        const randomTitle = topicInfo.titles[Math.floor(Math.random() * topicInfo.titles.length)]\n        const randomId = Math.floor(Math.random() * 1000)\n\n        posts.push({\n          id: Date.now() + i,\n          title: randomTitle,\n          username: `用户${randomId}`,\n          userAvatar: `https://picsum.photos/100/100?random=${randomId}`,\n          content: `${randomTitle} - 分享我的生活点滴，希望大家喜欢！`,\n          coverImage: `https://picsum.photos/300/400?random=${Date.now() + i}`,\n          images: [`https://picsum.photos/300/400?random=${Date.now() + i}`],\n          topics: [topicInfo.name],\n          topicId: randomTopicKey,\n          likeCount: Math.floor(Math.random() * 2000),\n          commentCount: Math.floor(Math.random() * 100),\n          isLiked: Math.random() > 0.7,\n          createTime: new Date(Date.now() - Math.random() * 86400000 * 7)\n        })\n      }\n      return posts\n    },\n\n    onRefresh() {\n      this.refreshing = true\n      this.page = 1\n      this.postList = []\n      this.loadPosts(true)\n    },\n\n    loadMore() {\n      if (!this.loading) {\n        this.page++\n        this.loadPosts()\n      }\n    },\n\n    formatTime(time) {\n      const now = new Date()\n      const diff = now - new Date(time)\n      const minutes = Math.floor(diff / 60000)\n      const hours = Math.floor(diff / 3600000)\n      const days = Math.floor(diff / 86400000)\n      \n      if (minutes < 60) return `${minutes}分钟前`\n      if (hours < 24) return `${hours}小时前`\n      return `${days}天前`\n    },\n\n    async onPostLike(post) {\n      try {\n        if (post.isLiked) {\n          // 取消点赞\n          await unlikePost(post.id)\n          post.isLiked = false\n          post.likeCount = Math.max(0, post.likeCount - 1)\n        } else {\n          // 点赞\n          await likePost(post.id)\n          post.isLiked = true\n          post.likeCount += 1\n        }\n\n        // 更新帖子列表中的数据\n        const index = this.postList.findIndex(p => p.id === post.id)\n        if (index !== -1) {\n          this.$set(this.postList, index, { ...post })\n        }\n\n      } catch (error) {\n        console.error('点赞操作失败:', error)\n        uni.showToast({\n          title: '操作失败',\n          icon: 'none'\n        })\n      }\n    },\n\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      })\n    },\n\n    goUserProfile(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${post.userId || post.id}`\n      })\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: '/pagesSub/social/search/index'\n      })\n    },\n\n    selectTopic(index) {\n      if (this.currentTopicIndex === index) return\n\n      this.currentTopicIndex = index\n      this.currentTopic = this.topicList[index].id\n      this.page = 1\n      this.postList = []\n\n      // 重新加载帖子\n      this.loadPosts(true)\n    },\n\n    // 格式化时间\n    formatTime(dateString) {\n      if (!dateString) return ''\n\n      const date = new Date(dateString)\n      const now = new Date()\n      const diff = now - date\n\n      const minutes = Math.floor(diff / (1000 * 60))\n      const hours = Math.floor(diff / (1000 * 60 * 60))\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\n\n      if (minutes < 1) return '刚刚'\n      if (minutes < 60) return `${minutes}分钟前`\n      if (hours < 24) return `${hours}小时前`\n      if (days < 7) return `${days}天前`\n\n      return date.toLocaleDateString()\n    },\n\n    getEmptyText() {\n      const currentTopicName = this.topicList.find(topic => topic.id === this.currentTopic)?.name || '全部'\n      return this.currentTopic === 'all' ? '暂无帖子' : `暂无${currentTopicName}相关帖子`\n    },\n\n    getEmptyDesc() {\n      return this.currentTopic === 'all' ? '快来发布第一条帖子吧' : '换个话题看看其他内容吧'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.home-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 100px;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 2rpx solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 88rpx;\n\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 32rpx;\n\n}\n\n.logo-text {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #2979ff;\n}\n\n.topic-tabs-container {\n  position: fixed;\n  top: calc(88rpx + var(--status-bar-height));\n  left: 0;\n  right: 0;\n  z-index: 99;\n  background: #fff;\n  border-bottom: 2rpx solid #f0f0f0;\n}\n\n/* uview tabs组件样式优化 */\n.topic-tabs-container ::v-deep .u-tabs {\n  background: #fff;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item {\n  padding: 0 32rpx !important;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item__text {\n  font-size: 28rpx !important;\n  font-weight: 500;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__line {\n  border-radius: 6rpx;\n}\n\n.post-list {\n  margin-top: calc(168rpx + var(--status-bar-height));\n  margin: 230rpx 26rpx;\n  width: auto;\n}\n\n.post-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 14rpx;\n  padding-bottom: 40rpx;\n}\n\n.post-card-item {\n  width: calc(50% - 8rpx);\n  margin-bottom: 16rpx;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 160rpx 40rpx;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #999;\n  margin: 32rpx 0 16rpx;\n}\n\n.empty-desc {\n  font-size: 28rpx;\n  color: #ccc;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.load-text {\n  margin-left: 16rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 375px) {\n  .post-list {\n    padding: 12rpx;\n  }\n\n  .post-grid {\n    gap: 12rpx;\n  }\n\n  .post-card-item {\n    width: calc(50% - 6rpx);\n  }\n}\n\n@media screen and (min-width: 768px) {\n  .post-grid {\n    gap: 24rpx;\n  }\n\n  .post-card-item {\n    width: calc(33.33% - 16rpx);\n  }\n}\n\n@media screen and (min-width: 1024px) {\n  .post-list {\n    padding: 32rpx 64rpx;\n  }\n\n  .post-card-item {\n    width: calc(25% - 18rpx);\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752819717896\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/home/<USER>'\ncreatePage(Page)"], "sourceRoot": ""}