<template>
  <view class="chat-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <view class="header-left" @click="goBack">
          <u-avatar
            :src="otherUserAvatar"
            size="50"
            class="header-avatar"
          ></u-avatar>
          <view class="chat-info">
            <view class="name-container">
              <text class="chat-name">{{ chatName }}</text>
              <view v-if="isOnline" class="online-indicator"></view>
            </view>
          </view>
        </view>
        <view class="header-actions">
          <u-icon name="phone" size="24" color="#333" @click="makeCall"></u-icon>
          <u-icon name="more-dot-fill" size="24" color="#333" @click="showMoreActions"></u-icon>
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <scroll-view 
      class="message-list" 
      scroll-y 
      :scroll-top="scrollTop"
      @scrolltoupper="loadMoreMessages"
    >
      <view class="message-item" v-for="message in messageList" :key="message.id">
        <!-- 时间分割线 -->
        <view v-if="message.showTime" class="time-divider">
          <text class="time-text">{{ formatMessageTime(message.timestamp) }}</text>
        </view>

        <!-- 消息内容 -->
        <view class="message-wrapper" :class="{ 'is-mine': message.isMine }">
          <!-- 对方消息：左边头像，右边内容 -->
          <template v-if="!message.isMine">
            <u-avatar
              :src="message.avatar"
              size="64"
              class="message-avatar"
            ></u-avatar>

            <view class="message-content">
              <!-- 文字消息 -->
              <view
                v-if="message.type === 'text'"
                class="message-bubble text-bubble"
              >
                <text class="message-text">{{ message.content }}</text>
              </view>

              <!-- 图片消息 -->
              <view
                v-else-if="message.type === 'image'"
                class="message-bubble image-bubble"
                @click="previewImage(message.content)"
              >
                <image :src="message.content" class="message-image" mode="aspectFill" />
              </view>

              <!-- 语音消息 -->
              <view
                v-else-if="message.type === 'voice'"
                class="message-bubble voice-bubble"
                @click="playVoice(message)"
              >
                <u-icon name="volume-fill" size="16" color="#fff"></u-icon>
                <text class="voice-duration">{{ message.duration }}''</text>
                <view v-if="message.isPlaying" class="voice-animation">
                  <view class="wave"></view>
                  <view class="wave"></view>
                  <view class="wave"></view>
                </view>
              </view>
            </view>
          </template>

          <!-- 我的消息：左边内容，右边头像 -->
          <template v-else>
            <view class="message-content">
              <!-- 文字消息 -->
              <view
                v-if="message.type === 'text'"
                class="message-bubble text-bubble mine"
              >
                <text class="message-text">{{ message.content }}</text>
              </view>

              <!-- 图片消息 -->
              <view
                v-else-if="message.type === 'image'"
                class="message-bubble image-bubble"
                @click="previewImage(message.content)"
              >
                <image :src="message.content" class="message-image" mode="aspectFill" />
              </view>

              <!-- 语音消息 -->
              <view
                v-else-if="message.type === 'voice'"
                class="message-bubble voice-bubble mine"
                @click="playVoice(message)"
              >
                <u-icon name="volume-fill" size="16" color="#fff"></u-icon>
                <text class="voice-duration">{{ message.duration }}''</text>
                <view v-if="message.isPlaying" class="voice-animation">
                  <view class="wave"></view>
                  <view class="wave"></view>
                  <view class="wave"></view>
                </view>
              </view>

              <!-- 消息状态 -->
              <view class="message-status">
                <u-icon
                  v-if="message.status === 'sending'"
                  name="loading"
                  size="12"
                  color="#999"
                ></u-icon>
                <u-icon
                  v-else-if="message.status === 'sent'"
                  name="checkmark"
                  size="12"
                  color="#999"
                ></u-icon>
                <u-icon
                  v-else-if="message.status === 'read'"
                  name="checkmark-done"
                  size="12"
                  color="#2979ff"
                ></u-icon>
                <u-icon
                  v-else-if="message.status === 'failed'"
                  name="close-circle"
                  size="12"
                  color="#ff4757"
                  @click="resendMessage(message)"
                ></u-icon>
              </view>
            </view>

          </template>
        </view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area">
      <!-- 扩展功能面板 -->
      <view v-if="showExtensions" class="extensions-panel">
        <view class="extension-grid">
          <view class="extension-item" @click="chooseImage">
            <view class="extension-icon photo">
              <u-icon name="camera" color="#fff" size="24"></u-icon>
            </view>
            <text class="extension-text">照片</text>
          </view>
          
          <view class="extension-item" @click="startVoiceRecord">
            <view class="extension-icon voice">
              <u-icon name="mic" color="#fff" size="24"></u-icon>
            </view>
            <text class="extension-text">语音</text>
          </view>
          
          <view class="extension-item" @click="chooseLocation">
            <view class="extension-icon location">
              <u-icon name="map-pin" color="#fff" size="24"></u-icon>
            </view>
            <text class="extension-text">位置</text>
          </view>
          
          <view class="extension-item" @click="chooseFile">
            <view class="extension-icon file">
              <u-icon name="folder" color="#fff" size="24"></u-icon>
            </view>
            <text class="extension-text">文件</text>
          </view>
        </view>
      </view>

      <!-- 表情面板 -->
      <view v-if="showEmojis" class="emoji-panel">
        <scroll-view class="emoji-scroll" scroll-y>
          <view class="emoji-grid">
            <text 
              v-for="emoji in emojiList" 
              :key="emoji"
              class="emoji-item"
              @click="insertEmoji(emoji)"
            >
              {{ emoji }}
            </text>
          </view>
        </scroll-view>
      </view>

      <!-- 输入栏 -->
      <view class="input-bar">
        <u-icon 
          name="mic" 
          size="24" 
          color="#666" 
          @click="toggleVoiceMode"
          v-if="!inputText && !voiceMode"
        ></u-icon>
        
        <view class="input-wrapper" v-if="!voiceMode">
          <u-input
            v-model="inputText"
            type="textarea"
            placeholder="输入消息..."
            :auto-height="true"
            :show-confirm-bar="false"
            :clearable="false"
            :border="false"
            :custom-style="inputCustomStyle"
            :placeholder-style="placeholderStyle"
            :maxlength="500"
            :cursor-spacing="10"
            :adjust-position="true"
            @focus="onInputFocus"
            @blur="onInputBlur"
            @input="onInputChange"
          />
        </view>

        <!-- 语音录制按钮 -->
        <view 
          v-else
          class="voice-record-btn"
          :class="{ recording: isRecording }"
          @touchstart="startRecord"
          @touchend="stopRecord"
          @touchcancel="cancelRecord"
        >
          <text class="record-text">
            {{ isRecording ? '松开发送' : '按住说话' }}
          </text>
        </view>

        <view class="input-actions">
          <u-icon 
            name="emoji" 
            size="24" 
            :color="showEmojis ? '#2979ff' : '#666'"
            @click="toggleEmojis"
          ></u-icon>
          
          <u-icon 
            v-if="!inputText && !voiceMode" 
            name="plus" 
            size="24" 
            :color="showExtensions ? '#2979ff' : '#666'"
            @click="toggleExtensions"
          ></u-icon>
          
          <view 
            v-if="inputText || voiceMode"
            class="send-btn"
            @click="sendMessage"
          >
            <u-icon name="send" color="#fff" size="18"></u-icon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getConversationMessages, sendMessage, markMessageAsRead } from '@/utils/socialApi.js'

export default {
  name: 'ChatDetail',
  data() {
    return {
      chatId: '',
      chatName: '',
      otherUserAvatar: 'https://picsum.photos/100/100?random=800',
      isOnline: true,
      messageList: [],
      inputText: '',
      scrollTop: 0,
      showExtensions: false,
      showEmojis: false,
      voiceMode: false,
      isRecording: false,
      currentUser: {
        avatar: 'https://picsum.photos/100/100?random=999'
      },
      emojiList: [
        '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
        '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
        '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
        '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏'
      ],
      // uview输入框自定义样式
      inputCustomStyle: {
        backgroundColor: 'transparent',
        fontSize: '32rpx',
        lineHeight: '1.4',
        minHeight: '40rpx',
        maxHeight: '200rpx',
        padding: '0',
        color: '#333'
      },
      // placeholder样式
      placeholderStyle: 'color: #999; font-size: 32rpx;'
    }
  },
  onLoad(options) {
    this.chatId = options.id
    this.chatName = options.name || '聊天'
    this.loadMessages()
  },
  methods: {
    async loadMessages() {
      try {
        const result = await getConversationMessages(this.chatId, {
          current: 1,
          size: 50
        })

        if (result && result.length > 0) {
          this.messageList = result.map((message, index) => {
            const prevMessage = result[index - 1]
            const showTime = !prevMessage ||
              (new Date(message.createTime) - new Date(prevMessage.createTime)) > 300000 // 5分钟

            return {
              id: message.id,
              type: this.getMessageTypeString(message.messageType),
              content: message.content,
              isMine: message.senderId === this.getCurrentUserId(),
              avatar: message.senderAvatar || this.otherUserAvatar,
              timestamp: new Date(message.createTime),
              showTime: showTime,
              status: message.isRead === 1 ? 'read' : 'sent'
            }
          }).reverse() // 消息按时间正序显示

          // 标记消息已读
          await this.markAllMessagesAsRead()

        } else {
          // 使用模拟数据作为后备
          this.messageList = this.generateMockMessages()
        }
      } catch (error) {
        console.error('加载消息失败:', error)
        // 使用模拟数据作为后备
        this.messageList = this.generateMockMessages()
      }

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 生成模拟消息数据
    generateMockMessages() {
      return [
        {
          id: 1,
          type: 'text',
          content: '你好！',
          isMine: false,
          avatar: 'https://picsum.photos/100/100?random=800',
          timestamp: new Date(Date.now() - 3600000),
          showTime: true,
          status: 'read'
        },
        {
          id: 2,
          type: 'text',
          content: '你好，很高兴认识你！',
          isMine: true,
          timestamp: new Date(Date.now() - 3500000),
          showTime: false,
          status: 'read'
        },
        {
          id: 3,
          type: 'image',
          content: 'https://picsum.photos/200/200?random=801',
          isMine: false,
          avatar: 'https://picsum.photos/100/100?random=800',
          timestamp: new Date(Date.now() - 1800000),
          showTime: true,
          status: 'read'
        },
        {
          id: 4,
          type: 'voice',
          content: '',
          duration: 3,
          isMine: true,
          timestamp: new Date(Date.now() - 300000),
          showTime: false,
          status: 'read',
          isPlaying: false
        }
      ]
      
      this.scrollToBottom()
    },

    // 获取当前用户ID
    getCurrentUserId() {
      // TODO: 从用户状态或本地存储获取当前用户ID
      return 1
    },

    // 将消息类型数字转换为字符串
    getMessageTypeString(messageType) {
      const typeMap = {
        1: 'text',
        2: 'image',
        3: 'voice',
        4: 'video'
      }
      return typeMap[messageType] || 'text'
    },

    // 标记所有消息已读
    async markAllMessagesAsRead() {
      try {
        await markMessageAsRead({
          userId: this.chatId,
          conversationId: this.chatId
        })
      } catch (error) {
        console.error('标记消息已读失败:', error)
      }
    },

    formatMessageTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 86400000) { // 今天
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      }
    },

    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = 999999
      })
    },

    async sendMessage() {
      if (!this.inputText.trim()) return

      const messageContent = this.inputText.trim()
      const tempId = Date.now()

      const message = {
        id: tempId,
        type: 'text',
        content: messageContent,
        isMine: true,
        timestamp: new Date(),
        showTime: false,
        status: 'sending'
      }

      this.messageList.push(message)
      this.inputText = ''
      this.scrollToBottom()

      try {
        // 调用发送消息API
        const result = await sendMessage({
          receiverId: this.chatId,
          messageType: 'text',
          content: messageContent
        })

        if (result) {
          // 更新消息状态和ID
          message.id = result.id || tempId
          message.status = 'sent'

          // 模拟已读状态
          setTimeout(() => {
            message.status = 'read'
          }, 1000)
        } else {
          message.status = 'failed'
        }

      } catch (error) {
        console.error('发送消息失败:', error)
        message.status = 'failed'
        uni.showToast({
          title: '发送失败',
          icon: 'none'
        })
      }
    },

    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const message = {
            id: Date.now(),
            type: 'image',
            content: res.tempFilePaths[0],
            isMine: true,
            timestamp: new Date(),
            showTime: false,
            status: 'sending'
          }
          
          this.messageList.push(message)
          this.showExtensions = false
          this.scrollToBottom()
          
          // 模拟上传成功
          setTimeout(() => {
            message.status = 'sent'
          }, 1000)
        }
      })
    },

    previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      })
    },

    startVoiceRecord() {
      this.voiceMode = true
      this.showExtensions = false
    },

    toggleVoiceMode() {
      this.voiceMode = !this.voiceMode
    },

    startRecord() {
      this.isRecording = true
      // 开始录音逻辑
    },

    stopRecord() {
      if (!this.isRecording) return
      
      this.isRecording = false
      
      // 模拟语音消息
      const message = {
        id: Date.now(),
        type: 'voice',
        content: '',
        duration: Math.floor(Math.random() * 10) + 1,
        isMine: true,
        timestamp: new Date(),
        showTime: false,
        status: 'sending',
        isPlaying: false
      }
      
      this.messageList.push(message)
      this.scrollToBottom()
      
      setTimeout(() => {
        message.status = 'sent'
      }, 500)
    },

    cancelRecord() {
      this.isRecording = false
    },

    playVoice(message) {
      // 停止其他语音播放
      this.messageList.forEach(msg => {
        if (msg.type === 'voice') {
          msg.isPlaying = false
        }
      })
      
      message.isPlaying = true
      
      // 模拟播放完成
      setTimeout(() => {
        message.isPlaying = false
      }, message.duration * 1000)
    },

    toggleEmojis() {
      this.showEmojis = !this.showEmojis
      this.showExtensions = false
    },

    toggleExtensions() {
      this.showExtensions = !this.showExtensions
      this.showEmojis = false
    },

    insertEmoji(emoji) {
      this.inputText += emoji
    },

    onInputFocus() {
      this.showEmojis = false
      this.showExtensions = false
      // 滚动到底部，确保输入框可见
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    onInputBlur() {
      // 输入框失焦时的处理
      // 可以在这里添加自动保存草稿等功能
    },

    onInputChange(value) {
      // 处理输入内容变化
      this.inputText = value
      // 如果需要，可以在这里添加输入实时验证或字数统计
    },

    goBack() {
      uni.navigateBack()
    },

    makeCall() {
      uni.makePhoneCall({
        phoneNumber: '10086'
      })
    },

    showMoreActions() {
      uni.showActionSheet({
        itemList: ['查看资料', '清空聊天记录', '举报'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.viewUserProfile()
              break
            case 1:
              this.clearChatHistory()
              break
            case 2:
              this.reportUser()
              break
          }
        }
      })
    },

    // 查看用户资料
    viewUserProfile() {
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?userId=${this.chatId}&name=${this.chatName}`
      })
    },

    // 清空聊天记录
    clearChatHistory() {
      uni.showModal({
        title: '清空聊天记录',
        content: '确定要清空与该用户的所有聊天记录吗？此操作不可恢复。',
        confirmText: '清空',
        confirmColor: '#ff4757',
        success: (res) => {
          if (res.confirm) {
            // 清空消息列表
            this.messageList = []

            // 显示成功提示
            uni.showToast({
              title: '聊天记录已清空',
              icon: 'success',
              duration: 2000
            })

            // 这里可以调用后端API清空服务器端的聊天记录
            this.clearChatHistoryFromServer()
          }
        }
      })
    },

    // 从服务器清空聊天记录
    clearChatHistoryFromServer() {
      // TODO: 调用后端API清空聊天记录
      // 示例API调用
      /*
      uni.request({
        url: '/api/chat/clear',
        method: 'POST',
        data: {
          chatId: this.chatId
        },
        success: (res) => {
          console.log('服务器聊天记录已清空')
        },
        fail: (err) => {
          console.error('清空聊天记录失败:', err)
          uni.showToast({
            title: '清空失败，请重试',
            icon: 'error'
          })
        }
      })
      */
    },

    // 举报用户
    reportUser() {
      const reportReasons = [
        '发送垃圾信息',
        '发送不当内容',
        '骚扰他人',
        '诈骗行为',
        '其他违规行为'
      ]

      uni.showActionSheet({
        itemList: reportReasons,
        success: (res) => {
          const reason = reportReasons[res.tapIndex]
          this.confirmReport(reason)
        }
      })
    },

    // 确认举报
    confirmReport(reason) {
      uni.showModal({
        title: '举报用户',
        content: `确定要举报该用户"${reason}"吗？我们会认真处理您的举报。`,
        confirmText: '举报',
        confirmColor: '#ff4757',
        success: (res) => {
          if (res.confirm) {
            // 提交举报
            this.submitReport(reason)
          }
        }
      })
    },

    // 提交举报到服务器
    submitReport(reason) {
      // 显示加载提示
      uni.showLoading({
        title: '提交中...'
      })

      // TODO: 调用后端API提交举报
      // 模拟API调用
      setTimeout(() => {
        uni.hideLoading()

        uni.showToast({
          title: '举报已提交',
          icon: 'success',
          duration: 2000
        })

        // 实际API调用示例
        /*
        uni.request({
          url: '/api/report/submit',
          method: 'POST',
          data: {
            reportedUserId: this.chatId,
            reportedUserName: this.chatName,
            reason: reason,
            reportType: 'chat'
          },
          success: (res) => {
            uni.hideLoading()
            uni.showToast({
              title: '举报已提交',
              icon: 'success'
            })
          },
          fail: (err) => {
            uni.hideLoading()
            console.error('举报提交失败:', err)
            uni.showToast({
              title: '提交失败，请重试',
              icon: 'error'
            })
          }
        })
        */
      }, 1000)
    },

    loadMoreMessages() {
      // 加载更多历史消息
    },

    resendMessage(message) {
      message.status = 'sending'
      setTimeout(() => {
        message.status = 'sent'
      }, 500)
    },

    chooseLocation() {
      this.showExtensions = false
      this.$u.toast('位置功能开发中')
    },

    chooseFile() {
      this.showExtensions = false
      this.$u.toast('文件功能开发中')
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-container {
  height: 99vh;
  display: flex;
  flex-direction: column;
  background: #f0f0f0;
}

.header {
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.header-left {
  display: flex;
  align-items: center;
}

.chat-info {
  margin-left: 24rpx;
}

.name-container {
  position: relative;
  display: inline-block;
}

.chat-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  padding-right: 20rpx;
}

.online-indicator {
  position: absolute;
  top: 25rpx;
  right: 0;
  width: 16rpx;
  height: 16rpx;
  background: #52c41a;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
  animation: online-pulse 2s infinite;
}

@keyframes online-pulse {
  0% {
    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
  }
  50% {
    box-shadow: 0 0 0 6rpx rgba(82, 196, 26, 0.1);
  }
  100% {
    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
  }
}

.header-actions {
  display: flex;
  gap: 32rpx;
}

.message-list {
  flex: 1;
  padding: 0 32rpx 32rpx 32rpx;
  width: 92%;
}

.message-item {
  margin-bottom: 32rpx;
  margin-top: 32rpx;
}

.time-divider {
  text-align: center;
  margin-bottom: 32rpx;
}

.time-text {
  font-size: 24rpx;
  color: #999;
  background: rgba(0, 0, 0, 0.1);
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
}

.message-wrapper {
  display: flex;
  align-items: flex-end;
}

.message-wrapper.is-mine {
  justify-content: flex-end;
}

.message-avatar {
  margin: 0 16rpx;
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

/* 对方消息：内容左对齐 */
.message-wrapper:not(.is-mine) .message-content {
  align-items: flex-start;
}

/* 我的消息：内容右对齐 */
.message-wrapper.is-mine .message-content {
  align-items: flex-end;
}

.message-bubble {
  padding: 24rpx 32rpx;
  border-radius: 36rpx;
  margin-bottom: 8rpx;
}

.text-bubble {
  background: #fff;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.text-bubble.mine {
  background: #2979ff;
}

.message-text {
  font-size: 32rpx;
  line-height: 1.4;
  color: #333;
}

.mine .message-text {
  color: #fff;
}

.image-bubble {
  padding: 0;
  overflow: hidden;
  background: transparent;
}

.message-image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 24rpx;
}

.voice-bubble {
  background: #2979ff;
  display: flex;
  align-items: center;
  min-width: 160rpx;
  position: relative;
}

.voice-duration {
  color: #fff;
  font-size: 28rpx;
  margin-left: 16rpx;
}

.voice-animation {
  display: flex;
  gap: 4rpx;
  margin-left: 16rpx;
}

.wave {
  width: 4rpx;
  height: 24rpx;
  background: #fff;
  animation: wave 1s infinite;
}

.wave:nth-child(2) {
  animation-delay: 0.1s;
}

.wave:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes wave {
  0%, 100% { height: 8rpx; }
  50% { height: 24rpx; }
}

.message-status {
  margin-top: 8rpx;
}

.input-area {
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
}

.extensions-panel, .emoji-panel {
  height: 400rpx;
  border-bottom: 2rpx solid #e4e7ed;
}

.extension-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 40rpx;
  gap: 40rpx;
}

.extension-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(25% - 30rpx);
}

.extension-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.extension-icon.photo {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.extension-icon.voice {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.extension-icon.location {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.extension-icon.file {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.extension-text {
  font-size: 24rpx;
  color: #666;
}

.emoji-scroll {
  height: 100%;
  padding: 32rpx;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.emoji-item {
  font-size: 48rpx;
  padding: 16rpx;
  text-align: center;
  width: 80rpx;
  height: 80rpx;
  line-height: 48rpx;
}

.input-bar {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  gap: 24rpx;
  min-height: 104rpx;
  /* 确保在键盘弹起时的兼容性 */
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.input-wrapper {
  flex: 1;
  background: #f5f5f5;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  min-height: 72rpx;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.input-wrapper:focus-within {
  background: #f0f0f0;
  box-shadow: 0 0 0 2rpx rgba(41, 121, 255, 0.1);
}

/* 重置uview输入框样式 */
.input-wrapper /deep/ .u-input {
  flex: 1;
  min-height: auto;
}

.input-wrapper /deep/ .u-input__input,
.input-wrapper /deep/ .u-input__textarea {
  font-size: 32rpx !important;
  color: #333 !important;
  line-height: 1.4 !important;
  padding: 0 !important;
  background: transparent !important;
  min-height: 40rpx !important;
}

.input-wrapper /deep/ .u-input__textarea {
  max-height: 200rpx !important;
  overflow-y: auto;
  resize: none;
  /* 防止在某些设备上出现滚动条样式问题 */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.input-wrapper /deep/ .u-input__textarea::-webkit-scrollbar {
  display: none;
}

/* 确保placeholder样式一致 */
.input-wrapper /deep/ .u-input__textarea::placeholder,
.input-wrapper /deep/ .u-input__input::placeholder {
  color: #999 !important;
  font-size: 32rpx !important;
  opacity: 1;
}

/* 微信小程序特殊样式适配 */
/* #ifdef MP-WEIXIN */
.input-wrapper /deep/ .u-input__textarea {
  word-break: break-all;
  white-space: pre-wrap;
}
/* #endif */

/* 防止输入时的布局跳动 */
.input-wrapper {
  will-change: height;
  contain: layout style;
}

.voice-record-btn {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-record-btn.recording {
  background: #ff4757;
}

.record-text {
  font-size: 32rpx;
  color: #333;
}

.recording .record-text {
  color: #fff;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.send-btn {
  width: 72rpx;
  height: 72rpx;
  background: #2979ff;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
