package com.yupi.springbootinit.model.dto.search;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 搜索请求
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@ApiModel(description = "搜索请求")
public class SearchRequest implements Serializable {

    /**
     * 搜索关键词
     */
    @ApiModelProperty(value = "搜索关键词", example = "街舞")
    private String keyword;

    /**
     * 搜索类型（all-全部, post-帖子, user-用户, tag-话题）
     */
    @ApiModelProperty(value = "搜索类型", example = "all")
    private String type;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer current;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer size;

    /**
     * 排序方式（relevance-相关性, time-时间, hot-热度）
     */
    @ApiModelProperty(value = "排序方式", example = "relevance")
    private String sortBy;

    /**
     * 用户ID（用于个性化搜索）
     */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    private static final long serialVersionUID = 1L;

    // 手动添加getter/setter方法
    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getCurrent() {
        return current;
    }

    public void setCurrent(Integer current) {
        this.current = current;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
