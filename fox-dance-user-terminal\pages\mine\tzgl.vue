<template>
	<view class="tzgl">
		<view class="tzglOne">
			<view class="tzglOne_t" @click="navTo('/pages/mine/tzglxq')">
				<view class="tzglOne_t_l">小程序消息管理</view>
				<view class="tzglOne_t_r">详细设置<image src="/static/images/right_more.png"></image></view>
			</view>
		</view>
		 
		<view class="tzglTwo">
			<view class="tzglOne_t">
				<view class="tzglOne_t_l">公众号消息设置</view>
				<view class="tzglOne_t_r" v-if="glInfo.bind_status == 0"><image src="/static/images/icon88.png"></image>未绑定</view>
				<view class="tzglOne_t_r" style="color:#131315"  v-if="glInfo.bind_status == 1"><image src="/static/images/icon88-1.png"></image>已绑定</view>
			</view>
			<view class="tzglTwo_b">
				<view @click="wemTap"><image src="/static/images/icon89.png"></image>{{glInfo.bind_status == 0 ? '绑定微信公众号' : glInfo.bind_status == 1 ? '解绑微信公众号' : ''}}</view>
				<view @click="xxtsTap"><image src="/static/images/icon90.png"></image>消息推送测试</view>
			</view>
		</view>
		
		<!-- 绑定二维码 go -->
		<view class="bdewmCon" v-if="ewmToggle">
			<view class="bdewmCon_t">
				<view class="bdewmCon_t_a">绑定二维码</view>
				<image :src="glInfo.qrcode" mode="aspectFill" class="bdewmCon_t_b" :show-menu-by-longpress="true"></image>
				<view class="bdewmCon_t_c">使用说明</view>
				<view class="bdewmCon_t_d"><text></text>长按二维码围片识别<text></text></view>
			</view>
			<image src="/static/images/popup_close1.png" class="bdewmCon_b" @click="ewmEndTap"></image>
		</view>
		<!-- 绑定二维码 end -->
		
		
		<!-- 提示 go -->
		<view class="tstsTanc" v-if="succrssTsToggle">
			<view class="tstsTanc_n">
				<image src="/static/images/icon91.png" class="tstsTanc_bj"></image>
				<view class="tstsTanc_n_n">
					<view class="tstsTanc_a">提示</view>
					<view class="tstsTanc_b">通知推送完毕，请查收，如您未收到消息，请联系商家。</view>
					<view class="tstsTanc_c" @click="succrssTsToggle = false">我知道了</view>
				</view>
			</view>
		</view>
		<!-- 提示 end -->
		
		<!-- 提示 go -->
		<view class="tstsTanc" v-if="errTsToggle">
			<view class="tstsTanc_n">
				<image src="/static/images/icon91-1.png" class="tstsTanc_bj"></image>
				<view class="tstsTanc_n_n">
					<view class="tstsTanc_a">提示</view>
					<view class="tstsTanc_b">通知消息推送不成功，请检查以下可能</view>
					<view class="tstsTanc_b tstsTanc_b_cha"><text></text>您的账号暂未绑定公众号，请先关注</view>
					<view class="tstsTanc_c" @click="errTsToggle = false">我知道了</view>
				</view>
			</view>
		</view>
		<!-- 提示 end -->
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	jcBindApi,
	noticeManageApi,
	pushTestingApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			ewmToggle:false,
			succrssTsToggle:false,
			errTsToggle:false,
			glInfo:{bind_status:-1,qrcode:''}
		}
	},
	onShow() {
		this.noticeManageData(); //通知管理二维码
		this.ewmToggle = false;
	},
	methods: {
		//公众号绑定/解绑
		wemTap(){
			if(this.glInfo.bind_status == 0){
				this.ewmToggle = true;
			}else{
				//解绑二维码
				uni.showLoading({
					title: '加载中'
				});
				let that = this;
				jcBindApi({}).then(res => {
					console.log('解绑二维码',res);
					if (res.code == 1) {
						uni.hideLoading();
						that.noticeManageData();//通知管理二维码
						uni.showToast({
							icon: 'success',
							title: '解绑成功'
						});
					}
				})
			}
		},
		//关闭二维码弹窗
		ewmEndTap(){
			this.noticeManageData(); //通知管理二维码
			this.ewmToggle = false;
		},
		//通知管理二维码
		noticeManageData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			noticeManageApi({}).then(res => {
				console.log('通知管理二维码',res);
				uni.hideLoading();
				if (res.code == 1) {
					that.glInfo = res.data;
				}
			})
		},
		//消息推送
		xxtsTap(){
			
			uni.showLoading({
				title: '推送中'
			});
			let that = this;
			pushTestingApi({}).then(res => {
				console.log('消息推送测试',res);
				uni.hideLoading();
				if (res.code == 1) {
					that.succrssTsToggle = true;
					that.errTsToggle = false;
				}else{
					that.succrssTsToggle = false;
					that.errTsToggle = true;
				}
			})
			
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.tzgl{overflow: hidden;}
page{padding-bottom: 0;}
</style>