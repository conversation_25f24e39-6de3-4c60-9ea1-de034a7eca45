# 用户资料更新接口使用指南

## 概述

本文档介绍了优化后的用户资料更新接口，使用MyBatis-Plus的UpdateWrapper实现精确字段更新，避免了全实体更新的性能问题。

## 核心改进

### 1. 使用UpdateWrapper精确更新

**之前的方式（不推荐）：**
```java
// 查询整个实体
BaUser user = baUserService.getById(userId);
// 修改字段
user.setNickname(newNickname);
user.setBio(newBio);
// 更新整个实体
baUserService.updateById(user);
```

**现在的方式（推荐）：**
```java
// 只更新需要更新的字段
LambdaUpdateWrapper<BaUser> updateWrapper = new LambdaUpdateWrapper<>();
updateWrapper.eq(BaUser::getId, userId);

if (StringUtils.isNotBlank(nickname)) {
    updateWrapper.set(BaUser::getNickname, nickname);
}
if (bio != null) {
    updateWrapper.set(BaUser::getBio, bio);
}

baUserService.update(updateWrapper);
```

### 2. 优势

1. **性能优化**：只更新需要更新的字段，减少数据库IO
2. **并发安全**：避免了查询-修改-更新过程中的并发问题
3. **SQL优化**：生成的SQL更精确，如：`UPDATE ba_user SET nickname = ? WHERE id = ?`
4. **空值处理**：智能处理null值，避免不必要的字段更新

## API接口

### 1. 更新用户资料

**接口地址：** `PUT /user/profile`

**请求体：**
```json
{
  "nickname": "新昵称",
  "bio": "新的个人简介",
  "danceType": "街舞",
  "avatar": "https://example.com/avatar.jpg"
}
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```

**特点：**
- 只传递需要更新的字段
- null或空字符串的字段不会被更新
- 支持部分字段更新

### 2. 测试接口

#### 基础测试
**接口地址：** `POST /social/test/update-user/{userId}`

**参数：**
- `nickname`: 昵称（可选）
- `bio`: 个人简介（可选）
- `danceType`: 舞种（可选）

#### 高级测试
**接口地址：** `POST /social/test/test-update-wrapper/{userId}`

**请求体：**
```json
{
  "nickname": "测试昵称",
  "bio": "测试简介"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "status": "success",
    "message": "UpdateWrapper精确更新成功",
    "beforeUpdate": {
      "id": 1,
      "nickname": "原昵称",
      "bio": "原简介",
      "danceType": "原舞种"
    },
    "afterUpdate": {
      "id": 1,
      "nickname": "测试昵称",
      "bio": "测试简介",
      "danceType": "原舞种"
    },
    "updateFields": {
      "nickname": "测试昵称",
      "bio": "测试简介"
    },
    "changes": {
      "nickname": "原昵称 -> 测试昵称",
      "bio": "原简介 -> 测试简介"
    }
  }
}
```

## 实现细节

### BaUserService接口

```java
/**
 * 更新用户资料（只更新非空字段）
 *
 * @param userId 用户ID
 * @param updateRequest 更新请求
 * @return 是否成功
 */
boolean updateUserProfile(Long userId, UserProfileUpdateRequest updateRequest);
```

### BaUserServiceImpl实现

```java
@Override
public boolean updateUserProfile(Long userId, UserProfileUpdateRequest updateRequest) {
    LambdaUpdateWrapper<BaUser> updateWrapper = new LambdaUpdateWrapper<>();
    updateWrapper.eq(BaUser::getId, userId);
    
    boolean hasUpdate = false;
    
    // 只更新非空字段
    if (StringUtils.isNotBlank(updateRequest.getNickname())) {
        updateWrapper.set(BaUser::getNickname, updateRequest.getNickname());
        hasUpdate = true;
    }
    
    if (updateRequest.getBio() != null) {
        updateWrapper.set(BaUser::getBio, updateRequest.getBio());
        hasUpdate = true;
    }
    
    // ... 其他字段
    
    return hasUpdate ? this.update(updateWrapper) : true;
}
```

## 测试验证

### 1. 数据库连接测试
```bash
GET /social/test/database
```

### 2. 用户资料查询测试
```bash
GET /social/test/user-profile/1
```

### 3. 用户资料更新测试
```bash
POST /social/test/update-user/1?nickname=新昵称&bio=新简介
```

### 4. UpdateWrapper功能测试
```bash
POST /social/test/test-update-wrapper/1
Content-Type: application/json

{
  "nickname": "UpdateWrapper测试",
  "danceType": "现代舞"
}
```

## 注意事项

1. **字段验证**：nickname使用`StringUtils.isNotBlank()`验证，bio允许空字符串
2. **并发安全**：UpdateWrapper天然支持并发更新
3. **性能优化**：只更新变化的字段，减少数据库负载
4. **日志记录**：详细记录更新操作和结果
5. **错误处理**：完善的异常处理和错误提示

## 生成的SQL示例

**只更新昵称：**
```sql
UPDATE ba_user SET nickname = '新昵称' WHERE id = 1
```

**更新多个字段：**
```sql
UPDATE ba_user SET nickname = '新昵称', bio = '新简介', dance_type = '街舞' WHERE id = 1
```

**无字段更新时：**
不执行任何SQL，直接返回成功

---

**更新时间：** 2025-07-17  
**版本：** v1.0  
**维护人员：** 开发团队
