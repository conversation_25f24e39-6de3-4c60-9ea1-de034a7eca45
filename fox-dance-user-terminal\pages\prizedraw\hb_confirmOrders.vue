<template>
	<view class="confirmOrder jpdh" :style="{ '--qjbutton-color': qjbutton }" v-if="productxq.id">
		
		<view class="qrdd_a" v-if="!payment_code" @click="navTo('/pages/prizedraw/edit_skxx')"><image src="/static/images/icon33-1.png"></image>添加微信收款码</view>
		<view class="hbdhCon_a" v-else>
			<image src='/static/images/icon62.png' class="hbdhCon_a_bj"></image>
			<view class="hbdhCon_a_n" @click="navTo('/pages/prizedraw/edit_skxx')">
				<image :src="payment_code == null || payment_code == '' ? '/static/images/icon70.png' : imgbaseUrl + payment_code" class="hbdhCon_a_l" mode="aspectFill"></image>
				<view class="hbdhCon_a_r">收款信息</view>
			</view>
		</view>
		 
		<view class="qrdd_c">
			<view class="qrdd_c_li">
				<!-- <image :src="imgbaseUrl + productxq.image" mode="aspectFill" class="qrdd_c_li_l"></image> -->
				<!-- <image src="/static/images/icon23.jpg" mode="aspectFill" class="qrdd_c_li_l"></image> -->
				<view class="jlcon_li_hb">
					<view class="pri_two_b_li_hb">
						<image src='/static/images/icon83.png' class="pri_two_b_li_hb_bj"></image>
						<view class="pri_two_b_li_hb_n">{{price}}<image src='/static/images/icon83-1.png'></image></view>
					</view>
				</view>
				<view class="qrdd_c_li_r">
					<view class="qrdd_c_li_r_a">{{price}}元现金红包</view>
					<!-- <view class="qrdd_c_li_r_b">已选：420g</view> -->
					<view class="qrdd_c_li_r_c"><view></view><text>x1</text></view>
				</view>
			</view>
		</view>
		
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<view class="peodex_foo">
			<view class="peodex_foo_l">共1件<text></text></view>
			<view class="peodex_foo_r" @click="dhSubTap">兑换</view>
		</view>
		
		
	</view>
</template>


<script>
import {
	exchangeSubApi,
	addrList,
	exchangeRedPackApi,
	userInfoApi,
	paymentcodeApi,
	exchangeRedPackLevelApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			productxq:{id:1},
			imgbaseUrl:'',
			remark:'',
			shouAddr:{area:''},
			switchVal: false,
			jpid:0,//奖品id
			price:0,//红包金额
			payment_code:'',//收款二维码
			qjbutton:'#131315',
		}
	},
	onShow() {
		if (uni.getStorageSync('diancan')) {
			this.shouAddr = uni.getStorageSync('diancan')
		}else{
			this.shouAddr = {area:''}
		}
		this.userData();//个人信息
	},
	onLoad(option) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.imgbaseUrl = this.$baseUrl;
		// this.productxq = JSON.parse(option.productxq);
		this.addressData();
		console.log(this.productxq)
		this.jpid = option.jpid;
		this.price = option.price;
		
		if(!uni.getStorageSync('skxxMr')){
			uni.setStorageSync('skxxMr',1)
		}
	},
	onUnload: function() {
		console.log('onHide','onHide');
		if(uni.getStorageSync('skxxMr') == 2 && (this.payment_code != '' && this.payment_code != null)){
			console.log(this.payment_code,'this.payment_code')
			this.tjxxTap('notz');//清空收款信息
		}
	},
	methods: {
		//红包兑换提交
		dhSubTap(){
			// uni.getStorageSync('storeInfo')
			if(this.payment_code == null || this.payment_code == ''){
				uni.showToast({
					title: '请传收款信息',
					icon: 'none',
					duration: 2000
				})
				return false;
			}
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			
			if(uni.getStorageSync('dy_type') == 'cj'){
				exchangeRedPackApi({
					id:that.jpid,
				}).then(res => {
					console.log('红包兑换提交',res)
					if (res.code == 1) {
						if(uni.getStorageSync('skxxMr') == 2){
							that.tjxxTap();//清空收款信息
						}else{
							uni.hideLoading();
							uni.redirectTo({
								url:'/pages/prizedraw/success'
							})
						}
						/*uni.showToast({
							title: '兑换成功',
							icon: 'success',
							duration: 2000
						})
						setTimeout(function(){
							uni.navigateBack({})
						},1500)*/
					}
				})
			}
			
			
			if(uni.getStorageSync('dy_type') == 'dj'){
				exchangeRedPackLevelApi({
					id:that.jpid,
				}).then(res => {
					console.log('红包兑换提交',res)
					if (res.code == 1) {
						if(uni.getStorageSync('skxxMr') == 2){
							that.tjxxTap();//清空收款信息
						}else{
							uni.hideLoading();
							uni.redirectTo({
								url:'/pages/prizedraw/success'
							})
						}
						/*uni.showToast({
							title: '兑换成功',
							icon: 'success',
							duration: 2000
						})
						setTimeout(function(){
							uni.navigateBack({})
						},1500)*/
					}
				})
			}
			
		},
		//清空收款信息
		tjxxTap(notz){
			paymentcodeApi({
				payment_code:''
			}).then(res => {
				if (res.code == 1) {
					if(!notz){
						uni.hideLoading();
						uni.redirectTo({
							url:'/pages/prizedraw/success'
						})
					}
				}else{
					
				}
			})
		},
		//个人信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userInfoApi({}).then(res => {
				if (res.code == 1) {
					console.log('个人信息',res);
					that.payment_code = res.data.payment_code;
					uni.hideLoading();
				}
			})
		},
		change(e) {
			console.log('change', );
			this.switchVal = e
		},
		//收货地址
		goToAddr(type) {
			uni.navigateTo({
				url: `/pages/mine/address?type=${type}`
			})
		},
		//收货地址
		addressData(){
			addrList({page: 1,size: 999,}).then(res => {
				if (res.code == 1) {
					console.log(res,'地址列表')
					var arrdArr = [];
					for(var i=0;i<res.data.length;i++){
						if(res.data[i].is_default == 1){
							arrdArr.push(res.data[i])
						}
					}
					if(arrdArr.length == 0){
						this.shouAddr = {area:''}
					}else{
						this.shouAddr = arrdArr[0]
						uni.setStorageSync('diancan',arrdArr[0])
					}
				} else {
					this.mescroll.endErr()
					// this.mescroll.endSuccess();
				}
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.confirmOrder{overflow: hidden;}
page{padding-bottom: 0;}
</style>