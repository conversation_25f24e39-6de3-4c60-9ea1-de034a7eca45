package com.yupi.springbootinit.model.dto.post;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 更新帖子DTO
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
public class PostUpdateDTO implements Serializable {

    /**
     * 帖子ID
     */
    @NotNull(message = "帖子ID不能为空")
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 帖子内容
     */
    @Size(max = 2000, message = "帖子内容不能超过2000字符")
    private String content;

    /**
     * 帖子图片数组
     */
    @Size(max = 9, message = "图片数量不能超过9张")
    private List<String> images;

    /**
     * 位置名称
     */
    @Size(max = 200, message = "位置名称不能超过200字符")
    private String locationName;

    /**
     * 纬度
     */
    private BigDecimal locationLatitude;

    /**
     * 经度
     */
    private BigDecimal locationLongitude;

    /**
     * 详细地址
     */
    @Size(max = 500, message = "详细地址不能超过500字符")
    private String locationAddress;

    /**
     * 是否公开：0-私密，1-公开
     */
    private Integer isPublic;

    /**
     * 状态：0-草稿，1-已发布
     */
    private Integer status;

    /**
     * 帖子标签列表
     */
    private List<String> tags;

    private static final long serialVersionUID = 1L;
}
