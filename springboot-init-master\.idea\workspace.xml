<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ace81e03-f1b1-41d9-85eb-5740f740f433" name="Changes" comment="投票最终版">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/creat_init.sql" beforeDir="false" afterPath="$PROJECT_DIR$/sql/creat_init.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/common/DeleteRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/common/DeleteRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/config/JsonConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/config/JsonConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/config/JsonTypeHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/config/JsonTypeHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/config/ListJsonTypeHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/config/ListJsonTypeHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/controller/BaUserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/controller/BaUserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/controller/MetroLineController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/controller/MetroLineController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/mapper/VoteRecordMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/mapper/VoteRecordMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/model/entity/BaUser.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/model/entity/BaUser.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/model/entity/VoteRecord.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/model/entity/VoteRecord.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/service/BaUserService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/service/BaUserService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/service/impl/BaUserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/service/impl/BaUserServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/service/impl/VipMemberScheduleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/service/impl/VipMemberScheduleServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/service/impl/VoteRecordServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yupi/springbootinit/service/impl/VoteRecordServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/BaUserMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/BaUserMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/MetroLineMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/MetroLineMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/VoteRecordMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/VoteRecordMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\DevolopmentTools\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
        <option name="userSettingsFile" value="D:\DevolopmentTools\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2x9wfhZ435iI5BY1PyIdecyVYNR" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.springboot-init [package].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.MainApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Project/fox/用户端/springboot-init-master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "redis",
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Project\voteProject\springboot-init-master\src\main\java\com\yupi\springbootinit\aop" />
      <recent name="D:\Project\voteProject\springboot-init-master\src\main\java\com\yupi\springbootinit\controller" />
      <recent name="D:\Project\voteProject\springboot-init-master\src\main\java\com\yupi\springbootinit\model\dto" />
      <recent name="D:\Project\voteProject\springboot-init-master\src\main\java\com\yupi\springbootinit\mapper" />
      <recent name="D:\Project\voteProject\springboot-init-master\src\main\java\com\yupi\springbootinit\model\vo" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Project\voteProject\springboot-init-master\src\main\java\com\yupi\springbootinit\model\vo" />
      <recent name="D:\Project\voteProject\springboot-init-master\src\main\resources\mapper" />
      <recent name="D:\Project\voteProject\springboot-init-master\sql" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.MainApplication">
    <configuration name="vote_records.sql" type="DatabaseScript" editBeforeRun="true" temporary="true" nameIsGenerated="true">
      <script-file value="a7e5f657-a31b-4faa-8e7b-71d0ef99bfbd/schema/admin_foxdance_c/table/vote_records.sql" />
      <script-mode>FILE</script-mode>
      <method v="2" />
    </configuration>
    <configuration name="MainApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="prod" />
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="springboot-init" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yupi.springbootinit.MainApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Database Script.vote_records.sql" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.19416.15" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.19416.15" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ace81e03-f1b1-41d9-85eb-5740f740f433" name="Changes" comment="" />
      <created>1747363702009</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747363702009</updated>
      <workItem from="1747363703103" duration="26106000" />
      <workItem from="1747646019849" duration="24492000" />
      <workItem from="1747895751129" duration="18729000" />
      <workItem from="1748507666036" duration="16251000" />
      <workItem from="1748913780411" duration="30143000" />
      <workItem from="1749112706887" duration="106000" />
      <workItem from="1749113069726" duration="20000" />
      <workItem from="1749203121833" duration="9824000" />
      <workItem from="1749287759533" duration="17392000" />
      <workItem from="1749632951911" duration="26851000" />
      <workItem from="1749806243978" duration="46274000" />
      <workItem from="1750740783979" duration="91597000" />
      <workItem from="1751619830427" duration="33521000" />
      <workItem from="1752112174660" duration="19872000" />
      <workItem from="1752542358581" duration="28320000" />
    </task>
    <task id="LOCAL-00001" summary="腾讯云验证码验证失败，找不出问题">
      <option name="closed" value="true" />
      <created>1747714003769</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1747714003769</updated>
    </task>
    <task id="LOCAL-00002" summary="投票+验证码功能正常">
      <option name="closed" value="true" />
      <created>1747730723579</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1747730723579</updated>
    </task>
    <task id="LOCAL-00003" summary="增加了接口的权限校验。">
      <option name="closed" value="true" />
      <created>1749277472633</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1749277472633</updated>
    </task>
    <task id="LOCAL-00004" summary="投票最终版">
      <option name="closed" value="true" />
      <created>1749709215422</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1749709215422</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="master" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="HEAD" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$/springboot-init-master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="腾讯云验证码验证失败，找不出问题" />
    <MESSAGE value="投票+验证码功能正常" />
    <MESSAGE value="增加了接口的权限校验。" />
    <MESSAGE value="投票最终版" />
    <option name="LAST_COMMIT_MESSAGE" value="投票最终版" />
  </component>
</project>