<template>
	<view :style="{ '--qjbutton-color': qjbutton }" v-if="loding" style="overflow: hidden;">
		<view class="fox">
			<view class="title">
				<view class="title_text">
					Fox介绍
				</view>
				<view class="title_bottom"></view>
			</view>
			<view class="fox_cont flex col-top">
				<view class="fox_cont_text">{{foxInfo.introduce}}</view>
				<view class="fox_cont_img">
					<image mode="aspectFill" :src="imgbaseUrl + foxInfo.introduce_image"></image>
				</view>
			</view>
		</view>

		<view class="fox">
			<view class="title">
				<view class="title_text">
					主理人介绍
				</view>
				<view class="title_bottom"></view>
			</view>
			<view class="fox_cont flex col-top">
				<view class="fox_cont_img" style="margin-left: 0;margin-right: 32rpx;">
					<image mode="aspectFill" :src="imgbaseUrl + foxInfo.host_image"></image>
				</view>
				<view class="fox_cont_text">{{foxInfo.host}}</view>
			</view>
		</view>
		<view class="fox">
			<view class="title">
				<view class="title_text">
					成立背景
				</view>
				<view class="title_bottom"></view>
			</view>
			<view class="fox_cont flex col-top">
				<view class="fox_cont_text" style="padding-top: 0;">{{foxInfo.establish_background}}</view>
			</view>
		</view>
		<!-- 旗下师资 -->
		<view class="fox teach" v-if="foxInfo.teacher.length > 0">
			<view class="flex row-between" style="padding:26rpx ;">
				<view class="title">
					<view class="title_text">
						旗下师资
					</view>
					<view class="title_bottom"></view>
				</view>
				<view class="more flex" @click="navTo('/pages/index/teacherDetail')">
					查看更多 <image src="/static/images/introduce_more.png" mode="scaleToFill"></image>
				</view>
			</view>
			<view class="teach_list">
				<view class="teach_li flex" v-for="(item,index) in foxInfo.teacher" :key="index" @click="navTo('/pages/index/teacherDetail')">
					<view class="teach_li_l">
						<image :src="imgbaseUrl + item.image" mode="aspectFill"></image>
					</view>
					<view class="teach_li_r flex-1">
						<view class="teach_li_r_t flex">
							<view class="teach_li_r_name">{{item.name}}老师</view>
							<view class="teach_li_r_tag" v-for="(itemerj,indexerj) in item.levelTable.name" :key="indexerj">{{itemerj}}</view>
						</view>
						<view class="teach_li_r_c">擅长舞种：{{item.skilled_dance}}</view>
						<view class="teach_li_r_c" v-if="item.work_year*1 > 0">工作年限：{{item.work_year}}年</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 所有门店 -->
		<view class="fox teach  store" v-if="foxInfo.store.length > 0">
			<view class="flex row-between" style="padding:26rpx ;">
				<view class="title">
					<view class="title_text">
						所有门店
					</view>
					<view class="title_bottom">

					</view>
				</view>
				<view class="more flex" @click="navTo('/pages/index/switchStores?mdlist=1')">
					查看更多 <image src="/static/images/introduce_more.png" mode="scaleToFill"></image>
				</view>
			</view>
			<view class="store_list">
				<view class="store_li " v-for="(item,index) in foxInfo.store" :key="index" @click="navTo('/pages/index/storesDetail?id=' + item.id)">
					<view class="store_li_t flex">
						<view class="store_li_t_l">
							<image :src="imgbaseUrl + item.image" mode="aspectFill"></image>
						</view>
						<view class="store_li_t_r flex-1">
							<view class="">{{item.name}}</view>
							<view class="">{{item.introduce}}</view>
							<view class="flex">
								<image src="/static/images/store_map_icon.png" mode="scaleToFill"></image>
								距离你{{item.distance}}km
							</view>
						</view>
					</view>
					<view class="store_li_d flex row-between" @click.stop="dhTap(item)">
						<view class="line-1">{{item.address}}</view>
						<view class="btn">导航前往</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import {
	foxJsApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			loding:false,
			imgbaseUrl:'',//图片地址
			foxInfo:{},
			qjbutton:'#131315',
		}
	},
	onShow() {
		this.imgbaseUrl = this.$baseUrl;
		this.foxDetail();//FOX介绍
	},
	onLoad() {
		this.qjbutton = uni.getStorageSync('storeInfo').button
	},
	methods: {
		//导航
		dhTap(item){
			var that = this;
			uni.openLocation({
				name:item.address,
				latitude: item.latitude*1,
				longitude: item.longitude*1,
				success: function () {
					console.log('success');
				}
			});
		},
		//FOX介绍
		foxDetail(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			foxJsApi({
				longitude:uni.getStorageSync('postion').longitude,
				latitude:uni.getStorageSync('postion').latitude,
			}).then(res => {
				console.log('FOX介绍',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.loding = true;
					that.foxInfo = res.data;
				}
			})
			
		},
		navTo(url) {
			uni.navigateTo({
				url: url
			})
		}
	}
}
</script>

<style lang="scss">
	.title {
		position: relative;

		.title_text {
			position: relative;
			z-index: 11;
			font-family: Maoken Glitch Sans, Maoken Glitch Sans;
			font-size: 32rpx;
			color: #333333;
			line-height: 38rpx;
		}

		.title_bottom {
			position: absolute;
			left: 0;
			bottom: 6rpx;
			width: 126rpx;
			height: 10rpx;
			background: linear-gradient(90deg, #131315 28%, rgba(255, 255, 255, 0) 100%);
		}
	}

	.fox {
		margin: 20rpx auto 0;
		width: 698rpx;
		padding: 26rpx;
		// height: 298rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;

		.fox_cont {
			margin-top: 26rpx;

			.fox_cont_text {
				padding-top: 16rpx;
				font-size: 26rpx;
				color: #333333;
				line-height: 30rpx;
			}

			.fox_cont_img {
				margin-left: 32rpx;

				image {
					width: 266rpx;
					height: 174rpx;
					border-radius: 10rpx 10rpx 10rpx 10rpx;
					background-color: pink;
				}
			}
		}
	}

	.teach {
		background-color: transparent;
		padding: 0;

		.more {
			font-size: 26rpx;
			color: #999999;
			line-height: 30rpx;

			image {
				width: 10.46rpx;
				height: 16rpx;
				margin-left: 12rpx;
			}
		}

		.teach_list {
			.teach_li {
				margin: 20rpx auto 0;
				padding: 42rpx 26rpx 40rpx;
				width: 698rpx;
				height: 200rpx;
				background: #FFFFFF;
				border-radius: 20rpx 20rpx 20rpx 20rpx;

				&:nth-child(1) {
					margin-top: 0;
				}

				.teach_li_l {
					image {
						width: 118rpx;
						height: 118rpx;
						border-radius: 10rpx 10rpx 10rpx 10rpx;
						background-color: pink;
					}
				}

				.teach_li_r {
					margin-left: 26rpx;

					.teach_li_r_t {
						.teach_li_r_name {
							font-weight: bold;
							font-size: 32rpx;
							color: #333333;
							line-height: 38rpx;
							margin-right: 28rpx;
						}

						.teach_li_r_tag {
							margin-right: 10rpx;
							padding: 0 6rpx;
							height: 34rpx;
							background: #131315;
							border-radius: 10rpx 10rpx 10rpx 10rpx;
							font-size: 22rpx;
							color: #FFFFFF;
							line-height: 34rpx;
						}
					}

					.teach_li_r_c {
						margin-top: 4rpx;
						font-size: 26rpx;
						color: #999999;
						line-height: 30rpx;

						&:nth-child(2) {
							margin-top: 8rpx;
						}
					}
				}
			}
		}
	}

	.store {
		margin-top: 28rpx;
		margin-bottom: 28rpx;
		.store_list {
			.store_li {
				margin: 22rpx auto 0;
				width: 698rpx;
				background: #FFFFFF;
				border-radius:20rpx;

				// border-radius: 0rpx 0rpx 0rpx 0rpx;
				// border: 2rpx solid rgba(153, 153, 153, 0.2);
				&:nth-child(1) {
					margin-top: 0rpx;
				}

				.store_li_t {
					padding: 18rpx 26rpx;
					border-bottom: 2rpx solid rgba(153, 153, 153, 0.2);

					.store_li_t_l {
						image {
							width: 120rpx;
							height: 120rpx;
							border-radius: 10rpx 10rpx 10rpx 10rpx;
							background-color: pink;
						}
					}

					.store_li_t_r {
						margin-left: 26rpx;

						view {
							&:nth-child(1) {
								font-weight: bold;
								font-size: 32rpx;
								color: #333;
								line-height: 38rpx;
							}

							&:nth-child(2) {
								margin-top: 6rpx;
								font-size: 26rpx;
								color: #999999;
								line-height: 36rpx;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp:2;
								overflow: hidden;
							}

							&:nth-child(3) {
								margin-top: 6rpx;
								font-size: 26rpx;
								color: #999999;
								line-height: 30rpx;

								image {
									width: 32rpx;
									height: 32rpx;

								}
							}
						}
					}
				}

				.store_li_d {
					width: 698rpx;
					height: 112rpx;
					padding: 0 26rpx;

					view {
						&:nth-child(1) {
							max-width: 470rpx;
							font-size: 26rpx;
							color: #999999;
							line-height: 30rpx;
						}

						&:nth-child(2) {
							width: 144rpx;
							height: 60rpx;
							border-radius: 82rpx 82rpx 82rpx 82rpx;
							font-size: 26rpx;
						}
					}
				}
			}
		}
	}
</style>