package com.yupi.springbootinit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yupi.springbootinit.model.entity.Tag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标签数据库操作
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Mapper
public interface TagMapper extends BaseMapper<Tag> {

    /**
     * 根据名称查询标签
     *
     * @param name 标签名称
     * @return 标签
     */
    Tag selectByName(@Param("name") String name);

    /**
     * 批量根据名称查询标签
     *
     * @param names 标签名称列表
     * @return 标签列表
     */
    List<Tag> selectByNames(@Param("names") List<String> names);

    /**
     * 查询热门标签
     *
     * @param page 分页对象
     * @param limit 限制数量
     * @return 热门标签列表
     */
    Page<Tag> selectHotTags(@Param("page") Page<Tag> page, @Param("limit") Integer limit);

    /**
     * 查询热门标签（不分页）
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<Tag> selectHotTagsList(@Param("limit") Integer limit);

    /**
     * 查询使用次数最多的标签
     *
     * @param limit 限制数量
     * @return 标签列表
     */
    List<Tag> selectTopUsedTags(@Param("limit") Integer limit);

    /**
     * 增加标签使用次数
     *
     * @param tagId 标签ID
     * @param increment 增量
     * @return 影响行数
     */
    int incrementUseCount(@Param("tagId") Long tagId, @Param("increment") Integer increment);

    /**
     * 搜索标签（模糊匹配）
     *
     * @param page 分页对象
     * @param keyword 关键词
     * @return 标签列表
     */
    Page<Tag> searchTags(@Param("page") Page<Tag> page, @Param("keyword") String keyword);

    /**
     * 获取推荐标签
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐标签列表
     */
    List<Tag> selectRecommendTags(@Param("userId") Long userId, @Param("limit") Integer limit);
}
