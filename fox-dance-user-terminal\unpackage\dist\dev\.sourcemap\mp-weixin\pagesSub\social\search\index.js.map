{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?adce", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?9a28", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?5d08", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?a495", "uni-app:///pagesSub/social/search/index.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?ba5b", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?0f37"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "PostCard", "data", "keyword", "searched", "searchHistory", "hotSearches", "results", "methods", "onSearch", "updateHistory", "clearHistory", "onTagClick", "generateMockResults", "mockPosts", "id", "title", "username", "userAvatar", "coverImage", "likeCount", "commentCount"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+OAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCqExvB;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/search/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/search/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2813d7e8&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2813d7e8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2813d7e8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/search/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2813d7e8&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-search/u-search\" */ \"@/components/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-empty/u-empty\" */ \"@/components/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tag/u-tag\" */ \"@/components/uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.searched ? _vm.results.length : null\n  var g1 = !_vm.searched ? _vm.searchHistory.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"search-container\">\r\n    <!-- 搜索栏 -->\r\n    <view class=\"search-bar\">\r\n      <u-search\r\n        v-model=\"keyword\"\r\n        placeholder=\"搜索帖子、用户或话题\"\r\n        :show-action=\"true\"\r\n        action-text=\"搜索\"\r\n        @search=\"onSearch\"\r\n        @custom=\"onSearch\"\r\n      ></u-search>\r\n    </view>\r\n\r\n    <!-- 搜索结果 -->\r\n    <scroll-view v-if=\"searched\" class=\"search-results\" scroll-y>\r\n      <view v-if=\"results.length > 0\" class=\"results-list\">\r\n        <PostCard\r\n          v-for=\"post in results\"\r\n          :key=\"post.id\"\r\n          :post=\"post\"\r\n          class=\"post-card-item\"\r\n        />\r\n      </view>\r\n      <u-empty v-else mode=\"search\" text=\"没有找到相关内容\"></u-empty>\r\n    </scroll-view>\r\n\r\n    <!-- 搜索历史和热门搜索 -->\r\n    <view v-else class=\"discovery-section\">\r\n      <!-- 搜索历史 -->\r\n      <view v-if=\"searchHistory.length > 0\" class=\"history-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">搜索历史</text>\r\n          <u-icon name=\"trash\" color=\"#999\" size=\"20\" @click=\"clearHistory\"></u-icon>\r\n        </view>\r\n        <view class=\"tags-container\">\r\n          <u-tag\r\n            v-for=\"(item, index) in searchHistory\"\r\n            :key=\"index\"\r\n            :text=\"item\"\r\n            type=\"info\"\r\n            @click=\"onTagClick(item)\"\r\n          ></u-tag>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 热门搜索 -->\r\n      <view class=\"hot-searches-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">热门搜索</text>\r\n        </view>\r\n        <view class=\"tags-container\">\r\n          <u-tag\r\n            v-for=\"(item, index) in hotSearches\"\r\n            :key=\"index\"\r\n            :text=\"item\"\r\n            type=\"warning\"\r\n            plain\r\n            @click=\"onTagClick(item)\"\r\n          ></u-tag>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport PostCard from '../components/PostCard.vue'\r\n\r\nexport default {\r\n  name: 'SearchPage',\r\n  components: {\r\n    PostCard\r\n  },\r\n  data() {\r\n    return {\r\n      keyword: '',\r\n      searched: false,\r\n      searchHistory: ['街舞', '美食探店', '周末去哪儿'],\r\n      hotSearches: ['即兴舞蹈', '旅行vlog', '健身打卡', '美食制作', '摄影技巧'],\r\n      results: []\r\n    }\r\n  },\r\n  methods: {\r\n    onSearch(value) {\r\n      if (!value) return\r\n      this.searched = true\r\n      this.updateHistory(value)\r\n      // 模拟API调用\r\n      this.results = this.generateMockResults(value)\r\n    },\r\n    updateHistory(keyword) {\r\n      const index = this.searchHistory.indexOf(keyword)\r\n      if (index > -1) {\r\n        this.searchHistory.splice(index, 1)\r\n      }\r\n      this.searchHistory.unshift(keyword)\r\n      if (this.searchHistory.length > 10) {\r\n        this.searchHistory.pop()\r\n      }\r\n    },\r\n    clearHistory() {\r\n      this.searchHistory = []\r\n    },\r\n    onTagClick(tag) {\r\n      this.keyword = tag\r\n      this.onSearch(tag)\r\n    },\r\n    generateMockResults(keyword) {\r\n      const mockPosts = []\r\n      for (let i = 0; i < 5; i++) {\r\n        mockPosts.push({\r\n          id: `search-${i}`,\r\n          title: `关于“${keyword}”的帖子标题 ${i + 1}`,\r\n          username: `用户${Math.floor(Math.random() * 1000)}`,\r\n          userAvatar: `https://picsum.photos/100/100?random=${i}`,\r\n          coverImage: `https://picsum.photos/300/400?random=${i}`,\r\n          likeCount: Math.floor(Math.random() * 100),\r\n          commentCount: Math.floor(Math.random() * 20)\r\n        })\r\n      }\r\n      return mockPosts\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.search-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #f8f9fa;\r\n}\r\n.search-bar {\r\n  padding: 16rpx;\r\n  background-color: #fff;\r\n  border-bottom: 1rpx solid #e4e7ed;\r\n}\r\n.discovery-section, .search-results {\r\n  padding: 32rpx;\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n.history-section, .hot-searches-section {\r\n  margin-bottom: 40rpx;\r\n}\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24rpx;\r\n}\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n.tags-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20rpx;\r\n}\r\n.results-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16rpx;\r\n}\r\n.post-card-item {\r\n  width: 100%;\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2813d7e8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2813d7e8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752818685585\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}