package com.yupi.springbootinit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yupi.springbootinit.model.entity.Follow;
import com.yupi.springbootinit.model.vo.FollowVO;

import java.util.List;
import java.util.Map;

/**
 * 关注服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface FollowService extends IService<Follow> {

    /**
     * 关注用户
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean followUser(Long followerId, Long followingId);

    /**
     * 取消关注用户
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean unfollowUser(Long followerId, Long followingId);

    /**
     * 检查是否已关注
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否已关注
     */
    boolean isFollowing(Long followerId, Long followingId);

    /**
     * 获取关注状态信息
     *
     * @param currentUserId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 关注状态信息
     */
    Map<String, Object> getFollowStatus(Long currentUserId, Long targetUserId);

    /**
     * 获取关注列表
     *
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @param currentUserId 当前用户ID（用于判断关注状态）
     * @return 关注列表
     */
    List<FollowVO> getFollowingList(Long userId, Integer current, Integer size, Long currentUserId);

    /**
     * 获取粉丝列表
     *
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @param currentUserId 当前用户ID（用于判断关注状态）
     * @return 粉丝列表
     */
    List<FollowVO> getFollowersList(Long userId, Integer current, Integer size, Long currentUserId);

    /**
     * 获取关注统计
     *
     * @param userId 用户ID
     * @return 关注统计信息
     */
    Map<String, Integer> getFollowStats(Long userId);

    /**
     * 批量检查关注状态
     *
     * @param currentUserId 当前用户ID
     * @param userIds 目标用户ID列表
     * @return 关注状态映射
     */
    Map<Long, Boolean> batchCheckFollowStatus(Long currentUserId, List<Long> userIds);

    /**
     * 获取互相关注的用户列表
     *
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @param currentUserId 当前用户ID
     * @return 互相关注的用户列表
     */
    List<FollowVO> getMutualFollows(Long userId, Integer current, Integer size, Long currentUserId);

    /**
     * 获取用户的关注数
     *
     * @param userId 用户ID
     * @return 关注数
     */
    Integer getFollowingCount(Long userId);

    /**
     * 获取用户的粉丝数
     *
     * @param userId 用户ID
     * @return 粉丝数
     */
    Integer getFollowerCount(Long userId);

    /**
     * 更新用户关注统计
     *
     * @param userId 用户ID
     * @param followingIncrement 关注数增量
     * @param followerIncrement 粉丝数增量
     * @return 是否成功
     */
    boolean updateFollowStats(Long userId, Integer followingIncrement, Integer followerIncrement);

    /**
     * 获取推荐关注的用户
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐用户列表
     */
    List<FollowVO> getRecommendUsers(Long userId, Integer limit);

    /**
     * 获取最近关注的用户
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近关注的用户列表
     */
    List<FollowVO> getRecentFollowing(Long userId, Integer limit);

    /**
     * 获取最近的粉丝
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近的粉丝列表
     */
    List<FollowVO> getRecentFollowers(Long userId, Integer limit);
}
