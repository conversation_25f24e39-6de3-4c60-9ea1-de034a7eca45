<template>
	<view class="couponbag" :style="{ '--qjbutton-color': qjbutton }" v-if="loding">
		
		<view class="ord_nav">
			<view class="ord_nav_li" :class="type == 1 ? 'ord_nav_li_ac' : ''" @click="navTap(1)"><view><text>未使用</text><text></text></view></view>
			<view class="ord_nav_li" :class="type == 2 ? 'ord_nav_li_ac' : ''" @click="navTap(2)"><view><text>已使用</text><text></text></view></view>
			<view class="ord_nav_li" :class="type == 3 ? 'ord_nav_li_ac' : ''" @click="navTap(3)"><view><text>已失效</text><text></text></view></view>
		</view>
		
		<view class="cou_con" v-if="couponLists.length > 0">
			<view class="cou_con_li" v-for="(item,index) in couponLists" :key="index" :style="type == 2 || type == 3 ? 'opacity:.6' : ''">
				<view class="cou_con_li_l">
					<view class="cou_con_li_l_a">课包通用</view>
					<view class="cou_con_li_l_b">￥<text>{{item.discount_price*1}}</text></view>
					<view class="cou_con_li_l_c">{{item.type*1 == 1 ? '无门槛' : '满'+item.full_price+'可用'}}</view>
				</view>
				<view class="cou_con_li_r">
					<view class="cou_con_li_r_a">{{item.type*1 == 1 ? '无门槛优惠券' : '平台现金券'}}</view>
					<view class="cou_con_li_r_b">有效期:{{item.effective_stage}}</view>
					<view class="cou_con_li_r_c">每次仅能使用一张</view>
					<view class="cou_con_li_r_d" @click="gosyTap">去使用</view>
				</view>
			</view>
		</view>
		
		<view class="gg_zwsj" style="margin-top:92rpx;" v-if="couponLists.length == 0">
			<view class="gg_zwsj_w">
				<image src="/static/images/wusj.png" mode="widthFix"></image>
				<text>暂无数据</text>
			</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>

<script>
import {
	couponListApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:false,//是否登录
			isH5:false,//是否是h5
			loding:false,
			type:1,
			couponLists:[],//
			qjbutton:'#131315',
		}
	},
	created(){
		
	},
	onLoad(options) {
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.couponData();//优惠券列表
	},
	methods: {
		//去使用
		gosyTap(){
			uni.setStorageSync('qbtz','1')
			uni.switchTab({
				url:'/pages/buy/buy'
			})
		},
		navTap(index){
			this.type = index;
			this.couponData();//优惠券列表
		},
		//优惠券列表
		couponData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			couponListApi({type:that.type}).then(res => {
				if (res.code == 1) {
					console.log('优惠券列表',res);
					that.loding = true;
					that.couponLists = res.data;
					uni.hideLoading();
				}
			})
		},
	},
}
</script>

<style lang="less">
page{padding-bottom:0;}
.couponbag{
	overflow:hidden;
}
</style>
