<template>
  <view class="comment-skeleton">
    <!-- 用户头像骨架 -->
    <view class="skeleton-avatar">
      <view class="skeleton-circle"></view>
    </view>
    
    <!-- 评论内容骨架 -->
    <view class="skeleton-content">
      <!-- 用户名和时间骨架 -->
      <view class="skeleton-header">
        <view class="skeleton-line skeleton-name"></view>
        <view class="skeleton-line skeleton-time"></view>
      </view>
      
      <!-- 评论文本骨架 -->
      <view class="skeleton-text">
        <view class="skeleton-line skeleton-text-line1"></view>
        <view class="skeleton-line skeleton-text-line2"></view>
        <view class="skeleton-line skeleton-text-line3"></view>
      </view>
      
      <!-- 操作按钮骨架 -->
      <view class="skeleton-actions">
        <view class="skeleton-line skeleton-action"></view>
        <view class="skeleton-line skeleton-action"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CommentSkeleton'
}
</script>

<style lang="scss" scoped>
.comment-skeleton {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 32rpx;
  margin-bottom: 20rpx;
  display: flex;
  border-radius: 28rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

.skeleton-avatar {
  margin-right: 24rpx;
  
  .skeleton-circle {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeletonShimmer 1.5s infinite;
  }
}

.skeleton-content {
  flex: 1;
}

.skeleton-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 16rpx;
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonShimmer 1.5s infinite;
  border-radius: 8rpx;
}

.skeleton-name {
  width: 120rpx;
  height: 32rpx;
}

.skeleton-time {
  width: 80rpx;
  height: 24rpx;
}

.skeleton-text {
  margin-bottom: 20rpx;
}

.skeleton-text-line1 {
  width: 100%;
  height: 32rpx;
  margin-bottom: 12rpx;
}

.skeleton-text-line2 {
  width: 85%;
  height: 32rpx;
  margin-bottom: 12rpx;
}

.skeleton-text-line3 {
  width: 60%;
  height: 32rpx;
}

.skeleton-actions {
  display: flex;
  gap: 24rpx;
}

.skeleton-action {
  width: 80rpx;
  height: 28rpx;
}

/* 骨架屏动画 */
@keyframes skeletonShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes skeletonPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 小红书风格优化 */
.comment-skeleton {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 107, 135, 0.1),
      transparent
    );
    animation: skeletonSweep 2s infinite;
  }
}

@keyframes skeletonSweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .comment-skeleton {
    padding: 28rpx;
    margin-bottom: 16rpx;
  }
  
  .skeleton-avatar .skeleton-circle {
    width: 72rpx;
    height: 72rpx;
  }
}
</style>
