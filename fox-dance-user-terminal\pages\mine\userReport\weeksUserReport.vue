<template>
	<view class="userReport" v-if="loding">
		
		<u-navbar :back-icon-color="swiperIndex == 0 || swiperIndex == 1 ? '#fff' : '#333'" back-icon-size="42" title="用户报告" background="none" :border-bottom="false" :title-color="swiperIndex == 0 || swiperIndex == 1 ? '#fff' : '#333'" title-size="32">
		</u-navbar>
		
		<view class="yueb weeks_yueb">
			<swiper class="swiper" :current="swiperIndex" :vertical="true" @change="swiperChange" @animationfinish="swiperEnd">
				<swiper-item>
					<view class="swiper-item weeks_one">
						<view :style="'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'"></view>
						<image :src="imgbaseUrlOss + '/userreport/icon12.png'" mode="aspectFill" class="years_one_bj"></image>
						<view class="weeks_one_a animate__animated animate__bounceInDown" v-if="configDate.oneAni1"><text>你的</text><text>FOX周报</text></view>
						<view class="weeks_one_b animate__animated animate__bounceInLeft" v-if="configDate.oneAni2"></view>
						<view class="weeks_one_c animate__animated animate__bounceInUp" v-if="configDate.oneAni3"><text>WORLD</text><text>THEATRE DAY</text></view>
						<view class="weeks_one_d animate__animated animate__zoomInDown" v-if="configDate.oneAni4" @click="swiperIndex = 1">开启旅程</view>
						
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item weeks_two">
						<view :style="'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'"></view>
						<image :src="imgbaseUrlOss + '/userreport/icon13.png'" mode="aspectFill" class="years_one_bj"></image>
						  
						<view class="weeks_two_n">
							<view class="weeks_two_a animate__animated animate__rollIn" v-if="configDate.twoAni0">这一周，你在FOX的线上课累计观</view>
							<view class="weeks_two_a animate__animated animate__rollIn" v-if="configDate.twoAni1">看了<text>{{userReport.cloud_time*1}}</text>分钟，足足超过</view>
							<view class="weeks_two_a animate__animated animate__rollIn" v-if="configDate.twoAni2"><text>{{userReport.over_ratio*1}}%</text>的用户。</view>
							<view class="weeks_two_a animate__animated animate__rollIn" v-if="configDate.twoAni3">你不仅在舞蹈教室舞动自如，连家</view>
							<view class="weeks_two_a animate__animated animate__rollIn" v-if="configDate.twoAni4">里的客厅都成了你的练舞场!</view>
						</view>
					</view>
				</swiper-item>
				<swiper-item v-if="userReport.invite_num*1 > 0">
					<view class="swiper-item weeks_thr">
						<view :style="'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'"></view>
						<image :src="imgbaseUrlOss + '/userreport/icon14.png'" mode="aspectFill" class="years_one_bj"></image>
						<view class="weeks_thr_a">
							<view class="animate__animated animate__lightSpeedInLeft" v-if="configDate.thrAni1">你太会分享了!</view>
							<view class="animate__animated animate__lightSpeedInLeft" v-if="configDate.thrAni2">这一周你成功邀请了<text>{{userReport.invite_num*1}}</text>位 新朋友加入FOX</view>
							<view class="animate__animated animate__lightSpeedInLeft" v-if="configDate.thrAni3">共收获了<text>{{userReport.get_points}}</text>积分</view>
						</view>
						<view class="weeks_thr_b animate__animated animate__bounceInLeft" v-if="configDate.thrAni4"></view>
						<view class="weeks_thr_c animate__animated animate__fadeInUp" v-if="configDate.thrAni5">看来你不仅自己跳舞，还带着朋友们一起在舞蹈的世界里挥酒热情</view>
						  
						
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item weeks_fou">
						<view :style="'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'"></view>
						<image :src="imgbaseUrlOss + '/userreport/icon15.png'" mode="aspectFill" class="years_one_bj"></image>
						<view class="weeks_fou_a animate__animated animate__lightSpeedInLeft" v-if="configDate.fouAni1">在FOX的刷课榜单上：</view>
						<view class="weeks_fou_b animate__animated animate__lightSpeedInLeft" v-if="configDate.fouAni2">你本周位列第<text>{{pmNum}}</text>名</view>
						<view class="weeks_fou_c animate__animated animate__lightSpeedInLeft" v-if="configDate.fouAni3"></view>
						<view class="weeks_fou_d animate__animated animate__lightSpeedInLeft" v-if="configDate.fouAni4">
							{{pmNum == 1 ? '你是步步生花”的舞神，每一步都绽放出惊艳的花朵，你的天赋与努力让你在排行榜上高居第一，无可替代!' : (pmNum > 1 && pmNum <= 10) ? '稳步向前。继续保持，下一定能夺冠！' : (pmNum > 10 && pmNum < 101) ? '稳步向前。继续保持，下一周冲进前10不是梦!' : (pmNum >= 101) ? '稳步向前。继续保持，下一周冲进前100不是梦!' : ''}}
						</view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item weeks_five">
						<view :style="'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'"></view>
						<image :src="imgbaseUrlOss + '/userreport/icon16.png'" mode="aspectFill" class="years_one_bj"></image>												
						<view class="weeks_five_a animate__animated animate__flipInX" v-if="configDate.fiveAni1 && userReport.last_time && userReport.last_time.type == 0">你最晚的一次线上练习是在 下午<text>{{userReport.last_time.last_time}}</text>和照的阳光下，我知道，你在辛勤的耕耘，等待胜利的果实</view>
						<view class="weeks_five_a animate__animated animate__flipInX" v-if="configDate.fiveAni1 && userReport.last_time && userReport.last_time.type == 1">你最晚的一次线上练习是在 晚上<text>{{userReport.last_time.last_time}}</text>看来深夜也是你的舞蹈时刻。这个时间点还在舞品,简直是灵魂夜猫子的最佳写照</view>
						<view class="weeks_five_a animate__animated animate__flipInX" v-if="configDate.fiveAni1 && userReport.last_time && userReport.last_time.type == 2">你最早的一次线上练习是在 上午<text>{{userReport.last_time.last_time}}</text>,伴着晨光，我知道，你在辛勤的耕耘，等待胜利的果实</view>
						<view class="weeks_five_a animate__animated animate__flipInX" v-if="configDate.fiveAni1 && !userReport.last_time">本周你还没有线上练习，躺在床上很舒服，但跳起来更有成就感，希望看到下周活力焕发的你!</view>
						<view class="weeks_five_b animate__animated animate__fadeInLeft" v-if="configDate.fiveAni2"></view>
						<view class="weeks_five_a animate__animated animate__flipInX" v-if="configDate.fiveAni3">Persistence is a necessary quality for the achievement of heroes.</view>
					</view>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- <view class="userd1 animate__animated animate__bounce">搜索</view>
		
		<view class="userd1 animate__animated animate__fadeInLeftBig">搜索</view> -->

	</view>
</template>


<script>
import {
	weekReportApi,
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			imgbaseUrl:'',//图片地址
			imgbaseUrlOss:'',
			safeAreaTop:wx.getWindowInfo().safeArea.top,
			menuButtonInfoHeight:uni.getMenuButtonBoundingClientRect().height,
			loding:false,
			isLogined:true,
			swiperIndex:0,
			configDate:{
				oneAni1:false,
				oneAni2:false,
				oneAni3:false,
				oneAni4:false,
				
				twoAni0:false,
				twoAni1:false,
				twoAni2:false,
				twoAni3:false,
				twoAni4:false,
				
				thrAni1:false,
				thrAni2:false,
				thrAni3:false,
				thrAni4:false,
				thrAni5:false,
				
				fouAni1:false,
				fouAni2:false,
				fouAni3:false,
				fouAni4:false,
				
				fiveAni1:false,
				fiveAni2:false,
				fiveAni3:false,
				
			},
			pmNum:0,
			userReport:{}
		}
	},
	onShow: function(){
	   this.imgbaseUrl = this.$baseUrl;
	   this.imgbaseUrlOss = this.$baseUrlOss;
	},
	onLoad(option) {
		this.weekUserReportData(option.id);//周报
	},
	methods: {
		//周报
		weekUserReportData(id){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			weekReportApi({id:id}).then(res => {
				console.log('周报1',res)
				if (res.code == 1) {
					uni.hideLoading();
					/*// res.data.invite_num = 2
					res.data.last_time =  {
						"last_time": "10:22",
						"create_time": 1734142950
					}*/
					
					
					if(res.data.last_time){
						var date = new Date(Number(res.data.last_time.create_time + '000'));
						var hours = date.getHours();
						if (hours >= 12 && hours < 18) {
							res.data.last_time.type = 0
							// console.log("属于中午12:00~下午5:59这个时间段---");
						} else if ((hours >= 18 && hours <= 23) || (hours >= 0 && hours < 3)) {
							res.data.last_time.type = 1
							// console.log("属于晚上6:00~凌晨2:59这个时间段");
						} else {
							res.data.last_time.type = 2
							// console.log("属于凌晨3:00~中午11:59这个时间段");
						}
					}
					that.pmNum = res.data.rank*1;
					that.userReport = res.data;
					console.log('周报2',that.userReport);
					that.loding = true;
					that.cshData()
				}
			})
		},
		//初始化
		cshData(){
			var that = this;
			if(this.swiperIndex == 0){
				that.configDate.oneAni1 = true;
				setTimeout(()=>{
					that.configDate.oneAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.oneAni3 = true;
				},2000);
				setTimeout(()=>{
					that.configDate.oneAni4 = true;
				},3000);
			}
		},
		//监听swiper
		swiperChange(e){
			var that = this;
			// console.log(e,'监听swiper')
			this.swiperIndex = e.detail.current
			
			if(this.swiperIndex == 1){
				that.configDate.twoAni0 = true;
				setTimeout(()=>{
					that.configDate.twoAni1 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.twoAni2 = true;
				},2000);
				setTimeout(()=>{
					that.configDate.twoAni3 = true;
				},3000);
				setTimeout(()=>{
					that.configDate.twoAni4 = true;
				},4000);
			}
			
			if(this.userReport.invite_num*1 > 0){
				if(this.swiperIndex == 2){
					that.configDate.thrAni1 = true;
					setTimeout(()=>{
						that.configDate.thrAni2 = true;
					},1000);
					setTimeout(()=>{
						that.configDate.thrAni3 = true;
					},2000);
					setTimeout(()=>{
						that.configDate.thrAni4 = true;
					},3000);
					setTimeout(()=>{
						that.configDate.thrAni5 = true;
					},4000);
				}
			}
			
			
			
			
			// if(this.swiperIndex == 3){
			if(this.swiperIndex == (this.userReport.invite_num*1 > 0 ? 3 : 2)){
				that.configDate.fouAni1 = true;
				setTimeout(()=>{
					that.configDate.fouAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.fouAni3 = true;
				},2000);
				setTimeout(()=>{
					that.configDate.fouAni4 = true;
				},3000);
				setTimeout(()=>{
					that.configDate.fouAni5 = true;
				},4000);
				setTimeout(()=>{
					that.configDate.fouAni6 = true;
				},5000);
			}
			
			// if(this.swiperIndex == 4){
			if(this.swiperIndex == (this.userReport.invite_num*1 > 0 ? 4 : 3)){
				that.configDate.fiveAni1 = true;
				setTimeout(()=>{
					that.configDate.fiveAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.fiveAni3 = true;
				},2000);
			}
			
		},
		//动画结束时会触发
		swiperEnd(e){
			// console.log(e,'动画结束时会触发')
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">

.userReport{-overflow: hidden;}
page{padding-bottom: 0;background:#fff;}
.userd1{
	width: 200rpx;
	height: 200rpx;
	background:red;
	margin: auto;
}
</style>