{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?3cec", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?3929", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?cd03", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?4a77", "uni-app:///pagesSub/social/message/index.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?8170", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?56ee", "uni-app:///main.js"], "names": ["name", "data", "chatList", "loading", "refreshing", "systemUnreadCount", "likeUnreadCount", "followUnreadCount", "onLoad", "onShow", "console", "activated", "methods", "loadUnreadCounts", "unreadData", "loadChatList", "current", "size", "result", "id", "userId", "avatar", "lastMessage", "lastMessageTime", "lastMessageType", "unreadCount", "isOnline", "isMuted", "generateMockChatList", "formatTime", "onRefresh", "openChat", "chat", "conversationId", "index", "uni", "url", "showChatActions", "itemList", "success", "toggleChatTop", "toggleChatMute", "deleteChat", "title", "content", "goSearch", "startNewChat", "goSystemMessages", "goLikeMessages", "goFollowMessages", "forceRefresh", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAouB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6GxvB;AAAA;AAAA;AAAA,eAEA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EAEAC;IACA;IACA;MACAC;MACA;MACA;IACA;EACA;EAEA;EACAC;IACA;MACAD;MACA;MACA;IACA;EACA;EACAE;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAKA;kBACA;oBAAA;sBACAC;sBACAC;sBACApB;sBACAqB;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBAAA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAjB;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAkB;MACA,QACA;QACAT;QACAnB;QACAqB;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAR;QACAnB;QACAqB;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAR;QACAnB;QACAqB;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAR;QACAnB;QACAqB;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAR;QACAnB;QACAqB;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA,EACA;IACA;IAEAE;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MAEA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,MAGAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAb;gBACA;cAAA;gBAEA;gBACAY;;gBAEA;gBACAE;kBAAA;gBAAA;gBACA;kBACA;gBACA;cAAA;gBAGAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA1B;gBACA;gBACAyB;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MACA;MAEAF;QACAG;QACAC;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACAT;MACA;IACA;IAEAU;MAAA;MACAP;QACAQ;QACAC;QACAL;UACA;YACA;cAAA;YAAA;YACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAM;MACAV;QACAC;MACA;IACA;IAEAU;MACAX;QACAC;MACA;IACA;IAEAW;MACAZ;QACAC;MACA;IACA;IAEAY;MACAb;QACAC;MACA;IACA;IAEAa;MACAd;QACAC;MACA;IACA;IAEA;IACAc;MACAxC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzYA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAyC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pagesSub/social/message/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2caef1dd&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2caef1dd\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/message/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2caef1dd&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.chatList, function (chat, __i0__) {\n    var $orig = _vm.__get_orig(chat)\n    var m0 = _vm.formatTime(chat.lastMessageTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = !_vm.chatList.length && !_vm.loading\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"message-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"title\">消息</text>\n        <view class=\"header-actions\">\n          <u-icon name=\"search\" size=\"24\" color=\"#333\" @click=\"goSearch\"></u-icon>\n          <u-icon name=\"plus\" size=\"24\" color=\"#333\" @click=\"startNewChat\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 功能入口 -->\n    <view class=\"quick-actions\">\n      <view class=\"action-item\" @click=\"goSystemMessages\">\n        <view class=\"action-icon system\">\n          <u-icon name=\"bell\" color=\"#fff\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"action-text\">系统消息</text>\n        <view v-if=\"systemUnreadCount\" class=\"unread-badge\">{{ systemUnreadCount }}</view>\n      </view>\n      \n      <view class=\"action-item\" @click=\"goLikeMessages\">\n        <view class=\"action-icon like\">\n          <u-icon name=\"heart\" color=\"#fff\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"action-text\">赞和评论</text>\n        <view v-if=\"likeUnreadCount\" class=\"unread-badge\">{{ likeUnreadCount }}</view>\n      </view>\n      \n      <view class=\"action-item\" @click=\"goFollowMessages\">\n        <view class=\"action-icon follow\">\n          <u-icon name=\"account-fill\" color=\"#fff\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"action-text\">新粉丝</text>\n        <view v-if=\"followUnreadCount\" class=\"unread-badge\">{{ followUnreadCount }}</view>\n      </view>\n    </view>\n\n    <scroll-view \n      class=\"chat-list\" \n      scroll-y \n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <!-- 聊天列表 -->\n      <view \n        v-for=\"chat in chatList\" \n        :key=\"chat.id\"\n        class=\"chat-item\"\n        @click=\"openChat(chat)\"\n        @longpress=\"showChatActions(chat)\"\n      >\n        <view class=\"chat-avatar\">\n          <u-avatar :src=\"chat.avatar\" size=\"50\"></u-avatar>\n          <view v-if=\"chat.isOnline\" class=\"online-dot\"></view>\n        </view>\n        \n        <view class=\"chat-content\">\n          <view class=\"chat-header\">\n            <text class=\"chat-name\">{{ chat.name }}</text>\n            <text class=\"chat-time\">{{ formatTime(chat.lastMessageTime) }}</text>\n          </view>\n          \n          <view class=\"chat-preview\">\n            <view class=\"message-preview\">\n              <text v-if=\"chat.lastMessageType === 'text'\" class=\"preview-text\">\n                {{ chat.lastMessage }}\n              </text>\n              <text v-else-if=\"chat.lastMessageType === 'image'\" class=\"preview-text\">\n                [图片]\n              </text>\n              <text v-else-if=\"chat.lastMessageType === 'voice'\" class=\"preview-text\">\n                [语音]\n              </text>\n              <text v-else class=\"preview-text\">\n                {{ chat.lastMessage }}\n              </text>\n            </view>\n            \n            <view class=\"chat-status\">\n              <view v-if=\"chat.unreadCount\" class=\"unread-count\">\n                {{ chat.unreadCount > 99 ? '99+' : chat.unreadCount }}\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"!chatList.length && !loading\" class=\"empty-state\">\n        <u-icon name=\"chat\" color=\"#ccc\" size=\"60\"></u-icon>\n        <text class=\"empty-text\">暂无消息</text>\n        <text class=\"empty-desc\">开始与朋友聊天吧</text>\n      </view>\n\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-state\">\n        <u-loading mode=\"circle\" size=\"24\"></u-loading>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n    </scroll-view>\n\n  </view>\n</template>\n\n<script>\nimport { getConversations, getUnreadCount, markMessageAsRead } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'SocialMessage',\n  data() {\n    return {\n      chatList: [],\n      loading: false,\n      refreshing: false,\n      systemUnreadCount: 3,\n      likeUnreadCount: 12,\n      followUnreadCount: 5\n    }\n  },\n  onLoad() {\n    this.loadUnreadCounts()\n    this.loadChatList()\n  },\n\n  onShow() {\n    // 页面显示时检查是否需要加载数据\n    if (!this.chatList || this.chatList.length === 0) {\n      console.log('消息页显示时重新加载数据')\n      this.loadUnreadCounts()\n      this.loadChatList()\n    }\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    if (!this.chatList || this.chatList.length === 0) {\n      console.log('消息页激活时重新加载数据')\n      this.loadUnreadCounts()\n      this.loadChatList()\n    }\n  },\n  methods: {\n    // 加载未读消息统计\n    async loadUnreadCounts() {\n      try {\n        const unreadData = await getUnreadCount()\n        if (unreadData) {\n          this.systemUnreadCount = unreadData.systemCount || 0\n          this.likeUnreadCount = unreadData.likeCount || 0\n          this.followUnreadCount = unreadData.followCount || 0\n        }\n      } catch (error) {\n        console.error('加载未读消息统计失败:', error)\n        // 使用默认值\n      }\n    },\n\n    async loadChatList() {\n      this.loading = true\n      try {\n        const result = await getConversations({\n          current: 1,\n          size: 50\n        })\n\n        if (result && result.length > 0) {\n          this.chatList = result.map(conversation => ({\n            id: conversation.id,\n            userId: conversation.otherUserId,\n            name: conversation.otherUserNickname || '用户' + conversation.otherUserId,\n            avatar: conversation.otherUserAvatar || 'https://picsum.photos/100/100?random=' + conversation.otherUserId,\n            lastMessage: conversation.lastMessageContent || '',\n            lastMessageTime: new Date(conversation.lastMessageTime),\n            lastMessageType: 'text',\n            unreadCount: conversation.unreadCount || 0,\n            isOnline: false,\n            isMuted: false\n          }))\n        } else {\n          // 使用模拟数据作为后备\n          this.chatList = this.generateMockChatList()\n        }\n      } catch (error) {\n        console.error('加载聊天列表失败:', error)\n        // 使用模拟数据作为后备\n        this.chatList = this.generateMockChatList()\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n\n    generateMockChatList() {\n      return [\n        {\n          id: 1,\n          name: '小美',\n          avatar: 'https://picsum.photos/100/100?random=700',\n          lastMessage: '今天的穿搭真好看！',\n          lastMessageTime: new Date(Date.now() - 300000), // 5分钟前\n          lastMessageType: 'text',\n          unreadCount: 2,\n          isOnline: true,\n          isMuted: false\n        },\n        {\n          id: 2,\n          name: '旅行达人',\n          avatar: 'https://picsum.photos/100/100?random=701',\n          lastMessage: '分享一下这次旅行的照片',\n          lastMessageTime: new Date(Date.now() - 1800000), // 30分钟前\n          lastMessageType: 'image',\n          unreadCount: 0,\n          isOnline: false,\n          isMuted: false\n        },\n        {\n          id: 3,\n          name: '美食家',\n          avatar: 'https://picsum.photos/100/100?random=702',\n          lastMessage: '这家餐厅真的很不错',\n          lastMessageTime: new Date(Date.now() - 3600000), // 1小时前\n          lastMessageType: 'text',\n          unreadCount: 1,\n          isOnline: true,\n          isMuted: true\n        },\n        {\n          id: 4,\n          name: '摄影师',\n          avatar: 'https://picsum.photos/100/100?random=703',\n          lastMessage: '发了一段语音',\n          lastMessageTime: new Date(Date.now() - 7200000), // 2小时前\n          lastMessageType: 'voice',\n          unreadCount: 0,\n          isOnline: false,\n          isMuted: false\n        },\n        {\n          id: 5,\n          name: '健身达人',\n          avatar: 'https://picsum.photos/100/100?random=704',\n          lastMessage: '明天一起去健身房吗？',\n          lastMessageTime: new Date(Date.now() - 86400000), // 1天前\n          lastMessageType: 'text',\n          unreadCount: 0,\n          isOnline: false,\n          isMuted: false\n        }\n      ]\n    },\n\n    formatTime(time) {\n      const now = new Date()\n      const diff = now - new Date(time)\n      const minutes = Math.floor(diff / 60000)\n      const hours = Math.floor(diff / 3600000)\n      const days = Math.floor(diff / 86400000)\n      \n      if (minutes < 1) return '刚刚'\n      if (minutes < 60) return `${minutes}分钟前`\n      if (hours < 24) return `${hours}小时前`\n      if (days < 7) return `${days}天前`\n      \n      const date = new Date(time)\n      return `${date.getMonth() + 1}/${date.getDate()}`\n    },\n\n    onRefresh() {\n      this.refreshing = true\n      this.loadChatList()\n    },\n\n    async openChat(chat) {\n      try {\n        // 标记消息已读\n        if (chat.unreadCount > 0) {\n          await markMessageAsRead({\n            conversationId: chat.id,\n            userId: chat.userId\n          })\n\n          // 清除未读数\n          chat.unreadCount = 0\n\n          // 更新聊天列表\n          const index = this.chatList.findIndex(c => c.id === chat.id)\n          if (index !== -1) {\n            this.$set(this.chatList, index, { ...chat })\n          }\n        }\n\n        uni.navigateTo({\n          url: `/pagesSub/social/chat/detail?id=${chat.userId}&name=${encodeURIComponent(chat.name)}`\n        })\n      } catch (error) {\n        console.error('打开聊天失败:', error)\n        // 即使标记已读失败，也要跳转到聊天页面\n        uni.navigateTo({\n          url: `/pagesSub/social/chat/detail?id=${chat.userId}&name=${encodeURIComponent(chat.name)}`\n        })\n      }\n    },\n\n    showChatActions(chat) {\n      const actions = ['置顶', chat.isMuted ? '取消免打扰' : '免打扰', '删除聊天']\n      \n      uni.showActionSheet({\n        itemList: actions,\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0:\n              this.toggleChatTop(chat)\n              break\n            case 1:\n              this.toggleChatMute(chat)\n              break\n            case 2:\n              this.deleteChat(chat)\n              break\n          }\n        }\n      })\n    },\n\n    toggleChatTop(chat) {\n      // 置顶/取消置顶逻辑\n      this.$u.toast('置顶成功')\n    },\n\n    toggleChatMute(chat) {\n      chat.isMuted = !chat.isMuted\n      this.$u.toast(chat.isMuted ? '已开启免打扰' : '已关闭免打扰')\n    },\n\n    deleteChat(chat) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这个聊天吗？',\n        success: (res) => {\n          if (res.confirm) {\n            const index = this.chatList.findIndex(item => item.id === chat.id)\n            if (index > -1) {\n              this.chatList.splice(index, 1)\n              this.$u.toast('删除成功')\n            }\n          }\n        }\n      })\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: '/pagesSub/social/search/chat'\n      })\n    },\n\n    startNewChat() {\n      uni.navigateTo({\n        url: '/pagesSub/social/contact/select'\n      })\n    },\n\n    goSystemMessages() {\n      uni.navigateTo({\n        url: '/pagesSub/social/message/system'\n      })\n    },\n\n    goLikeMessages() {\n      uni.navigateTo({\n        url: '/pagesSub/social/message/likes'\n      })\n    },\n\n    goFollowMessages() {\n      uni.navigateTo({\n        url: '/pagesSub/social/message/followers'\n      })\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log('强制刷新消息页数据...')\n      this.chatList = []\n      this.loadUnreadCounts()\n      this.loadChatList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.message-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 100px;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 1px solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n}\n\n.title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.header-actions {\n  display: flex;\n  gap: 16px;\n}\n\n.quick-actions {\n  margin-top: calc(44px + var(--status-bar-height));\n  background: #fff;\n  padding: 16px;\n  display: flex;\n  justify-content: space-around;\n  border-bottom: 8px solid #f8f9fa;\n}\n\n.action-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n}\n\n.action-icon {\n  width: 44px;\n  height: 44px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8px;\n}\n\n.action-icon.system {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.action-icon.like {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.action-icon.follow {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.action-text {\n  font-size: 12px;\n  color: #666;\n}\n\n.unread-badge {\n  position: absolute;\n  top: -2px;\n  right: 8px;\n  background: #ff4757;\n  color: #fff;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  min-width: 16px;\n  text-align: center;\n}\n\n.chat-list {\n  background: #fff;\n}\n\n.chat-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.chat-item:last-child {\n  border-bottom: none;\n}\n\n.chat-avatar {\n  position: relative;\n  margin-right: 12px;\n}\n\n.online-dot {\n  position: absolute;\n  bottom: 2px;\n  right: 2px;\n  width: 12px;\n  height: 12px;\n  background: #52c41a;\n  border: 2px solid #fff;\n  border-radius: 6px;\n}\n\n.chat-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.chat-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 4px;\n}\n\n.chat-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.chat-time {\n  font-size: 12px;\n  color: #999;\n}\n\n.chat-preview {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.message-preview {\n  flex: 1;\n  min-width: 0;\n}\n\n.preview-text {\n  font-size: 14px;\n  color: #666;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: block;\n}\n\n.chat-status {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.unread-count {\n  background: #ff4757;\n  color: #fff;\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 10px;\n  min-width: 18px;\n  text-align: center;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80px 20px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #999;\n  margin: 16px 0 8px;\n}\n\n.empty-desc {\n  font-size: 14px;\n  color: #ccc;\n}\n\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.loading-text {\n  margin-left: 8px;\n  color: #999;\n  font-size: 14px;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752827020003\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/message/index.vue'\ncreatePage(Page)"], "sourceRoot": ""}