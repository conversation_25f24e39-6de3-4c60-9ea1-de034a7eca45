<template>
	<view class="index" :style="'background:' + pageBj">
		<!-- <u-navbar :is-back="false" title="FOX舞蹈" :background="{ background:'none' }"
			:border-bottom="false" :title-color="navBg==1?'#fff':'#fff' " title-size="32">
		</u-navbar> -->
		 <!-- :style="'margin-top:-'+navHeight+'px'" -->
		<view class="ind_ban">
			<view class="ind_swiper_bj">
				<!-- <image :src="bannerLists[swiperIndex].bigimg" mode="scaleToFill"></image> -->
				<image  @click="goBannerTap(item.jump_address)" v-for="(item,index) in topBanBj" :key="index" :src="imgbaseUrl + item.carousel_background" :style="topBanIndex == index ? 'opacity:1;' : 'opacity:0;'" mode="aspectFill"></image>
			</view>
			<swiper circular="true" @change="topChange">
				<swiper-item v-for="(item,index) in topBan" :key="index">
					<view><image :src="imgbaseUrl + item.carousel" mode="aspectFill"  @click="goBannerTap(item.jump_address)"></image></view>
				</swiper-item>
			</swiper>
			<!-- <view class="ind_ban_box" :style="'box-shadow:0 0 16rpx '+pageBj+';background:'+pageBj"></view> -->
			<view class="ind_ban_box" :style="'background: linear-gradient(to top, '+pageBj+' 0%, transparent 100%)'"></view>
		</view>
		
		<view class="ind_one">
			<view style="width: 100%;height:auto;overflow:hidden;"></view>
			<view class="ind_one_tx">
				<image src="/static/images/toux.png" mode="aspectFill" v-if="!isLogined"></image>
				<image :src="userInfo.avatar == '' ? '/static/images/toux.png' : imgbaseUrl + userInfo.avatar"
					mode="aspectFill" v-if="isLogined"></image>
			</view>
			<view class="ind_one_a"><view>HI，{{isLogined ? (userInfo.nickname == '' ? '微信用户' : userInfo.nickname) : '请先登录'}}</view><text>{{userInfo.level_name}}</text></view>
			<view class="ind_one_b">
				<!-- <view class="ind_one_b_li">
					<view class="ind_one_b_li_a">当前积分</view>
					<view class="ind_one_b_li_b">{{isLogined ? userInfo.score*1 : 0}}</view>
					<view class="xian"></view>
				</view> -->
				<view class="ind_one_b_li" @click="navTo('/pages/prizedraw/prizedraw')" style="width: 50%;">
					<view class="ind_one_b_li_a">抽奖次数</view>
					<view class="ind_one_b_li_b">{{isLogined ? userInfo.luck_draw_frequency*1 : 0}}次</view>
					<view class="xian"></view>
				</view>
				<view class="ind_one_b_li" @click="navTo('/pages/prizedraw/prizedraw')" style="width: 50%;">
					<view class="ind_one_b_li_a">惊喜抽奖</view>
					<view class="ind_one_b_li_b">可抽奖</view>
				</view>
			</view>
			<view class="ind_one_c">
				<view class="ind_one_c_l" @click="navTo('/pages/prizedraw/dengji')">经验值<image src="/static/images/icon72.png"></image></view>
				<view class="ind_one_c_c"><text>{{userInfo.experience_value}}</text>/{{userInfo.upgrade}}</view>
				<view class="ind_one_c_r"><view :style="'width:'+(userInfo.experience_value*1)/userInfo.upgrade*100+'%;'"></view></view>
			</view>
		</view>

		
		<view class="ind_two">
			<view class="ind_two_li" @click="navTo('/pages/index/teacherDetail',1)">
				<image src='/static/images/icon73.png'></image>
				<view class="ind_two_li_a">导师介绍</view>
				<view class="ind_two_li_b">130+位广州实力导师</view>
				<text></text>
			</view>
			<view class="ind_two_li" @click="navTo('/pages/index/foxDetail',1)">
				<image src='/static/images/icon74.png'></image>
				<view class="ind_two_li_a">FOX介绍</view>
				<view class="ind_two_li_b">F5.周年品牌沉跑</view>
				<text></text>
			</view>
			<view class="ind_two_li" @click="navTo('/pages/index/storesDetail?id=' + userInfo.store.id,1)">
				<image src='/static/images/icon75.png'></image>
				<view class="ind_two_li_a">门店介绍</view>
				<view class="ind_two_li_b">灵动舞之坊</view>
			</view>
		</view>
		
		<view class="ind_thr">
			<view class="ind_thr_l"><view>当前门店</view><text>ADDRE55</text><text class="xian"></text></view>
			<view class="ind_thr_c">
				<!-- <image src="/static/images/icon76.png"></image> -->
				<u-icon name="map-fill" :color="pageBj" size="40"></u-icon>
				<view :style="'color:'+pageBj">{{userInfo.store.name}}</view>
				<text class="xian"></text>
			</view>
			<view class="ind_thr_r" @click="navTo('/pages/index/switchStores')">
				<view class="ind_thr_r_a"><image src="/static/images/icon77.png"></image>切换门店<image src="/static/images/icon77.png"></image></view>
				<view class="ind_thr_r_b">在此切换门店</view>
			</view>
		</view>
		
		<view class="ind_fou">
			<image src="/static/images/icon78.png" class="ind_fou_l"></image>
			<view class="ind_fou_r">
				<uni-notice-bar scrollable single :text="userInfo.notice" background-color="transparent" color="#333"
					:single="true"></uni-notice-bar>
				<!-- <u-notice-bar mode="horizontal" :list="notice_text"></u-notice-bar> -->
			</view>
		</view>
		
		<view class="ind_fiv" v-if="kczxBan.length > 0">
			<view class="ind_fiv_t"><view>最新课程咨询</view><text>不定期福利等你领取</text></view>
			<view class="ind_fiv_b">
				<swiper circular="true" @change="kczxChange">
					<swiper-item v-for="(item,index) in kczxBan" :key="index">
						<image :src="imgbaseUrl + item.carousel" mode="aspectFill" @click="goBannerTap(item.jump_address)"></image>
					</swiper-item>
				</swiper>
				<view class="ind_fiv_b_yd"><text v-for="(item,index) in kczxBan" :class="kczxIndex == index ? 'ind_fiv_b_yd_ac' : ''"></text></view>
			</view>
		</view>
		
		<view class="" style="height: 300rpx;"></view>
		

		<view class="use">
			<view class="share">
				<image src="/static/images/index_share.png" mode=""></image>
				<button open-type="share"></button>
			</view>
			<!-- <view class="concat" @click="showConcat = false"> -->
			<view class="concat" @click="navTo('/pages/mine/tzgl')">
				<image src="/static/images/index_concat_kf1.png" mode="scaleToFill"></image>
				<view><text>消息</text><text>推送</text></view>
			</view>
		</view>
		
		

		<u-popup v-model="showConcat" border-radius="20" mode="center">
			<view class="concat_box">
				<view class="concat_box_title flex col-top row-between">
					<view class="">
						选择门店
					</view>
					<image src="/static/images/popup_close.png" mode="scaleToFill" @click="showConcat = false"></image>
				</view>
				<view class="concat_box_list">
					<view class="concat_box_li flex row-between" v-for="(item,index) in storesLists" :key="index">
						<view class="concat_box_li_l flex">
							<view class="concat_box_li_l_img">
								<image :src="imgbaseUrl + item.image" mode="scaleToFill"></image>
							</view>
							<view class="concat_box_li_l_name">
								<view class="text">{{item.name}}<view class="title_bottom"></view></view>
								
							</view>
						</view>
						<view class="concat_box_li_r btn" @click="navTo('/pages/index/service?id=' + item.id)">联系客服</view>
					</view>
					<!-- <view class="concat_box_li flex row-between" v-for="(item,index) in storesLists" :key="index">
						<view class="concat_box_li_l flex">
							<view class="concat_box_li_l_img">
								<image :src="imgbaseUrl + item.image" mode="scaleToFill"></image>
							</view>
							<view class="concat_box_li_l_name">
								<view class="text">{{item.name}}</view>
								<view class="title_bottom"></view>
							</view>
						</view>
						<view class="concat_box_li_r btn" @click="navTo('/pages/index/service',1)">
							联系客服
						</view>
					</view>
					<view class="concat_box_li flex row-between" v-for="(item,index) in storesLists" :key="index">
						<view class="concat_box_li_l flex">
							<view class="concat_box_li_l_img">
								<image :src="imgbaseUrl + item.image" mode="scaleToFill"></image>
							</view>
							<view class="concat_box_li_l_name">
								<view class="text">{{item.name}}</view>
								<view class="title_bottom"></view>
							</view>
						</view>
						<view class="concat_box_li_r btn" @click="navTo('/pages/index/service',1)">
							联系客服
						</view>
					</view> -->
				</view>
			</view>
		</u-popup>

		<tabbar ref="tabbar" :current="0"></tabbar>
		
		<view class="lodingg" v-if="loding"></view>
	</view>
</template>


<script>
import {
	userInfoApi,
	homeDataApi,
	storeListsApi,
	changePidApi,
	teacherApi,
	upImg,
	getContractApi
} from '@/config/http.achieve.js'
import {
		authIsPass
	} from '@/utils/auth.js'
import tabbar from '@/components/tabbar.vue'
export default {
	components: {
		tabbar,
	},
	data() {
		return {
			isLogined:true,
			navBg: '',
			notice_text: [],
			showConcat: false, //联系客服弹窗
			imgbaseUrl:'',//图片地址
			userInfo:{
				avatar: "",
				frequency: 0,
				nickname: "",
				notice:'',
				score: 0,
				poster:[],
				store:{address:'',name:'',id:0},
				luck_draw_frequency:0,
				level_name:'LV0',
				experience_value:0,
				upgrade:0,
			},
			storesLists:[],
			kczxBan:[],//底部轮播图
			kczxIndex:0,//轮播图索引
			
			topBan:[],//顶部轮播图
			topBanBj:[],//顶部轮播图背景
			topBanIndex:0,//顶部轮播图索引
			navHeight:0,
			pageBj:'#fff',
			cssVariables:{},
			
			image2:'',
			loding:true
		}
	},
	onShow() {
		// this.cssVariables = {'--main-color':uni.getStorageSync('storeInfo') ? uni.getStorageSync('storeInfo').button : '#131315'}
		// const page = this.$scope || this.$mp.page;
		// console.log(this.cssVariables,'this.cssVariables',page.$el)
		// uni.setStorageSync('token','49e2e458-2c96-489d-a19a-9aacf70b3c13')
		this.$refs.tabbar.setColor();
		this.imgbaseUrl = this.$baseUrl;
		this.isLogined = uni.getStorageSync('token') ? true : false;
		if(this.isLogined){
			// this.userData();//个人信息
			this.getContractData();//获取未签署的合同
		}else{
			this.loding = false;
		}
		if(uni.getStorageSync('postion')){
			this.homeData();//首页数据
		}
		uni.hideTabBar()
		if(this.isLogined && uni.getStorageSync('pid')){
			this.changePidData();//更改邀请人
		}
	},
	onPageScroll(e) {
		const top = uni.upx2px(100)
		const {
			scrollTop
		} = e
		let percent = scrollTop / top > 1 ? 1 : scrollTop / top
		this.navBg = percent
	},
	onLoad(options) {
		// this.teacherData();//老师
		uni.setStorageSync('qhwc',1)
		this.navHeight = (uni.getSystemInfoSync().statusBarHeight + 44);
		if (!uni.getStorageSync('postion')) {
			//this.getPosition()
		} else {
			// this.areaInfo = uni.getStorageSync('postion')
		}
		this.getPosition()
		
		this.storeData();//门店列表
		uni.hideTabBar();
		
		
		if(options.pid){
			uni.setStorageSync('pid',options.pid);
			if(this.isLogined){
				this.changePidData();//更改邀请人
			}
			console.log('options进去了？userid',uni.getStorageSync('pid'))
		}
		/*if(options.q){
			const scene = decodeURIComponent(options.q);
			uni.setStorageSync('pid',scene.split('=')[1])
		}*/
		
	},
	methods: {
		//获取未签署的合同
		getContractData(){
			var that = this;
			getContractApi({
				
			}).then(res => {
				console.log('获取未签署的合同',res)
				if (res.code == 1) {
					// res.data = 22;
					that.loding = res.data ? true : false;
					if(res.data){
						uni.reLaunch({
							url:'/pages/index/signing?id=' + res.data
						})
					}
					/*uni.reLaunch({
						url:'/pages/index/signing'
					})*/
				}
			})
		},
		//签名提交
		qmTap(){
			this.sctxTap(this.image2)
		},
		toPop1(){
			this.$refs.signature1.toPop()
		},
		//上传头像
		sctxTap(tempFilePaths){
			console.log(tempFilePaths,'tempFilePaths')
			var that = this;
			uni.showLoading({
				title:'加载中'
			})
			upImg(tempFilePaths, 'file',{driver:'cos'}).then(ress => {
				console.log('上传图片',ress)
				if (ress.code == 1) {
					uni.hideLoading();
					// that.avatar = ress.data.file.url
				}
			})
		},
		//跳转tabview
		goBannerTap(url){
			if(url != ''){
				if (url.includes("http")) {
					uni.navigateTo({
						url: '/pages/webView/webView?url=' + url
					})
				} else {
					uni.navigateTo({
						url: url
					})
				}
			}
		},
		//更改邀请人
		changePidData(){
			changePidApi({
				pid:uni.getStorageSync('pid')
			}).then(res => {
				console.log('更改邀请人',res)
				if (res.code == 1) {
				}
			})
			
		},
		//顶部轮播图
		topChange(e){
			this.topBanIndex = e.detail.current;
			// this.pageBj = e.detail.current == 0 || e.detail.current == 2  ? '#FADAFF' : '#EDB1D0'
		},
		//底部轮播图
		kczxChange(e){
			this.kczxIndex = e.detail.current;
		},
		//老师-本地缓存
		teacherData(){
			let that = this;
			teacherApi({}).then(res => {
				console.log('老师',res)
				if (res.code == 1) {
					uni.hideLoading();
					// uni.setStorageSync('teacherlist',res.data.data);
					// that.storesLists = res.data.data;
				}
			})
			
		},
		//门店列表
		storeData(){
			let that = this;
			storeListsApi({
				type:1,
				limit:9999,
			}).then(res => {
				console.log('门店列表',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.storesLists = res.data.data;
				}
			})
			
		},
		//打开图片
		openImg(idx, imgs) {
			let arr = []
			for (let i = 0; i < imgs.length; i++) {
				arr.push(this.imgbaseUrl + imgs[i])
			}
			console.log(idx, imgs);
			uni.previewImage({
				current: idx,
				urls: arr
			})
		},
		//首页数据
		homeData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			homeDataApi({
				longitude:uni.getStorageSync('postion').longitude,
				latitude:uni.getStorageSync('postion').latitude,
				store_id:uni.getStorageSync('storeInfo') ? uni.getStorageSync('storeInfo').id : 0
			}).then(res => {
				if (res.code == 1) {
					console.log('首页',res);
					// uni.setStorageSync('storeInfo',res.data.store);
					// res.data.store.carousel = ['/storage/default/20250208/微信截图_20250208152c336795f3a876e19e0480fdee90d32329932868.png','/storage/default/20250122/微信图片_2025012222d3cc6c1e11de20f706d6f9f4382f51c5fff11900.jpg']
					// res.data.store.carousel_background = ['/storage/default/20250208/icon701013d4966a6214114e903c141e4155dc8fe24204.png','/storage/default/20250122/微信图片_2025012222d3cc6c1e11de20f706d6f9f4382f51c5fff11900.jpg']
					this.pageBj = res.data.store.background;//首页背景图
					this.kczxBan = res.data.course_carousel;//底部课程轮播图
					this.topBan = res.data.carousel;//顶部轮播
					this.topBanBj = res.data.carousel;
					uni.setStorageSync('storeInfo',res.data.store);
					that.userInfo = res.data;
					that.$refs.tabbar.setColor(res.data.ecology);
					/*if(uni.getStorageSync('storeInfo')){
						that.userInfo = res.data;
						this.userInfo.store = uni.getStorageSync('storeInfo');
						uni.setStorageSync('server_token',res.data.server_token)
					}else{
						uni.setStorageSync('storeInfo',res.data.store);
						that.userInfo = res.data;
					}*/
					
					uni.hideLoading();
				}
			})
		},
		//获取自身位置
		async getPosition() {
			
			let that = this
			const flag = await authIsPass('scope.userLocation')
			if (this.IsOpenMap == false) {
				return
			}
			console.log(flag);
			if (!flag) {
				this.IsOpenMap = false
				uni.authorize({
					scope: 'scope.userLocation',
					fail: (res) => {
						uni.showModal({
							title: '使用该功能必须允许位置服务，是否重新授权？',
							showCancel: false,
							success: ({
								confirm
							}) => {
								if (confirm) {
									uni.openSetting({
										success() {
											uni.getLocation({
												type: 'wgs84',
												success: function(res) {
													console.log('定位1',res)
													let data = {
														latitude:res.latitude,
														longitude:res.longitude
													}
													uni.setStorageSync('postion',data)
													that.homeData();
												},
												fail: function(err) {
													
												}
											});
											console.log('开启权限成功')
										},
										fail() {
											console.log('开启权限失败')
										},
									})
								}
							},
						})
					},
					success: () => {
						uni.getLocation({
							type: 'wgs84',
							success: function(res) {
								console.log('定位2',res)
								let data = {
									latitude:res.latitude,
									longitude:res.longitude
								}
								uni.setStorageSync('postion',data)
								that.homeData();
							},
							fail: function(err) {
								
							}
						});
					},
				})
			} else {
				uni.getLocation({
					type: 'wgs84',
					success: function(res) {
						console.log('定位3',res)
						let data = {
							latitude:res.latitude,
							longitude:res.longitude
						}
						uni.setStorageSync('postion',data)
						that.homeData();
					},
					fail: function(err) {
					}
				});
			}
		},
		navTo(url,ismd){
			if(ismd){
				uni.navigateTo({
					url:url
				});
				return false;
			}
			var that = this;
			if(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				//setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				//},1000)
			}else{
				uni.navigateTo({
					url:url
				})
			}
		},
	},
	// 分享到微信好友 
	onShareAppMessage() {
	  var that = this;
	  return {
		title:'FOX舞蹈，快来和我一起体验吧，超多福利等你来拿!',
		path: '/pages/index/index?pid=' + uni.getStorageSync('userid') ? uni.getStorageSync('userid') : 0,
		// imageUrl:that.bannerLists[0].images,
	  }
	}
}
</script>

<style lang="scss">
	
	page {
		padding-bottom: 0;
		background:#FADAFF;
		
	}

	.index {
		// background-image: url(/static/login/login_top_bgi.png);
		-background-image: url(https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/userreport/login_top_bgi.png);
		
		background-size: 100% auto;
		background-repeat: no-repeat;
		min-height: 100vh;
		transition:0.8s;
	}

	.use {
		position: fixed;
		right: 14rpx;
		bottom: 30vh;
		z-index: 11;
		.share{
			position: relative;
			button{
				display:block;
				width: 100%;
				height: 100%;
				position:absolute;
				top: 0;left:0;
				opacity: 0;
			}
		}
		.concat{
			position: relative;
			view{
				width:100%;
				height:82rpx;
				overflow:hidden;
				display: flex;
				align-items: center;
				justify-content:center;
				flex-direction: column;
				position:absolute;
				top:0;left:0;
				text{
					display:block;
					font-size:22rpx;
					color:#333;
					letter-spacing:1px;
					position:relative;
					left:1px;
				}
			}
			button{
				display:block;
				width: 100%;
				height: 100%;
				position:absolute;
				top: 0;left:0;
				opacity: 0;
			}
		}
		image {
			width: 106rpx;
			height: 106rpx;

			&:nth-child(1) {
				margin-bottom: 34rpx;
			}
		}
	}

	.userInfo {
		margin: 494rpx auto 0;
		padding: 42rpx 32rpx 32rpx;
		width: 700rpx;
		background: #FFFFFF;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		position: relative;

		.userInfo_t {
			.userInfo_t_l {
				padding-left: 20rpx;

				.userInfo_t_l_t {
					font-family: Maoken Glitch Sans;
					font-size: 26rpx;
					color: #333333;
					line-height: 30rpx;
				}

				.userInfo_t_l_d {
					margin-top: 12rpx;
					font-weight: Medium;
					font-size: 28rpx;
					color: #333333;
					line-height: 33rpx;
				}
			}

			.userInfo_t_r {
				position: absolute;
				right: 52rpx;
				top: -44rpx;

				image {
					width: 140rpx;
					height: 140rpx;
					border-radius: 50%;
					border: 4rpx solid #FFFFFF;
					background-color: pink;
				}
			}
		}

		.userInfo_count {
			margin-top: 26rpx;
			padding-bottom: 24rpx;
			border-bottom: 2rpx solid rgba(160, 160, 160, 0.2);

			.userInfo_count_li {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.userInfo_count_li_num {
					font-weight: 500;
					font-size: 40rpx;
					color: #333333;
					line-height: 47rpx;

					text {
						font-size: 26rpx;
						line-height: 33rpx;
					}
				}

				.userInfo_count_li_text {
					margin-top: 26rpx;
					font-size: 26rpx;
					color: #333333;
					line-height: 30rpx;
				}
			}
		}

		.userInfo_shop {
			padding-top: 32rpx;

			.userInfo_shop_l {
				.userInfo_shop_l_t {
					font-weight: 500;
					font-size: 26rpx;
					color: #333333;
					line-height: 30rpx;
					display: flex;
					align-items: center;
					image {
						// margin-left: 24rpx;
						width: 10.46rpx;
						height: 16rpx;
					}
				}

				.userInfo_shop_l_d {
					margin-top: 12rpx;
					font-size: 26rpx;
					color: #333333;
					line-height: 30rpx;
				}
			}

			.userInfo_shop_r {

				.btn {
					width: 144rpx;
					font-size: 26rpx;
					height: 56rpx;
					background: #945048;
					border-radius: 124rpx 124rpx 124rpx 124rpx;
				}
			}
		}
	}

	.notice {
		margin: 24rpx auto 0;
		width: 670rpx;
		height: 80rpx;
		background: #fff;
		padding: 0 26rpx;
		height: 72rpx;
		background: #FFFFFF;
		border-radius: 90rpx 90rpx 90rpx 90rpx;

		.notice_l {
			image {
				width: 32rpx;
				height: 32rpx;
			}
		}

		.notice_r {
			font-size: 26rpx;
			color: #333333;
			line-height: 30rpx;

			/deep/ .uni-noticebar {
				margin-bottom: 0 !important;
			}

		}
	}

	.nav {
		padding: 26rpx 22rpx 0 26rpx;

		.title_bottom {
			position: absolute;
			left: 0;
			bottom: 6rpx;
			width: 126rpx;
			height: 10rpx;
			background: linear-gradient(90deg, #945048 28%, rgba(255, 255, 255, 0) 100%);
		}

		.nav_l {
			width: 362rpx;
			height: 308rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;

			.nav_l_t {
				position: relative;

				.nav_l_text {
					position: relative;
					z-index: 11;
					font-weight: 600;
					font-size: 32rpx;
					color: #333333;
					line-height: 38rpx;
					
				}
			}

			.nav_l_d {
				image {
					width: 202rpx;
					height: 202rpx;
					margin: 30rpx auto 0;
				}
			}
		}

		.nav_r {
			width: 314rpx;
			height: 308rpx;

			.nav_r_t {
				width: 314rpx;
				height: 136rpx;
				background: #FFFFFF;
				border-radius: 20rpx 20rpx 20rpx 20rpx;

				.nav_r_t_text {
					position: relative;

					.text {
						position: relative;
						z-index: 11;
						font-family: Maoken Glitch Sans;
						font-weight: 600;
						font-size: 32rpx;
						color: #333333;
						line-height: 38rpx;
					}
				}

				.nav_r_t_img {
					image {
						width: 108rpx;
						height: 108rpx;
						margin-left: 26rpx;
					}
				}
			}
		}
	}

	.poster {
		image {
			width: 698rpx;
			height: 238rpx;
			margin: 20rpx auto 0;

			&:nth-child(1) {
				margin-top: 28rpx;
			}
		}
	}

	.concat_box {
		padding: 26rpx 66rpx;
		width: 662rpx;
		height: 770rpx;
		height:auto;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		overflow: auto;
		.concat_box_title {
			padding-bottom: 26rpx;
			border-bottom: 2rpx solid rgba(148, 80, 72, 0.2);

			view {
				font-size: 32rpx;
				color: #333333;
				line-height: 38rpx;
			}

			image {
				width: 40rpx;
				height: 40rpx;

			}
		}
		.concat_box_list{
			-height:calc(100% - 100rpx);
			-overflow: auto;
			.concat_box_li{
				padding: 14rpx 30rpx;
				padding: 14rpx 0;
				margin: 26rpx 0  0;
				.concat_box_li_l{
					.concat_box_li_l_img{
						image{
							width: 108rpx;
							height: 108rpx;
						}
					}
					.concat_box_li_l_name{
						width:200rpx;
						position: relative;
						margin-left: 40rpx;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp:1;
						overflow: hidden;
						margin-right: 20rpx;
						.text {
							position: relative;
							z-index: 11;
							color: #333333;
							font-family: Maoken Glitch Sans;
							font-size: 26rpx;
							line-height: 30rpx;
							float:left;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp:1;
							overflow: hidden;
						}
						.title_bottom {
							position: absolute;
							left: 0;
							bottom: 6rpx;
							-width: 126rpx;
							height: 10rpx;
							background: linear-gradient(90deg, #945048 28%, rgba(255, 255, 255, 0) 100%);
							float:left;
							width: 100%;
							z-index: -1;
						}
					}
				}
				.concat_box_li_r{
					width: 152rpx;
					
					height: 56rpx;
					background: #945048;
					border-radius: 92rpx 92rpx 92rpx 92rpx;
					font-size: 26rpx;
				}
			}
		}
	}
</style>