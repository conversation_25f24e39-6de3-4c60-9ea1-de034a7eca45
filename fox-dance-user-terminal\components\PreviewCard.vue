<template>
  <view class="product-card"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        :animation="animationData"
        :style="cardStyle">
    <view class="product-image-container">
      <image class="product-image" :src="image" mode="aspectFill" :animation="imageAnimationData"
             :style="imageStyle"></image>
    </view>
    <view class="product-info">
      <text class="product-tag">{{ tag || 'Fox Dance Studio' }}</text>
      <text class="product-name">{{ title || '敬请期待' }}</text>
    </view>

    <!-- 下拉提示区域 -->
    <view class="swipe-hint">
      <text class="swipe-hint-text">下拉查看详情</text>
      <view class="arrow-container">
        <image class="arrow-down" src="/static/icon/下拉.svg" mode="aspectFit" :class="{'pulse': isPulling}"></image>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PreviewCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    tag: {
      type: String,
      default: ''
    },
    image: {
      type: String,
      default: ''
    },
    targetPage: {
      type: String,
      default: '/pagesSub/switch/vote' // 默认导航到vote页面
    }
  },
  computed: {
    // 使用计算属性替代内联样式
    cardStyle() {
      if (!this.animationData && this.pullDistance > 0) {
        const scale = 1 + (this.pullDistance / this.pullThreshold) * 0.1;
        const opacity = 1 - (this.pullDistance / this.pullThreshold) * 0.3;
        return {
          transform: `translateY(${this.pullDistance}px) scale(${scale})`,
          opacity: opacity
        }
      }
      return {}
    },
    imageStyle() {
      if (!this.imageAnimationData && this.imageScale > 1) {
        return {
          transform: `scale(${this.imageScale})`
        }
      }
      return {}
    }
  },
  data() {
    return {
      touchStartY: 0,
      isPulling: false,
      pullThreshold: 80, // 降低触发导航的下拉阈值，原值为150
      pullDistance: 0,
      animationData: {}, // 动画实例数据对象
      animation: null,    // 动画实例
      navigating: false,   // 是否正在导航中
      imageAnimationData: {}, // 图片动画数据对象
      imageScale: 1.0 // 图片缩放比例
    }
  },
  // 在组件创建时初始化动画实例
  created() {
    // 创建动画实例，确保阴影效果能够正确跟随
    try {
      if (typeof uni.createAnimation === 'function') {
        this.animation = uni.createAnimation({
          duration: 300,
          timingFunction: 'ease-out',
          delay: 0,
          transformOrigin: '50% 50% 0'
        });
      }
    } catch (e) {
      console.error('创建动画实例失败:', e);
    }
  },
  methods: {
    handleTouchStart(e) {
      // 如果正在导航，不处理触摸事件
      if (this.navigating) return;

      // 获取触摸位置，兼容不同平台事件对象结构
      let startY;
      if (e.touches && e.touches[0]) {
        startY = e.touches[0].clientY;
      } else if (e.changedTouches && e.changedTouches[0]) {
        startY = e.changedTouches[0].clientY;
      } else if (e.detail) {
        startY = e.detail.y;
      } else {
        return; // 无法获取触摸位置，不处理
      }

      this.touchStartY = startY;
      this.isPulling = false;
      this.pullDistance = 0;
      this.imageScale = 1.0;

      try {
        // 重置动画
        if (this.animation && typeof this.animation.translateY === 'function') {
          this.animation.translateY(0).scale(1).opacity(1).step({duration: 0});
          this.animationData = this.animation.export();
        }
      } catch (e) {
        console.error('重置动画失败:', e);
      }
    },

    handleTouchMove(e) {
      // 如果正在导航，不处理触摸事件
      if (this.navigating) return;

      // 获取触摸位置，兼容不同平台事件对象结构
      let currentY;
      if (e.touches && e.touches[0]) {
        currentY = e.touches[0].clientY;
      } else if (e.changedTouches && e.changedTouches[0]) {
        currentY = e.changedTouches[0].clientY;
      } else if (e.detail) {
        currentY = e.detail.y;
      } else {
        return; // 无法获取触摸位置，不处理
      }

      const diffY = currentY - this.touchStartY;

      // 只有下拉时才有效
      if (diffY > 0) {
        this.isPulling = true;
        // 计算下拉距离，使用衰减函数使拉动感觉更自然
        this.pullDistance = Math.min(diffY * 0.5, this.pullThreshold);

        // 使用动画实例设置transform和opacity
        let scaleValue = 1.0;
        let opacityValue = 1.0;
        let imageScale = 1.0;

        // 当下拉超过阈值的一定比例时，开始缩放和改变透明度
        if (this.pullDistance > this.pullThreshold * 0.5) { // 降低动画触发阈值，原值为0.7
          const progress = (this.pullDistance - this.pullThreshold * 0.5) / (this.pullThreshold * 0.5);
          scaleValue = 1 + progress * 0.1;
          opacityValue = 1 - progress * 0.3;

          // 计算图片缩放比例，与卡片保持一致
          imageScale = scaleValue;

          // 应用图片缩放效果
          this.setImageTransform(imageScale);
        }

        try {
          // 检查动画API是否可用
          if (this.animation && typeof this.animation.translateY === 'function') {
            // 设置动画并导出 - 确保z-index在变换过程中保持较高值
            this.animation
                .translateY(this.pullDistance)
                .scale(scaleValue)
                .opacity(opacityValue)
                .step({duration: 0});
            this.animationData = this.animation.export();
          }
        } catch(e) {
          console.error('应用动画失败:', e);
        }

        // 阻止默认滚动
        e.preventDefault && e.preventDefault();

        // 通知父组件我们正在下拉，可能需要增加容器高度
        this.$emit('pulling', {
          distance: this.pullDistance,
          scale: scaleValue,
          imageScale: imageScale
        });
      }
    },

    // 添加设置图片transform的方法
    setImageTransform(scale) {
      try {
        // 检查动画API是否存在
        if (typeof uni.createAnimation === 'function') {
          // 创建一个动画实例专门用于图片
          const imageAnimation = uni.createAnimation({
            duration: 0,
            timingFunction: 'ease-out',
            transformOrigin: '50% 50% 0'
          });

          // 应用缩放动画，但保持图片不超出容器
          imageAnimation.scale(scale).step();

          // 将动画应用到图片上
          this.$nextTick(() => {
            this.imageAnimationData = imageAnimation.export();
          });
        } else {
          // 如果动画API不可用，可以使用样式直接设置
          this.imageScale = scale;
        }
      } catch (e) {
        console.error('创建图片动画失败:', e);
      }
    },

    handleTouchEnd() {
      // 如果正在导航，不处理触摸事件
      if (this.navigating || !this.isPulling) return;

      try {
        if (this.pullDistance >= this.pullThreshold * 0.6) {
          // 触发导航到vote页面
          console.log('下拉距离满足条件，准备导航');
          this.navigateToVote();
        } else {
          // 没有达到阈值，恢复原位
          this.resetPosition();
        }

        // 无论如何，都通知父组件触摸已结束
        this.$emit('touchEnded');
      } catch (e) {
        console.error('触摸结束处理失败:', e);
        // 发生错误时重置位置
        this.resetPosition();
      }
    },

    resetPosition() {
      // 设置恢复原位的动画
      this.animation.translateY(0).scale(1).opacity(1).step();
      this.animationData = this.animation.export();

      // 重置图片缩放
      this.resetImageScale();

      // 动画结束后重置状态
      setTimeout(() => {
        this.isPulling = false;
        this.pullDistance = 0;
      }, 300);
    },

    resetImageScale() {
      // 创建动画恢复到原始大小
      const imageAnimation = uni.createAnimation({
        duration: 300,
        timingFunction: 'ease-out',
        transformOrigin: '50% 50% 0'
      });

      imageAnimation.scale(1).step();

      // 应用动画
      this.$nextTick(() => {
        this.imageAnimationData = imageAnimation.export();
      });
    },

    navigateToVote() {
      // 标记正在导航中
      this.navigating = true;

      try {
        // 创建缩放淡出的动画
        if (this.animation && typeof this.animation.translateY === 'function') {
          this.animation.translateY(this.pullDistance + 30).scale(1.2).opacity(0).step();
          this.animationData = this.animation.export();
        }

        // 重置图片缩放，避免导航后图片还是放大状态
        this.resetImageScale();

        // 等待动画完成后导航
        setTimeout(() => {
          // 正常情况下使用路由导航
          const navigateTo = () => {
            console.log('准备导航到:', this.targetPage);
            uni.navigateTo({
              url: this.targetPage, // 使用prop传入的目标页面
              success: () => {
                console.log('导航成功');
                // 导航成功后重置
                setTimeout(() => {
                  this.resetAfterNavigation();
                }, 100);
              },
              fail: (err) => {
                console.error('导航失败', err);
                // 导航失败也需要重置
                this.resetAfterNavigation();
              }
            });
          };

          // 执行导航
          navigateTo();
        }, 300);
      } catch (e) {
        console.error('导航过程中出错:', e);
        // 出错时也进行导航
        uni.navigateTo({
          url: this.targetPage
        });
      }
    },

    resetAfterNavigation() {
      // 重置动画和状态
      this.animation.translateY(0).scale(1).opacity(1).step({duration: 0});
      this.animationData = this.animation.export();

      this.isPulling = false;
      this.pullDistance = 0;
      this.navigating = false;
    }
  },
  beforeDestroy() {
    // 重置动画
    if (this.animation) {
      this.animation.translateY(0).scale(1).opacity(1).step({duration: 0});
      this.animationData = this.animation.export();
    }

    // 重置状态
    this.isPulling = false;
    this.pullDistance = 0;
    this.navigating = false;
  }
}
</script>

<style scoped>
.product-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 66rpx;
  overflow: visible; /* 改为visible使阴影可以超出容器 */
  background-color: #fff;
  position: relative;
  /* 添加阴影效果 */
  box-shadow: 0 25rpx 50rpx rgba(0,0,0,0.25);
  /* 确保阴影能在动画过程中正确显示 */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  /* 增加z-index确保下拉时不被其他元素遮挡 */
  z-index: 1000;
}

.product-image-container {
  width: 85%;
  height: 45%;
  margin: 49rpx auto 0;
  overflow: hidden;
  position: relative;
  border-radius: 66rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-image {
  width: 100%; /* 设置为100%宽度填满容器 */
  height: 100%;
  object-fit: cover;
  border-radius: 66rpx;
  margin: 0;
  background-color: #fff;
  transition: transform 0.1s ease;
  transform-origin: center center;
}

.product-info {
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-name {
  font-size: 54rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.product-tag {
  font-size: 23rpx;
  font-weight: 500;
  color: #666;
  padding: 20rpx;
}

/* 下拉提示样式 */
.swipe-hint {
  margin-top: auto;
  margin-bottom: 56rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.swipe-hint-text {
  font-size: 24rpx;
  color: #666;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.arrow-container {
  height: 30rpx;
  width: 100%;
  display: flex;
  justify-content: center;
}

.arrow-down {
  width: 50rpx;
  height: 50rpx;
  animation: bounce 1.5s infinite ease-in-out;
}

/* 微信小程序兼容动画 */
.arrow-down.pulse {
  animation: pulse 0.8s infinite ease-in-out;
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(6rpx);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style> 