2025-07-19 09:08:37.064 [HikariPool-8 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-8 - Thread starvation or clock leap detected (housekeeper delta=14h38m39s897ms884µs400ns).
2025-07-19 09:22:36.531 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: c4900e24-045d-423f-9c90-241666043c6f, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-19 09:22:36.578 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==>  Preparing: SELECT * FROM tags WHERE is_delete = 0 AND is_hot = 1 ORDER BY use_count DESC, create_time DESC LIMIT ?
2025-07-19 09:22:36.584 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==> Parameters: 10(Integer)
2025-07-19 09:22:36.634 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectHotTagsList - <==      Total: 5
2025-07-19 09:22:36.635 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: c4900e24-045d-423f-9c90-241666043c6f, cost: 106ms
2025-07-19 09:22:36.668 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: dbf71fb1-a29c-400b-b6d1-0c10aa3ac7bf, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10), org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:22:36.685 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-19 09:22:36.686 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-19 09:22:36.735 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-19 09:22:36.736 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-19 09:22:36.737 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-19 09:22:36.780 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:22:36.780 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"] -> [https://example.com/image1.jpg, https://example.com/image2.jpg]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image4.jpg]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image6.jpg]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image8.jpg]
2025-07-19 09:22:36.781 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"]
2025-07-19 09:22:36.782 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image10.jpg]
2025-07-19 09:22:36.782 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:22:36.782 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:22:36.782 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:22:36.782 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:22:36.782 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:22:36.782 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:22:36.782 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-19 09:22:36.782 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: dbf71fb1-a29c-400b-b6d1-0c10aa3ac7bf, cost: 113ms
2025-07-19 09:22:40.101 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: fca91821-2065-43a3-b80f-f6ec23309b8f, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-19 09:22:40.138 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==>  Preparing: SELECT * FROM tags WHERE is_delete = 0 AND is_hot = 1 ORDER BY use_count DESC, create_time DESC LIMIT ?
2025-07-19 09:22:40.138 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==> Parameters: 10(Integer)
2025-07-19 09:22:40.176 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.TagMapper.selectHotTagsList - <==      Total: 5
2025-07-19 09:22:40.176 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: fca91821-2065-43a3-b80f-f6ec23309b8f, cost: 75ms
2025-07-19 09:22:40.198 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 193311cc-f157-4ab6-88af-e929a77d4a75, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10), org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:22:40.201 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-19 09:22:40.201 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-19 09:22:40.238 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-19 09:22:40.238 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-19 09:22:40.239 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-19 09:22:40.278 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:22:40.278 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:22:40.278 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:22:40.278 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:22:40.278 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:22:40.278 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"] -> [https://example.com/image1.jpg, https://example.com/image2.jpg]
2025-07-19 09:22:40.278 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"]
2025-07-19 09:22:40.278 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image4.jpg]
2025-07-19 09:22:40.278 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"]
2025-07-19 09:22:40.278 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image6.jpg]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image8.jpg]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image10.jpg]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-19 09:22:40.279 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 193311cc-f157-4ab6-88af-e929a77d4a75, cost: 80ms
2025-07-19 09:42:50.214 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: 9e08cac9-d2fb-4260-bba1-aa10058c75c5, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-19 09:42:50.255 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==>  Preparing: SELECT * FROM tags WHERE is_delete = 0 AND is_hot = 1 ORDER BY use_count DESC, create_time DESC LIMIT ?
2025-07-19 09:42:50.255 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==> Parameters: 10(Integer)
2025-07-19 09:42:50.300 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.TagMapper.selectHotTagsList - <==      Total: 5
2025-07-19 09:42:50.301 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: 9e08cac9-d2fb-4260-bba1-aa10058c75c5, cost: 86ms
2025-07-19 09:42:50.322 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: 63cda43e-e009-4e1c-ba40-40eb3591ec13, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10), org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:42:50.324 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-19 09:42:50.324 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-19 09:42:50.365 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-19 09:42:50.366 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-19 09:42:50.366 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-19 09:42:50.411 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:42:50.411 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:42:50.411 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:42:50.411 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:42:50.411 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:42:50.411 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"] -> [https://example.com/image1.jpg, https://example.com/image2.jpg]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image4.jpg]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image6.jpg]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image8.jpg]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image10.jpg]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-19 09:42:50.412 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: 63cda43e-e009-4e1c-ba40-40eb3591ec13, cost: 90ms
2025-07-19 09:44:21.077 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 6a36953f-40a0-421a-99ba-d07ca2ec23ed, path: /api/notifications/unread-count, ip: 0:0:0:0:0:0:0:1, params: [org.apache.catalina.connector.RequestFacade@39d9791a]
2025-07-19 09:44:21.086 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 449b1ae7-7a47-4e33-9ccc-efa5e2957150, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:44:21.088 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 1, count: 5
2025-07-19 09:44:21.088 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 449b1ae7-7a47-4e33-9ccc-efa5e2957150, cost: 2ms
2025-07-19 09:44:21.127 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM notifications WHERE is_delete=0 AND (user_id = ? AND is_read = ?)
2025-07-19 09:44:21.127 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - ==> Parameters: 1(Long), 0(Integer)
2025-07-19 09:44:21.173 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - <==      Total: 1
2025-07-19 09:44:21.178 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM notifications WHERE is_delete=0 AND (user_id = ? AND type = ? AND is_read = ?)
2025-07-19 09:44:21.179 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - ==> Parameters: 1(Long), 4(Integer), 0(Integer)
2025-07-19 09:44:21.220 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - <==      Total: 1
2025-07-19 09:44:21.221 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM notifications WHERE is_delete=0 AND (user_id = ? AND type = ? AND is_read = ?)
2025-07-19 09:44:21.221 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - ==> Parameters: 1(Long), 1(Integer), 0(Integer)
2025-07-19 09:44:21.262 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - <==      Total: 1
2025-07-19 09:44:21.263 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM notifications WHERE is_delete=0 AND (user_id = ? AND type = ? AND is_read = ?)
2025-07-19 09:44:21.263 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - ==> Parameters: 1(Long), 2(Integer), 0(Integer)
2025-07-19 09:44:21.305 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - <==      Total: 1
2025-07-19 09:44:21.305 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM notifications WHERE is_delete=0 AND (user_id = ? AND type = ? AND is_read = ?)
2025-07-19 09:44:21.306 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - ==> Parameters: 1(Long), 3(Integer), 0(Integer)
2025-07-19 09:44:21.347 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.N.selectCount - <==      Total: 1
2025-07-19 09:44:21.348 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.c.NotificationController - 获取未读消息数成功 - userId: 1, unreadCount: {total=0, system=0, like=0, comment=0, follow=0}
2025-07-19 09:44:21.348 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 6a36953f-40a0-421a-99ba-d07ca2ec23ed, cost: 270ms
2025-07-19 09:44:51.707 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 1aa1e3eb-e0d4-47fd-809a-493eab986d2b, path: /api/post/detail, ip: 0:0:0:0:0:0:0:1, params: [1, org.apache.catalina.connector.RequestFacade@39d9791a]
2025-07-19 09:44:51.780 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.PostMapper.selectPostDetail - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.id = ? AND p.is_delete = 0
2025-07-19 09:44:51.781 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.PostMapper.selectPostDetail - ==> Parameters: null, null, null, 1(Long)
2025-07-19 09:44:51.785 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: 35fb2a35-16c2-48b4-9251-5355b25977b9, path: /api/comments/post/1, ip: 0:0:0:0:0:0:0:1, params: [1, 1, hot, 1, 10, org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:44:51.785 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.CommentController - 获取帖子评论列表请求开始 - postId: 1, userId: 1, filter: hot, current: 1, pageSize: 10
2025-07-19 09:44:51.786 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.CommentServiceImpl - 获取帖子评论列表服务 - postId: 1, filter: hot, userId: 1, current: 1, pageSize: 10
2025-07-19 09:44:51.822 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:44:51.822 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:44:51.822 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.PostMapper.selectPostDetail - <==      Total: 1
2025-07-19 09:44:51.823 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.PostMapper.selectById - ==>  Preparing: SELECT id,user_id AS userId,content,images,cover_image AS coverImage,location_name AS locationName,location_latitude AS locationLatitude,location_longitude AS locationLongitude,location_address AS locationAddress,like_count AS likeCount,comment_count AS commentCount,share_count AS shareCount,view_count AS viewCount,is_public AS isPublic,status,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM posts WHERE id=? AND is_delete=0
2025-07-19 09:44:51.824 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.PostMapper.selectById - ==> Parameters: 1(Long)
2025-07-19 09:44:51.827 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (post_id = ? AND is_delete = ?)
2025-07-19 09:44:51.827 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 1(Long), 0(Integer)
2025-07-19 09:44:51.866 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:44:51.866 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:44:51.866 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.PostMapper.selectById - <==      Total: 1
2025-07-19 09:44:51.866 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 1aa1e3eb-e0d4-47fd-809a-493eab986d2b, cost: 159ms
2025-07-19 09:44:51.871 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-19 09:44:51.872 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.CommentServiceImpl - 获取帖子评论列表服务完成 - 总评论数: 0
2025-07-19 09:44:51.872 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.CommentController - 🎯 帖子评论分页查询成功 - postId: 1, 总数: 0, 当前页: 1/0, 返回数: 0, hasMore: false
2025-07-19 09:44:51.872 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: 35fb2a35-16c2-48b4-9251-5355b25977b9, cost: 87ms
2025-07-19 09:44:56.583 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 2dbfb753-16f0-41ff-bc2f-918b7a1423a2, path: /api/post/detail, ip: 0:0:0:0:0:0:0:1, params: [2, org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:44:56.586 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: fc45f595-7b1a-4218-bbad-8589382e6b72, path: /api/comments/post/2, ip: 0:0:0:0:0:0:0:1, params: [2, 1, hot, 1, 10, org.apache.catalina.connector.RequestFacade@39d9791a]
2025-07-19 09:44:56.586 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.CommentController - 获取帖子评论列表请求开始 - postId: 2, userId: 1, filter: hot, current: 1, pageSize: 10
2025-07-19 09:44:56.586 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.CommentServiceImpl - 获取帖子评论列表服务 - postId: 2, filter: hot, userId: 1, current: 1, pageSize: 10
2025-07-19 09:44:56.623 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostDetail - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.id = ? AND p.is_delete = 0
2025-07-19 09:44:56.624 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostDetail - ==> Parameters: null, null, null, 2(Long)
2025-07-19 09:44:56.626 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (post_id = ? AND is_delete = ?)
2025-07-19 09:44:56.627 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 2(Long), 0(Integer)
2025-07-19 09:44:56.670 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-19 09:44:56.670 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:44:56.670 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:44:56.670 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.CommentServiceImpl - 获取帖子评论列表服务完成 - 总评论数: 0
2025-07-19 09:44:56.670 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostDetail - <==      Total: 1
2025-07-19 09:44:56.670 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.CommentController - 🎯 帖子评论分页查询成功 - postId: 2, 总数: 0, 当前页: 1/0, 返回数: 0, hasMore: false
2025-07-19 09:44:56.671 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: fc45f595-7b1a-4218-bbad-8589382e6b72, cost: 85ms
2025-07-19 09:44:56.671 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.PostMapper.selectById - ==>  Preparing: SELECT id,user_id AS userId,content,images,cover_image AS coverImage,location_name AS locationName,location_latitude AS locationLatitude,location_longitude AS locationLongitude,location_address AS locationAddress,like_count AS likeCount,comment_count AS commentCount,share_count AS shareCount,view_count AS viewCount,is_public AS isPublic,status,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM posts WHERE id=? AND is_delete=0
2025-07-19 09:44:56.671 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.PostMapper.selectById - ==> Parameters: 2(Long)
2025-07-19 09:44:56.712 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:44:56.712 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:44:56.712 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.PostMapper.selectById - <==      Total: 1
2025-07-19 09:44:56.712 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 2dbfb753-16f0-41ff-bc2f-918b7a1423a2, cost: 129ms
2025-07-19 09:45:00.599 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: b1957ad7-bf6a-495f-be39-c9d621b0ac7e, path: /api/comments/post/3, ip: 0:0:0:0:0:0:0:1, params: [3, 1, hot, 1, 10, org.apache.catalina.connector.RequestFacade@39d9791a]
2025-07-19 09:45:00.599 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.controller.CommentController - 获取帖子评论列表请求开始 - postId: 3, userId: 1, filter: hot, current: 1, pageSize: 10
2025-07-19 09:45:00.599 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.s.impl.CommentServiceImpl - 获取帖子评论列表服务 - postId: 3, filter: hot, userId: 1, current: 1, pageSize: 10
2025-07-19 09:45:00.602 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: d9dcc3a5-6129-434e-acf6-66f6729643fb, path: /api/post/detail, ip: 0:0:0:0:0:0:0:1, params: [3, org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:45:00.641 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostDetail - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.id = ? AND p.is_delete = 0
2025-07-19 09:45:00.641 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostDetail - ==> Parameters: null, null, null, 3(Long)
2025-07-19 09:45:00.642 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (post_id = ? AND is_delete = ?)
2025-07-19 09:45:00.643 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 3(Long), 0(Integer)
2025-07-19 09:45:00.683 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:00.683 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:00.683 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostDetail - <==      Total: 1
2025-07-19 09:45:00.683 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.PostMapper.selectById - ==>  Preparing: SELECT id,user_id AS userId,content,images,cover_image AS coverImage,location_name AS locationName,location_latitude AS locationLatitude,location_longitude AS locationLongitude,location_address AS locationAddress,like_count AS likeCount,comment_count AS commentCount,share_count AS shareCount,view_count AS viewCount,is_public AS isPublic,status,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM posts WHERE id=? AND is_delete=0
2025-07-19 09:45:00.683 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.PostMapper.selectById - ==> Parameters: 3(Long)
2025-07-19 09:45:00.684 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-19 09:45:00.684 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.s.impl.CommentServiceImpl - 获取帖子评论列表服务完成 - 总评论数: 0
2025-07-19 09:45:00.684 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.controller.CommentController - 🎯 帖子评论分页查询成功 - postId: 3, 总数: 0, 当前页: 1/0, 返回数: 0, hasMore: false
2025-07-19 09:45:00.684 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: b1957ad7-bf6a-495f-be39-c9d621b0ac7e, cost: 84ms
2025-07-19 09:45:00.725 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:00.725 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:00.725 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.PostMapper.selectById - <==      Total: 1
2025-07-19 09:45:00.726 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: d9dcc3a5-6129-434e-acf6-66f6729643fb, cost: 123ms
2025-07-19 09:45:02.693 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: 8e01721e-c1ac-44d9-8de2-a427d59f45a5, path: /api/post/detail, ip: 0:0:0:0:0:0:0:1, params: [4, org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:45:02.697 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 07eb2d7c-540e-4cf1-9cc8-ccf25c6ee42a, path: /api/comments/post/4, ip: 0:0:0:0:0:0:0:1, params: [4, 1, hot, 1, 10, org.apache.catalina.connector.RequestFacade@39d9791a]
2025-07-19 09:45:02.697 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.CommentController - 获取帖子评论列表请求开始 - postId: 4, userId: 1, filter: hot, current: 1, pageSize: 10
2025-07-19 09:45:02.697 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.s.impl.CommentServiceImpl - 获取帖子评论列表服务 - postId: 4, filter: hot, userId: 1, current: 1, pageSize: 10
2025-07-19 09:45:02.736 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.PostMapper.selectPostDetail - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.id = ? AND p.is_delete = 0
2025-07-19 09:45:02.736 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.PostMapper.selectPostDetail - ==> Parameters: null, null, null, 4(Long)
2025-07-19 09:45:02.737 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (post_id = ? AND is_delete = ?)
2025-07-19 09:45:02.737 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 4(Long), 0(Integer)
2025-07-19 09:45:02.777 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-19 09:45:02.777 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.s.impl.CommentServiceImpl - 获取帖子评论列表服务完成 - 总评论数: 0
2025-07-19 09:45:02.777 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.CommentController - 🎯 帖子评论分页查询成功 - postId: 4, 总数: 0, 当前页: 1/0, 返回数: 0, hasMore: false
2025-07-19 09:45:02.777 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 07eb2d7c-540e-4cf1-9cc8-ccf25c6ee42a, cost: 79ms
2025-07-19 09:45:02.779 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"]
2025-07-19 09:45:02.780 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image4.jpg]
2025-07-19 09:45:02.780 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.PostMapper.selectPostDetail - <==      Total: 1
2025-07-19 09:45:02.780 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.PostMapper.selectById - ==>  Preparing: SELECT id,user_id AS userId,content,images,cover_image AS coverImage,location_name AS locationName,location_latitude AS locationLatitude,location_longitude AS locationLongitude,location_address AS locationAddress,like_count AS likeCount,comment_count AS commentCount,share_count AS shareCount,view_count AS viewCount,is_public AS isPublic,status,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM posts WHERE id=? AND is_delete=0
2025-07-19 09:45:02.780 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.PostMapper.selectById - ==> Parameters: 4(Long)
2025-07-19 09:45:02.821 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"]
2025-07-19 09:45:02.821 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image4.jpg]
2025-07-19 09:45:02.821 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.PostMapper.selectById - <==      Total: 1
2025-07-19 09:45:02.821 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: 8e01721e-c1ac-44d9-8de2-a427d59f45a5, cost: 127ms
2025-07-19 09:45:06.356 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: dfa65289-a2cd-49f1-b04b-ebfdc774530a, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-19 09:45:06.395 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==>  Preparing: SELECT * FROM tags WHERE is_delete = 0 AND is_hot = 1 ORDER BY use_count DESC, create_time DESC LIMIT ?
2025-07-19 09:45:06.395 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==> Parameters: 10(Integer)
2025-07-19 09:45:06.435 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectHotTagsList - <==      Total: 5
2025-07-19 09:45:06.435 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: dfa65289-a2cd-49f1-b04b-ebfdc774530a, cost: 78ms
2025-07-19 09:45:06.438 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 1a4318ed-0183-4cf1-9465-16564eca8db6, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10), org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:45:06.481 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-19 09:45:06.481 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-19 09:45:06.523 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-19 09:45:06.523 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-19 09:45:06.523 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"] -> [https://example.com/image1.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image4.jpg]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image6.jpg]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image8.jpg]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image10.jpg]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-19 09:45:06.567 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 1a4318ed-0183-4cf1-9465-16564eca8db6, cost: 129ms
2025-07-19 09:45:19.657 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 20d1eb92-fbfc-40e5-99bf-9e7d8cadbcb5, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=1, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=20), org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:45:19.699 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.user_id = ?) TOTAL
2025-07-19 09:45:19.699 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null, 1(Long)
2025-07-19 09:45:19.744 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-19 09:45:19.745 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.user_id = ? ORDER BY p.create_time desc LIMIT ?
2025-07-19 09:45:19.745 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 1(Long), 20(Long)
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"] -> [https://example.com/image1.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 6
2025-07-19 09:45:19.790 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 20d1eb92-fbfc-40e5-99bf-9e7d8cadbcb5, cost: 133ms
2025-07-19 09:45:22.803 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 20906264-d10b-4e74-b960-7dafcc0e6e17, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=1, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=20), org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:45:22.846 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.user_id = ?) TOTAL
2025-07-19 09:45:22.846 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null, 1(Long)
2025-07-19 09:45:22.890 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-19 09:45:22.891 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.user_id = ? ORDER BY p.create_time desc LIMIT ?
2025-07-19 09:45:22.891 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 1(Long), 20(Long)
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"] -> [https://example.com/image1.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:22.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 6
2025-07-19 09:45:22.933 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 20906264-d10b-4e74-b960-7dafcc0e6e17, cost: 129ms
2025-07-19 09:45:33.305 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: 19e3ee35-c6dd-4fce-9d69-e4ae18bcfa52, path: /api/notifications/unread-count, ip: 0:0:0:0:0:0:0:1, params: [org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:45:33.305 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 9ed1211f-613b-4e88-b12a-8754804f51f4, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@39d9791a]
2025-07-19 09:45:33.305 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 1, count: 5
2025-07-19 09:45:33.305 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 9ed1211f-613b-4e88-b12a-8754804f51f4, cost: 0ms
2025-07-19 09:45:33.346 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM notifications WHERE is_delete=0 AND (user_id = ? AND is_read = ?)
2025-07-19 09:45:33.346 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - ==> Parameters: 1(Long), 0(Integer)
2025-07-19 09:45:33.389 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - <==      Total: 1
2025-07-19 09:45:33.389 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM notifications WHERE is_delete=0 AND (user_id = ? AND type = ? AND is_read = ?)
2025-07-19 09:45:33.389 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - ==> Parameters: 1(Long), 4(Integer), 0(Integer)
2025-07-19 09:45:33.430 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - <==      Total: 1
2025-07-19 09:45:33.431 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM notifications WHERE is_delete=0 AND (user_id = ? AND type = ? AND is_read = ?)
2025-07-19 09:45:33.431 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - ==> Parameters: 1(Long), 1(Integer), 0(Integer)
2025-07-19 09:45:33.472 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - <==      Total: 1
2025-07-19 09:45:33.474 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM notifications WHERE is_delete=0 AND (user_id = ? AND type = ? AND is_read = ?)
2025-07-19 09:45:33.474 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - ==> Parameters: 1(Long), 2(Integer), 0(Integer)
2025-07-19 09:45:33.514 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - <==      Total: 1
2025-07-19 09:45:33.515 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM notifications WHERE is_delete=0 AND (user_id = ? AND type = ? AND is_read = ?)
2025-07-19 09:45:33.515 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - ==> Parameters: 1(Long), 3(Integer), 0(Integer)
2025-07-19 09:45:33.556 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.N.selectCount - <==      Total: 1
2025-07-19 09:45:33.556 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.c.NotificationController - 获取未读消息数成功 - userId: 1, unreadCount: {total=0, system=0, like=0, comment=0, follow=0}
2025-07-19 09:45:33.556 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: 19e3ee35-c6dd-4fce-9d69-e4ae18bcfa52, cost: 251ms
2025-07-19 09:45:36.087 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 6368f058-bce5-4ca9-9146-fd11570bdd5c, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=1, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=20), org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:45:36.130 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.user_id = ?) TOTAL
2025-07-19 09:45:36.131 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null, 1(Long)
2025-07-19 09:45:36.172 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-19 09:45:36.172 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.user_id = ? ORDER BY p.create_time desc LIMIT ?
2025-07-19 09:45:36.174 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 1(Long), 20(Long)
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"] -> [https://example.com/image1.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 6
2025-07-19 09:45:36.219 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 6368f058-bce5-4ca9-9146-fd11570bdd5c, cost: 131ms
2025-07-19 09:45:44.355 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: 96da1bfa-2299-45ce-8653-c10089e6c0d4, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-19 09:45:44.397 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==>  Preparing: SELECT * FROM tags WHERE is_delete = 0 AND is_hot = 1 ORDER BY use_count DESC, create_time DESC LIMIT ?
2025-07-19 09:45:44.398 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==> Parameters: 10(Integer)
2025-07-19 09:45:44.437 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.TagMapper.selectHotTagsList - <==      Total: 5
2025-07-19 09:45:44.437 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: 96da1bfa-2299-45ce-8653-c10089e6c0d4, cost: 82ms
2025-07-19 09:45:44.440 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 33f222ca-6b02-4873-896d-7ef2189b5885, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10), org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:45:44.448 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-19 09:45:44.448 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-19 09:45:44.488 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-19 09:45:44.488 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-19 09:45:44.488 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-19 09:45:44.529 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"] -> [https://example.com/image1.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image4.jpg]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image6.jpg]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image8.jpg]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image10.jpg]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:45:44.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:44.531 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:44.531 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:45:44.531 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:45:44.531 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-19 09:45:44.531 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 33f222ca-6b02-4873-896d-7ef2189b5885, cost: 91ms
2025-07-19 09:46:54.163 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 048ac4dd-b5c5-4b93-9115-9ab6e1b47aa5, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-19 09:46:54.294 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==>  Preparing: SELECT * FROM tags WHERE is_delete = 0 AND is_hot = 1 ORDER BY use_count DESC, create_time DESC LIMIT ?
2025-07-19 09:46:54.294 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==> Parameters: 10(Integer)
2025-07-19 09:46:54.336 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.TagMapper.selectHotTagsList - <==      Total: 5
2025-07-19 09:46:54.336 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 048ac4dd-b5c5-4b93-9115-9ab6e1b47aa5, cost: 172ms
2025-07-19 09:46:54.343 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 2ab63f7f-b52b-4551-a371-e600595c155a, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10), org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:46:54.349 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-19 09:46:54.349 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-19 09:46:54.403 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-19 09:46:54.404 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-19 09:46:54.404 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-19 09:46:54.449 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"] -> [https://example.com/image1.jpg, https://example.com/image2.jpg]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image4.jpg]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image6.jpg]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image8.jpg]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image10.jpg]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-19 09:46:54.450 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 2ab63f7f-b52b-4551-a371-e600595c155a, cost: 107ms
2025-07-19 09:47:32.799 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 07e35e2e-b2bd-42f6-894d-317ef772ddf2, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-19 09:47:32.843 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==>  Preparing: SELECT * FROM tags WHERE is_delete = 0 AND is_hot = 1 ORDER BY use_count DESC, create_time DESC LIMIT ?
2025-07-19 09:47:32.844 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==> Parameters: 10(Integer)
2025-07-19 09:47:32.885 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.TagMapper.selectHotTagsList - <==      Total: 5
2025-07-19 09:47:32.885 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 07e35e2e-b2bd-42f6-894d-317ef772ddf2, cost: 86ms
2025-07-19 09:47:32.966 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 3781b028-d191-4809-b229-ab87c2cf83f9, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10), org.apache.catalina.connector.RequestFacade@33fb1143]
2025-07-19 09:47:33.007 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-19 09:47:33.007 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-19 09:47:33.046 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-19 09:47:33.046 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-19 09:47:33.047 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/test1.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/test1.jpg", "https://example.com/test2.jpg"] -> [https://example.com/test1.jpg, https://example.com/test2.jpg]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"] -> [https://example.com/image1.jpg, https://example.com/image2.jpg]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image4.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image4.jpg]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image6.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image6.jpg]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image8.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image8.jpg]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image10.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image10.jpg]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/image2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/image2.jpg]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg", "https://example.com/test2.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg, https://example.com/test2.jpg]
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-19 09:47:33.087 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 3781b028-d191-4809-b229-ab87c2cf83f9, cost: 120ms
2025-07-19 09:49:52.940 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 7fc19bbc-2e8f-4000-b7f9-d662ded11b26, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-19 09:49:52.980 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==>  Preparing: SELECT * FROM tags WHERE is_delete = 0 AND is_hot = 1 ORDER BY use_count DESC, create_time DESC LIMIT ?
2025-07-19 09:49:52.980 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.TagMapper.selectHotTagsList - ==> Parameters: 10(Integer)
2025-07-19 09:49:53.022 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.TagMapper.selectHotTagsList - <==      Total: 5
2025-07-19 09:49:53.022 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 7fc19bbc-2e8f-4000-b7f9-d662ded11b26, cost: 81ms
2025-07-19 09:50:02.337 [http-nio-0.0.0.0-8101-exec-2] ERROR c.y.s.e.GlobalExceptionHandler - RuntimeException
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Invalid UTF-8 middle byte 0xfa; nested exception is com.fasterxml.jackson.databind.JsonMappingException: Invalid UTF-8 middle byte 0xfa
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 39] (through reference chain: com.yupi.springbootinit.model.dto.post.PostQueryDTO["tags"]->java.util.ArrayList[0])
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:391)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:343)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:185)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:160)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:133)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1070)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.JsonMappingException: Invalid UTF-8 middle byte 0xfa
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 39] (through reference chain: com.yupi.springbootinit.model.dto.post.PostQueryDTO["tags"]->java.util.ArrayList[0])
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:392)
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:363)
	at com.fasterxml.jackson.databind.deser.std.StringCollectionDeserializer.deserialize(StringCollectionDeserializer.java:221)
	at com.fasterxml.jackson.databind.deser.std.StringCollectionDeserializer.deserialize(StringCollectionDeserializer.java:182)
	at com.fasterxml.jackson.databind.deser.std.StringCollectionDeserializer.deserialize(StringCollectionDeserializer.java:25)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:313)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:176)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3682)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:380)
	... 54 common frames omitted
Caused by: com.fasterxml.jackson.core.JsonParseException: Invalid UTF-8 middle byte 0xfa
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 39]
	at com.fasterxml.jackson.core.JsonParser._constructError(JsonParser.java:2391)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportError(ParserMinimalBase.java:735)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._reportInvalidOther(UTF8StreamJsonParser.java:3652)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._reportInvalidOther(UTF8StreamJsonParser.java:3659)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._decodeUtf8_2(UTF8StreamJsonParser.java:3433)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._finishString2(UTF8StreamJsonParser.java:2555)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._finishAndReturnString(UTF8StreamJsonParser.java:2507)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser.getText(UTF8StreamJsonParser.java:334)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser.nextTextValue(UTF8StreamJsonParser.java:1318)
	at com.fasterxml.jackson.databind.deser.std.StringCollectionDeserializer.deserialize(StringCollectionDeserializer.java:201)
	... 63 common frames omitted
2025-07-19 09:50:02.355 [http-nio-0.0.0.0-8101-exec-2] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Invalid UTF-8 middle byte 0xfa; nested exception is com.fasterxml.jackson.databind.JsonMappingException: Invalid UTF-8 middle byte 0xfa<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 39] (through reference chain: com.yupi.springbootinit.model.dto.post.PostQueryDTO["tags"]->java.util.ArrayList[0])]
