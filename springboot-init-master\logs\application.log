2025-07-18 09:08:40.618 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15h6m21s644ms93µs600ns).
2025-07-18 10:02:27.368 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 10:02:27.374 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-18 10:02:27.948 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 8820 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:02:27.948 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.533 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.534 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.534 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.534 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.534 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.534 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.534 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.534 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.534 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:02:29.534 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:02:29.743 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:02:29.743 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:02:29.743 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:02:29.744 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:02:29.778 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:02:29.778 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1824 ms
2025-07-18 10:02:29.853 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@26e05f61'
2025-07-18 10:02:29.874 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:02:29.890 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:02:29.907 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:02:29.926 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:02:29.944 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:02:29.958 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:02:29.960 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:02:29.963 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:02:29.964 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:02:29.965 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:02:29.965 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:02:29.965 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:02:29.988 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:02:30.006 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:02:30.022 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:02:30.040 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:02:30.061 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:02:30.077 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:02:30.083 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:02:30.098 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:02:30.231 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:02:30.251 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:02:30.265 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:02:30.279 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:02:30.303 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:02:30.322 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:02:30.341 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:02:30.363 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:02:30.381 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:02:30.393 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:02:30.408 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:02:30.462 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:02:30.465 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:02:30.467 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:02:30.534 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:02:30.539 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:02:30.629 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:02:31.040 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-18 10:02:31.104 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-18 10:02:31.137 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-18 10:02:31.171 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-18 10:02:31.211 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:02:31.214 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-18 10:02:31.214 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-18 10:02:31.214 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-18 10:02:31.231 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-18 10:02:31.258 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-18 10:02:31.290 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-18 10:02:31.291 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-18 10:02:31.292 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-18 10:02:31.292 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-18 10:02:31.293 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-18 10:02:31.380 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 3.486 seconds (JVM running for 58371.734)
2025-07-18 10:02:31.382 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-18 10:03:02.172 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:03:02.174 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:03:02.203 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-18 10:03:02.203 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-18 10:03:02.768 [restartedMain] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 10:03:02.776 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.777 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.778 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.779 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:03:02.779 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:03:03.330 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:03:03.337 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:03:03.338 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:03:03.338 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:03:03.389 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:03:03.389 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1185 ms
2025-07-18 10:03:03.510 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@190c7ce1'
2025-07-18 10:03:03.604 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:03:03.620 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:03:03.639 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:03:03.652 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:03:03.663 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:03:03.668 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:03:03.670 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:03:03.674 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:03:03.675 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:03:03.676 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:03:03.676 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:03:03.676 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:03:03.689 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:03:03.700 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:03:03.709 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:03:03.722 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:03:03.729 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:03:03.732 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:03:03.739 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:03:03.747 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:03:03.757 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:03:03.768 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:03:03.780 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:03:03.792 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:03:03.801 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:03:03.812 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:03:03.821 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:03:03.833 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:03:03.841 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:03:03.850 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:03:03.862 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:03:03.961 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:03:03.966 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:03:03.967 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:03:04.025 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:03:04.039 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:03:04.228 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:03:04.685 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-18 10:03:04.766 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-18 10:03:04.845 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-18 10:03:04.935 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-18 10:03:05.005 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:03:05.014 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-18 10:03:05.014 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-18 10:03:05.016 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-18 10:03:05.046 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-18 10:03:05.124 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-18 10:03:05.191 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-18 10:03:05.192 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-18 10:03:05.193 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-18 10:03:05.194 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-18 10:03:05.196 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-18 10:03:05.317 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 3.401 seconds (JVM running for 3.883)
2025-07-18 10:03:11.058 [http-nio-0.0.0.0-8101-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 10:03:11.058 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 10:03:11.059 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 10:03:15.586 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: d4b093a4-ff3f-4ddf-96d2-48ab16af3d2d, path: /api/social/test/user-profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-18 10:03:15.617 [http-nio-0.0.0.0-8101-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 10:03:16.131 [http-nio-0.0.0.0-8101-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 10:03:16.136 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-18 10:03:16.149 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-18 10:03:16.203 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-18 10:03:16.210 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id AS userId,following_count AS followingCount,follower_count AS followerCount,post_count AS postCount,like_received_count AS likeReceivedCount,update_time AS updateTime FROM user_stats WHERE user_id=?
2025-07-18 10:03:16.210 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-18 10:03:16.249 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 0
2025-07-18 10:03:16.264 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.UserStatsMapper.insert - ==>  Preparing: INSERT INTO user_stats ( user_id, following_count, follower_count, post_count, like_received_count, update_time ) VALUES ( ?, ?, ?, ?, ?, ? )
2025-07-18 10:03:16.264 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.UserStatsMapper.insert - ==> Parameters: 24840(Long), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2025-07-18 10:03:16.264(Timestamp)
2025-07-18 10:03:16.347 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.UserStatsMapper.insert - <==    Updates: 1
2025-07-18 10:03:16.347 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.c.SocialTestController - 用户资料测试成功 - userId: 24840
2025-07-18 10:03:16.348 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: d4b093a4-ff3f-4ddf-96d2-48ab16af3d2d, cost: 775ms
2025-07-18 10:09:48.441 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 35061048-e2f0-4aa9-9bfc-94081358e78b, path: /api/social/test/user-profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-18 10:09:48.481 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-18 10:09:48.482 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-18 10:09:48.522 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-18 10:09:48.523 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id AS userId,following_count AS followingCount,follower_count AS followerCount,post_count AS postCount,like_received_count AS likeReceivedCount,update_time AS updateTime FROM user_stats WHERE user_id=?
2025-07-18 10:09:48.523 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-18 10:09:48.569 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-18 10:09:48.570 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.c.SocialTestController - 用户资料测试成功 - userId: 24840
2025-07-18 10:09:48.570 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 35061048-e2f0-4aa9-9bfc-94081358e78b, cost: 128ms
2025-07-18 10:09:50.177 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 20074237-355f-4a23-a020-22a1eb3fe7af, path: /api/social/test/user-profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-18 10:09:50.217 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-18 10:09:50.217 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-18 10:09:50.257 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-18 10:09:50.257 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id AS userId,following_count AS followingCount,follower_count AS followerCount,post_count AS postCount,like_received_count AS likeReceivedCount,update_time AS updateTime FROM user_stats WHERE user_id=?
2025-07-18 10:09:50.257 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-18 10:09:50.298 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-18 10:09:50.299 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.c.SocialTestController - 用户资料测试成功 - userId: 24840
2025-07-18 10:09:50.299 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 20074237-355f-4a23-a020-22a1eb3fe7af, cost: 123ms
2025-07-18 10:10:18.127 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: b6b72438-b0c1-4f47-9889-6c3749f7588b, path: /api/social/test/update-user/24840, ip: 0:0:0:0:0:0:0:1, params: [24840, com.yupi.springbootinit.model.dto.user.UserProfileUpdateRequest@657606f0]
2025-07-18 10:10:18.169 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-18 10:10:18.169 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-18 10:10:18.210 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-18 10:10:18.215 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.BaUserServiceImpl - 更新用户资料 - userId: 24840, updateRequest: com.yupi.springbootinit.model.dto.user.UserProfileUpdateRequest@657606f0
2025-07-18 10:10:18.224 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.BaUserServiceImpl - nickname: 小伍
2025-07-18 10:10:18.225 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.BaUserServiceImpl - 设置更新字段 nickname: 小伍
2025-07-18 10:10:18.238 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.update - ==>  Preparing: UPDATE ba_user SET nickname=?,bio=?,dance_type=? WHERE (id = ?)
2025-07-18 10:10:18.241 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.update - ==> Parameters: 小伍(String), 123(String), 街舞(String), 24840(Long)
2025-07-18 10:10:18.320 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.update - <==    Updates: 1
2025-07-18 10:10:18.321 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.BaUserServiceImpl - 用户资料更新成功 - userId: 24840
2025-07-18 10:10:18.321 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-18 10:10:18.321 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-18 10:10:18.360 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-18 10:10:18.361 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.c.SocialTestController - 用户资料更新测试 - userId: 24840, success: true
2025-07-18 10:10:18.361 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: b6b72438-b0c1-4f47-9889-6c3749f7588b, cost: 243ms
2025-07-18 10:10:22.598 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 181525f5-a0f7-4e74-b35a-3cd6569fb686, path: /api/social/test/user-profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-18 10:10:22.637 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-18 10:10:22.637 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-18 10:10:22.678 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-18 10:10:22.679 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id AS userId,following_count AS followingCount,follower_count AS followerCount,post_count AS postCount,like_received_count AS likeReceivedCount,update_time AS updateTime FROM user_stats WHERE user_id=?
2025-07-18 10:10:22.679 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-18 10:10:22.719 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-18 10:10:22.719 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.c.SocialTestController - 用户资料测试成功 - userId: 24840
2025-07-18 10:10:22.719 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 181525f5-a0f7-4e74-b35a-3cd6569fb686, cost: 120ms
2025-07-18 10:13:23.034 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 10:13:23.042 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-18 10:13:23.206 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:13:23.206 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:13:23.666 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.667 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.668 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.668 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.668 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.668 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.668 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.668 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.668 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.668 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:23.668 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:13:23.844 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:13:23.846 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:13:23.847 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:13:23.847 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:13:23.869 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:13:23.870 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 660 ms
2025-07-18 10:13:23.982 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@365baeb4'
2025-07-18 10:13:24.000 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:13:24.013 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:13:24.027 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:13:24.044 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:13:24.062 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:13:24.074 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:13:24.076 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:13:24.083 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:13:24.084 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:13:24.085 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:13:24.086 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:13:24.087 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:13:24.107 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:13:24.124 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:13:24.139 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:13:24.158 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:13:24.173 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:13:24.179 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:13:24.191 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:13:24.206 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:13:24.222 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:13:24.241 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:13:24.254 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:13:24.268 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:13:24.282 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:13:24.298 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:13:24.309 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:13:24.324 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:13:24.339 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:13:24.352 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:13:24.367 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:13:24.482 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:13:24.488 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:13:24.490 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:13:24.601 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:13:24.616 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:13:24.756 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:13:25.387 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'searchController' defined in file [D:\Project\fox\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\controller\SearchController.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.yupi.springbootinit.controller.SearchController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.model.dto.search cannot be resolved
	The import com.yupi.springbootinit.model.vo.SearchResultVO cannot be resolved
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchRequest cannot be resolved to a type
	SearchRequest cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

2025-07-18 10:13:25.388 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:13:25.398 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:13:25.434 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'searchController' defined in file [D:\Project\fox\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\controller\SearchController.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.yupi.springbootinit.controller.SearchController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.model.dto.search cannot be resolved
	The import com.yupi.springbootinit.model.vo.SearchResultVO cannot be resolved
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchRequest cannot be resolved to a type
	SearchRequest cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.yupi.springbootinit.controller.SearchController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.model.dto.search cannot be resolved
	The import com.yupi.springbootinit.model.vo.SearchResultVO cannot be resolved
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchRequest cannot be resolved to a type
	SearchRequest cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:224)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1326)
	... 22 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.model.dto.search cannot be resolved
	The import com.yupi.springbootinit.model.vo.SearchResultVO cannot be resolved
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchRequest cannot be resolved to a type
	SearchRequest cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

	at com.yupi.springbootinit.controller.SearchController.<init>(SearchController.java:6)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:211)
	... 24 common frames omitted
2025-07-18 10:13:41.321 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:13:41.321 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.652 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:13:41.653 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:13:41.768 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:13:41.768 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:13:41.769 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:13:41.769 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:13:41.787 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:13:41.787 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 463 ms
2025-07-18 10:13:41.870 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@407add9f'
2025-07-18 10:13:41.885 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:13:41.896 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:13:41.908 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:13:41.935 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:13:41.948 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:13:41.953 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:13:41.954 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:13:41.958 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:13:41.959 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:13:41.960 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:13:41.960 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:13:41.960 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:13:41.975 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:13:41.990 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:13:42.006 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:13:42.024 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:13:42.039 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:13:42.043 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:13:42.053 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:13:42.073 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:13:42.090 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:13:42.107 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:13:42.124 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:13:42.140 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:13:42.152 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:13:42.163 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:13:42.174 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:13:42.193 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:13:42.206 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:13:42.217 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:13:42.227 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:13:42.326 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:13:42.336 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:13:42.338 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:13:42.435 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:13:42.447 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:13:42.555 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:13:42.843 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'searchController' defined in file [D:\Project\fox\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\controller\SearchController.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.yupi.springbootinit.controller.SearchController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.model.vo.SearchResultVO cannot be resolved
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

2025-07-18 10:13:42.845 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:13:42.853 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:13:42.856 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'searchController' defined in file [D:\Project\fox\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\controller\SearchController.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.yupi.springbootinit.controller.SearchController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.model.vo.SearchResultVO cannot be resolved
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.yupi.springbootinit.controller.SearchController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.model.vo.SearchResultVO cannot be resolved
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:224)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1326)
	... 22 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.model.vo.SearchResultVO cannot be resolved
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchResultVO cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

	at com.yupi.springbootinit.controller.SearchController.<init>(SearchController.java:7)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:211)
	... 24 common frames omitted
2025-07-18 10:14:12.791 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:14:12.791 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:13.158 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:14:13.273 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:14:13.273 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:14:13.273 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:14:13.273 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:14:13.292 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:14:13.292 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 500 ms
2025-07-18 10:14:13.387 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@621d9882'
2025-07-18 10:14:13.405 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:14:13.419 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:14:13.435 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:14:13.451 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:14:13.474 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:14:13.482 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:14:13.484 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:14:13.488 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:14:13.489 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:14:13.490 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:14:13.490 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:14:13.490 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:14:13.506 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:14:13.521 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:14:13.534 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:14:13.550 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:14:13.559 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:14:13.567 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:14:13.574 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:14:13.588 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:14:13.603 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:14:13.617 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:14:13.637 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:14:13.653 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:14:13.668 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:14:13.684 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:14:13.694 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:14:13.708 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:14:13.722 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:14:13.734 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:14:13.743 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:14:13.838 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:14:13.843 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:14:13.844 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:14:13.941 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:14:13.953 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:14:14.041 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:14:14.290 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'searchController' defined in file [D:\Project\fox\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\controller\SearchController.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.yupi.springbootinit.controller.SearchController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

2025-07-18 10:14:14.291 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:14:14.296 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:14:14.299 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'searchController' defined in file [D:\Project\fox\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\controller\SearchController.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.yupi.springbootinit.controller.SearchController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.yupi.springbootinit.controller.SearchController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:224)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1326)
	... 22 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problems: 
	The import com.yupi.springbootinit.service.SearchService cannot be resolved
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type
	SearchService cannot be resolved to a type

	at com.yupi.springbootinit.controller.SearchController.<init>(SearchController.java:8)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:211)
	... 24 common frames omitted
2025-07-18 10:14:28.141 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:14:28.141 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.513 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:14:28.514 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:14:28.648 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:14:28.649 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:14:28.649 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:14:28.649 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:14:28.667 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:14:28.667 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 524 ms
2025-07-18 10:14:28.752 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@762df62'
2025-07-18 10:14:28.769 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:14:28.781 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:14:28.792 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:14:28.808 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:14:28.823 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:14:28.827 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:14:28.832 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:14:28.836 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:14:28.837 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:14:28.838 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:14:28.838 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:14:28.838 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:14:28.853 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:14:28.876 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:14:28.890 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:14:28.906 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:14:28.918 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:14:28.922 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:14:28.931 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:14:28.941 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:14:28.956 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:14:29.009 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:14:29.023 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:14:29.038 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:14:29.051 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:14:29.062 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:14:29.075 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:14:29.089 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:14:29.101 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:14:29.122 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:14:29.135 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:14:29.232 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:14:29.238 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:14:29.239 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:14:29.326 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:14:29.339 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:14:29.433 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:14:29.718 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'searchController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.yupi.springbootinit.service.SearchService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
2025-07-18 10:14:29.719 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:14:29.723 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:14:29.732 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.yupi.springbootinit.service.SearchService' that could not be found.


Action:

Consider defining a bean of type 'com.yupi.springbootinit.service.SearchService' in your configuration.

2025-07-18 10:15:11.716 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:15:11.716 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:15:12.048 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.048 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.048 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.048 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.048 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:15:12.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:15:12.152 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:15:12.153 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:15:12.153 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:15:12.153 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:15:12.170 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:15:12.170 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 452 ms
2025-07-18 10:15:12.251 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@77f46abd'
2025-07-18 10:15:12.267 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:15:12.288 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:15:12.304 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:15:12.324 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:15:12.342 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:15:12.351 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:15:12.353 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:15:12.358 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:15:12.359 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:15:12.359 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:15:12.359 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:15:12.360 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:15:12.376 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:15:12.391 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:15:12.405 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:15:12.422 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:15:12.433 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:15:12.436 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:15:12.442 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:15:12.456 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:15:12.470 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:15:12.483 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:15:12.492 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:15:12.516 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:15:12.526 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:15:12.540 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:15:12.552 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:15:12.564 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:15:12.574 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:15:12.584 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:15:12.594 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:15:12.687 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:15:12.692 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:15:12.693 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:15:12.789 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:15:12.802 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:15:12.898 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:15:13.736 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-18 10:15:13.871 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-18 10:15:13.956 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-18 10:15:14.026 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-18 10:15:14.133 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:15:14.135 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-18 10:15:14.136 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-18 10:15:14.136 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-18 10:15:14.172 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-18 10:15:14.242 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-18 10:15:14.332 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-18 10:15:14.334 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-18 10:15:14.335 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-18 10:15:14.336 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-18 10:15:14.337 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-18 10:15:14.569 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.881 seconds (JVM running for 733.135)
2025-07-18 10:15:14.570 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-18 10:17:12.748 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:17:12.748 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:17:13.082 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:13.083 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:17:13.200 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:17:13.201 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:17:13.201 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:17:13.201 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:17:13.221 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:17:13.221 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 470 ms
2025-07-18 10:17:13.311 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@16e403c4'
2025-07-18 10:17:13.326 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:17:13.341 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:17:13.358 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:17:13.383 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:17:13.395 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:17:13.401 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:17:13.402 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:17:13.406 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:17:13.407 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:17:13.408 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:17:13.408 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:17:13.409 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:17:13.424 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:17:13.441 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:17:13.454 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:17:13.469 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:17:13.481 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:17:13.485 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:17:13.492 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:17:13.505 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:17:13.520 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:17:13.536 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:17:13.549 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:17:13.572 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:17:13.588 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:17:13.601 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:17:13.616 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:17:13.628 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:17:13.642 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:17:13.655 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:17:13.668 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:17:13.764 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:17:13.770 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:17:13.771 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:17:13.864 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:17:13.874 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:17:13.986 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:17:14.801 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-18 10:17:14.909 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-18 10:17:14.989 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-18 10:17:15.049 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-18 10:17:15.133 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:17:15.136 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-18 10:17:15.136 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-18 10:17:15.137 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-18 10:17:15.165 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-18 10:17:15.204 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-18 10:17:15.259 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-18 10:17:15.260 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-18 10:17:15.265 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-18 10:17:15.266 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-18 10:17:15.267 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-18 10:17:15.425 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.715 seconds (JVM running for 853.992)
2025-07-18 10:17:15.426 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-18 10:17:27.124 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:17:27.124 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:27.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:17:27.539 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:17:27.540 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:17:27.540 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:17:27.540 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:17:27.558 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:17:27.558 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 432 ms
2025-07-18 10:17:27.636 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@65c404ca'
2025-07-18 10:17:27.651 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:17:27.661 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:17:27.673 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:17:27.687 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:17:27.700 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:17:27.704 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:17:27.705 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:17:27.710 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:17:27.711 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:17:27.721 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:17:27.721 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:17:27.721 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:17:27.735 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:17:27.747 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:17:27.757 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:17:27.773 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:17:27.785 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:17:27.787 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:17:27.793 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:17:27.806 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:17:27.822 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:17:27.841 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:17:27.856 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:17:27.874 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:17:27.892 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:17:27.912 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:17:27.927 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:17:27.951 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:17:27.962 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:17:27.973 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:17:27.984 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:17:28.075 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:17:28.084 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:17:28.085 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:17:28.180 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:17:28.191 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:17:28.287 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:17:29.063 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-18 10:17:29.158 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-18 10:17:29.223 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-18 10:17:29.277 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-18 10:17:29.365 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:17:29.379 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-18 10:17:29.381 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-18 10:17:29.382 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-18 10:17:29.410 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-18 10:17:29.447 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-18 10:17:29.503 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-18 10:17:29.504 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-18 10:17:29.505 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-18 10:17:29.506 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-18 10:17:29.506 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-18 10:17:29.693 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.596 seconds (JVM running for 868.259)
2025-07-18 10:17:29.694 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-18 10:17:42.148 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:17:42.149 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:17:42.505 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:17:42.506 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:17:42.631 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:17:42.632 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:17:42.633 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:17:42.633 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:17:42.651 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:17:42.651 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 500 ms
2025-07-18 10:17:42.741 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@5da03575'
2025-07-18 10:17:42.759 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:17:42.773 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:17:42.787 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:17:42.801 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:17:42.822 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:17:42.825 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:17:42.826 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:17:42.833 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:17:42.834 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:17:42.834 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:17:42.834 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:17:42.835 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:17:42.849 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:17:42.860 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:17:42.874 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:17:42.891 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:17:42.904 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:17:42.907 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:17:42.916 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:17:42.926 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:17:42.940 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:17:42.956 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:17:42.968 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:17:42.983 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:17:43.002 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:17:43.015 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:17:43.027 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:17:43.042 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:17:43.056 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:17:43.067 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:17:43.079 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:17:43.187 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:17:43.193 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:17:43.199 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:17:43.292 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:17:43.304 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:17:43.402 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:17:44.199 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-18 10:17:44.301 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-18 10:17:44.388 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-18 10:17:44.443 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-18 10:17:44.526 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:17:44.541 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-18 10:17:44.541 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-18 10:17:44.542 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-18 10:17:44.571 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-18 10:17:44.608 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-18 10:17:44.659 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-18 10:17:44.659 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-18 10:17:44.660 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-18 10:17:44.662 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-18 10:17:44.664 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-18 10:17:44.806 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.698 seconds (JVM running for 883.372)
2025-07-18 10:17:44.807 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-18 10:18:05.843 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:18:05.843 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.208 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:06.209 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:18:06.223 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.CannotLoadBeanClassException: Error loading class [com.yupi.springbootinit.service.impl.TagServiceImpl] for bean with name 'tagServiceImpl' defined in file [D:\Project\fox\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\service\impl\TagServiceImpl.class]: problem with class file or dependent class; nested exception is java.lang.ClassFormatError: Duplicate method name "followTag" with signature "(Ljava.lang.Long;Ljava.lang.Long;)Z" in class file com/yupi/springbootinit/service/impl/TagServiceImpl
2025-07-18 10:18:06.226 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:18:06.230 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.CannotLoadBeanClassException: Error loading class [com.yupi.springbootinit.service.impl.TagServiceImpl] for bean with name 'tagServiceImpl' defined in file [D:\Project\fox\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\service\impl\TagServiceImpl.class]: problem with class file or dependent class; nested exception is java.lang.ClassFormatError: Duplicate method name "followTag" with signature "(Ljava.lang.Long;Ljava.lang.Long;)Z" in class file com/yupi/springbootinit/service/impl/TagServiceImpl
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveBeanClass(AbstractBeanFactory.java:1545)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:704)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:674)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getType(AbstractBeanFactory.java:722)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findMergedAnnotationOnBean(DefaultListableBeanFactory.java:751)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAnnotationOnBean(DefaultListableBeanFactory.java:744)
	at org.springframework.boot.sql.init.dependency.AnnotationDependsOnDatabaseInitializationDetector.detect(AnnotationDependsOnDatabaseInitializationDetector.java:36)
	at org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor.detectDependsOnInitializationBeanNames(DatabaseInitializationDependencyConfigurer.java:148)
	at org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor.postProcessBeanFactory(DatabaseInitializationDependencyConfigurer.java:111)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:325)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:191)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:746)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:564)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.ClassFormatError: Duplicate method name "followTag" with signature "(Ljava.lang.Long;Ljava.lang.Long;)Z" in class file com/yupi/springbootinit/service/impl/TagServiceImpl
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1017)
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:150)
	at java.base/java.net.URLClassLoader.defineClass(URLClassLoader.java:524)
	at java.base/java.net.URLClassLoader$1.run(URLClassLoader.java:427)
	at java.base/java.net.URLClassLoader$1.run(URLClassLoader.java:421)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.base/java.net.URLClassLoader.findClass(URLClassLoader.java:420)
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.findClass(RestartClassLoader.java:160)
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:142)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.beans.factory.support.AbstractBeanDefinition.resolveBeanClass(AbstractBeanDefinition.java:469)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doResolveBeanClass(AbstractBeanFactory.java:1607)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveBeanClass(AbstractBeanFactory.java:1534)
	... 24 common frames omitted
2025-07-18 10:18:42.215 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:18:42.215 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:18:42.577 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:18:42.578 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:18:42.676 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:18:42.676 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:18:42.676 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:18:42.676 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:18:42.696 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:18:42.696 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 479 ms
2025-07-18 10:18:42.774 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1ff9e7cd'
2025-07-18 10:18:42.790 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:18:42.805 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:18:42.819 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:18:42.835 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:18:42.849 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:18:42.853 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:18:42.854 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:18:42.858 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:18:42.859 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:18:42.860 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:18:42.860 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:18:42.861 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:18:42.891 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:18:42.916 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:18:42.927 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:18:42.943 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:18:42.957 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:18:42.961 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:18:42.970 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:18:42.988 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:18:43.000 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:18:43.013 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:18:43.023 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:18:43.037 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:18:43.048 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:18:43.058 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:18:43.073 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:18:43.093 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:18:43.108 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:18:43.119 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:18:43.130 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:18:43.225 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:18:43.232 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:18:43.234 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:18:43.322 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:18:43.335 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:18:43.436 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:18:44.167 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-18 10:18:44.276 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-18 10:18:44.342 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-18 10:18:44.417 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-18 10:18:44.504 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:18:44.506 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-18 10:18:44.507 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-18 10:18:44.507 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-18 10:18:44.536 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-18 10:18:44.574 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-18 10:18:44.626 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-18 10:18:44.627 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-18 10:18:44.631 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-18 10:18:44.632 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-18 10:18:44.633 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-18 10:18:44.809 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.623 seconds (JVM running for 943.375)
2025-07-18 10:18:44.810 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-18 10:19:36.022 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:19:36.022 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.327 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.328 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.328 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.328 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.328 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.328 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.328 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.328 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.328 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.328 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:36.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:19:36.462 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:19:36.463 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:19:36.464 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:19:36.464 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:19:36.484 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:19:36.484 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 460 ms
2025-07-18 10:19:36.565 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@355de500'
2025-07-18 10:19:36.580 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:19:36.592 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:19:36.607 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:19:36.623 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:19:36.636 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:19:36.640 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:19:36.641 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:19:36.645 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:19:36.647 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:19:36.648 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:19:36.649 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:19:36.649 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:19:36.660 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:19:36.682 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:19:36.691 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:19:36.707 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:19:36.719 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:19:36.721 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:19:36.729 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:19:36.741 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:19:36.755 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:19:36.768 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:19:36.783 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:19:36.797 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:19:36.808 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:19:36.821 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:19:36.835 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:19:36.847 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:19:36.858 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:19:36.870 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:19:36.881 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:19:36.984 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:19:36.989 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:19:36.990 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:19:37.082 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:19:37.092 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:19:37.182 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:19:37.272 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'notificationController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.yupi.springbootinit.service.NotificationService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
2025-07-18 10:19:37.273 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:19:37.278 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:19:37.283 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.yupi.springbootinit.service.NotificationService' that could not be found.


Action:

Consider defining a bean of type 'com.yupi.springbootinit.service.NotificationService' in your configuration.

2025-07-18 10:19:52.243 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:19:52.246 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:19:52.636 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:19:52.739 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:19:52.739 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:19:52.740 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:19:52.740 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:19:52.758 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:19:52.758 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 507 ms
2025-07-18 10:19:52.839 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@7cc10c9'
2025-07-18 10:19:52.858 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:19:52.872 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:19:52.888 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:19:52.906 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:19:52.926 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:19:52.934 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:19:52.936 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:19:52.941 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:19:52.943 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:19:52.944 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:19:52.944 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:19:52.945 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:19:52.959 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:19:52.973 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:19:52.984 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:19:52.997 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:19:53.017 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:19:53.020 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:19:53.026 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:19:53.038 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:19:53.050 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:19:53.062 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:19:53.073 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:19:53.088 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:19:53.101 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:19:53.112 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:19:53.123 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:19:53.137 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:19:53.150 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:19:53.159 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:19:53.171 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:19:53.268 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:19:53.274 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:19:53.275 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:19:53.376 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:19:53.391 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:19:53.495 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:19:53.588 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'notificationController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.yupi.springbootinit.service.NotificationService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
2025-07-18 10:19:53.589 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:19:53.593 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:19:53.599 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.yupi.springbootinit.service.NotificationService' that could not be found.


Action:

Consider defining a bean of type 'com.yupi.springbootinit.service.NotificationService' in your configuration.

2025-07-18 10:20:36.886 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:20:36.886 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:20:37.215 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.215 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.215 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.215 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.215 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:37.216 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:20:37.324 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:20:37.324 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:20:37.324 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:20:37.324 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:20:37.343 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:20:37.343 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 454 ms
2025-07-18 10:20:37.423 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@db902c7'
2025-07-18 10:20:37.439 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:20:37.450 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:20:37.459 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:20:37.474 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:20:37.488 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:20:37.491 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:20:37.493 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:20:37.500 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:20:37.501 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:20:37.502 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:20:37.502 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:20:37.503 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:20:37.518 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:20:37.536 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:20:37.549 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:20:37.564 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:20:37.575 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:20:37.580 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:20:37.589 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:20:37.602 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:20:37.614 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:20:37.626 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:20:37.654 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:20:37.670 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:20:37.684 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:20:37.694 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:20:37.709 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:20:37.723 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:20:37.736 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:20:37.747 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:20:37.758 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:20:37.865 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:20:37.871 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:20:37.872 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:20:37.973 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:20:37.986 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:20:38.091 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:20:38.193 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'notificationController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.yupi.springbootinit.service.NotificationService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
2025-07-18 10:20:38.193 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:20:38.199 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:20:38.202 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.yupi.springbootinit.service.NotificationService' that could not be found.


Action:

Consider defining a bean of type 'com.yupi.springbootinit.service.NotificationService' in your configuration.

2025-07-18 10:20:50.442 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:20:50.443 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.833 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:20:50.834 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:20:50.940 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:20:50.940 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:20:50.940 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:20:50.940 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:20:50.963 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:20:50.963 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 515 ms
2025-07-18 10:20:51.051 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@b9f3868'
2025-07-18 10:20:51.070 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:20:51.085 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:20:51.105 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:20:51.126 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:20:51.148 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:20:51.155 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:20:51.157 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:20:51.162 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:20:51.164 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:20:51.165 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:20:51.165 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:20:51.166 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:20:51.178 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:20:51.192 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:20:51.203 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:20:51.216 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:20:51.225 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:20:51.231 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:20:51.239 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:20:51.260 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:20:51.275 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:20:51.289 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:20:51.301 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:20:51.314 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:20:51.324 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:20:51.336 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:20:51.347 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:20:51.359 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:20:51.371 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:20:51.383 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:20:51.395 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:20:51.488 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:20:51.493 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:20:51.497 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:20:51.588 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:20:51.600 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:20:51.693 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:20:51.793 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'notificationController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.yupi.springbootinit.service.NotificationService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
2025-07-18 10:20:51.794 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:20:51.802 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:20:51.805 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.yupi.springbootinit.service.NotificationService' that could not be found.


Action:

Consider defining a bean of type 'com.yupi.springbootinit.service.NotificationService' in your configuration.

2025-07-18 10:21:08.000 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:21:08.001 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.369 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:08.370 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:21:08.486 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:21:08.486 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:21:08.486 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:21:08.486 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:21:08.505 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:21:08.505 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 502 ms
2025-07-18 10:21:08.586 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@72f60eb5'
2025-07-18 10:21:08.600 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:21:08.610 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:21:08.623 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:21:08.636 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:21:08.649 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:21:08.652 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:21:08.654 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:21:08.657 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:21:08.658 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:21:08.659 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:21:08.659 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:21:08.659 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:21:08.674 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:21:08.701 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:21:08.710 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:21:08.725 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:21:08.737 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:21:08.740 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:21:08.751 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:21:08.760 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:21:08.775 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:21:08.789 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:21:08.801 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:21:08.813 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:21:08.824 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:21:08.837 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:21:08.849 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:21:08.860 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:21:08.880 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:21:08.891 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:21:08.905 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:21:09.002 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:21:09.007 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:21:09.009 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:21:09.122 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:21:09.135 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:21:09.236 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:21:09.315 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'notificationController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.yupi.springbootinit.service.NotificationService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
2025-07-18 10:21:09.316 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:21:09.321 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:21:09.324 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.yupi.springbootinit.service.NotificationService' that could not be found.


Action:

Consider defining a bean of type 'com.yupi.springbootinit.service.NotificationService' in your configuration.

2025-07-18 10:21:50.369 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:21:50.369 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.683 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.683 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.683 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.683 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.683 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:21:50.683 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:21:50.823 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:21:50.824 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:21:50.824 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:21:50.824 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:21:50.849 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:21:50.849 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 478 ms
2025-07-18 10:21:50.935 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@5663ee0d'
2025-07-18 10:21:50.951 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:21:50.961 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:21:50.974 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:21:50.990 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:21:51.003 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:21:51.006 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:21:51.007 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:21:51.011 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:21:51.015 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:21:51.016 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:21:51.016 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:21:51.016 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:21:51.027 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:21:51.042 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:21:51.055 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:21:51.069 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:21:51.090 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:21:51.092 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:21:51.102 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:21:51.112 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:21:51.126 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:21:51.142 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:21:51.156 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:21:51.171 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:21:51.192 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:21:51.206 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:21:51.220 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:21:51.237 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:21:51.249 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:21:51.257 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:21:51.272 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:21:51.367 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:21:51.372 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:21:51.373 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:21:51.468 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:21:51.482 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:21:51.583 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:21:51.659 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'notificationController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.yupi.springbootinit.service.NotificationService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
2025-07-18 10:21:51.661 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:21:51.668 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:21:51.671 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.yupi.springbootinit.service.NotificationService' that could not be found.


Action:

Consider defining a bean of type 'com.yupi.springbootinit.service.NotificationService' in your configuration.

2025-07-18 10:22:06.897 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:22:06.897 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.264 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:07.265 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:22:07.381 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:22:07.382 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:22:07.382 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:22:07.382 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:22:07.401 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:22:07.401 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 499 ms
2025-07-18 10:22:07.490 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@4fa6d089'
2025-07-18 10:22:07.507 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:22:07.534 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:22:07.550 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:22:07.567 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:22:07.583 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:22:07.587 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:22:07.589 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:22:07.593 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:22:07.595 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:22:07.597 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:22:07.597 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:22:07.597 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:22:07.614 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:22:07.629 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:22:07.643 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:22:07.668 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:22:07.679 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:22:07.684 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:22:07.691 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:22:07.701 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:22:07.713 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:22:07.725 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:22:07.737 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:22:07.750 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:22:07.759 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:22:07.772 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:22:07.785 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:22:07.798 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:22:07.808 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:22:07.821 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:22:07.833 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:22:07.926 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:22:07.934 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:22:07.936 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:22:08.039 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:22:08.051 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:22:08.157 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:22:08.247 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'notificationController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.yupi.springbootinit.service.NotificationService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
2025-07-18 10:22:08.249 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 10:22:08.255 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 10:22:08.258 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.yupi.springbootinit.service.NotificationService' that could not be found.


Action:

Consider defining a bean of type 'com.yupi.springbootinit.service.NotificationService' in your configuration.

2025-07-18 10:22:59.356 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:22:59.357 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.681 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:22:59.682 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:22:59.793 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:22:59.796 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:22:59.796 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:22:59.796 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:22:59.815 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:22:59.815 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 457 ms
2025-07-18 10:22:59.897 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1717af1d'
2025-07-18 10:22:59.910 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:22:59.922 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:22:59.935 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:22:59.949 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:22:59.959 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:22:59.966 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:22:59.967 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:22:59.972 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:22:59.972 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:22:59.973 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:22:59.973 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:22:59.974 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:22:59.988 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:23:00.003 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:23:00.014 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:23:00.034 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:23:00.043 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:23:00.050 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:23:00.057 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:23:00.068 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:23:00.080 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:23:00.091 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:23:00.115 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:23:00.126 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:23:00.139 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:23:00.154 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:23:00.165 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:23:00.175 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:23:00.189 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:23:00.200 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:23:00.210 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:23:00.302 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:23:00.307 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:23:00.308 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:23:00.399 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:23:00.410 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:23:00.500 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:23:01.481 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-18 10:23:01.576 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-18 10:23:01.640 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-18 10:23:01.698 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-18 10:23:01.791 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:23:01.793 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-18 10:23:01.793 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-18 10:23:01.794 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-18 10:23:01.823 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-18 10:23:01.867 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-18 10:23:01.938 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-18 10:23:01.938 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-18 10:23:01.939 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-18 10:23:01.940 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-18 10:23:01.941 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-18 10:23:02.099 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.767 seconds (JVM running for 1200.665)
2025-07-18 10:23:02.100 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-18 10:23:31.987 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:23:31.987 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:23:32.344 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.345 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.346 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.346 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.346 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.346 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.346 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.346 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.346 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.346 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-18 10:23:32.346 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-18 10:23:32.466 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-18 10:23:32.467 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:23:32.467 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:23:32.467 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-18 10:23:32.485 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:23:32.485 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 496 ms
2025-07-18 10:23:32.563 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@2af152f4'
2025-07-18 10:23:32.632 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-18 10:23:32.642 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-18 10:23:32.656 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-18 10:23:32.669 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-18 10:23:32.680 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-18 10:23:32.685 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-18 10:23:32.686 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-18 10:23:32.690 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-18 10:23:32.691 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-18 10:23:32.691 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-18 10:23:32.691 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-18 10:23:32.692 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-18 10:23:32.707 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-18 10:23:32.721 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-18 10:23:32.732 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-18 10:23:32.747 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-18 10:23:32.760 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-18 10:23:32.766 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - This "postId" is the table primary key by @TableId annotation in Class: "com.yupi.springbootinit.model.entity.PostStats",So @TableField annotation will not work!
2025-07-18 10:23:32.774 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-18 10:23:32.785 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-18 10:23:32.798 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-18 10:23:32.810 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-18 10:23:32.823 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-18 10:23:32.846 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-18 10:23:32.859 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-18 10:23:32.874 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-18 10:23:32.884 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-18 10:23:32.895 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-18 10:23:32.906 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-18 10:23:32.917 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-18 10:23:32.926 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-18 10:23:33.023 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-18 10:23:33.029 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-18 10:23:33.031 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-18 10:23:33.118 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-18 10:23:33.129 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-18 10:23:33.221 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-18 10:23:34.132 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-18 10:23:34.235 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-18 10:23:34.305 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-18 10:23:34.364 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-18 10:23:34.466 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-18 10:23:34.469 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-18 10:23:34.469 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-18 10:23:34.470 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-18 10:23:34.501 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-18 10:23:34.540 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-18 10:23:34.618 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-18 10:23:34.618 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-18 10:23:34.619 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-18 10:23:34.620 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-18 10:23:34.621 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-18 10:23:34.765 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.889 seconds (JVM running for 1233.331)
2025-07-18 10:23:34.766 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-18 10:39:25.852 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:39:25.852 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:39:26.871 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
2025-07-18 10:39:26.876 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:189)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:331)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:746)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:564)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.core.io.ClassPathResource.getInputStream(ClassPathResource.java:199)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:55)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:49)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.createMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:86)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.getMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:73)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:81)
	at org.springframework.context.annotation.ConfigurationClassParser.asSourceClass(ConfigurationClassParser.java:696)
	at org.springframework.context.annotation.ConfigurationClassParser$SourceClass.getInterfaces(ConfigurationClassParser.java:1024)
	at org.springframework.context.annotation.ConfigurationClassParser.processInterfaces(ConfigurationClassParser.java:386)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:332)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:199)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:304)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:207)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:175)
	... 18 common frames omitted
2025-07-18 10:45:51.536 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:45:51.536 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:45:51.561 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
2025-07-18 10:45:51.562 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:189)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:331)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:746)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:564)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.core.io.ClassPathResource.getInputStream(ClassPathResource.java:199)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:55)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:49)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.createMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:86)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.getMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:73)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:81)
	at org.springframework.context.annotation.ConfigurationClassParser.asSourceClass(ConfigurationClassParser.java:696)
	at org.springframework.context.annotation.ConfigurationClassParser$SourceClass.getInterfaces(ConfigurationClassParser.java:1024)
	at org.springframework.context.annotation.ConfigurationClassParser.processInterfaces(ConfigurationClassParser.java:386)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:332)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:199)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:304)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:207)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:175)
	... 18 common frames omitted
2025-07-18 10:48:13.031 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:48:13.031 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:48:13.080 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
2025-07-18 10:48:13.083 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:189)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:331)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:746)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:564)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.core.io.ClassPathResource.getInputStream(ClassPathResource.java:199)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:55)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:49)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.createMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:86)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.getMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:73)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:81)
	at org.springframework.context.annotation.ConfigurationClassParser.asSourceClass(ConfigurationClassParser.java:696)
	at org.springframework.context.annotation.ConfigurationClassParser$SourceClass.getInterfaces(ConfigurationClassParser.java:1024)
	at org.springframework.context.annotation.ConfigurationClassParser.processInterfaces(ConfigurationClassParser.java:386)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:332)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:199)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:304)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:207)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:175)
	... 18 common frames omitted
2025-07-18 10:48:25.890 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:48:25.890 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:48:25.939 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
2025-07-18 10:48:25.942 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:189)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:331)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:746)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:564)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.core.io.ClassPathResource.getInputStream(ClassPathResource.java:199)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:55)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:49)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.createMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:86)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.getMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:73)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:81)
	at org.springframework.context.annotation.ConfigurationClassParser.asSourceClass(ConfigurationClassParser.java:696)
	at org.springframework.context.annotation.ConfigurationClassParser$SourceClass.getInterfaces(ConfigurationClassParser.java:1024)
	at org.springframework.context.annotation.ConfigurationClassParser.processInterfaces(ConfigurationClassParser.java:386)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:332)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:199)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:304)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:207)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:175)
	... 18 common frames omitted
2025-07-18 10:48:37.958 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:48:37.958 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:48:38.006 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
2025-07-18 10:48:38.008 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:189)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:331)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:746)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:564)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.core.io.ClassPathResource.getInputStream(ClassPathResource.java:199)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:55)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:49)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.createMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:86)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.getMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:73)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:81)
	at org.springframework.context.annotation.ConfigurationClassParser.asSourceClass(ConfigurationClassParser.java:696)
	at org.springframework.context.annotation.ConfigurationClassParser$SourceClass.getInterfaces(ConfigurationClassParser.java:1024)
	at org.springframework.context.annotation.ConfigurationClassParser.processInterfaces(ConfigurationClassParser.java:386)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:332)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:199)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:304)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:207)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:175)
	... 18 common frames omitted
2025-07-18 10:49:27.276 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:49:27.276 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:49:27.325 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
2025-07-18 10:49:27.329 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:189)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:331)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:746)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:564)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.core.io.ClassPathResource.getInputStream(ClassPathResource.java:199)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:55)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:49)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.createMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:86)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.getMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:73)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:81)
	at org.springframework.context.annotation.ConfigurationClassParser.asSourceClass(ConfigurationClassParser.java:696)
	at org.springframework.context.annotation.ConfigurationClassParser$SourceClass.getInterfaces(ConfigurationClassParser.java:1024)
	at org.springframework.context.annotation.ConfigurationClassParser.processInterfaces(ConfigurationClassParser.java:386)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:332)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:199)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:304)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:207)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:175)
	... 18 common frames omitted
2025-07-18 10:49:52.698 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 33024 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-18 10:49:52.698 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-18 10:49:52.751 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
2025-07-18 10:49:52.754 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.yupi.springbootinit.MainApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:189)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:331)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:746)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:564)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.io.FileNotFoundException: class path resource [com/yupi/springbootinit/service/NotificationService.class] cannot be opened because it does not exist
	at org.springframework.core.io.ClassPathResource.getInputStream(ClassPathResource.java:199)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:55)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:49)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.createMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:86)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.getMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:73)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:81)
	at org.springframework.context.annotation.ConfigurationClassParser.asSourceClass(ConfigurationClassParser.java:696)
	at org.springframework.context.annotation.ConfigurationClassParser$SourceClass.getInterfaces(ConfigurationClassParser.java:1024)
	at org.springframework.context.annotation.ConfigurationClassParser.processInterfaces(ConfigurationClassParser.java:386)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:332)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:199)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:304)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:250)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:207)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:175)
	... 18 common frames omitted
