<template>
	<view class="userReport" v-if="loding">
		
		<view class="yueb">
			<swiper class="swiper" :current="swiperIndex" :vertical="true" @change="swiperChange" @animationfinish="swiperEnd">
				<swiper-item>
					<view class="swiper-item">
						<view class="yueb_one_l animate__animated animate__bounceInLeft" v-if="configDate.oneAni1"><image :src="imgbaseUrlOss + '/userreport/icon2.png'"></image></view>
						<view class="yueb_one_r animate__animated  animate__bounceInRight" v-if="configDate.oneAni2"><image :src="imgbaseUrlOss + '/userreport/icon1.png'"></image></view>
						<view class="yueb_one_f animate__animated  animate__bounceInUp" v-if="configDate.oneAni3"><image :src="imgbaseUrlOss + '/userreport/icon3.png'"></image><view @click="swiperIndex = 1"><text>立即</text><text>查看</text></view></view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item">
						<image class="yueb_two_a animate__animated animate__bounceInRight"  v-if="configDate.twoAni0"  mode="widthFix" :src="imgbaseUrlOss + '/userreport/icon4.png'">
						<view class="yueb_two_b">
							<view class="yueb_two_b_li">
								<view class="animate__animated animate__bounceInDown" v-if="configDate.twoAni1">这个月里</view>
								<view class="animate__animated animate__bounceInDown" v-if="configDate.twoAni2">你一共在FOX约课<text>11</text>次，<text>660</text>分钟</view>
							</view>
							<view class="yueb_two_b_li">
								<view class="animate__animated animate__bounceInDown" v-if="configDate.twoAni3">你热衷<text>跳舞</text></view>
								<view class="animate__animated animate__bounceInDown" v-if="configDate.twoAni4">喜欢<text>爵士舞</text>，和<text>拉丁舞</text></view>
							</view>
							<view class="yueb_two_b_li">
								<view class="animate__animated animate__bounceInDown" v-if="configDate.twoAni5">你热爱<text>学习</text></view>
								<view class="animate__animated animate__bounceInDown" v-if="configDate.twoAni6">舞蹈课里藏着你的许多回忆</view>
							</view>
						</view>
						<image  v-if="configDate.twoAni7" class="yueb_two_f animate__animated animate__bounceInRight" mode="widthFix" :src="imgbaseUrlOss + '/userreport/icon5.png'">
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item">
						<image class="yueb_two_a animate__animated animate__bounceInRight" v-if="configDate.thrAni1"  mode="widthFix" :src="imgbaseUrlOss + '/userreport/icon4.png'">
						<view class="yueb_two_b">
							<view class="yueb_two_b_li">
								<view class="animate__animated animate__bounceInLeft" v-if="configDate.thrAni2">这个月里</view>
								<view class="animate__animated animate__bounceInLeft" v-if="configDate.thrAni3">你一共在FOX约课<text>0</text>次，<text>0</text>分钟</view>
							</view>
							<view class="yueb_two_b_li">
								<view class="animate__animated animate__bounceInLeft" v-if="configDate.thrAni4" style="width:470rpx;">别让遗忘成为你的绊脚石，记得完成你的计划哦！</view>
							</view>
						</view>
						<image class="yueb_two_f animate__animated animate__bounceInUp" v-if="configDate.thrAni5" mode="widthFix" :src="imgbaseUrlOss + '/userreport/icon5.png'">
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item">
						<view class="yueb_fou">
							<view class="yueb_fou_a animate__animated animate__bounceInDown" v-if="configDate.fouAni1">最近一次你上的课程是JOASON的</view>
							<view class="yueb_fou_b animate__animated animate__jackInTheBox" v-if="configDate.fouAni2">《爵士练习课》</view>
							<image class="yueb_fou_f animate__animated animate__bounceInUp" v-if="configDate.fouAni3"  mode="widthFix" :src="imgbaseUrlOss + '/userreport/icon6.png'">
						</view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item">
						<view class="yueb_fou yueb_five">
							<view class="yueb_fou_a animate__animated animate__bounceInRight" v-if="configDate.fiveAni1">本月你还没有上课</view>
							<view class="yueb_fou_a yueb_five_b animate__animated animate__bounceInRight" v-if="configDate.fiveAni2">生活虽忙，但别忘了给自己的计划打个勾，那件事你还没做，记得补上哦！</view>
							<image class="yueb_fou_f animate__animated animate__bounceInRight" v-if="configDate.fiveAni3"  mode="widthFix" :src="imgbaseUrlOss + '/userreport/icon6.png'">
						</view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item">
						<view class="yueb_six">
							<view class="yueb_six_a">
								<view class="animate__animated animate__bounceInLeft" v-if="configDate.sixAni1">在家里</view>
								<view class="animate__animated animate__bounceInLeft" v-if="configDate.sixAni2">FOX也陪伴着你</view>
							</view>
							<view class="yueb_six_b">
								<view class="yueb_six_b_a animate__animated animate__bounceInRight" v-if="configDate.sixAni3">你的云课观看时长超过</view>
								<view class="yueb_six_b_a animate__animated animate__bounceInRight" v-if="configDate.sixAni4"><text>91%</text>用户</view>
							</view>
							<view class="yueb_six_c">
								<view class="yueb_six_c_a animate__animated animate__bounceInLeft" v-if="configDate.sixAni5"><text>《成为街舞高手的3个心得》</text>这节课</view>
								<view class="yueb_six_c_a animate__animated animate__bounceInLeft" v-if="configDate.sixAni6">是你看过次数最多的</view>
							</view>
							<image class="yueb_six_f animate__animated animate__bounceInUp" v-if="configDate.sixAni7"  mode="widthFix" :src="imgbaseUrlOss + '/userreport/icon7.png'">
						</view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="swiper-item">
						<view class="yueb_six">
							<view class="yueb_six_a animate__animated animate__bounceInRight">
								<view class="animate__animated animate__bounceInLeft" v-if="configDate.sevAni1">在家里</view>
								<view class="animate__animated animate__bounceInLeft" v-if="configDate.sevAni2">FOX也陪伴着你</view>
							</view>
							<view class="yueb_six_b" style="margin-right:46rpx;">
								<view class="yueb_six_b_a animate__animated animate__bounceInRight" v-if="configDate.sevAni3"><text style="font-size:32rpx;">本月你还未观看线上课程</text>，忙碌中也要记得照顾好每一件事，那个被你遗忘的小任务正在等你呢！</view>
							</view>
							<image class="yueb_six_f animate__animated animate__bounceInUp" v-if="configDate.sevAni4"  mode="widthFix" :src="imgbaseUrlOss + '/userreport/icon7.png'">
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- <view class="userd1 animate__animated animate__bounce">搜索</view>
		
		<view class="userd1 animate__animated animate__fadeInLeftBig">搜索</view> -->

	</view>
</template>


<script>
export default {
	data() {
		return {
			imgbaseUrl:'',//图片地址
			imgbaseUrlOss:'',
			loding:false,
			isLogined:true,
			swiperIndex:0,
			configDate:{
				oneAni1:false,
				oneAni2:false,
				oneAni3:false,
				
				twoAni0:false,
				twoAni1:false,
				twoAni2:false,
				twoAni3:false,
				twoAni4:false,
				twoAni5:false,
				twoAni6:false,
				twoAni7:false,
				
				thrAni1:false,
				thrAni2:false,
				thrAni3:false,
				thrAni4:false,
				thrAni5:false,
				
				fouAni1:false,
				fouAni2:false,
				fouAni3:false,
				
				fiveAni1:false,
				fiveAni2:false,
				fiveAni3:false,
				
				sixAni1:false,
				sixAni2:false,
				sixAni3:false,
				sixAni4:false,
				sixAni5:false,
				sixAni6:false,
				sixAni7:false,
				
				sevAni1:false,
				sevAni2:false,
				sevAni3:false,
				sevAni4:false,
			}
		}
	},
	onShow: function(){
	   this.imgbaseUrl = this.$baseUrl;
	   this.imgbaseUrlOss = this.$baseUrlOss;
	   
	  
	},
	onLoad() {
		
		uni.showLoading({
			title: '加载中'
		});
		var that = this;
		setTimeout(function(){
		   uni.hideLoading();
		   that.loding = true;
		   that.cshData()
		},1000)
		
		
	},
	methods: {
		//初始化
		cshData(){
			var that = this;
			if(this.swiperIndex == 0){
				that.configDate.oneAni1 = true;
				setTimeout(()=>{
					that.configDate.oneAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.oneAni3 = true;
				},2000);
			}
		},
		//监听swiper
		swiperChange(e){
			var that = this;
			// console.log(e,'监听swiper')
			this.swiperIndex = e.detail.current
			
			if(this.swiperIndex == 1){
				that.configDate.twoAni0 = true;
				setTimeout(()=>{
					that.configDate.twoAni1 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.twoAni2 = true;
				},2000);
				setTimeout(()=>{
					that.configDate.twoAni3 = true;
				},3000);
				setTimeout(()=>{
					that.configDate.twoAni4 = true;
				},4000);
				setTimeout(()=>{
					that.configDate.twoAni5 = true;
				},5000);
				setTimeout(()=>{
					that.configDate.twoAni6 = true;
				},6000);
				setTimeout(()=>{
					that.configDate.twoAni7 = true;
				},7000);
			}
			
			if(this.swiperIndex == 2){
				that.configDate.thrAni1 = true;
				setTimeout(()=>{
					that.configDate.thrAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.thrAni3 = true;
				},2000);
				setTimeout(()=>{
					that.configDate.thrAni4 = true;
				},3000);
				setTimeout(()=>{
					that.configDate.thrAni5 = true;
				},4000);
			}
			
			if(this.swiperIndex == 3){
				that.configDate.fouAni1 = true;
				setTimeout(()=>{
					that.configDate.fouAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.fouAni3 = true;
				},2000);
			}
			
			if(this.swiperIndex == 4){
				that.configDate.fiveAni1 = true;
				setTimeout(()=>{
					that.configDate.fiveAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.fiveAni3 = true;
				},2000);
			}
			
			if(this.swiperIndex == 5){
				that.configDate.sixAni1 = true;
				setTimeout(()=>{
					that.configDate.sixAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.sixAni3 = true;
				},2000);
				setTimeout(()=>{
					that.configDate.sixAni4 = true;
				},3000);
				setTimeout(()=>{
					that.configDate.sixAni5 = true;
				},4000);
				setTimeout(()=>{
					that.configDate.sixAni6 = true;
				},5000);
				setTimeout(()=>{
					that.configDate.sixAni7 = true;
				},6000);
			}
			
			
			if(this.swiperIndex == 6){
				that.configDate.sevAni1 = true;
				setTimeout(()=>{
					that.configDate.sevAni2 = true;
				},1000);
				setTimeout(()=>{
					that.configDate.sevAni3 = true;
				},2000);
				setTimeout(()=>{
					that.configDate.sevAni4 = true;
				},3000);
			}
		},
		//动画结束时会触发
		swiperEnd(e){
			// console.log(e,'动画结束时会触发')
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">

.userReport{-overflow: hidden;}
page{padding-bottom: 0;background:#fff;}
.userd1{
	width: 200rpx;
	height: 200rpx;
	background:red;
	margin: auto;
}
</style>