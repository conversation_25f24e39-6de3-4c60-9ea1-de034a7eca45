package com.yupi.springbootinit.model.dto.notification;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 消息通知查询请求
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@ApiModel(description = "消息通知查询请求")
public class NotificationQueryRequest implements Serializable {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    /**
     * 消息类型（all-全部, system-系统, like-点赞, follow-关注, comment-评论）
     */
    @ApiModelProperty(value = "消息类型", example = "all")
    private String type;

    /**
     * 是否已读（null-全部, true-已读, false-未读）
     */
    @ApiModelProperty(value = "是否已读", example = "false")
    private Boolean isRead;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer current;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer size;

    /**
     * 排序方式（time-时间, read-已读状态）
     */
    @ApiModelProperty(value = "排序方式", example = "time")
    private String sortBy;

    private static final long serialVersionUID = 1L;

    // 手动添加getter/setter方法
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getIsRead() {
        return isRead;
    }

    public void setIsRead(Boolean isRead) {
        this.isRead = isRead;
    }

    public Integer getCurrent() {
        return current;
    }

    public void setCurrent(Integer current) {
        this.current = current;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }
}
