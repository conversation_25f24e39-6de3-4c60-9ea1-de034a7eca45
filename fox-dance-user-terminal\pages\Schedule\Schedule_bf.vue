<template>
	<view class="schedule">
		<view :style="'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'"></view>
		<view class="my_head">
			<view class="my_head_n" :style="'margin-top:'+(safeAreaTop+10)+'px'">
				
				<view class="my_head_t" :style="'height:'+menuButtonInfoHeight+'px'">
					<view class="my_head_t_l"><image src="/static/images/icon18-1.png"></image><text>FOX岗顶店</text></view>
					<view class="my_head_t_title">约课</view>
				</view>
				<view class="my_head_b">
					<view class="lsxq_head_l">
						<view class="stor_thr_c_n">
							<view class="stor_thr_c_li" :class="jbToggle ? 'stor_thr_c_li_ac' : ''" @click="jbStartTap">{{jibText == '' ? '级别' : jibText}}<text></text></view>
							<view class="stor_thr_c_li" :class="wuzToggle ? 'stor_thr_c_li_ac' : ''" @click="wuzStartTap">{{wuzText == '' ? '舞种' : wuzText}}<text></text></view>
							<view class="stor_thr_c_li" :class="laosToggle ? 'stor_thr_c_li_ac' : ''" @click="laosStartTap">{{laosText == '' ? '老师' : laosText}}<text></text></view>
						</view>
					</view>
					<view class="lsxq_head_r"><text></text><image src="/static/images/icon36.png" @click="navTo('/pages/Schedule/search')"></image></view>
					
				</view>
			</view>
		</view>
		
		<view class="gg_rgba" v-if="jbToggle || wuzToggle || laosToggle" @click="gbTcTap"></view>
		<!-- 级别 go -->
		<view class="teaxzTanc" :style="'top:'+(safeAreaTop+menuButtonInfoHeight+60)+'px;'" v-if="jbToggle">
			<view class="teaxzTanc_t">
				<view v-for="(item,index) in jibLists" :key="index" :class="jibIndex == index ? 'teaxzTanc_t_ac' : ''" @click="jibTap(index)">{{item.name}}</view>
			</view>
			<view class="teaxzTanc_b"><view @click="jibReact">重置</view><text @click="jibSubTap">提交</text></view>
		</view>
		<!-- 级别 end -->
		<!-- 舞种 go -->
		<view class="teaxzTanc" :style="'top:'+(safeAreaTop+menuButtonInfoHeight+60)+'px;'" v-if="wuzToggle">
			<view class="teaxzTanc_t">
				<view v-for="(item,index) in wuzLists" :key="index" :class="wuzIndex == index ? 'teaxzTanc_t_ac' : ''" @click="wuzTap(index)">{{item.name}}</view>
			</view>
			<view class="teaxzTanc_b"><view @click="wuzReact">重置</view><text @click="wuzSubTap">提交</text></view>
		</view>
		<!-- 舞种 end -->
		<!-- 老师 go -->
		<view class="teaxzTanc" :style="'top:'+(safeAreaTop+menuButtonInfoHeight+60)+'px;'" v-if="laosToggle">
			<view class="teaxzTanc_t">
				<view v-for="(item,index) in laosLists" :key="index" :class="laosIndex == index ? 'teaxzTanc_t_ac' : ''" @click="laosTap(index)">{{item.name}}</view>
			</view>
			<view class="teaxzTanc_b"><view @click="laosReact">重置</view><text @click="laosSubTap">提交</text></view>
		</view>
		<!-- 老师 end -->
		
		
		<view class="rlxz_con" style="margin-top:120rpx;">
			<view class="rlxz_con_l">
				<scroll-view scroll-x="true" :scroll-left="scrollLeft" @scroll="scrollJt">
					<view class="rlxz_con_l_li" :class="sjsxIndex == index ? 'rlxz_con_l_li_ac' : ''" v-for="(item,index) in sjsxLists" :key="index" @click="sjsxTap(index,item)">
						<view>{{item.week}}</view><view>{{item.day}}</view><text></text>
					</view>
				</scroll-view>
			</view>
			<view class="rlxz_con_r">
				<image src="/static/images/icon53.png"></image>
				<picker mode="date" :value="date_sx" :start="startDate" :end="endDate" @change="bindDateChange_sx">
					<view class="uni-input">{{date_sx}}</view>
				</picker>
			</view>
		</view>
		
		<view class="md_xz">
			<image src="/static/images/icon52-1.png" class="md_xz_bj"></image>
			<view class="md_xz_title">{{array_md[index_md]}}</view>
			<image src="/static/images/icon52-2.png" class="md_xz_xt"></image>
			<picker @change="bindPickerChange_md" :value="index_md" :range="array_md">
				<view class="uni-input">{{array_md[index_md]}}</view>
			</picker>
		</view>
		
		<view class="notice flex">
			<view class="notice_l">
				<image src="/static/images/index_notice.png" mode="scaleToFill"></image>
			</view>
			<view class="notice_r flex-1">
				<uni-notice-bar scrollable single :text="notice_text" background-color="transparent" color="#333"
					:single="true"> </uni-notice-bar>
				<!-- <u-notice-bar mode="horizontal" :list="notice_text"></u-notice-bar> -->
			</view>
		</view>
		
		<view class="teaCon">
			<view class="teaCon_li" v-for="(item,index) in 4" :key="index">
				<view class="teaCon_li_a">拉丁舞练习</view>
				<view class="teaCon_li_b">
					<image src="/static/images/icon23.jpg" mode="aspectFill" class="teaCon_li_b_l"></image>
					<view class="teaCon_li_b_c">
						<view class="teaCon_li_b_c_a">16:00-17:00</view>
						<view class="teaCon_li_b_c_b">上课老师：LINDA</view>
						<view class="teaCon_li_b_c_c"><text>入门</text><text>拉丁舞</text></view>
					</view>
					<view class="teaCon_li_b_r" @click="navTo('/pages/Schedule/Schedulexq')">预约</view>
				</view>
				<view class="teaCon_li_c">
					<view class="teaCon_li_c_l">
						<image src="/static/images/toux.png" v-for="(item,index) in 6" :key="index"></image>
					</view>
					<view class="teaCon_li_c_r">已预约：<text>23</text>人;<text>3</text>人在等位</view>
				</view>
			</view>
		</view>
		
		<!-- 提示预约弹窗 go -->
		<view class="yytnCon" v-if="ljtkToggle"><view class="yytnCon_n"><image src="/static/images/icon55.png"></image><text @click="ljktTap"></text></view><image src="/static/images/icon56.png" @click="ljtkToggle = false"></image></view>
		<!-- 提示预约弹窗 end -->

		<tabbar ref="tabbar" :current="3"></tabbar>
	</view>
</template>


<script>
import tabbar from '@/components/tabbar.vue'
export default {
	components: {
		tabbar,
	},
	data() {
		const currentDate = this.getDate({
			format: true
		})
		return {
			safeAreaTop:wx.getWindowInfo().safeArea.top,
			menuButtonInfoHeight:uni.getMenuButtonBoundingClientRect().height,
			notice_text: ['这是一段滚动公告这是一段滚动公告这是一段滚动公告这是一段滚动公告这是一段滚动公告这是一段滚动公告这是一段滚动公告这是一段滚动公告'],
			
			jibLists:[{name:'级别1'},{name:'级别2'},{name:'级别3'},{name:'级别4'},{name:'级别5'},{name:'级别6'},{name:'级别1'},{name:'级别1'},{name:'级别1'},{name:'级别1'}],
			jibIndex:-1,
			jibText:'',
			jbToggle:false,
			
			wuzLists:[{name:'舞种1'},{name:'舞种2'},{name:'舞种3'},{name:'舞种4'},{name:'舞种5'},{name:'舞种6'},{name:'舞种1'},{name:'舞种1'},{name:'舞种'},{name:'舞种1'}],
			wuzIndex:-1,
			wuzText:'',
			wuzToggle:false,
			
			laosLists:[{name:'老师1'},{name:'老师2'},{name:'老师3'},{name:'老师4'},{name:'老师5'},{name:'老师6'},{name:'老师1'},{name:'老师1'},{name:'老师'},{name:'老师1'}],
			laosIndex:-1,
			laosText:'',
			laosToggle:false,
			
			sjsxLists:[],//时间筛选
			sjsxIndex:0,
			scrollLeft:0,
			date_sx: currentDate,
			array_md: ['FOX舞蹈岗顶店','门店1', '门店2', '门店3'],
			index_md: 0,
			
			ljtkToggle:false,
		}
	},
	onShow() {
		uni.hideTabBar()
	},
	onLoad() {
		uni.hideTabBar()
		let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		//获取胶囊对上高度
		this.searchBarTop = menuButtonInfo.top;
		//获取胶囊高度
		this.searchBarHeight = menuButtonInfo.height;
		
		console.log('getWindowInfo',wx.getWindowInfo());
		console.log('menuButtonInfo',menuButtonInfo);/**/
		
		this.dateDatasx(this.getFormattedCurrentDate());//获取最近15日
	},
	computed: {
		startDate() {
			return this.getDate('start');
		},
		endDate() {
			return this.getDate('end');
		}
	},
	methods: {
		//选择门店
		bindPickerChange_md: function(e) {
			console.log('picker发送选择改变，携带值为', e.detail.value)
			this.index_md = e.detail.value
		},
		//日期转化
		dateDatasx(date){
			let startDate = date;
			let daysToAdd = 15;
			this.sjsxLists = this.getDateArrayWithWeekday(startDate, daysToAdd);
			this.scrollLeft = 1;
		},
		//指定日期往后推迟xx日
		getDateArrayWithWeekday(startDateStr, daysToAdd) {
		      let dateArray = [];
		      let weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
		      let startDate = new Date(startDateStr);
		      for (let i = 0; i < daysToAdd; i++) {
		        let newDate = new Date(startDate);
		        newDate.setDate(startDate.getDate() + i);
		        let year = newDate.getFullYear();
		        let month = (newDate.getMonth() + 1).toString().padStart(2, '0');
		        let day = newDate.getDate().toString().padStart(2, '0');
		        let weekdayIndex = newDate.getDay();
		        let weekday = weekdays[weekdayIndex];
		        let formattedDate = `${year}-${month}-${day}（${weekday}）`;
		        dateArray.push({week:weekday,day:`${month}-${day}`,date:`${year}-${month}-${day}`});
		      }
		      return dateArray;
		},
		//获取当前日期
		getFormattedCurrentDate() {
		  let currentDate = new Date();
		  let year = currentDate.getFullYear();
		  let month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
		  let day = currentDate.getDate().toString().padStart(2, '0');
		  return `${year}-${month}-${day}`;
		},
		cIndex(e) {
			this.currentIndex = e.detail.current;			
		},
		scrollJt(e){
		},
		bindDateChange_sx: function(e) {
			var that = this;
			this.date_sx = e.detail.value;
			this.dateDatasx(this.date_sx);
			this.sjsxIndex = 0;
			setTimeout(function(){
				that.scrollLeft = 0;
			},0)
			
		},
		getDate(type) {
			const date = new Date();
			let year = date.getFullYear();
			let month = date.getMonth() + 1;
			let day = date.getDate();
		
			if (type === 'start') {
				year = year;
			} else if (type === 'end') {
				year = year + 1;
			}
			month = month > 9 ? month : '0' + month;
			day = day > 9 ? day : '0' + day;
			return `${year}-${month}-${day}`;
		},
		//时间筛选选择
		sjsxTap(index){
			this.sjsxIndex = index;
			console.log(this.getDate('start'),'sssss')
		},
		navTap(index){
			this.type = index;
		},
		//关闭所有弹窗
		gbTcTap(){
			this.jbToggle = false;
			this.wuzToggle = false;
			this.laosToggle = false;
		},
		//级别弹窗开启
		jbStartTap(){
			this.jbToggle = !this.jbToggle;
			this.wuzToggle = false;
			this.laosToggle = false;
		},
		//级别选择
		jibTap(index){
			this.jibIndex = index;
		},
		//级别提交
		jibSubTap(){
			if(this.jibIndex == -1){
				this.jibText = ''
			}else{
				this.jibText = this.jibLists[this.jibIndex].name
			}
			this.jbToggle = false;
		},
		//级别重置
		jibReact(){
			this.jibIndex = -1;
		},
		//舞种弹窗开启
		wuzStartTap(){
			this.jbToggle = false;
			this.wuzToggle = !this.wuzToggle;
			this.laosToggle = false;
		},
		//舞种选择
		wuzTap(index){
			this.wuzIndex = index;
		},
		//舞种提交
		wuzSubTap(){
			if(this.wuzIndex == -1){
				this.wuzText = ''
			}else{
				this.wuzText = this.wuzLists[this.wuzIndex].name
			}
			this.wuzToggle = false;
		},
		//舞种重置
		wuzReact(){
			this.wuzIndex = -1;
		},
		//老师弹窗开启
		laosStartTap(){			
			this.jbToggle = false;
			this.wuzToggle = false;
			this.laosToggle = !this.laosToggle;
		},
		//老师选择
		laosTap(index){
			this.laosIndex = index;
		},
		//老师提交
		laosSubTap(){
			if(this.laosIndex == -1){
				this.laosText = ''
			}else{
				this.laosText = this.laosLists[this.laosIndex].name
			}
			this.laosToggle = false;
		},
		//老师重置
		laosReact(){
			this.laosIndex = -1;
		},
		//立即开通会员
		ljktTap(){
			this.ljtkToggle = false;
			uni.switchTab({
				url:'/pages/buy/buy'
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
	}
}
</script>

<style lang="scss">
.schedule{overflow: hidden;}
page{padding-bottom: 0;}
.notice {
	margin: 26rpx;
	-width: 670rpx;
	height: 80rpx;
	background: #fff;
	padding: 0 26rpx;
	height: 72rpx;
	background: #FFFFFF;
	border-radius: 90rpx 90rpx 90rpx 90rpx;

	.notice_l {
		image {
			width: 32rpx;
			height: 32rpx;
		}
	}

	.notice_r {
		font-size: 26rpx;
		color: #333333;
		line-height: 30rpx;

		/deep/ .uni-noticebar {
			margin-bottom: 0 !important;
		}

	}
}
</style>