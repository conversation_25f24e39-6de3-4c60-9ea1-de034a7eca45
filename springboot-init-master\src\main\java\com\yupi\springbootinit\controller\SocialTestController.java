package com.yupi.springbootinit.controller;

import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.model.dto.user.UserProfileUpdateRequest;
import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.model.entity.UserStats;
import com.yupi.springbootinit.service.BaUserService;
import com.yupi.springbootinit.service.UserStatsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 社交模块测试接口
 * 用于验证阶段一的基础接口功能
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/social/test")
@Slf4j
@Api(tags = "社交模块测试接口")
public class SocialTestController {

    @Resource
    private BaUserService baUserService;

    @Resource
    private UserStatsService userStatsService;

    /**
     * 测试数据库连接和基础查询
     */
    @GetMapping("/database")
    @ApiOperation(value = "测试数据库连接")
    public BaseResponse<Map<String, Object>> testDatabase() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 测试用户表查询
            long userCount = baUserService.count();
            result.put("userCount", userCount);
            
            // 测试用户统计表查询
            long userStatsCount = userStatsService.count();
            result.put("userStatsCount", userStatsCount);
            
            // 测试查询第一个用户
            BaUser firstUser = baUserService.list().stream().findFirst().orElse(null);
            if (firstUser != null) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", firstUser.getId());
                userInfo.put("username", firstUser.getUsername());
                userInfo.put("nickname", firstUser.getNickname());
                userInfo.put("bio", firstUser.getBio());
                userInfo.put("danceType", firstUser.getDance_type());
                result.put("sampleUser", userInfo);
            }
            
            result.put("status", "success");
            result.put("message", "数据库连接正常");
            
            log.info("数据库测试成功 - userCount: {}, userStatsCount: {}", userCount, userStatsCount);
            return ResultUtils.success(result);
            
        } catch (Exception e) {
            log.error("数据库测试失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("status", "error");
            errorResult.put("message", "数据库连接失败: " + e.getMessage());
            return ResultUtils.success(errorResult);
        }
    }

    /**
     * 测试用户资料接口
     */
    @GetMapping("/user-profile/{userId}")
    @ApiOperation(value = "测试用户资料接口")
    public BaseResponse<Map<String, Object>> testUserProfile(@PathVariable Long userId) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 查询用户基本信息
            BaUser user = baUserService.getById(userId);
            if (user == null) {
                result.put("status", "error");
                result.put("message", "用户不存在");
                return ResultUtils.success(result);
            }
            
            // 查询用户统计信息
            UserStats userStats = userStatsService.getById(userId);
            if (userStats == null) {
                // 创建默认统计信息
                userStats = new UserStats();
                userStats.setUserId(userId);
                userStats.setFollowingCount(0);
                userStats.setFollowerCount(0);
                userStats.setPostCount(0);
                userStats.setLikeReceivedCount(0);
                userStatsService.save(userStats);
                result.put("statsCreated", true);
            }
            
            // 构建返回数据
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("userId", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("nickname", user.getNickname());
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("bio", user.getBio());
            userInfo.put("danceType", user.getDance_type());
            userInfo.put("level", user.getLevel());
            
            Map<String, Object> statsInfo = new HashMap<>();
            statsInfo.put("followingCount", userStats.getFollowingCount());
            statsInfo.put("followerCount", userStats.getFollowerCount());
            statsInfo.put("postCount", userStats.getPostCount());
            statsInfo.put("likeReceivedCount", userStats.getLikeReceivedCount());
            
            result.put("user", userInfo);
            result.put("stats", statsInfo);
            result.put("status", "success");
            
            log.info("用户资料测试成功 - userId: {}", userId);
            return ResultUtils.success(result);
            
        } catch (Exception e) {
            log.error("用户资料测试失败 - userId: {}", userId, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("status", "error");
            errorResult.put("message", "测试失败: " + e.getMessage());
            return ResultUtils.success(errorResult);
        }
    }

    /**
     * 测试用户资料更新
     */
    @PostMapping("/update-user/{userId}")
    @ApiOperation(value = "测试用户资料更新")
    public BaseResponse<Map<String, Object>> testUpdateUser(@PathVariable Long userId,
                                                           @RequestParam(required = false) String nickname,
                                                           @RequestParam(required = false) String bio,
                                                           @RequestParam(required = false) String danceType) {
        try {
            Map<String, Object> result = new HashMap<>();

            BaUser user = baUserService.getById(userId);
            if (user == null) {
                result.put("status", "error");
                result.put("message", "用户不存在");
                return ResultUtils.success(result);
            }

            // 构建更新请求对象
            UserProfileUpdateRequest updateRequest = new UserProfileUpdateRequest();
            updateRequest.setNickname(nickname);
            updateRequest.setBio(bio);
            updateRequest.setDanceType(danceType);

            // 使用新的更新方法（只更新非空字段）
            boolean success = baUserService.updateUserProfile(userId, updateRequest);

            if (success) {
                result.put("status", "success");
                result.put("message", "用户资料更新成功");
                result.put("updateMethod", "UpdateWrapper - 只更新非空字段");

                // 查询更新后的用户信息
                BaUser updatedUser = baUserService.getById(userId);
                result.put("updatedUser", updatedUser);
            } else {
                result.put("status", "error");
                result.put("message", "用户资料更新失败");
            }

            log.info("用户资料更新测试 - userId: {}, success: {}", userId, success);
            return ResultUtils.success(result);

        } catch (Exception e) {
            log.error("用户资料更新测试失败 - userId: {}", userId, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("status", "error");
            errorResult.put("message", "测试失败: " + e.getMessage());
            return ResultUtils.success(errorResult);
        }
    }

    /**
     * 测试UpdateWrapper精确更新功能
     */
    @PostMapping("/test-update-wrapper/{userId}")
    @ApiOperation(value = "测试UpdateWrapper精确更新功能")
    public BaseResponse<Map<String, Object>> testUpdateWrapper(@PathVariable Long userId,
                                                              @RequestBody UserProfileUpdateRequest updateRequest) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 查询更新前的用户信息
            BaUser beforeUser = baUserService.getById(userId);
            if (beforeUser == null) {
                result.put("status", "error");
                result.put("message", "用户不存在");
                return ResultUtils.success(result);
            }

            result.put("beforeUpdate", beforeUser);

            // 记录更新的字段
            Map<String, Object> updateFields = new HashMap<>();
            if (updateRequest.getNickname() != null) {
                updateFields.put("nickname", updateRequest.getNickname());
            }
            if (updateRequest.getBio() != null) {
                updateFields.put("bio", updateRequest.getBio());
            }
            if (updateRequest.getDanceType() != null) {
                updateFields.put("danceType", updateRequest.getDanceType());
            }
            if (updateRequest.getAvatar() != null) {
                updateFields.put("avatar", updateRequest.getAvatar());
            }

            result.put("updateFields", updateFields);

            // 执行更新
            boolean success = baUserService.updateUserProfile(userId, updateRequest);

            if (success) {
                // 查询更新后的用户信息
                BaUser afterUser = baUserService.getById(userId);
                result.put("afterUpdate", afterUser);

                // 比较更新前后的差异
                Map<String, Object> changes = new HashMap<>();
                if (!java.util.Objects.equals(beforeUser.getNickname(), afterUser.getNickname())) {
                    changes.put("nickname", beforeUser.getNickname() + " -> " + afterUser.getNickname());
                }
                if (!java.util.Objects.equals(beforeUser.getBio(), afterUser.getBio())) {
                    changes.put("bio", beforeUser.getBio() + " -> " + afterUser.getBio());
                }
                if (!java.util.Objects.equals(beforeUser.getDance_type(), afterUser.getDance_type())) {
                    changes.put("danceType", beforeUser.getDance_type() + " -> " + afterUser.getDance_type());
                }
                if (!java.util.Objects.equals(beforeUser.getAvatar(), afterUser.getAvatar())) {
                    changes.put("avatar", beforeUser.getAvatar() + " -> " + afterUser.getAvatar());
                }

                result.put("changes", changes);
                result.put("status", "success");
                result.put("message", "UpdateWrapper精确更新成功");
            } else {
                result.put("status", "error");
                result.put("message", "UpdateWrapper更新失败");
            }

            log.info("UpdateWrapper测试 - userId: {}, success: {}, updateFields: {}",
                    userId, success, updateFields.keySet());
            return ResultUtils.success(result);

        } catch (Exception e) {
            log.error("UpdateWrapper测试失败 - userId: {}", userId, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("status", "error");
            errorResult.put("message", "测试失败: " + e.getMessage());
            return ResultUtils.success(errorResult);
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @ApiOperation(value = "健康检查")
    public BaseResponse<Map<String, Object>> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "healthy");
        result.put("timestamp", System.currentTimeMillis());
        result.put("message", "社交模块基础接口运行正常");
        result.put("updateMethod", "使用MyBatis-Plus UpdateWrapper精确更新");

        return ResultUtils.success(result);
    }
}
