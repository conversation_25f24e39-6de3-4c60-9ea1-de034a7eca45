package com.yupi.springbootinit.controller;

import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.model.entity.UserStats;
import com.yupi.springbootinit.service.BaUserService;
import com.yupi.springbootinit.service.UserStatsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 社交模块测试接口
 * 用于验证阶段一的基础接口功能
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/social/test")
@Slf4j
@Api(tags = "社交模块测试接口")
public class SocialTestController {

    @Resource
    private BaUserService baUserService;

    @Resource
    private UserStatsService userStatsService;

    /**
     * 测试数据库连接和基础查询
     */
    @GetMapping("/database")
    @ApiOperation(value = "测试数据库连接")
    public BaseResponse<Map<String, Object>> testDatabase() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 测试用户表查询
            long userCount = baUserService.count();
            result.put("userCount", userCount);
            
            // 测试用户统计表查询
            long userStatsCount = userStatsService.count();
            result.put("userStatsCount", userStatsCount);
            
            // 测试查询第一个用户
            BaUser firstUser = baUserService.list().stream().findFirst().orElse(null);
            if (firstUser != null) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", firstUser.getId());
                userInfo.put("username", firstUser.getUsername());
                userInfo.put("nickname", firstUser.getNickname());
                userInfo.put("bio", firstUser.getBio());
                userInfo.put("danceType", firstUser.getDance_type());
                result.put("sampleUser", userInfo);
            }
            
            result.put("status", "success");
            result.put("message", "数据库连接正常");
            
            log.info("数据库测试成功 - userCount: {}, userStatsCount: {}", userCount, userStatsCount);
            return ResultUtils.success(result);
            
        } catch (Exception e) {
            log.error("数据库测试失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("status", "error");
            errorResult.put("message", "数据库连接失败: " + e.getMessage());
            return ResultUtils.success(errorResult);
        }
    }

    /**
     * 测试用户资料接口
     */
    @GetMapping("/user-profile/{userId}")
    @ApiOperation(value = "测试用户资料接口")
    public BaseResponse<Map<String, Object>> testUserProfile(@PathVariable Long userId) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 查询用户基本信息
            BaUser user = baUserService.getById(userId);
            if (user == null) {
                result.put("status", "error");
                result.put("message", "用户不存在");
                return ResultUtils.success(result);
            }
            
            // 查询用户统计信息
            UserStats userStats = userStatsService.getById(userId);
            if (userStats == null) {
                // 创建默认统计信息
                userStats = new UserStats();
                userStats.setUserId(userId);
                userStats.setFollowingCount(0);
                userStats.setFollowerCount(0);
                userStats.setPostCount(0);
                userStats.setLikeReceivedCount(0);
                userStatsService.save(userStats);
                result.put("statsCreated", true);
            }
            
            // 构建返回数据
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("userId", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("nickname", user.getNickname());
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("bio", user.getBio());
            userInfo.put("danceType", user.getDance_type());
            userInfo.put("level", user.getLevel());
            
            Map<String, Object> statsInfo = new HashMap<>();
            statsInfo.put("followingCount", userStats.getFollowingCount());
            statsInfo.put("followerCount", userStats.getFollowerCount());
            statsInfo.put("postCount", userStats.getPostCount());
            statsInfo.put("likeReceivedCount", userStats.getLikeReceivedCount());
            
            result.put("user", userInfo);
            result.put("stats", statsInfo);
            result.put("status", "success");
            
            log.info("用户资料测试成功 - userId: {}", userId);
            return ResultUtils.success(result);
            
        } catch (Exception e) {
            log.error("用户资料测试失败 - userId: {}", userId, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("status", "error");
            errorResult.put("message", "测试失败: " + e.getMessage());
            return ResultUtils.success(errorResult);
        }
    }

    /**
     * 测试用户资料更新
     */
    @PostMapping("/update-user/{userId}")
    @ApiOperation(value = "测试用户资料更新")
    public BaseResponse<Map<String, Object>> testUpdateUser(@PathVariable Long userId,
                                                           @RequestParam(required = false) String nickname,
                                                           @RequestParam(required = false) String bio,
                                                           @RequestParam(required = false) String danceType) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            BaUser user = baUserService.getById(userId);
            if (user == null) {
                result.put("status", "error");
                result.put("message", "用户不存在");
                return ResultUtils.success(result);
            }
            
            // 更新用户信息
            boolean updated = false;
            if (nickname != null) {
                user.setNickname(nickname);
                updated = true;
            }
            if (bio != null) {
                user.setBio(bio);
                updated = true;
            }
            if (danceType != null) {
                user.setDance_type(danceType);
                updated = true;
            }
            
            if (updated) {
                boolean success = baUserService.updateById(user);
                result.put("updateSuccess", success);
                
                if (success) {
                    result.put("status", "success");
                    result.put("message", "用户资料更新成功");
                    result.put("updatedUser", user);
                } else {
                    result.put("status", "error");
                    result.put("message", "用户资料更新失败");
                }
            } else {
                result.put("status", "warning");
                result.put("message", "没有需要更新的字段");
            }
            
            log.info("用户资料更新测试 - userId: {}, updated: {}", userId, updated);
            return ResultUtils.success(result);
            
        } catch (Exception e) {
            log.error("用户资料更新测试失败 - userId: {}", userId, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("status", "error");
            errorResult.put("message", "测试失败: " + e.getMessage());
            return ResultUtils.success(errorResult);
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @ApiOperation(value = "健康检查")
    public BaseResponse<Map<String, Object>> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "healthy");
        result.put("timestamp", System.currentTimeMillis());
        result.put("message", "社交模块基础接口运行正常");
        
        return ResultUtils.success(result);
    }
}
