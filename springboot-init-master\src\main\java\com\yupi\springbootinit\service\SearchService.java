package com.yupi.springbootinit.service;

import com.yupi.springbootinit.model.dto.search.SearchRequest;
import com.yupi.springbootinit.model.vo.SearchResultVO;

import java.util.List;

/**
 * 搜索服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface SearchService {

    /**
     * 综合搜索
     *
     * @param searchRequest 搜索请求
     * @return 搜索结果
     */
    SearchResultVO comprehensiveSearch(SearchRequest searchRequest);

    /**
     * 获取热门搜索词
     *
     * @param limit 限制数量
     * @return 热门搜索词列表
     */
    List<String> getHotKeywords(Integer limit);

    /**
     * 获取搜索历史
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 搜索历史列表
     */
    List<String> getSearchHistory(Long userId, Integer limit);

    /**
     * 保存搜索历史
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 是否成功
     */
    boolean saveSearchHistory(Long userId, String keyword);

    /**
     * 清空搜索历史
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean clearSearchHistory(Long userId);

    /**
     * 删除单个搜索历史
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 是否成功
     */
    boolean deleteSearchHistory(Long userId, String keyword);

    /**
     * 获取搜索建议
     *
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 搜索建议列表
     */
    List<String> getSearchSuggestions(String keyword, Integer limit);

    /**
     * 更新搜索统计
     *
     * @param keyword 搜索关键词
     * @param userId 用户ID
     */
    void updateSearchStats(String keyword, Long userId);
}
