-- ========================================
-- 社交模块测试数据插入脚本
-- 执行时间：2025-07-18
-- 说明：为验证API功能添加测试数据
-- ========================================

-- 1. 插入测试用户数据到ba_user表
INSERT INTO ba_user (username, nickname, mobile, avatar, level, is_member, remaining_votes, bio, dance_type, create_time, update_time) VALUES
('testuser1', '舞蹈小王子', '13800138001', 'https://example.com/avatar1.jpg', 1, 1, 10, '热爱街舞，专注Breaking和Popping', 'Breaking', NOW(), NOW()),
('testuser2', '优雅芭蕾', '13800138002', 'https://example.com/avatar2.jpg', 2, 0, 5, '古典芭蕾舞者，追求完美的艺术表达', '芭蕾', NOW(), NOW()),
('testuser3', '现代舞者', '13800138003', 'https://example.com/avatar3.jpg', 1, 1, 8, '现代舞爱好者，喜欢自由表达', '现代舞', NOW(), NOW()),
('testuser4', '拉丁达人', '13800138004', 'https://example.com/avatar4.jpg', 3, 1, 15, '拉丁舞专业选手，参加过多次比赛', '拉丁舞', NOW(), NOW()),
('testuser5', '爵士女王', '13800138005', 'https://example.com/avatar5.jpg', 2, 0, 3, '爵士舞教练，有丰富的教学经验', '爵士舞', NOW(), NOW());

-- 2. 插入测试话题数据到tags表
INSERT INTO tags (name, description, color, use_count, is_hot, create_time, update_time, is_delete) VALUES
('生活', '记录生活点滴', '#1890ff', 156, 1, NOW(), NOW(), 0),
('美食', '分享美食体验', '#f5222d', 89, 1, NOW(), NOW(), 0),
('旅行', '旅行见闻分享', '#52c41a', 234, 1, NOW(), NOW(), 0),
('摄影', '摄影作品展示', '#722ed1', 67, 1, NOW(), NOW(), 0),
('运动', '运动健身相关', '#fa8c16', 123, 1, NOW(), NOW(), 0),
('舞蹈', '舞蹈相关内容', '#eb2f96', 345, 1, NOW(), NOW(), 0),
('音乐', '音乐分享交流', '#13c2c2', 78, 1, NOW(), NOW(), 0),
('时尚', '时尚穿搭分享', '#faad14', 92, 1, NOW(), NOW(), 0),
('学习', '学习心得分享', '#52c41a', 156, 0, NOW(), NOW(), 0),
('工作', '职场经验分享', '#1890ff', 45, 0, NOW(), NOW(), 0);

-- 3. 插入测试帖子数据到posts表
INSERT INTO posts (user_id, content, title, cover_image, images, location_name, location_latitude, location_longitude, location_address, like_count, comment_count, share_count, view_count, is_public, status, create_time, update_time, is_delete) VALUES
(1, '今天在舞蹈室练习了新的Breaking动作，感觉进步很大！大家一起来交流舞蹈心得吧 💃', 'Breaking练习日记', 'https://example.com/cover1.jpg', '["https://example.com/image1.jpg", "https://example.com/image2.jpg"]', '星光舞蹈工作室', 39.9042, 116.4074, '北京市朝阳区建国门外大街1号', 23, 5, 2, 156, 1, 1, DATE_SUB(NOW(), INTERVAL 2 HOUR), NOW(), 0),

(2, '芭蕾舞的优雅需要日复一日的练习，每一个动作都要追求完美。今天练习了天鹅湖的经典片段 🦢', '芭蕾练习心得', 'https://example.com/cover2.jpg', '["https://example.com/image3.jpg", "https://example.com/image4.jpg", "https://example.com/image5.jpg"]', '优雅芭蕾舞蹈学院', 31.2304, 121.4737, '上海市黄浦区南京东路100号', 45, 8, 3, 234, 1, 1, DATE_SUB(NOW(), INTERVAL 5 HOUR), NOW(), 0),

(3, '现代舞让我找到了自由表达的方式，每一次舞蹈都是情感的释放 ✨', '现代舞的魅力', 'https://example.com/cover3.jpg', '["https://example.com/image6.jpg"]', '自由舞蹈空间', 22.5431, 114.0579, '深圳市福田区华强北路200号', 67, 12, 5, 345, 1, 1, DATE_SUB(NOW(), INTERVAL 1 DAY), NOW(), 0),

(4, '拉丁舞比赛准备中，每天都在刻苦训练。感谢舞伴的配合，我们一定能取得好成绩！🏆', '拉丁舞比赛准备', 'https://example.com/cover4.jpg', '["https://example.com/image7.jpg", "https://example.com/image8.jpg"]', '激情拉丁舞蹈中心', 30.5728, 104.0668, '成都市锦江区春熙路300号', 89, 15, 7, 456, 1, 1, DATE_SUB(NOW(), INTERVAL 3 HOUR), NOW(), 0),

(5, '爵士舞课程开课啦！欢迎大家来体验爵士舞的魅力，让我们一起燃烧卡路里 🔥', '爵士舞课程招生', 'https://example.com/cover5.jpg', '["https://example.com/image9.jpg", "https://example.com/image10.jpg", "https://example.com/image11.jpg"]', '爵士舞培训基地', 23.1291, 113.2644, '广州市天河区天河路400号', 34, 6, 4, 189, 1, 1, DATE_SUB(NOW(), INTERVAL 6 HOUR), NOW(), 0),

(1, '分享一些Breaking的基础动作教学，希望对初学者有帮助 📚', 'Breaking基础教学', 'https://example.com/cover6.jpg', '["https://example.com/image12.jpg"]', '街舞训练基地', 39.9042, 116.4074, '北京市朝阳区三里屯500号', 56, 9, 3, 267, 1, 1, DATE_SUB(NOW(), INTERVAL 8 HOUR), NOW(), 0),

(3, '今天尝试了一个新的现代舞编舞，融合了一些古典元素，效果不错！', '现代舞编舞实验', 'https://example.com/cover7.jpg', '["https://example.com/image13.jpg", "https://example.com/image14.jpg"]', '创意舞蹈工作室', 22.5431, 114.0579, '深圳市南山区科技园600号', 78, 11, 6, 298, 1, 1, DATE_SUB(NOW(), INTERVAL 12 HOUR), NOW(), 0);

-- 4. 插入测试评论数据到comments表
INSERT INTO comments (content_id, user_id, content, likes, reply_count, nickname, avatar, created_at, updated_at, is_delete) VALUES
('1', 2, '你的Breaking动作真的很棒！能分享一下练习心得吗？', 5, 2, '优雅芭蕾', 'https://example.com/avatar2.jpg', DATE_SUB(NOW(), INTERVAL 1 HOUR), NOW(), 0),
('1', 3, '看起来很酷！我也想学Breaking，有推荐的入门教程吗？', 3, 1, '现代舞者', 'https://example.com/avatar3.jpg', DATE_SUB(NOW(), INTERVAL 30 MINUTE), NOW(), 0),
('2', 1, '芭蕾舞真的很优雅，你的基本功很扎实！', 8, 0, '舞蹈小王子', 'https://example.com/avatar1.jpg', DATE_SUB(NOW(), INTERVAL 2 HOUR), NOW(), 0),
('2', 4, '天鹅湖是我最喜欢的芭蕾舞剧，你跳得很有感觉！', 6, 1, '拉丁达人', 'https://example.com/avatar4.jpg', DATE_SUB(NOW(), INTERVAL 45 MINUTE), NOW(), 0),
('3', 5, '现代舞的自由表达真的很吸引人，你的舞蹈很有感染力！', 12, 3, '爵士女王', 'https://example.com/avatar5.jpg', DATE_SUB(NOW(), INTERVAL 20 HOUR), NOW(), 0),
('4', 2, '拉丁舞比赛加油！期待你们的精彩表现！', 15, 2, '优雅芭蕾', 'https://example.com/avatar2.jpg', DATE_SUB(NOW(), INTERVAL 2 HOUR), NOW(), 0),
('5', 1, '爵士舞课程什么时候开始？我想报名参加！', 6, 1, '舞蹈小王子', 'https://example.com/avatar1.jpg', DATE_SUB(NOW(), INTERVAL 5 HOUR), NOW(), 0),
('6', 4, '这个Breaking教学很实用，谢谢分享！', 9, 0, '拉丁达人', 'https://example.com/avatar4.jpg', DATE_SUB(NOW(), INTERVAL 7 HOUR), NOW(), 0);

-- 5. 插入测试回复数据到comment_replies表
INSERT INTO comment_replies (comment_id, user_id, content, reply_to_id, likes, nickname, avatar, created_at, updated_at, is_delete) VALUES
(1, 1, '谢谢夸奖！主要是多练习基本功，然后慢慢增加难度动作', 2, 3, '舞蹈小王子', 'https://example.com/avatar1.jpg', DATE_SUB(NOW(), INTERVAL 50 MINUTE), NOW(), 0),
(1, 4, '我也觉得Breaking很酷，可以一起交流学习！', 3, 2, '拉丁达人', 'https://example.com/avatar4.jpg', DATE_SUB(NOW(), INTERVAL 25 MINUTE), NOW(), 0),
(2, 3, '我推荐先从Toprock开始学，这是Breaking的基础', 3, 1, '现代舞者', 'https://example.com/avatar3.jpg', DATE_SUB(NOW(), INTERVAL 20 MINUTE), NOW(), 0),
(4, 2, '谢谢！天鹅湖确实是经典，我会继续努力的', 4, 4, '优雅芭蕾', 'https://example.com/avatar2.jpg', DATE_SUB(NOW(), INTERVAL 40 MINUTE), NOW(), 0),
(5, 3, '谢谢支持！现代舞确实能很好地表达内心情感', 5, 8, '现代舞者', 'https://example.com/avatar3.jpg', DATE_SUB(NOW(), INTERVAL 19 HOUR), NOW(), 0),
(6, 4, '谢谢鼓励！我们会全力以赴的！', 2, 7, '拉丁达人', 'https://example.com/avatar4.jpg', DATE_SUB(NOW(), INTERVAL 1 HOUR), NOW(), 0),
(7, 5, '课程下周一开始，欢迎来体验！', 1, 4, '爵士女王', 'https://example.com/avatar5.jpg', DATE_SUB(NOW(), INTERVAL 4 HOUR), NOW(), 0);

-- 6. 更新帖子的评论数量（保持数据一致性）
UPDATE posts SET comment_count = (
    SELECT COUNT(*) FROM comments WHERE content_id = CAST(posts.id AS CHAR) AND is_delete = 0
) WHERE id IN (1, 2, 3, 4, 5, 6, 7);

-- 7. 更新评论的回复数量（保持数据一致性）
UPDATE comments SET reply_count = (
    SELECT COUNT(*) FROM comment_replies WHERE comment_id = comments.id AND is_delete = 0
) WHERE id IN (1, 2, 3, 4, 5, 6, 7, 8);

-- 验证数据插入结果
SELECT '用户数据' as '数据类型', COUNT(*) as '记录数' FROM ba_user WHERE mobile LIKE '138%'
UNION ALL
SELECT '话题数据' as '数据类型', COUNT(*) as '记录数' FROM tags WHERE is_delete = 0
UNION ALL
SELECT '帖子数据' as '数据类型', COUNT(*) as '记录数' FROM posts WHERE is_delete = 0
UNION ALL
SELECT '评论数据' as '数据类型', COUNT(*) as '记录数' FROM comments WHERE is_delete = 0
UNION ALL
SELECT '回复数据' as '数据类型', COUNT(*) as '记录数' FROM comment_replies WHERE is_delete = 0;
