package com.yupi.springbootinit.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yupi.springbootinit.model.dto.notification.NotificationQueryRequest;
import com.yupi.springbootinit.model.entity.Notification;
import com.yupi.springbootinit.model.vo.NotificationVO;

import java.util.List;
import java.util.Map;

/**
 * 消息通知服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface NotificationService extends IService<Notification> {

    /**
     * 发送通知
     *
     * @param userId 接收用户ID
     * @param senderId 发送用户ID
     * @param type 通知类型
     * @param title 通知标题
     * @param content 通知内容
     * @param relatedId 相关ID
     * @param relatedType 相关类型
     * @return 是否成功
     */
    Boolean sendNotification(Long userId, Long senderId, Integer type, String title, 
                           String content, Long relatedId, String relatedType);

    /**
     * 发送点赞通知
     *
     * @param postUserId 帖子作者ID
     * @param likeUserId 点赞用户ID
     * @param postId 帖子ID
     * @return 是否成功
     */
    Boolean sendLikeNotification(Long postUserId, Long likeUserId, Long postId);

    /**
     * 发送评论通知
     *
     * @param postUserId 帖子作者ID
     * @param commentUserId 评论用户ID
     * @param postId 帖子ID
     * @param commentId 评论ID
     * @return 是否成功
     */
    Boolean sendCommentNotification(Long postUserId, Long commentUserId, Long postId, Long commentId);

    /**
     * 发送关注通知
     *
     * @param followingUserId 被关注用户ID
     * @param followerUserId 关注用户ID
     * @return 是否成功
     */
    Boolean sendFollowNotification(Long followingUserId, Long followerUserId);

    /**
     * 发送系统通知
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @return 是否成功
     */
    Boolean sendSystemNotification(Long userId, String title, String content);

    /**
     * 获取用户通知列表
     *
     * @param userId 用户ID
     * @param type 通知类型（可选）
     * @param isRead 是否已读（可选）
     * @param current 页码
     * @param pageSize 每页大小
     * @return 通知分页结果
     */
    Page<NotificationVO> getUserNotifications(Long userId, Integer type, Integer isRead, 
                                            Integer current, Integer pageSize);

    /**
     * 获取用户未读通知数
     *
     * @param userId 用户ID
     * @return 未读通知数
     */
    Integer getUnreadCount(Long userId);

    /**
     * 按类型获取用户未读通知数
     *
     * @param userId 用户ID
     * @param type 通知类型
     * @return 未读通知数
     */
    Integer getUnreadCountByType(Long userId, Integer type);

    /**
     * 标记通知为已读
     *
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean markAsRead(Long notificationId, Long userId);

    /**
     * 标记所有通知为已读
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean markAllAsRead(Long userId);

    /**
     * 标记指定类型通知为已读
     *
     * @param userId 用户ID
     * @param type 通知类型
     * @return 是否成功
     */
    Boolean markTypeAsRead(Long userId, Integer type);

    /**
     * 删除通知
     *
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean deleteNotification(Long notificationId, Long userId);

    /**
     * 删除用户所有通知
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean deleteAllNotifications(Long userId);

    // ==================== 社交功能扩展方法 ====================

    /**
     * 获取未读消息数统计（按类型分组）
     *
     * @param userId 用户ID
     * @return 未读消息数统计
     */
    Map<String, Integer> getUnreadCountMap(Long userId);

    /**
     * 获取消息列表（支持复杂查询）
     *
     * @param queryRequest 查询请求
     * @return 消息列表
     */
    List<NotificationVO> getNotificationList(NotificationQueryRequest queryRequest);

    /**
     * 获取系统消息列表
     *
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @return 系统消息列表
     */
    List<NotificationVO> getSystemNotifications(Long userId, Integer current, Integer size);

    /**
     * 获取点赞消息列表
     *
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @return 点赞消息列表
     */
    List<NotificationVO> getLikeNotifications(Long userId, Integer current, Integer size);

    /**
     * 获取关注消息列表
     *
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @return 关注消息列表
     */
    List<NotificationVO> getFollowNotifications(Long userId, Integer current, Integer size);

    /**
     * 获取评论消息列表
     *
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @return 评论消息列表
     */
    List<NotificationVO> getCommentNotifications(Long userId, Integer current, Integer size);

    /**
     * 清空某类型消息
     *
     * @param type 消息类型
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean clearNotifications(String type, Long userId);
}
