<view class="publish-container data-v-bb7c3636"><view class="header data-v-bb7c3636"><view class="header-content data-v-bb7c3636"><text data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="cancel-btn data-v-bb7c3636" bindtap="__e">取消</text><text class="title data-v-bb7c3636">发布帖子</text><text data-event-opts="{{[['tap',[['publishPost',['$event']]]]]}}" class="{{['publish-btn','data-v-bb7c3636',(!canPublish)?'disabled':'']}}" bindtap="__e">发布</text></view></view><scroll-view class="content data-v-bb7c3636" scroll-y="{{true}}"><view class="user-section data-v-bb7c3636"><u-avatar vue-id="b4098e7e-1" src="{{userInfo.avatar}}" size="40" class="data-v-bb7c3636" bind:__l="__l"></u-avatar><text class="username data-v-bb7c3636">{{userInfo.nickname}}</text></view><view class="text-section data-v-bb7c3636"><textarea class="content-input data-v-bb7c3636" placeholder="分享你的生活..." maxlength="{{500}}" auto-height="{{true}}" show-confirm-bar="{{false}}" data-event-opts="{{[['input',[['__set_model',['','postContent','$event',[]]]]]]}}" value="{{postContent}}" bindinput="__e"></textarea><view class="char-count data-v-bb7c3636">{{$root.g0+"/500"}}</view></view><view class="image-section data-v-bb7c3636"><view class="image-grid data-v-bb7c3636"><block wx:for="{{selectedImages}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view class="image-item data-v-bb7c3636"><image class="uploaded-image data-v-bb7c3636" src="{{image}}" mode="aspectFill"></image><view data-event-opts="{{[['tap',[['removeImage',[index]]]]]}}" class="delete-btn data-v-bb7c3636" bindtap="__e"><u-icon vue-id="{{'b4098e7e-2-'+index}}" name="close" color="#fff" size="16" class="data-v-bb7c3636" bind:__l="__l"></u-icon></view></view></block><block wx:if="{{$root.g1<9}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="add-image-btn data-v-bb7c3636" bindtap="__e"><u-icon vue-id="b4098e7e-3" name="camera" color="#999" size="32" class="data-v-bb7c3636" bind:__l="__l"></u-icon><text class="add-text data-v-bb7c3636">添加图片</text></view></block></view></view><view class="options-section data-v-bb7c3636"><view data-event-opts="{{[['tap',[['selectTopic',['$event']]]]]}}" class="option-item data-v-bb7c3636" bindtap="__e"><view class="option-left data-v-bb7c3636"><u-icon vue-id="b4098e7e-4" name="tags" color="#2979ff" size="20" class="data-v-bb7c3636" bind:__l="__l"></u-icon><text class="option-text data-v-bb7c3636">添加话题</text></view><view class="option-right data-v-bb7c3636"><block wx:if="{{$root.g2}}"><text class="selected-topics data-v-bb7c3636">{{''+$root.g3+''}}</text></block><u-icon vue-id="b4098e7e-5" name="arrow-right" color="#999" size="16" class="data-v-bb7c3636" bind:__l="__l"></u-icon></view></view><view data-event-opts="{{[['tap',[['selectLocation',['$event']]]]]}}" class="option-item data-v-bb7c3636" bindtap="__e"><view class="option-left data-v-bb7c3636"><u-icon vue-id="b4098e7e-6" name="map" color="#2979ff" size="20" class="data-v-bb7c3636" bind:__l="__l"></u-icon><text class="option-text data-v-bb7c3636">添加位置</text></view><view class="option-right data-v-bb7c3636"><block wx:if="{{selectedLocation}}"><text class="selected-location data-v-bb7c3636">{{''+selectedLocation.name+''}}</text></block><u-icon vue-id="b4098e7e-7" name="arrow-right" color="#999" size="16" class="data-v-bb7c3636" bind:__l="__l"></u-icon></view></view><view data-event-opts="{{[['tap',[['setVisibility',['$event']]]]]}}" class="option-item data-v-bb7c3636" bindtap="__e"><view class="option-left data-v-bb7c3636"><u-icon vue-id="b4098e7e-8" name="eye" color="#2979ff" size="20" class="data-v-bb7c3636" bind:__l="__l"></u-icon><text class="option-text data-v-bb7c3636">可见性</text></view><view class="option-right data-v-bb7c3636"><text class="visibility-text data-v-bb7c3636">{{visibilityText}}</text><u-icon vue-id="b4098e7e-9" name="arrow-right" color="#999" size="16" class="data-v-bb7c3636" bind:__l="__l"></u-icon></view></view></view><view class="tips-section data-v-bb7c3636"><text class="tips-text data-v-bb7c3636">发布即表示同意《社区公约》，请文明发言，共建和谐社区</text></view></scroll-view><u-popup bind:input="__e" vue-id="b4098e7e-10" mode="bottom" border-radius="20" value="{{showTopicModal}}" data-event-opts="{{[['^input',[['__set_model',['','showTopicModal','$event',[]]]]]]}}" class="data-v-bb7c3636" bind:__l="__l" vue-slots="{{['default']}}"><view class="topic-modal data-v-bb7c3636"><view class="modal-header data-v-bb7c3636"><text class="modal-title data-v-bb7c3636">选择话题</text><u-icon vue-id="{{('b4098e7e-11')+','+('b4098e7e-10')}}" name="close" data-event-opts="{{[['^click',[['e0']]]]}}" bind:click="__e" class="data-v-bb7c3636" bind:__l="__l"></u-icon></view><view class="topic-search data-v-bb7c3636"><u-input vue-id="{{('b4098e7e-12')+','+('b4098e7e-10')}}" placeholder="搜索话题" prefix-icon="search" value="{{topicKeyword}}" data-event-opts="{{[['^input',[['__set_model',['','topicKeyword','$event',[]]],['searchTopics']]]]}}" bind:input="__e" class="data-v-bb7c3636" bind:__l="__l"></u-input></view><view class="topic-list data-v-bb7c3636"><block wx:for="{{$root.l0}}" wx:for-item="topic" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['toggleTopic',['$0'],[[['filteredTopics','id',topic.$orig.id]]]]]]]}}" class="{{['topic-option','data-v-bb7c3636',(topic.g4)?'selected':'']}}" bindtap="__e"><text class="topic-name data-v-bb7c3636">{{"#"+topic.$orig.name}}</text><text class="topic-count data-v-bb7c3636">{{topic.$orig.postCount+"条帖子"}}</text></view></block></view></view></u-popup><u-popup bind:input="__e" vue-id="b4098e7e-13" mode="bottom" border-radius="20" value="{{showLocationModal}}" data-event-opts="{{[['^input',[['__set_model',['','showLocationModal','$event',[]]]]]]}}" class="data-v-bb7c3636" bind:__l="__l" vue-slots="{{['default']}}"><view class="location-modal data-v-bb7c3636"><view class="modal-header data-v-bb7c3636"><text class="modal-title data-v-bb7c3636">选择位置</text><u-icon vue-id="{{('b4098e7e-14')+','+('b4098e7e-13')}}" name="close" data-event-opts="{{[['^click',[['e1']]]]}}" bind:click="__e" class="data-v-bb7c3636" bind:__l="__l"></u-icon></view><view class="location-list data-v-bb7c3636"><block wx:for="{{nearbyLocations}}" wx:for-item="location" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['selectLocationItem',['$0'],[[['nearbyLocations','id',location.id]]]]]]]}}" class="location-option data-v-bb7c3636" bindtap="__e"><u-icon vue-id="{{('b4098e7e-15-'+__i1__)+','+('b4098e7e-13')}}" name="map-pin" color="#2979ff" size="16" class="data-v-bb7c3636" bind:__l="__l"></u-icon><view class="location-info data-v-bb7c3636"><text class="location-name data-v-bb7c3636">{{location.name}}</text><text class="location-address data-v-bb7c3636">{{location.address}}</text></view></view></block></view></view></u-popup></view>