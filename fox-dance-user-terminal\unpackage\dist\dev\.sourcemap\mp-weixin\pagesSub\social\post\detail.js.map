{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/post/detail.vue?40ee", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/post/detail.vue?3006", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/post/detail.vue?8b60", "uni-app:///pagesSub/social/post/detail.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/post/detail.vue?c818", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/social/post/detail.vue?8ec5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "FollowButton", "data", "postId", "postData", "commentList", "commentText", "sortType", "currentTab", "tabList", "currentUser", "avatar", "nickname", "replyingTo", "onLoad", "console", "methods", "loadPostDetail", "result", "post", "id", "userId", "username", "userAvatar", "content", "images", "topics", "location", "likeCount", "commentCount", "shareCount", "isLiked", "isFollowed", "createTime", "uni", "title", "icon", "loadComments", "contentId", "filter", "page", "size", "comments", "replies", "formatTime", "goBack", "goUserProfile", "url", "onUserFollow", "onFollowChange", "toggleLike", "toggleCommentLike", "comment", "previewImage", "urls", "current", "sharePost", "itemList", "success", "goTopic", "changeSortType", "args", "focusComment", "onInputFocus", "onInputBlur", "replyComment", "sendComment", "commentData", "topicId", "newComment", "handleCommentSuccess", "showMoreActions"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnFA;AAAA;AAAA;AAAA;AAAquB,CAAgB,qsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACwLzvB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC,UACA;QAAAV;MAAA,GACA;QAAAA;MAAA,EACA;MACAW;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;;IAEA;IACA;;IAEAA;;IAEA;IACA;IAEA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAF;gBACAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAG;gBACAH;gBAAA,KAEAG;kBAAA;kBAAA;gBAAA;gBACAC;gBACA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACAlB;gBAAA;gBAAA;cAAA;gBAEAA;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAmB;kBACAC;kBACAC;gBACA;gBACA;gBACA;kBACAhB;kBACAE;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAtB;gBAAA;gBAAA,OACA;kBACAM;kBAAA;kBACAiB;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBANAvB;gBAOAH;gBACAA;gBACAA;gBAEA;kBACA;kBACA2B;kBACA;oBACAA;kBACA;oBACAA;kBACA;kBAEA;oBAAA;sBACAtB;sBACAE;sBACAC;sBACAC;sBACAI;sBACAG;sBACAE;sBACAU;oBACA;kBAAA;kBAEA5B;gBACA;kBACAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA,sBACA;kBACAK;kBACAE;kBACAC;kBACAC;kBACAI;kBACAG;kBACAE;kBACAU,UACA;oBACAvB;oBACAE;oBACAE;oBACAS;kBACA;gBAEA,GACA;kBACAb;kBACAE;kBACAC;kBACAC;kBACAI;kBACAG;kBACAE;kBACAU;gBACA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;IACA;IAEAC;MACAX;IACA;IAEAY;MACAZ;QACAa;MACA;IACA;IAEAC;MACAjC;MACA;IACA;IAEAkC;MACA;MACA;MACAlC;IACA;IAEAmC;MACA;MACA;IACA;IAEAC;MACAC;MACAA;IACA;IAEAC;MACAnB;QACAoB;QACAC;MACA;IACA;IAEAC;MAAA;MACAtB;QACAuB;QACAC;UACA;UACA;QACA;MACA;IACA;IAEAC;MACAzB;QACAa;MACA;IACA;IAEAa;MAAA;QAAAC;MAAA;MACA9C;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA+C;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAhC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKArB;;gBAEA;gBACAoD;kBACA7B;kBAAA;kBACAd;kBACAH;kBAAA;kBACA+C;gBACA;;gBAEArD;;gBAEA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAG;gBACAH;gBAAA,MAEAG;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAH;cAAA;gBAGA;gBACAA;gBACAsD;kBACAjD;kBACAE;kBACAC;kBACAC;kBACAI;kBACAG;kBACAE;kBACAU;gBACA,GAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA5B;gBACAmB;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkC;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACApC;QACAC;QACAC;MACA;;MAEA;MACA;IACA;IAEAmC;MACArC;QACAuB;QACAC;UACA3C;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/hBA;AAAA;AAAA;AAAA;AAAg5C,CAAgB,gxCAAG,EAAC,C;;;;;;;;;;;ACAp6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/post/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/post/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=0722cd1a&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=0722cd1a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0722cd1a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/post/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=0722cd1a&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uSticky: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-sticky/u-sticky\" */ \"@/components/uview-ui/components/u-sticky/u-sticky.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatTime(_vm.postData.createTime)\n  var a0 = {\n    id: _vm.postData.userId,\n    nickname: _vm.postData.username,\n  }\n  var g0 = _vm.postData.topics && _vm.postData.topics.length\n  var g1 = _vm.postData.images && _vm.postData.images.length\n  var g2 = g1 ? _vm.postData.images.length : null\n  var g3 = _vm.commentList.length\n  var l0 = _vm.__map(_vm.commentList, function (comment, __i1__) {\n    var $orig = _vm.__get_orig(comment)\n    var m1 = _vm.formatTime(comment.createTime)\n    var g4 = comment.replies && comment.replies.length\n    return {\n      $orig: $orig,\n      m1: m1,\n      g4: g4,\n    }\n  })\n  var g5 = _vm.commentText.trim()\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"detail-container\">\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 帖子内容 -->\n      <view class=\"post-detail\">\n        <!-- 用户信息 -->\n        <view class=\"user-info\">\n          <u-avatar :src=\"postData.userAvatar\" size=\"50\" @click=\"goUserProfile\"></u-avatar>\n          <view class=\"user-details\">\n            <text class=\"username\">{{ postData.username }}</text>\n            <text class=\"time\">{{ formatTime(postData.createTime) }}</text>\n          </view>\n          <FollowButton\n            :user=\"{ id: postData.userId, nickname: postData.username }\"\n            :followed=\"postData.isFollowed\"\n            size=\"mini\"\n            @follow=\"onUserFollow\"\n            @change=\"onFollowChange\"\n          />\n        </view>\n\n        <!-- 帖子文字内容 -->\n        <view class=\"post-content\">\n          <text class=\"content-text\">{{ postData.content }}</text>\n        </view>\n\n        <!-- 话题标签 -->\n        <view class=\"topic-tags\" v-if=\"postData.topics && postData.topics.length\">\n          <text \n            v-for=\"topic in postData.topics\" \n            :key=\"topic\"\n            class=\"topic-tag\"\n            @click=\"goTopic(topic)\"\n          >\n            #{{ topic }}\n          </text>\n        </view>\n\n        <!-- 帖子图片轮播 -->\n        <view class=\"post-images\" v-if=\"postData.images && postData.images.length\">\n          <swiper\n            class=\"image-swiper\"\n            :indicator-dots=\"postData.images.length > 1\"\n            :autoplay=\"false\"\n            :circular=\"true\"\n            indicator-color=\"rgba(255, 255, 255, 0.5)\"\n            indicator-active-color=\"#fff\"\n          >\n            <swiper-item\n              v-for=\"(img, index) in postData.images\"\n              :key=\"index\"\n            >\n              <image\n                :src=\"img\"\n                class=\"swiper-image\"\n                mode=\"aspectFill\"\n                @click=\"previewImage(index)\"\n              />\n            </swiper-item>\n          </swiper>\n        </view>\n\n        <!-- 互动数据 -->\n        <view class=\"post-stats\">\n          <text class=\"stat-item\">{{ postData.likeCount }}人点赞</text>\n          <text class=\"stat-item\">{{ postData.commentCount }}条评论</text>\n          <text class=\"stat-item\">{{ postData.shareCount }}次分享</text>\n        </view>\n\n        <!-- 互动按钮 -->\n        <view class=\"action-bar\">\n          <view class=\"action-item\" @click=\"toggleLike\">\n            <u-icon \n              :name=\"postData.isLiked ? 'heart-fill' : 'heart'\" \n              :color=\"postData.isLiked ? '#ff4757' : '#666'\"\n              size=\"24\"\n            ></u-icon>\n            <text class=\"action-text\">点赞</text>\n          </view>\n          <view class=\"action-item\" @click=\"focusComment\">\n            <u-icon name=\"chat\" color=\"#666\" size=\"24\"></u-icon>\n            <text class=\"action-text\">评论</text>\n          </view>\n          <view class=\"action-item\" @click=\"sharePost\">\n            <u-icon name=\"share\" color=\"#666\" size=\"24\"></u-icon>\n            <text class=\"action-text\">分享</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 评论列表 -->\n      <view class=\"comments-section\">\n        <view class=\"comments-header\">\n          <text class=\"comments-title\">评论 {{ commentList.length }}</text>\n          <u-sticky bgColor=\"#fff\">\n            <u-tabs\n              :list=\"tabList\"\n              :current=\"currentTab\"\n              @change=\"changeSortType\"\n              :scrollable=\"false\"\n              activeColor=\"#2979ff\"\n              inactiveColor=\"#999\"\n              fontSize=\"28\"\n              lineColor=\"#2979ff\"\n              lineWidth=\"20\"\n              lineHeight=\"3\"\n              height=\"40\"\n            ></u-tabs>\n          </u-sticky>\n        </view>\n\n        <view class=\"comment-list\">\n          <view \n            v-for=\"comment in commentList\" \n            :key=\"comment.id\"\n            class=\"comment-item\"\n          >\n            <u-avatar :src=\"comment.userAvatar\" size=\"36\"></u-avatar>\n            <view class=\"comment-content\">\n              <view class=\"comment-header\">\n                <text class=\"comment-username\">{{ comment.username }}</text>\n                <text class=\"comment-time\">{{ formatTime(comment.createTime) }}</text>\n              </view>\n              <text class=\"comment-text\">{{ comment.content }}</text>\n              \n              <!-- 回复列表 -->\n              <view v-if=\"comment.replies && comment.replies.length\" class=\"replies\">\n                <view \n                  v-for=\"reply in comment.replies\" \n                  :key=\"reply.id\"\n                  class=\"reply-item\"\n                >\n                  <text class=\"reply-user\">{{ reply.username }}</text>\n                  <text class=\"reply-text\">{{ reply.content }}</text>\n                </view>\n              </view>\n\n              <view class=\"comment-actions\">\n                <view class=\"comment-action\" @click=\"toggleCommentLike(comment)\">\n                  <u-icon \n                    :name=\"comment.isLiked ? 'heart-fill' : 'heart'\" \n                    :color=\"comment.isLiked ? '#ff4757' : '#999'\"\n                    size=\"16\"\n                  ></u-icon>\n                  <text class=\"action-count\">{{ comment.likeCount || '' }}</text>\n                </view>\n                <view class=\"comment-action\" @click=\"replyComment(comment)\">\n                  <u-icon name=\"chat\" color=\"#999\" size=\"16\"></u-icon>\n                  <text class=\"action-text\">回复</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 评论输入框 -->\n    <view class=\"comment-input-bar\">\n      <view class=\"input-container\">\n        <u-avatar :src=\"currentUser.avatar\" size=\"32\"></u-avatar>\n        <input \n          ref=\"commentInput\"\n          v-model=\"commentText\"\n          class=\"comment-input\"\n          placeholder=\"写评论...\"\n          @focus=\"onInputFocus\"\n          @blur=\"onInputBlur\"\n        />\n        <text \n          class=\"send-btn\"\n          :class=\"{ active: commentText.trim() }\"\n          @click=\"sendComment\"\n        >\n          发送\n        </text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport FollowButton from '../components/FollowButton.vue'\nimport { getPostDetail, getCommentList, createComment } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'PostDetail',\n  components: {\n    FollowButton\n  },\n  data() {\n    return {\n      postId: '',\n      postData: {},\n      commentList: [],\n      commentText: '',\n      sortType: 'time', // time, hot\n      currentTab: 0,\n      tabList: [\n        { name: '最新' },\n        { name: '最热' }\n      ],\n      currentUser: {\n        avatar: 'https://picsum.photos/100/100?random=999',\n        nickname: '我的昵称'\n      },\n      replyingTo: null\n    }\n  },\n  onLoad(options) {\n    console.log('详情页接收到的参数:', options)\n\n    // 获取帖子ID，支持多种参数名\n    this.postId = options.id || options.postId || '7' // 默认使用ID 7进行测试\n\n    console.log('最终使用的帖子ID:', this.postId)\n\n    // 确保postId是字符串类型\n    this.postId = String(this.postId)\n\n    this.loadPostDetail()\n    this.loadComments()\n  },\n  methods: {\n    async loadPostDetail() {\n      try {\n        console.log('加载帖子详情，ID:', this.postId)\n        console.log('postId类型:', typeof this.postId)\n\n        if (!this.postId) {\n          throw new Error('帖子ID为空')\n        }\n\n        const result = await getPostDetail(this.postId)\n        console.log('帖子详情API返回:', result)\n\n        if (result) {\n          const post = result\n          this.postData = {\n            id: post.id,\n            userId: post.userId,\n            username: post.nickname || '用户' + post.userId,\n            userAvatar: post.avatar || 'https://picsum.photos/100/100?random=' + post.userId,\n            content: post.content,\n            images: post.images || [],\n            topics: post.tags || [],\n            location: post.locationName || '',\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            shareCount: post.shareCount || 0,\n            isLiked: post.isLiked || false,\n            isFollowed: post.isFollowed || false,\n            createTime: new Date(post.createTime)\n          }\n          console.log('帖子数据加载成功:', this.postData)\n        } else {\n          console.error('API返回数据格式错误:', result)\n          throw new Error('获取帖子详情失败 - API返回格式错误')\n        }\n      } catch (error) {\n        console.error('加载帖子详情失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        })\n        // 使用默认数据作为后备\n        this.postData = {\n          id: this.postId,\n          username: '用户',\n          userAvatar: 'https://picsum.photos/100/100?random=100',\n          content: '帖子内容加载失败',\n          images: [],\n          topics: [],\n          location: '',\n          likeCount: 0,\n          commentCount: 0,\n          shareCount: 0,\n          isLiked: false,\n          isFollowed: false,\n          createTime: new Date()\n        }\n      }\n    },\n\n    async loadComments() {\n      try {\n        console.log('加载评论列表，帖子ID:', this.postId)\n        const result = await getCommentList({\n          userId: 1, // 当前用户ID，实际应该从用户状态获取\n          contentId: this.postId,\n          filter: this.sortType === 'time' ? 'latest' : 'hot',\n          page: 1,\n          size: 50\n        })\n        console.log('评论列表API返回:', result)\n        console.log('result.code:', result?.code)\n        console.log('result.data:', result?.data)\n\n        if (result && result.code === 0 && result.data) {\n          // 处理不同的API返回格式\n          let comments = []\n          if (result.data.comments) {\n            comments = result.data.comments\n          } else if (Array.isArray(result.data)) {\n            comments = result.data\n          }\n\n          this.commentList = comments.map(comment => ({\n            id: comment.id,\n            username: comment.nickname || '用户' + comment.userId,\n            userAvatar: comment.avatar || 'https://picsum.photos/100/100?random=' + comment.userId,\n            content: comment.content,\n            likeCount: comment.likes || 0,\n            isLiked: comment.isLiked || false,\n            createTime: new Date(comment.createdAt || comment.createTime),\n            replies: comment.replies || []\n          }))\n\n          console.log('评论列表加载成功，数量:', this.commentList.length)\n        } else {\n          console.log('评论API返回格式不正确或无数据')\n          this.commentList = []\n        }\n      } catch (error) {\n        console.error('加载评论失败:', error)\n        // 使用模拟数据作为后备\n        this.commentList = [\n          {\n            id: 1,\n            username: '小仙女',\n            userAvatar: 'https://picsum.photos/100/100?random=300',\n            content: '好好看啊！请问衬衫是什么牌子的？',\n            likeCount: 12,\n            isLiked: false,\n            createTime: new Date(Date.now() - 1800000),\n            replies: [\n              {\n                id: 11,\n                username: '时尚博主小美',\n                content: '是ZARA的哦，性价比很高！',\n                createTime: new Date(Date.now() - 1200000)\n              }\n            ]\n          },\n          {\n            id: 2,\n            username: '穿搭达人',\n            userAvatar: 'https://picsum.photos/100/100?random=301',\n            content: '这套搭配真的很棒！简约大方，我也要试试这样搭配',\n            likeCount: 8,\n            isLiked: true,\n            createTime: new Date(Date.now() - 3600000),\n            replies: []\n          }\n        ]\n      }\n    },\n\n    formatTime(time) {\n      const now = new Date()\n      const diff = now - new Date(time)\n      const minutes = Math.floor(diff / 60000)\n      const hours = Math.floor(diff / 3600000)\n      const days = Math.floor(diff / 86400000)\n      \n      if (minutes < 60) return `${minutes}分钟前`\n      if (hours < 24) return `${hours}小时前`\n      return `${days}天前`\n    },\n\n    goBack() {\n      uni.navigateBack()\n    },\n\n    goUserProfile() {\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${this.postData.userId}`\n      })\n    },\n\n    onUserFollow(data) {\n      console.log('关注操作:', data)\n      // 这里可以调用API进行关注/取消关注操作\n    },\n\n    onFollowChange(data) {\n      // 更新本地数据\n      this.postData.isFollowed = data.isFollowed\n      console.log('关注状态变化:', data)\n    },\n\n    toggleLike() {\n      this.postData.isLiked = !this.postData.isLiked\n      this.postData.likeCount += this.postData.isLiked ? 1 : -1\n    },\n\n    toggleCommentLike(comment) {\n      comment.isLiked = !comment.isLiked\n      comment.likeCount += comment.isLiked ? 1 : -1\n    },\n\n    previewImage(index) {\n      uni.previewImage({\n        urls: this.postData.images,\n        current: index\n      })\n    },\n\n    sharePost() {\n      uni.showActionSheet({\n        itemList: ['分享到微信', '分享到朋友圈', '复制链接'],\n        success: (res) => {\n          this.$u.toast('分享成功')\n          this.postData.shareCount++\n        }\n      })\n    },\n\n    goTopic(topic) {\n      uni.navigateTo({\n        url: `/pagesSub/social/topic/detail?name=${topic}`\n      })\n    },\n\n    changeSortType(...args) {\n      console.log('tabs点击参数:', args)\n      const index = typeof args[0] === 'number' ? args[0] : (args[0] && args[0].index !== undefined ? args[0].index : 0)\n      this.currentTab = index\n      this.sortType = index === 0 ? 'time' : 'hot'\n      // 重新加载评论\n      this.loadComments()\n      this.loadComments()\n    },\n\n    focusComment() {\n      this.$refs.commentInput.focus()\n    },\n\n    onInputFocus() {\n      // 输入框获得焦点\n    },\n\n    onInputBlur() {\n      // 输入框失去焦点\n    },\n\n    replyComment(comment) {\n      this.replyingTo = comment\n      this.commentText = `@${comment.username} `\n      this.focusComment()\n    },\n\n    async sendComment() {\n      if (!this.commentText.trim()) {\n        uni.showToast({\n          title: '请输入评论内容',\n          icon: 'none'\n        })\n        return\n      }\n\n      try {\n        console.log('发送评论，帖子ID:', this.postId, '内容:', this.commentText)\n\n        // 准备评论数据\n        const commentData = {\n          contentId: String(this.postId), // 确保是字符串类型\n          content: this.commentText.trim(),\n          userId: 1, // 当前用户ID，实际应该从用户状态获取\n          topicId: null // 帖子评论不需要topicId\n        }\n\n        console.log('评论数据:', commentData)\n\n        // 尝试调用API发送评论\n        try {\n          const result = await createComment(commentData)\n          console.log('发送评论API返回:', result)\n\n          if (result && result.code === 0) {\n            // API调用成功\n            this.handleCommentSuccess()\n            return\n          }\n        } catch (apiError) {\n          console.warn('评论API调用失败，使用临时方案:', apiError)\n        }\n\n        // API失败时的临时处理：直接添加到本地列表\n        console.log('使用临时评论方案')\n        const newComment = {\n          id: Date.now(),\n          username: '当前用户',\n          userAvatar: 'https://picsum.photos/100/100?random=999',\n          content: this.commentText.trim(),\n          likeCount: 0,\n          isLiked: false,\n          createTime: new Date(),\n          replies: []\n        }\n\n        // 添加到评论列表顶部\n        this.commentList.unshift(newComment)\n        this.handleCommentSuccess()\n\n      } catch (error) {\n        console.error('发送评论失败:', error)\n        uni.showToast({\n          title: '评论失败',\n          icon: 'none'\n        })\n      }\n    },\n\n    // 处理评论成功的通用逻辑\n    handleCommentSuccess() {\n      // 清空输入框和回复状态\n      this.commentText = ''\n      this.replyingTo = null\n\n      // 更新帖子评论数\n      this.postData.commentCount++\n\n      // 显示成功提示\n      uni.showToast({\n        title: '评论成功',\n        icon: 'success'\n      })\n\n      // 可选：重新加载评论列表以获取最新数据\n      // this.loadComments()\n    },\n\n    showMoreActions() {\n      uni.showActionSheet({\n        itemList: ['举报', '不感兴趣', '屏蔽用户'],\n        success: (res) => {\n          console.log('更多操作:', res.tapIndex)\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.detail-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 60px;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 2rpx solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 88rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 32rpx;\n}\n\n.title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n\n.post-detail {\n  background: #fff;\n  padding: 40rpx 32rpx;\n  margin-bottom: 16rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 32rpx;\n}\n\n.user-details {\n  flex: 1;\n  margin-left: 24rpx;\n}\n\n.username {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  display: block;\n}\n\n.time {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 4rpx;\n  display: block;\n}\n\n.location {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 4rpx;\n  display: block;\n}\n\n.content-text {\n  font-size: 32rpx;\n  line-height: 1.6;\n  color: #333;\n  margin-bottom: 24rpx;\n  display: block;\n}\n\n.topic-tags {\n  margin-bottom: 32rpx;\n}\n\n.topic-tag {\n  color: #2979ff;\n  font-size: 28rpx;\n  margin-right: 16rpx;\n}\n\n.post-images {\n  margin-bottom: 32rpx;\n}\n\n.image-swiper {\n  width: 100%;\n  height: 600rpx;\n  border-radius: 24rpx;\n  overflow: hidden;\n}\n\n.swiper-image {\n  width: 100%;\n  height: 100%;\n}\n\n.post-stats {\n  padding: 24rpx 0;\n  border-top: 2rpx solid #f0f0f0;\n  border-bottom: 2rpx solid #f0f0f0;\n  margin-bottom: 24rpx;\n}\n\n.stat-item {\n  font-size: 26rpx;\n  color: #666;\n  margin-right: 32rpx;\n}\n\n.action-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n}\n\n.action-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.action-text {\n  font-size: 24rpx;\n  color: #666;\n  margin-top: 8rpx;\n}\n\n.comments-section {\n  background: #fff;\n  padding: 32rpx;\n}\n\n.comments-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 32rpx;\n}\n\n.comments-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.comment-item {\n  display: flex;\n  margin-bottom: 32rpx;\n}\n\n.comment-content {\n  flex: 1;\n  margin-left: 24rpx;\n}\n\n.comment-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.comment-username {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-right: 16rpx;\n}\n\n.comment-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.comment-text {\n  font-size: 28rpx;\n  line-height: 1.4;\n  color: #333;\n  margin-bottom: 16rpx;\n  display: block;\n}\n\n.replies {\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  padding: 16rpx 24rpx;\n  margin-bottom: 16rpx;\n}\n\n.reply-item {\n  margin-bottom: 8rpx;\n}\n\n.reply-user {\n  font-size: 26rpx;\n  color: #2979ff;\n  margin-right: 8rpx;\n}\n\n.reply-text {\n  font-size: 26rpx;\n  color: #333;\n}\n\n.comment-actions {\n  display: flex;\n  align-items: center;\n  gap: 32rpx;\n}\n\n.comment-action {\n  display: flex;\n  align-items: center;\n}\n\n.action-count, .action-text {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 8rpx;\n}\n\n.comment-input-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n  border-top: 2rpx solid #e4e7ed;\n  padding: 16rpx 32rpx;\n  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));\n}\n\n.input-container {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  height: 104rpx;\n  position: relative;\n}\n\n.comment-input {\n  width: calc(100% - 128rpx);\n  height: 72rpx;\n  line-height: 72rpx;\n  background: #f5f5f5;\n  border: none;\n  border-radius: 36rpx;\n  padding: 0 32rpx;\n  font-size: 28rpx;\n  outline: none;\n  box-sizing: border-box;\n  -webkit-appearance: none;\n  appearance: none;\n  transition: none;\n  position: absolute;\n  left: 48rpx;\n  top: 16rpx;\n}\n\n.comment-input:focus {\n  background: #f5f5f5;\n  border: none;\n  outline: none;\n  box-shadow: none;\n  width: calc(100% - 128rpx);\n  height: 72rpx;\n  line-height: 72rpx;\n  padding: 0 32rpx;\n  left: 48rpx;\n  top: 16rpx;\n}\n\n.send-btn {\n  width: 80rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  color: #ccc;\n  white-space: nowrap;\n  position: absolute;\n  right: 0;\n  top: 16rpx;\n}\n\n.send-btn.active {\n  color: #2979ff;\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=0722cd1a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=0722cd1a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752828064462\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}