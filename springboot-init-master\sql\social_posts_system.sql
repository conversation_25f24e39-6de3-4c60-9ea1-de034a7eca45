-- =============================================
-- 帖子分享应用 - 数据库设计
-- 创建时间: 2025-07-11
-- 描述: 基于图片功能需求设计的完整社交分享系统数据库结构
-- =============================================


-- =============================================
-- 1. 帖子相关表
-- =============================================

-- 帖子表
CREATE TABLE IF NOT EXISTS posts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '帖子ID',
    user_id BIGINT NOT NULL COMMENT '发布用户ID，关联ba_user表',
    content TEXT COMMENT '帖子内容',
    images JSON DEFAULT NULL COMMENT '帖子图片数组，存储图片URL列表',
    location_name VARCHAR(200) DEFAULT NULL COMMENT '位置名称',
    location_latitude DECIMAL(10, 8) DEFAULT NULL COMMENT '纬度',
    location_longitude DECIMAL(11, 8) DEFAULT NULL COMMENT '经度',
    location_address VARCHAR(500) DEFAULT NULL COMMENT '详细地址',
    like_count INT NOT NULL DEFAULT 0 COMMENT '点赞数',
    comment_count INT NOT NULL DEFAULT 0 COMMENT '评论数',
    share_count INT NOT NULL DEFAULT 0 COMMENT '分享数',
    view_count INT NOT NULL DEFAULT 0 COMMENT '浏览数',
    is_public TINYINT NOT NULL DEFAULT 1 COMMENT '是否公开：0-私密，1-公开',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-草稿，1-已发布，2-已删除',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    
    -- 索引
    INDEX idx_user_id(user_id),
    INDEX idx_create_time(create_time),
    INDEX idx_status(status),
    INDEX idx_is_public(is_public),
    INDEX idx_like_count(like_count),
    INDEX idx_location(location_latitude, location_longitude),
    INDEX idx_composite_list(status, is_public, create_time)
) COMMENT='帖子表' COLLATE=utf8mb4_unicode_ci;

-- 帖子统计表（用于高频读取的统计数据）
CREATE TABLE IF NOT EXISTS post_stats (
    post_id BIGINT PRIMARY KEY COMMENT '帖子ID',
    like_count INT NOT NULL DEFAULT 0 COMMENT '点赞数',
    comment_count INT NOT NULL DEFAULT 0 COMMENT '评论数',
    share_count INT NOT NULL DEFAULT 0 COMMENT '分享数',
    view_count INT NOT NULL DEFAULT 0 COMMENT '浏览数',
    last_activity_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_like_count(like_count),
    INDEX idx_comment_count(comment_count),
    INDEX idx_last_activity(last_activity_time)
) COMMENT='帖子统计表' COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 2. 用户关系表
-- =============================================

-- 用户关注表
CREATE TABLE IF NOT EXISTS user_follows (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关注ID',
    follower_id BIGINT NOT NULL COMMENT '关注者用户ID',
    following_id BIGINT NOT NULL COMMENT '被关注者用户ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    
    -- 唯一约束，防止重复关注
    UNIQUE KEY uk_follow_relation(follower_id, following_id),
    
    -- 索引
    INDEX idx_follower(follower_id),
    INDEX idx_following(following_id),
    INDEX idx_create_time(create_time)
) COMMENT='用户关注表' COLLATE=utf8mb4_unicode_ci;

-- 用户统计表（关注数、粉丝数等）
CREATE TABLE IF NOT EXISTS user_stats (
    user_id BIGINT PRIMARY KEY COMMENT '用户ID',
    following_count INT NOT NULL DEFAULT 0 COMMENT '关注数',
    follower_count INT NOT NULL DEFAULT 0 COMMENT '粉丝数',
    post_count INT NOT NULL DEFAULT 0 COMMENT '帖子数',
    like_received_count INT NOT NULL DEFAULT 0 COMMENT '收到的点赞数',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_following_count(following_count),
    INDEX idx_follower_count(follower_count),
    INDEX idx_post_count(post_count)
) COMMENT='用户统计表' COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 3. 帖子互动表
-- =============================================

-- 帖子点赞表
CREATE TABLE IF NOT EXISTS post_likes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '点赞ID',
    post_id BIGINT NOT NULL COMMENT '帖子ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    
    -- 唯一约束，防止重复点赞
    UNIQUE KEY uk_post_user_like(post_id, user_id),
    
    -- 索引
    INDEX idx_post_id(post_id),
    INDEX idx_user_id(user_id),
    INDEX idx_create_time(create_time)
) COMMENT='帖子点赞表' COLLATE=utf8mb4_unicode_ci;

-- 帖子收藏表
CREATE TABLE IF NOT EXISTS post_favorites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '收藏ID',
    post_id BIGINT NOT NULL COMMENT '帖子ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    
    -- 唯一约束，防止重复收藏
    UNIQUE KEY uk_post_user_favorite(post_id, user_id),
    
    -- 索引
    INDEX idx_post_id(post_id),
    INDEX idx_user_id(user_id),
    INDEX idx_create_time(create_time)
) COMMENT='帖子收藏表' COLLATE=utf8mb4_unicode_ci;

-- 帖子分享记录表
CREATE TABLE IF NOT EXISTS post_shares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分享ID',
    post_id BIGINT NOT NULL COMMENT '帖子ID',
    user_id BIGINT NOT NULL COMMENT '分享用户ID',
    share_type TINYINT NOT NULL DEFAULT 1 COMMENT '分享类型：1-微信好友，2-朋友圈，3-复制链接',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分享时间',

    -- 索引
    INDEX idx_post_id(post_id),
    INDEX idx_user_id(user_id),
    INDEX idx_share_type(share_type),
    INDEX idx_create_time(create_time)
) COMMENT='帖子分享记录表' COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 4. 标签和话题表
-- =============================================

-- 标签表
CREATE TABLE IF NOT EXISTS tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
    name VARCHAR(50) NOT NULL COMMENT '标签名称',
    description VARCHAR(200) DEFAULT NULL COMMENT '标签描述',
    color VARCHAR(7) DEFAULT '#1890ff' COMMENT '标签颜色（十六进制）',
    use_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    is_hot TINYINT NOT NULL DEFAULT 0 COMMENT '是否热门标签：0-否，1-是',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',

    -- 唯一约束
    UNIQUE KEY uk_tag_name(name),

    -- 索引
    INDEX idx_use_count(use_count),
    INDEX idx_is_hot(is_hot),
    INDEX idx_create_time(create_time)
) COMMENT='标签表' COLLATE=utf8mb4_unicode_ci;

-- 帖子标签关联表
CREATE TABLE IF NOT EXISTS post_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    post_id BIGINT NOT NULL COMMENT '帖子ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 唯一约束，防止重复关联
    UNIQUE KEY uk_post_tag(post_id, tag_id),

    -- 索引
    INDEX idx_post_id(post_id),
    INDEX idx_tag_id(tag_id)
) COMMENT='帖子标签关联表' COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 5. 消息通知表
-- =============================================

-- 消息通知表
CREATE TABLE IF NOT EXISTS notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '通知ID',
    user_id BIGINT NOT NULL COMMENT '接收通知的用户ID',
    sender_id BIGINT DEFAULT NULL COMMENT '发送通知的用户ID',
    type TINYINT NOT NULL COMMENT '通知类型：1-点赞，2-评论，3-关注，4-系统通知',
    title VARCHAR(100) NOT NULL COMMENT '通知标题',
    content VARCHAR(500) DEFAULT NULL COMMENT '通知内容',
    related_id BIGINT DEFAULT NULL COMMENT '相关ID（帖子ID、评论ID等）',
    related_type VARCHAR(20) DEFAULT NULL COMMENT '相关类型（post、comment等）',
    is_read TINYINT NOT NULL DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    read_time DATETIME DEFAULT NULL COMMENT '阅读时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',

    -- 索引
    INDEX idx_user_id(user_id),
    INDEX idx_sender_id(sender_id),
    INDEX idx_type(type),
    INDEX idx_is_read(is_read),
    INDEX idx_create_time(create_time),
    INDEX idx_related(related_type, related_id)
) COMMENT='消息通知表' COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 6. 私信表
-- =============================================

-- 私信会话表
CREATE TABLE IF NOT EXISTS private_conversations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '会话ID',
    user1_id BIGINT NOT NULL COMMENT '用户1ID（较小的用户ID）',
    user2_id BIGINT NOT NULL COMMENT '用户2ID（较大的用户ID）',
    last_message_id BIGINT DEFAULT NULL COMMENT '最后一条消息ID',
    last_message_time DATETIME DEFAULT NULL COMMENT '最后消息时间',
    user1_unread_count INT NOT NULL DEFAULT 0 COMMENT '用户1未读消息数',
    user2_unread_count INT NOT NULL DEFAULT 0 COMMENT '用户2未读消息数',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',

    -- 唯一约束，确保两个用户只有一个会话
    UNIQUE KEY uk_conversation(user1_id, user2_id),

    -- 索引
    INDEX idx_user1(user1_id),
    INDEX idx_user2(user2_id),
    INDEX idx_last_message_time(last_message_time)
) COMMENT='私信会话表' COLLATE=utf8mb4_unicode_ci;

-- 私信消息表
CREATE TABLE IF NOT EXISTS private_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '消息ID',
    conversation_id BIGINT NOT NULL COMMENT '会话ID',
    sender_id BIGINT NOT NULL COMMENT '发送者用户ID',
    receiver_id BIGINT NOT NULL COMMENT '接收者用户ID',
    message_type TINYINT NOT NULL DEFAULT 1 COMMENT '消息类型：1-文本，2-图片，3-语音，4-视频',
    content TEXT COMMENT '消息内容',
    media_url VARCHAR(500) DEFAULT NULL COMMENT '媒体文件URL（图片、语音、视频）',
    is_read TINYINT NOT NULL DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
    read_time DATETIME DEFAULT NULL COMMENT '阅读时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',

    -- 外键约束
    FOREIGN KEY (conversation_id) REFERENCES private_conversations(id) ON DELETE CASCADE,

    -- 索引
    INDEX idx_conversation_id(conversation_id),
    INDEX idx_sender_id(sender_id),
    INDEX idx_receiver_id(receiver_id),
    INDEX idx_create_time(create_time),
    INDEX idx_is_read(is_read)
) COMMENT='私信消息表' COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 7. 优化现有评论表结构
-- =============================================

-- 为现有的comments表添加帖子相关字段（如果不存在）
ALTER TABLE comments
ADD COLUMN  post_id BIGINT DEFAULT NULL COMMENT '帖子ID' AFTER topic_id,
ADD INDEX  idx_post_id(post_id);

-- =============================================
-- 8. 系统配置表
-- =============================================

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string、number、boolean、json',
    description VARCHAR(200) DEFAULT NULL COMMENT '配置描述',
    is_public TINYINT NOT NULL DEFAULT 0 COMMENT '是否公开：0-私有，1-公开',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 唯一约束
    UNIQUE KEY uk_config_key(config_key),

    -- 索引
    INDEX idx_is_public(is_public)
) COMMENT='系统配置表' COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 9. 举报表
-- =============================================

-- 举报表
CREATE TABLE IF NOT EXISTS reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '举报ID',
    reporter_id BIGINT NOT NULL COMMENT '举报人用户ID',
    reported_user_id BIGINT DEFAULT NULL COMMENT '被举报用户ID',
    target_type VARCHAR(20) NOT NULL COMMENT '举报目标类型：post、comment、user',
    target_id BIGINT NOT NULL COMMENT '举报目标ID',
    reason TINYINT NOT NULL COMMENT '举报原因：1-垃圾信息，2-违法违规，3-色情内容，4-其他',
    description TEXT DEFAULT NULL COMMENT '举报描述',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '处理状态：0-待处理，1-已处理，2-已忽略',
    handler_id BIGINT DEFAULT NULL COMMENT '处理人ID',
    handle_time DATETIME DEFAULT NULL COMMENT '处理时间',
    handle_result VARCHAR(500) DEFAULT NULL COMMENT '处理结果',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 索引
    INDEX idx_reporter_id(reporter_id),
    INDEX idx_reported_user_id(reported_user_id),
    INDEX idx_target(target_type, target_id),
    INDEX idx_status(status),
    INDEX idx_create_time(create_time)
) COMMENT='举报表' COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 10. 初始化数据
-- =============================================

-- 插入默认标签
INSERT IGNORE INTO tags (name, description, color, is_hot) VALUES
('生活', '记录生活点滴', '#1890ff', 1),
('美食', '分享美食体验', '#f5222d', 1),
('旅行', '旅行见闻分享', '#52c41a', 1),
('摄影', '摄影作品展示', '#722ed1', 1),
('运动', '运动健身相关', '#fa8c16', 1),
('学习', '学习心得分享', '#13c2c2', 0),
('工作', '工作经验交流', '#eb2f96', 0),
('娱乐', '娱乐休闲内容', '#faad14', 0);

-- 插入系统配置
INSERT IGNORE INTO system_configs (config_key, config_value, config_type, description, is_public) VALUES
('post_max_images', '9', 'number', '帖子最大图片数量', 1),
('post_max_content_length', '2000', 'number', '帖子最大内容长度', 1),
('comment_max_length', '500', 'number', '评论最大长度', 1),
('daily_post_limit', '10', 'number', '每日发帖限制', 0),
('enable_location', 'true', 'boolean', '是否启用位置功能', 1),
('enable_private_message', 'true', 'boolean', '是否启用私信功能', 1),
('hot_tag_threshold', '100', 'number', '热门标签阈值', 0);

-- =============================================
-- 11. 触发器（用于自动更新统计数据）
-- =============================================

-- 帖子点赞触发器
DELIMITER $$

CREATE TRIGGER IF NOT EXISTS tr_post_like_insert
AFTER INSERT ON post_likes
FOR EACH ROW
BEGIN
    IF NEW.is_delete = 0 THEN
        -- 更新帖子统计
        INSERT INTO post_stats (post_id, like_count, last_activity_time)
        VALUES (NEW.post_id, 1, NOW())
        ON DUPLICATE KEY UPDATE
            like_count = like_count + 1,
            last_activity_time = NOW();

        -- 更新帖子表的点赞数
        UPDATE posts SET like_count = like_count + 1 WHERE id = NEW.post_id;

        -- 更新用户统计（被点赞用户）
        INSERT INTO user_stats (user_id, like_received_count)
        SELECT user_id, 1 FROM posts WHERE id = NEW.post_id
        ON DUPLICATE KEY UPDATE like_received_count = like_received_count + 1;
    END IF;
END$$

CREATE TRIGGER IF NOT EXISTS tr_post_like_update
AFTER UPDATE ON post_likes
FOR EACH ROW
BEGIN
    IF OLD.is_delete = 0 AND NEW.is_delete = 1 THEN
        -- 取消点赞
        UPDATE post_stats SET
            like_count = GREATEST(like_count - 1, 0),
            last_activity_time = NOW()
        WHERE post_id = NEW.post_id;

        UPDATE posts SET like_count = GREATEST(like_count - 1, 0) WHERE id = NEW.post_id;

        INSERT INTO user_stats (user_id, like_received_count)
        SELECT user_id, 0 FROM posts WHERE id = NEW.post_id
        ON DUPLICATE KEY UPDATE like_received_count = GREATEST(like_received_count - 1, 0);

    ELSEIF OLD.is_delete = 1 AND NEW.is_delete = 0 THEN
        -- 恢复点赞
        INSERT INTO post_stats (post_id, like_count, last_activity_time)
        VALUES (NEW.post_id, 1, NOW())
        ON DUPLICATE KEY UPDATE
            like_count = like_count + 1,
            last_activity_time = NOW();

        UPDATE posts SET like_count = like_count + 1 WHERE id = NEW.post_id;

        INSERT INTO user_stats (user_id, like_received_count)
        SELECT user_id, 1 FROM posts WHERE id = NEW.post_id
        ON DUPLICATE KEY UPDATE like_received_count = like_received_count + 1;
    END IF;
END$$

-- 用户关注触发器
CREATE TRIGGER IF NOT EXISTS tr_user_follow_insert
AFTER INSERT ON user_follows
FOR EACH ROW
BEGIN
    IF NEW.is_delete = 0 THEN
        -- 更新关注者的关注数
        INSERT INTO user_stats (user_id, following_count)
        VALUES (NEW.follower_id, 1)
        ON DUPLICATE KEY UPDATE following_count = following_count + 1;

        -- 更新被关注者的粉丝数
        INSERT INTO user_stats (user_id, follower_count)
        VALUES (NEW.following_id, 1)
        ON DUPLICATE KEY UPDATE follower_count = follower_count + 1;
    END IF;
END$$

CREATE TRIGGER IF NOT EXISTS tr_user_follow_update
AFTER UPDATE ON user_follows
FOR EACH ROW
BEGIN
    IF OLD.is_delete = 0 AND NEW.is_delete = 1 THEN
        -- 取消关注
        INSERT INTO user_stats (user_id, following_count)
        VALUES (NEW.follower_id, 0)
        ON DUPLICATE KEY UPDATE following_count = GREATEST(following_count - 1, 0);

        INSERT INTO user_stats (user_id, follower_count)
        VALUES (NEW.following_id, 0)
        ON DUPLICATE KEY UPDATE follower_count = GREATEST(follower_count - 1, 0);

    ELSEIF OLD.is_delete = 1 AND NEW.is_delete = 0 THEN
        -- 恢复关注
        INSERT INTO user_stats (user_id, following_count)
        VALUES (NEW.follower_id, 1)
        ON DUPLICATE KEY UPDATE following_count = following_count + 1;

        INSERT INTO user_stats (user_id, follower_count)
        VALUES (NEW.following_id, 1)
        ON DUPLICATE KEY UPDATE follower_count = follower_count + 1;
    END IF;
END$$

-- 帖子发布触发器
CREATE TRIGGER IF NOT EXISTS tr_post_insert
AFTER INSERT ON posts
FOR EACH ROW
BEGIN
    IF NEW.is_delete = 0 AND NEW.status = 1 THEN
        -- 初始化帖子统计
        INSERT INTO post_stats (post_id, like_count, comment_count, share_count, view_count, last_activity_time)
        VALUES (NEW.id, 0, 0, 0, 0, NEW.create_time);

        -- 更新用户帖子数
        INSERT INTO user_stats (user_id, post_count)
        VALUES (NEW.user_id, 1)
        ON DUPLICATE KEY UPDATE post_count = post_count + 1;
    END IF;
END$$

DELIMITER ;

-- =============================================
-- 12. 视图（用于复杂查询）
-- =============================================

-- 帖子详情视图
CREATE OR REPLACE VIEW v_post_details AS
SELECT
    p.id,
    p.user_id,
    p.content,
    p.images,
    p.location_name,
    p.location_latitude,
    p.location_longitude,
    p.location_address,
    p.is_public,
    p.status,
    p.create_time,
    p.update_time,
    -- 用户信息
    u.nickname,
    u.avatar,
    u.level,
    -- 统计信息
    COALESCE(ps.like_count, 0) as like_count,
    COALESCE(ps.comment_count, 0) as comment_count,
    COALESCE(ps.share_count, 0) as share_count,
    COALESCE(ps.view_count, 0) as view_count,
    ps.last_activity_time
FROM posts p
LEFT JOIN ba_user u ON p.user_id = u.id
LEFT JOIN post_stats ps ON p.id = ps.post_id
WHERE p.is_delete = 0;

-- 用户统计视图
CREATE OR REPLACE VIEW v_user_stats AS
SELECT
    u.id,
    u.nickname,
    u.avatar,
    u.level,
    COALESCE(us.following_count, 0) as following_count,
    COALESCE(us.follower_count, 0) as follower_count,
    COALESCE(us.post_count, 0) as post_count,
    COALESCE(us.like_received_count, 0) as like_received_count
FROM ba_user u
LEFT JOIN user_stats us ON u.id = us.user_id;

-- 添加个人简介字段
ALTER TABLE ba_user ADD COLUMN bio VARCHAR(500) DEFAULT '' COMMENT '个人简介';

-- 添加舞种字段
ALTER TABLE ba_user ADD COLUMN dance_type VARCHAR(50) DEFAULT '' COMMENT '学习舞种';

-- 为现有comments表添加帖子关联
ALTER TABLE comments ADD COLUMN post_id BIGINT DEFAULT NULL COMMENT '帖子ID' AFTER topic_id;
ALTER TABLE comments ADD INDEX idx_post_id(post_id);
