<template>
	<view class="signing">
		
		<view class="sig_one" style="margin:0;margin-bottom:600rpx;">
			<!-- 合同内容 -->
			<image :src="imgbaseUrl + htImgsrc" mode="widthFix" style="width:100%;" :show-menu-by-longpress="true"></image>
		</view>
		
		<view class="sig_two sig_two_fixed">
			<view class="sig_two_t" @click="cesTap">签字区域（必填）</view>
			<view class="sig_two_b" @click="toPop1">
				<text v-if="image2 == ''">点击签字</text>
				<image :src="image2" mode="heightFix" v-else></image>
			</view>
			<view class="sig_two_f" @click="qmTap">提交</view>
			<view class="aqjlViw"></view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		<jp-signature-popup ref="signature1" popup v-model="image2" :required="true" />
		
	</view>
</template>


<script>
import {
	getContractApi,
	upImg,
	hqhtnrApi,
	signContractApi
} from '@/config/http.achieve.js'
import {
	apis
} from '@/config/http.api.js'
export default {
	data() {
		return {
			isLogined:true,
			imgbaseUrl:'',
			image2:'',
			url: '',
			htImgsrc:'',
			htId:0
		}
	},
	onShow() {
		
	},
	onLoad(options) {
		console.log('options12',options,apis)
		this.imgbaseUrl = this.$baseUrl_ht;
		this.htId = options.id ? options.id : 0;
		this.htconData();//获取合同内容
	},
	methods: {
		//测试跳转
		cesTap(){
			uni.showModal({
				title: '提示',
				content: '查询到您有未签署的合同，请点击下一步继续签署',
				showCancel:false,
				confirmText:'下一步',
				success: function (res) {
					if (res.confirm) {
						console.log('用户点击确定');
						uni.reLaunch({
							url:'/pages/index/signing?id=23'
						})
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
		},
		//获取合同内容
		htconData(){
			uni.showLoading({
				title: '生成合同中'
			});
			let that = this;
			hqhtnrApi({id:that.htId}).then(res => {
				if (res.code == 1) {
					uni.hideLoading();
					that.htImgsrc = res.data;
				}
			})
		},
		//签名提交
		qmTap(){
			if(this.image2 == ''){
				uni.showToast({
					icon: 'none',
					title: '请先进行签字',
					duration: 2000
				});
				return false;
			}
			this.sctxTap(this.image2)
		},
		toPop1(){
			this.$refs.signature1.toPop()
		},
		//上传头像
		sctxTap(tempFilePaths){
			console.log(tempFilePaths,'tempFilePaths')
			var that = this;
			uni.showLoading({
				title:'加载中'
			})
			upImg(tempFilePaths, 'file').then(ress => {
				console.log('上传图片',ress)
				if (ress.code == 1) {
					uni.hideLoading();
					// that.avatar = ress.data.file.url
					that.qshtSub(ress.data.file.url);//签署合同提交
				}
			})
		},
		//签署合同提交
		qshtSub(image){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			signContractApi({id:that.htId,image:image}).then(res => {
				if (res.code == 1) {
					uni.hideLoading();
					that.getContractData(res.data);//继续查询是否有未签署完的合同，获取未签署的合同
					uni.showToast({
						icon: 'success',
						title: '签署成功',
						duration: 2000
					});
					
				}
			})
		},
		//继续查询是否有未签署完的合同
		//获取未签署的合同
		getContractData(num){
			var that = this;
			getContractApi({}).then(res => {
				console.log('获取未签署的合同',res)
				if (res.code == 1) {
					// res.data = 22;
					if(res.data){
						uni.showModal({
							title: '提示',
							content: '查询到您还有'+num+'份未签署的合同，请点击下一步继续签署',
							showCancel:false,
							confirmText:'下一步',
							success: function (ress) {
								if (ress.confirm) {
									console.log('用户点击确定');
									uni.reLaunch({
										url:'/pages/index/signing?id=' + res.data
									})
								} else if (ress.cancel) {
									console.log('用户点击取消');
								}
							}
						});
					}else{
						setTimeout(function(){
							uni.switchTab({
								url:'/pages/index/index'
							})
						},1500)
					}
					
				}
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.signing{overflow: hidden;
	-width: 100%;
	-height:100vh;
}
page{padding-bottom: 0;background:#fff;}
</style>