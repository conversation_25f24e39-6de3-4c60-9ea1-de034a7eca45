<template>
	<view class="service" v-if="loding">
		
		<view class="serkf_one">
			<view class="serkf_one_n" v-if="kefuInfo.to_user_id">
				<view class="serkf_one_t">
					<image :src="imgbaseUrl + kefuInfo.to_user_avatar" class="serkf_one_t_l" mode="aspectFill"></image>
					<view class="serkf_one_t_c">
						<view class="serkf_one_t_c_a">{{kefuInfo.to_user_nickname}}</view>
						<view class="serkf_one_t_c_b" v-if="kefuInfo.wechat_number != ''">微信号：{{kefuInfo.wechat_number}}<image src="/static/images/icon57.png" @click="copyText('zagsjdksag4')"></image></view>
					</view>
					<image :src="imgbaseUrl + kefuInfo.wechat_or_code" class="serkf_one_t_r" @click="openImg(imgbaseUrl + kefuInfo.wechat_or_code)"></image>
				</view>
				<view class="serkf_one_b"><image src="/static/images/icon58.png"></image>客服正在赶来，请耐心等待。可以添加客服微信联系哟~</view>
			</view>
		</view>
		
		<view class="serv_con">
			<!-- <view class="serv_con_li" :class="item.type == 1 ? 'serv_con_li_br' : ''" v-for="(item,index) in servLists" :key="index">
				<image class="serv_con_li_l" :src="item.type == 1 ? '/static/images/index_fox_lsjs.png' : '/static/images/toux1.png'"></image>
				<view class="serv_con_li_r"><view>{{item.content}}</view></view>
			</view> -->
			<!-- //用户类型:1=用户,2=客服 -->
			<view class="serv_con_li" :class="item.is_tourist == 1 ? 'serv_con_li_br' : ''" v-for="(item,index) in servLists" :key="index">
				
				<!-- <template v-else> -->
					<view class="serv_con_li_date" v-if="index > 0 && (index + 1) % 5 === 0">{{item._add_time}}</view>
					<image class="serv_con_li_l" :src="item.is_tourist == 1 ? (userInfo.avatar == '' ? '/static/images/toux.png' : imgbaseUrl + userInfo.avatar) : imgbaseUrl + kefuInfo.to_user_avatar"></image>
					<view class="serv_con_li_r">
						<view>
							<template v-if="item.msn_type == 1">{{item.msn}}</template>
							<image v-if="item.msn_type == 3" :src="item.msn" mode="aspectFill" @click="openImg(item.msn)"></image>
						</view>
					</view>
				<!-- </template> -->
			</view>
			<view class="serv_con_hhend" v-if="iscxhh">当前会话已结束，点此<text @click="cxfqTap">重新发起会话</text></view>
		</view>
		
		<view class="serv_foo">
			<input type="text" placeholder="请输入您想咨询的内容" v-model="msn" confirm-type="send" @confirm="sendTap" />
			<view @click="sendTap">发送</view>
		</view>
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	userInfoApi,
	kefuzrgApi,
	kefuzRecordApi,
	sendMessageApi,
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			loding:false,
			servLists:[
				// {content:'FOX岗顶店客服团子为您服务，请问您有什 么问题呢?',type:0,id:0},
				// {content:'你好',type:1,id:1},
			],
			userInfo:{},//个人信息1
			kefuInfo:{
				to_user_id:0,
				to_user_avatar: "",
				to_user_nickname: "",
				wechat_number: "",
				wechat_or_code: "",
			},//客服信息
			store_id:0,//门店id
			imgbaseUrl:'',//图片地址
			msn:'',//内容
			iscxhh:false,//是否需要重新发起会话
		}
	},
	onShow() {
		 this.imgbaseUrl = this.$baseUrl;
	},
	onLoad(option) {
		var that = this;
		this.store_id = option.id;
		this.serData();//连接Socket
		this.kefuzRecordData('chudi');//获取聊天记录
		
		this.userData();//个人信息
		
		uni.onSocketMessage(function (res) {
		  console.log('收到服务器内容：' + res.data,res);
		  // that.kefuzRecordData();//获取聊天记录
		});
		
	},
	methods: {
		//重新发起会话
		cxfqTap(){
			this.serData();//连接Socket
			this.kefuzRecordData('chudi');//获取聊天记录
		},
		//发送消息
		sendTap(){
			if(this.msn.split(' ').join('').length == 0){
				uni.showToast({
					icon:'none',
					title: '请输入要发送的内容',
					duration: 2000
				});
				return false;
			}
			if(this.iscxhh){
				uni.showToast({
					icon:'none',
					title: '当前会话已断开，请重新连接会话',
					duration: 2000
				});
				return false;
			}
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			sendMessageApi({
				to_user_id:that.kefuInfo.to_user_id,
				msn_type:1,
				msn:that.msn,
				user_id:uni.getStorageSync('userid'),
				store_id:that.store_id
			}).then(res => {
				console.log('发送消息',res)
				if (res.status == 200) {
					that.msn = '';
					uni.hideLoading();
					that.kefuzRecordData('chudi');//获取聊天记录
				}
			})
			
		},
		//个人信息
		userData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			userInfoApi({}).then(res => {
				console.log('个人中心',res)
				if (res.code == 1) {
					that.userInfo = res.data;
					uni.hideLoading();
				}
			})
		},
		//获取聊天记录
		kefuzRecordData(chudi){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			kefuzRecordApi({
				uid:uni.getStorageSync('userid'),
				store_id:that.store_id,
				page:1,
				limit:9999,
			}).then(res => {
				console.log('获取聊天记录',res)
				uni.hideLoading();
				if (res.status == 200) {
					that.servLists = res.data.serviceList;
					that.loding = true;
					if(chudi){
						setTimeout(()=>{
							uni.pageScrollTo({
								scrollTop:999999
							},0)
						},200);
					}
				}
				that.iscxhh = res.status == 200 ? false : true;
			})
		},
		//连接Socket
		serData(){
			var that = this;
			var server_token = uni.getStorageSync('server_token')
			uni.connectSocket({
				// url: 'wss://www.example.com/socket',
				url:`wss://dancekefu.xinzhiyukeji.cn/ws?token=${server_token}&type=user&form=pc`,
				// header: {
				// 	'content-type': 'application/json'
				// },
				// protocols: ['protocol1'],
				// method: 'GET',
				success: function (res) {
					console.log(res,'success');
					that.kefuzrgData();//转人工连接客服
				},
				fail: function (res) {
					console.log(res,'fail');
				},
				complete: function (res) {
					console.log(res,'complete');
				},
				
			});
		},
		//转人工连接客服
		kefuzrgData(){
			
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			var server_token = uni.getStorageSync('server_token')
			console.log(that.store_id,'that.store_id1111')
			kefuzrgApi({
				uid:uni.getStorageSync('userid'),
				store_id:that.store_id,
			}).then(res => {
				console.log('转人工连接客服',res)
				if (res.status == 200) {
					that.kefuInfo = res.data;
				}
			})
		},
		//打开图片
		openImg(imgs) {
			uni.previewImage({
				current: 0,
				urls: [imgs]
			})
		},
		//复制微信
		copyText(text) {
		  uni.setClipboardData({
			data: text,
			success: function () {
			  uni.showToast({
				title: '复制成功',
				icon: 'success',
				duration: 2000
			  });
			}
		  });
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.service{overflow: hidden;}
page{padding-bottom: 0;}
</style>